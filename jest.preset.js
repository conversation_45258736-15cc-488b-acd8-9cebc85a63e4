const nxPreset = require('@nx/jest/preset').default

/** @type {import('jest').Config} */
module.exports = {
  ...nxPreset,

  testTimeout: process.env.CI ? 10000 : 5000,

  // Allow jest to resolve the file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],

  transform: {
    '^(?!.*\\.(js|jsx|ts|tsx|css|json)$)': '@nx/react/plugins/jest',
    '^.+\\.[tj]sx?$': ['babel-jest', { presets: ['@nx/react/babel'] }]
  },
  transformIgnorePatterns: [
    'node_modules/(?!(change-case|camel-case|capital-case|constant-case|dot-case|header-case|no-case|param-case|pascal-case|path-case|sentence-case|snake-case|tslib)/)'
  ],

  /**
   * When you use Jest with JSDOM you are getting a broken test environment.
   * Some Node.js globals cease to exist (e.g. Request, Response, TextEncoder, TextDecoder, ReadableStream1),
   * while others stop behaving correctly (e.g. Event, MessageEvent2, structuredClone()3).
   * That is caused by jest-environment-jsdom and JSDOM relying on polyfills to implement standard APIs that
   * have been available globally both in the browser and in Node.js for years.
   *
   * This is a temporary workaround for https://github.com/mswjs/data/issues/306#issuecomment-2446904312
   */
  testEnvironment: 'jest-fixed-jsdom'
}
