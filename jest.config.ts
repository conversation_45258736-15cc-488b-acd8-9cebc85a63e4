import { getJestProjectsAsync } from '@nx/jest'
import { Config } from 'jest'

export default async (): Promise<Config> => ({
  projects: [...(await getJestProjectsAsync())],
  coverageReporters: ['clover', 'json', 'lcov', 'text', 'html'],
  maxWorkers: process.env.CI ? '50%' : '75%',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  setupFilesAfterEnv: ['./setupJestMock.ts'],
  testPathIgnorePatterns: ['<rootDir>/coverage/', '<rootDir>/dist/', '<rootDir>/node_modules/'],
  // CI-specific optimizations
  ...(process.env.CI && {
    testTimeout: 30000,
    bail: 1,
    verbose: false,
    silent: true
  }),
  // Performance optimizations
  collectCoverageFrom: [
    'libs/**/*.{ts,tsx}',
    'apps/**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/*.stories.{ts,tsx}',
    '!**/node_modules/**',
    '!**/coverage/**',
    '!**/dist/**'
  ],

  // Allow jest to resolve the paths to the libs
  moduleNameMapper: {
    '^@gc/components$': '<rootDir>/libs/shared/components/src/index.ts',
    '^@gc/hooks$': '<rootDir>/libs/hooks/src/index.ts',
    '^@gc/hooks/(.*)$': '<rootDir>/libs/hooks/src/$1',
    '^@gc/types$': '<rootDir>/libs/types/src/index.ts',
    '^@gc/utils$': '<rootDir>/libs/shared/utils/src/index.ts',
    '^@gc/redux-store$': '<rootDir>/libs/redux-store/src/index.ts',
    '^@gc/rtk-queries$': '<rootDir>/libs/api/rtk-queries/src/index.ts',
    '^@gc/shared/test$': '<rootDir>/libs/shared/test/src/index.ts',
    '^@gc/constants$': '<rootDir>/libs/shared/constants/src/index.ts',
    '^@gc/features-common-reporting$': '<rootDir>/libs/features/common/reporting/src/index.ts',
    '^@gc/shared/env$': '<rootDir>/libs/shared/env/src/index.ts',
    '^@gc/api/client$': '<rootDir>/libs/api/client/src/index.ts',
    '^@gc/shared/config$': '<rootDir>/libs/shared/config/index.ts'
  }
})
