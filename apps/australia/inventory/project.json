{"name": "australia-inventory", "$schema": "node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/australia/inventory/src", "projectType": "application", "tags": ["scope:australia-inventory", "type:app"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/australia/inventory", "outputFileName": "bundle", "baseHref": "/", "main": "apps/australia/inventory/src/faste.tsx", "tsConfig": "apps/australia/inventory/tsconfig.app.json", "assets": [], "styles": [], "scripts": [], "vendorChunk": false, "webpackConfig": "webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": false}, "production": {"fileReplacements": [{"replace": "apps/australia/inventory/src/environments/environment.ts", "with": "apps/australia/inventory/src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": false}, "browser": {"extractLicenses": false, "optimization": false, "sourceMap": false, "vendorChunk": true, "main": "apps/australia/inventory/src/browser.tsx", "outputFileName": "main"}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "australia-inventory:build", "port": 4002, "allowedHosts": "all"}, "configurations": {"development": {"buildTarget": "australia-inventory:build:development", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "production": {"buildTarget": "australia-inventory:build:production", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "browser": {"buildTarget": "australia-inventory:build:browser", "port": 1233}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/australia/inventory/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/australia/inventory/jest.config.ts", "passWithNoTests": true}}, "deploy": {"executor": "nx:run-commands", "options": {"cwd": "tools/scripts", "command": "./spectrum-deploy.sh australia/inventory australia-inventory {args.ENV}", "parallel": true}}, "webpack-bundle-analyzer": {"executor": "nx:run-commands", "options": {"command": "nx build --stats-json --skip-nx-cache --configuraion=production && webpack-bundle-analyzer dist/apps/australia/inventory/stats.json"}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventional"}}}}