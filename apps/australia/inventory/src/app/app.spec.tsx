import '@testing-library/jest-dom'

import { setUpStore } from '@gc/redux-store'
import { actAwait, render, screen } from '@gc/utils'
import { MemoryRouter, useParams } from 'react-router-dom'

import App from './app'

const mockUseParams = useParams

function MockComponent({ children, dataTestId }: Readonly<{ children: React.ReactNode; dataTestId: string }>) {
  const { tab } = mockUseParams()
  const testId = tab ? `${dataTestId}-${tab}` : dataTestId
  return <div data-testid={testId}>{children}</div>
}

// Mock the layout components
jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  Contingency: ({ children }: { children: React.ReactNode }) => <div data-testid='mock-contingency'>{children}</div>,
  Snackbar: ({ children }: { children: React.ReactNode }) => <div data-testid='mock-snackbar'>{children}</div>
}))

jest.mock('@gc/features-common-inventory', () => ({
  ...jest.requireActual('@gc/features-common-inventory'),
  Inventory: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-inventory'>{children}</MockComponent>
  ),
  Shipments: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-shipments'>{children}</MockComponent>
  ),
  Returns: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-returns'>{children}</MockComponent>
  ),
  Transfers: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-transfers'>{children}</MockComponent>
  ),
  TransferDetailsMobile: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-transfer-details-mobile'>{children}</MockComponent>
  ),
  ShipmentDetailsMobile: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-shipment-details-mobile'>{children}</MockComponent>
  ),
  ReturnDetailsMobile: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-return-details-mobile'>{children}</MockComponent>
  )
}))

const renderWithRouter = (initialRoute = '/', isMobile = false) => {
  return render(
    <MemoryRouter initialEntries={[initialRoute]}>
      <App />
    </MemoryRouter>,
    { store: setUpStore(), width: isMobile ? 900 : 1024 }
  )
}

describe('Inventory App', () => {
  it('should render successfully', () => {
    const { baseElement } = renderWithRouter()
    expect(baseElement).toBeTruthy()
  })

  it('should render Snackbar component', () => {
    renderWithRouter()
    expect(screen.getByTestId('mock-snackbar')).toBeInTheDocument()
  })

  it('should render Contingency component', () => {
    renderWithRouter()
    expect(screen.getByTestId('mock-contingency')).toBeInTheDocument()
  })

  it('should render inventory dashboard on root path', () => {
    renderWithRouter('/')
    expect(screen.getByTestId('mock-inventory')).toBeInTheDocument()
  })

  it('should render inventory id tab', () => {
    renderWithRouter('/1')
    expect(screen.getByTestId('mock-inventory-1')).toBeInTheDocument()
  })

  it('should render shipments tab', () => {
    renderWithRouter('/shipments')
    expect(screen.getByTestId('mock-shipments')).toBeInTheDocument()
  })

  it('should render shipments id tab in mobile', () => {
    renderWithRouter('/shipments/1', true)
    expect(screen.getByTestId('mock-shipment-details-mobile')).toBeInTheDocument()
  })

  it('should render returns tab', () => {
    renderWithRouter('/returns')
    expect(screen.getByTestId('mock-returns')).toBeInTheDocument()
  })

  it('should render returns id tab in mobile', async () => {
    renderWithRouter('/returns/1', true)
    await actAwait()
    expect(screen.getByTestId('mock-return-details-mobile')).toBeInTheDocument()
  })

  it('should render transfers tab', () => {
    renderWithRouter('/transfers')
    expect(screen.getByTestId('mock-transfers')).toBeInTheDocument()
  })

  it('should render transfers id tab in mobile', () => {
    renderWithRouter('/transfers/1', true)
    expect(screen.getByTestId('mock-transfer-details-mobile')).toBeInTheDocument()
  })
})
