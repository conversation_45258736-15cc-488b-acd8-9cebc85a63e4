import './app.module.scss'

import { Contingency, Snackbar } from '@gc/components'
import {
  Inventory,
  ReturnDetailsMobile,
  Returns,
  RootState,
  ShipmentDetailsMobile,
  Shipments,
  StockTake,
  TransferDetailsMobile,
  Transfers,
  useAppDispatch
} from '@gc/features-common-inventory'
import { useIsMobile } from '@gc/hooks'
import { type ReactElement, useMemo } from 'react'
import { Route, Routes } from 'react-router-dom'
const App = (): ReactElement => {
  const dispatch = useAppDispatch()
  const isMobile = useIsMobile()

  const mobileRoutes = useMemo(() => {
    return isMobile ? (
      <>
        <Route path='/returns/:code' element={<ReturnDetailsMobile />} />
        <Route path='/shipments/:code' element={<ShipmentDetailsMobile />} />
        <Route path='/transfers/:code' element={<TransferDetailsMobile />} />
      </>
    ) : null
  }, [isMobile])

  return (
    <>
      <Routes>
        <Route path='/' element={<Inventory />} />
        <Route path='/:tab' element={<Inventory />} />
        <Route path='/shipments' element={<Shipments />} />
        <Route path='/returns' element={<Returns />} />
        <Route path='/transfers' element={<Transfers />} />
        <Route path='/stock-take' element={<StockTake />} />
        {mobileRoutes}
      </Routes>
      <Snackbar />
      <Contingency<RootState> codes={['ALL']} types={['dialog', 'loadingModal']} dispatch={dispatch} />
    </>
  )
}

export default App
