import { store } from '@gc/features-common-inventory'
import { getFasteModule } from '@gc/shared/faste-app-starter'
import { updateStore } from '@gc/utils'
import { StrictMode } from 'react'
import { Provider } from 'react-redux'

import App from './app/app'

const FASTE = getFasteModule(() => {
  updateStore('locale', { code: 'en-AU', country: 'AU', language: 'en-AU' })

  return (
    <StrictMode>
      <Provider store={store}>
        <App />
      </Provider>
    </StrictMode>
  )
})
export default FASTE
