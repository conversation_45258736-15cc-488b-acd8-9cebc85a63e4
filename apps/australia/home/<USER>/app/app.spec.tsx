import '@testing-library/jest-dom'

import { render, screen } from '@gc/utils'

import App from './app'

// Mock NxWelcome component since we're only testing App
jest.mock('./nx-welcome', () => ({
  __esModule: true,
  default: ({ title }: { title: string }) => <div data-testid='nx-welcome'>{title}</div>
}))

describe('App', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<App />)
    expect(baseElement).toBeTruthy()
  })

  it('should render NxWelcome with correct title prop', () => {
    render(<App />)
    const welcomeElement = screen.getByTestId('nx-welcome')
    expect(welcomeElement).toHaveTextContent('australia-home')
  })
})
