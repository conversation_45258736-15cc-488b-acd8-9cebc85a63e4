import '@testing-library/jest-dom'

import { setUpStore } from '@gc/redux-store'
import { render, screen } from '@gc/utils'
import { MemoryRouter, useParams } from 'react-router-dom'

import App from './app'

const mockUseParams = useParams

function MockComponent({ children, dataTestId }: Readonly<{ children: React.ReactNode; dataTestId: string }>) {
  const { tab } = mockUseParams()
  const testId = tab ? `${dataTestId}-${tab}` : dataTestId
  return <div data-testid={testId}>{children}</div>
}

// Mock the layout components
jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  Contingency: ({ children }: { children: React.ReactNode }) => <div data-testid='mock-contingency'>{children}</div>,
  Snackbar: ({ children }: { children: React.ReactNode }) => <div data-testid='mock-snackbar'>{children}</div>,
  OrderDetails: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-order-details'>{children}</MockComponent>
  )
}))

jest.mock('@gc/features-common-orders', () => ({
  ...jest.requireActual('@gc/features-common-orders'),
  Orders: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-orders'>{children}</MockComponent>
  )
}))

const renderWithRouter = (initialRoute = '/', isMobile = false) => {
  return render(
    <MemoryRouter initialEntries={[initialRoute]}>
      <App />
    </MemoryRouter>,
    { store: setUpStore(), width: isMobile ? 900 : 1024 }
  )
}

describe('Orders App', () => {
  it('should render successfully', () => {
    const { baseElement } = renderWithRouter()
    expect(baseElement).toBeTruthy()
  })

  it('should render Snackbar component', () => {
    renderWithRouter()
    expect(screen.getByTestId('mock-snackbar')).toBeInTheDocument()
  })

  it('should render Contingency component', () => {
    renderWithRouter()
    expect(screen.getByTestId('mock-contingency')).toBeInTheDocument()
  })

  it('should render orders on root path', () => {
    renderWithRouter('/')
    expect(screen.getByTestId('mock-orders')).toBeInTheDocument()
  })

  it('should render order details on id tab', () => {
    renderWithRouter('/1')
    expect(screen.getByTestId('mock-order-details')).toBeInTheDocument()
  })
})
