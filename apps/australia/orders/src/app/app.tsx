import './app.module.scss'

import { Contingency, OrderDetails, Snackbar } from '@gc/components'
import { Orders, OrdersModalContainer, RootState, useAppDispatch } from '@gc/features-common-orders'
import type { ReactElement } from 'react'
import { Route, Routes } from 'react-router-dom'

const App = (): ReactElement => {
  const dispatch = useAppDispatch()
  return (
    <>
      <Routes>
        <Route path='/' element={<Orders />} />
        <Route path='/:code' element={<OrderDetails />} />
      </Routes>
      <Snackbar />
      <Contingency<RootState> codes={['ALL']} types={['dialog', 'loadingModal']} dispatch={dispatch} />
      <OrdersModalContainer />
    </>
  )
}

export default App
