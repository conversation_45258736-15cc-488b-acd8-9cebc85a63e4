{"name": "australia-orders", "$schema": "node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/australia/orders/src", "projectType": "application", "tags": ["scope:australia-orders", "type:app"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/australia/orders", "outputFileName": "bundle", "baseHref": "/", "main": "apps/australia/orders/src/faste.tsx", "tsConfig": "apps/australia/orders/tsconfig.app.json", "assets": [], "styles": [], "scripts": [], "vendorChunk": false, "webpackConfig": "webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true}, "production": {"fileReplacements": [{"replace": "apps/australia/orders/src/environments/environment.ts", "with": "apps/australia/orders/src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": false}, "browser": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true, "main": "apps/australia/orders/src/browser.tsx", "outputFileName": "main"}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "australia-orders:build", "port": 4001, "allowedHosts": "all"}, "configurations": {"development": {"buildTarget": "australia-orders:build:development", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "production": {"buildTarget": "australia-orders:build:production", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "browser": {"buildTarget": "australia-orders:build:browser", "port": 1233}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/australia/orders/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/australia/orders/jest.config.ts", "passWithNoTests": true}}, "deploy": {"executor": "nx:run-commands", "options": {"cwd": "tools/scripts", "command": "./spectrum-deploy.sh australia/orders australia-orders {args.ENV}", "parallel": true}}, "webpack-bundle-analyzer": {"executor": "nx:run-commands", "options": {"command": "nx build --stats-json --skip-nx-cache --configuraion=production && webpack-bundle-analyzer dist/apps/australia/orders/stats.json"}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventional"}}}}