{"name": "mycrop-farmers", "$schema": "node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/mycrop/farmers/src", "projectType": "application", "tags": ["scope:mycrop-farmers", "type:app"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/mycrop/farmers", "outputFileName": "bundle", "baseHref": "/", "main": "apps/mycrop/farmers/src/faste.tsx", "tsConfig": "apps/mycrop/farmers/tsconfig.app.json", "assets": [], "scripts": [], "vendorChunk": false, "webpackConfig": "webpack.config.js"}, "configurations": {"local": {"main": "apps/mycrop/farmers/src/local.ts", "extractLicenses": false, "optimization": false, "sourceMap": true}, "development": {"extractLicenses": false, "optimization": false, "sourceMap": true}, "production": {"fileReplacements": [{"replace": "apps/mycrop/farmers/src/environments/environment.ts", "with": "apps/mycrop/farmers/src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": false}, "browser": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true, "main": "apps/mycrop/farmers/src/browser.tsx", "outputFileName": "main"}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "mycrop-farmers:build", "port": 4200, "allowedHosts": "all"}, "configurations": {"local": {"buildTarget": "mycrop-farmers:build:local", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "development": {"buildTarget": "mycrop-farmers:build:development", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "production": {"buildTarget": "mycrop-farmers:build:production", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "browser": {"buildTarget": "mycrop-farmers:build:browser", "port": 1233}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/mycrop/farmers/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/mycrop/farmers/jest.config.ts", "passWithNoTests": true}}, "deploy": {"executor": "nx:run-commands", "options": {"cwd": "tools/scripts", "command": "./spectrum-deploy.sh mycrop/farmers mycrop-farmers {args.ENV}", "parallel": true}}, "webpack-bundle-analyzer": {"executor": "nx:run-commands", "options": {"command": "nx build --stats-json --skip-nx-cache --configuraion=production && webpack-bundle-analyzer dist/apps/mycrop/farmers/stats.json"}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventional"}}, "open": {"executor": "nx:run-commands", "options": {"commands": ["node ./scripts/openBrowser.js https://dev-mycrop.bayer.com/farmers/my-farmers"]}, "configurations": {"local": {"commands": ["sleep 3", "node ./scripts/openBrowser.js https://dev-mycrop.bayer.com/farmers/my-farmers"], "parallel": false}}}}}