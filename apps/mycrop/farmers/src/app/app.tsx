import './app.module.scss'

import {
  Dashboard,
  FarmerInfo,
  FarmersModalContainer,
  LicenseSearch,
  MyView,
  OldMyFarmers
} from '@gc/features-common-farmers'
import { Route, Routes } from 'react-router-dom'

const App = () => {
  return (
    <>
      <MyView />
      <Routes>
        <Route path='/' element={<Dashboard />} />
        <Route path='/my-farmers' element={<OldMyFarmers />} />
        <Route path='/farmer-info' element={<FarmerInfo />} />
        <Route path='/license-search' element={<LicenseSearch />} />
      </Routes>
      <FarmersModalContainer />
    </>
  )
}

export default App
