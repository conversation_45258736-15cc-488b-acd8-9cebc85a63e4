import './app.module.scss'

import { Contingency, Snackbar } from '@gc/components'
import {
  Inventory,
  InventoryModalContainer,
  ReturnDetailsMobile,
  Returns,
  RootState,
  SeedProductDetails,
  ShipmentDetailsMobile,
  Shipments,
  TransferDetailsMobile,
  Transfers,
  useAppDispatch
} from '@gc/features-common-inventory'
import { AuroraLocationPicker } from '@gc/features-common-location-picker'
import { handleAdminDispatch, useAdmin, useIsMobile } from '@gc/hooks'
import { shouldShowLocationPicker } from '@gc/utils'
import { type ReactElement, useMemo } from 'react'
import { Route, Routes, useLocation } from 'react-router-dom'

const allowedPaths = ['/', '/shipments', '/returns', '/transfers']

const App = ({ admin }: { admin?: boolean }): ReactElement => {
  const dispatch = useAppDispatch()
  const isMobile = useIsMobile()
  const isAdmin = useAdmin(admin)

  handleAdminDispatch(dispatch, isAdmin)
  const location = useLocation()

  const showLocationPicker = useMemo(
    () => shouldShowLocationPicker(location, isMobile, allowedPaths),
    [location, isMobile]
  )

  const mobileRoutes = useMemo(() => {
    return isMobile ? (
      <>
        <Route path='/returns/:code' element={<ReturnDetailsMobile />} />
        <Route path='/shipments/:code' element={<ShipmentDetailsMobile />} />
        <Route path='/transfers/:code' element={<TransferDetailsMobile />} />
      </>
    ) : null
  }, [isMobile])

  return (
    <>
      {showLocationPicker && <AuroraLocationPicker isAdmin={isAdmin} />}
      <Routes>
        <Route path='/' element={<Inventory />} />
        <Route path='/shipments' element={<Shipments />} />
        <Route path='/returns' element={<Returns />} />
        <Route path='/transfers' element={<Transfers />} />
        <Route path='/seed-products/:code' element={<SeedProductDetails />} />
        {mobileRoutes}
      </Routes>
      <Snackbar />
      <Contingency<RootState> codes={['ALL']} types={['dialog', 'loadingModal']} dispatch={dispatch} />
      <InventoryModalContainer />
    </>
  )
}

export default App
