import './app.module.scss'

import { Contingency, Snackbar } from '@gc/components'
import { IS_SMALL_MOBILE, IS_TABLET_MIN } from '@gc/constants'
import {
  DesktopViewRequired,
  ReportingModalContainer,
  Reports,
  RootState,
  useAppDispatch,
  ViewReport
} from '@gc/features-common-reporting'
import { type ReactElement } from 'react'
import MediaQuery from 'react-responsive'
import { Route, Routes } from 'react-router-dom'

const App = (): ReactElement => {
  const dispatch = useAppDispatch()

  return (
    <>
      <MediaQuery maxWidth={IS_SMALL_MOBILE}>
        <DesktopViewRequired />
      </MediaQuery>

      <MediaQuery minWidth={IS_TABLET_MIN}>
        <Routes>
          <Route path='/' element={<Reports />} />
          <Route path='/:reportId' element={<ViewReport />} />
        </Routes>

        <Snackbar />
        <ReportingModalContainer />
        <Contingency<RootState> codes={['ALL']} types={['dialog', 'loadingModal']} dispatch={dispatch} />
      </MediaQuery>
    </>
  )
}

export default App
