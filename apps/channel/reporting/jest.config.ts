export default {
  rootDir: '../../../',
  preset: './jest.preset.js',
  displayName: 'channel-reporting',
  coverageDirectory: 'coverage/apps/channel/reporting',
  setupFilesAfterEnv: ['<rootDir>/setupJestMock.ts'],
  testMatch: [
    '<rootDir>/apps/channel/reporting/src/**/*.(test|spec).{js,jsx,ts,tsx}',
    '<rootDir>/libs/shared/components/src/features/modals/reporting/**/*.(test|spec).{js,jsx,ts,tsx}'
  ],
  // Allow jest to resolve the paths to the libs
  moduleNameMapper: {
    '^@gc/components$': '<rootDir>/libs/shared/components/src/index.ts',
    '^@gc/hooks$': '<rootDir>/libs/hooks/src/index.ts',
    '^@gc/hooks/(.*)$': '<rootDir>/libs/hooks/src/$1',
    '^@gc/types$': '<rootDir>/libs/types/src/index.ts',
    '^@gc/utils$': '<rootDir>/libs/shared/utils/src/index.ts',
    '^@gc/redux-store$': '<rootDir>/libs/redux-store/src/index.ts',
    '^@gc/rtk-queries$': '<rootDir>/libs/api/rtk-queries/src/index.ts',
    '^@gc/shared/test$': '<rootDir>/libs/shared/test/src/index.ts',
    '^@gc/constants$': '<rootDir>/libs/shared/constants/src/index.ts',
    '^@gc/features-common-reporting$': '<rootDir>/libs/features/common/reporting/src/index.ts',
    '^@gc/shared/env$': '<rootDir>/libs/shared/env/src/index.ts',
    '^@gc/api/client$': '<rootDir>/libs/api/client/src/index.ts',
    '^@gc/shared/config$': '<rootDir>/libs/shared/config/index.ts'
  }
}
