import '@testing-library/jest-dom'

import * as reduxStore from '@gc/redux-store'
import { setUpStore } from '@gc/redux-store'
import { render, screen } from '@gc/utils'
import { MemoryRouter, useParams } from 'react-router-dom'

import App from './app'

const mockUseParams = useParams

function MockComponent({ children, dataTestId }: Readonly<{ children: React.ReactNode; dataTestId: string }>) {
  const { tab } = mockUseParams()
  const testId = tab ? `${dataTestId}-${tab}` : dataTestId
  return <div data-testid={testId}>{children}</div>
}

// Mock the layout components
jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  Contingency: ({ children }: { children: React.ReactNode }) => <div data-testid='mock-contingency'>{children}</div>,
  Snackbar: ({ children }: { children: React.ReactNode }) => <div data-testid='mock-snackbar'>{children}</div>,
  DiscountFooter: () => <div data-testid='mock-discount-footer' />
}))

jest.mock('@gc/features-common-quotes', () => ({
  ...jest.requireActual('@gc/features-common-quotes'),
  Quotes: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-quotes'>{children}</MockComponent>
  ),
  QuoteDetails: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-quote-details'>{children}</MockComponent>
  ),
  QuoteModalContainer: () => <div data-testid='mock-quote-modal-container' />
}))

jest.mock('@gc/features-common-location-picker', () => ({
  ...jest.requireActual('@gc/features-common-location-picker'),
  AuroraLocationPicker: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='aurora-location-picker'>{children}</MockComponent>
  )
}))

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useSelectedAccount: () => ({ sapAccountId: '**********' })
}))

jest.mock('@gc/redux-store', () => {
  const originalModule = jest.requireActual('@gc/redux-store')
  return {
    ...originalModule,
    useAppDispatch: jest.fn(() => jest.fn())
  }
})

const renderWithRouter = (initialRoute = '/', isMobile = false) => {
  return render(
    <MemoryRouter initialEntries={[initialRoute]}>
      <App base={''} />
    </MemoryRouter>,
    { store: setUpStore(), width: isMobile ? 900 : 1024 }
  )
}

describe('Quotes App', () => {
  it('should render successfully', () => {
    const { baseElement } = renderWithRouter()
    expect(baseElement).toBeTruthy()
  })

  it('should render Snackbar component', () => {
    renderWithRouter()
    expect(screen.getByTestId('mock-snackbar')).toBeInTheDocument()
  })

  it('should render Contingency component', () => {
    renderWithRouter()
    expect(screen.getByTestId('mock-contingency')).toBeInTheDocument()
  })

  it('should render quotes on root path', () => {
    renderWithRouter('/')
    expect(screen.getByTestId('mock-quotes')).toBeInTheDocument()
  })

  it('should render quote details on id tab', () => {
    renderWithRouter('/1')
    expect(screen.getByTestId('mock-quote-details')).toBeInTheDocument()
  })

  it('should render QuoteModalContainer component', () => {
    renderWithRouter()
    expect(screen.getByTestId('mock-quote-modal-container')).toBeInTheDocument()
  })

  it('should render DiscountFooter component', () => {
    const mockDispatch = jest.fn()
    renderWithRouter()
    expect(screen.getByTestId('mock-discount-footer')).toBeInTheDocument()
    jest.spyOn(reduxStore, 'useAppDispatch').mockReturnValue(mockDispatch)
  })

  it('should render AuroraLocationPicker when path is allowed and not mobile', () => {
    const { queryByTestId } = renderWithRouter('/', false)
    expect(queryByTestId('aurora-location-picker')).toBeTruthy()
  })

  it('should not render AuroraLocationPicker when path is not allowed', () => {
    const { queryByTestId } = renderWithRouter('/farmerId/deliveries/1', false)
    // Simulate a disallowed path
    expect(queryByTestId('aurora-location-picker')).toBeNull()
  })

  it('should not render AuroraLocationPicker when it is mobile', () => {
    jest.mock('@gc/hooks', () => ({
      ...jest.requireActual('@gc/hooks'),
      useIsMobile: () => true
    }))
    const { queryByTestId } = renderWithRouter('/', true)
    const picker = queryByTestId('aurora-location-picker')
    expect(picker).toBeNull()
  })
})
