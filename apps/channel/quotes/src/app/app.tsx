import './app.module.scss'

import { Contingency, DiscountFooter, Snackbar } from '@gc/components'
import { AuroraLocationPicker } from '@gc/features-common-location-picker'
import { QuoteDetails, QuoteModalContainer, Quotes, type RootState, useAppDispatch } from '@gc/features-common-quotes'
import { handleAdminDispatch, useAdmin, useIsMobile } from '@gc/hooks'
import { resetNotification, setNotification, useQuotesQueries } from '@gc/redux-store'
import { shouldShowLocationPicker } from '@gc/utils'
import { type ReactElement, useEffect, useMemo } from 'react'
import { Route, Routes, useLocation } from 'react-router-dom'

const allowedPaths = ['/']

const App = ({ base: _base, admin }: { base: string; admin?: boolean }): ReactElement => {
  const dispatch = useAppDispatch()
  const isAdmin = useAdmin(admin)
  const isMobile = useIsMobile()

  handleAdminDispatch(dispatch, isAdmin)
  const quotesApi = useQuotesQueries()

  useEffect(() => {
    return () => {
      // Before leaving the Quotes module, we are resetting quotes related APIs.
      if (!window.location.pathname.endsWith('quotes')) {
        dispatch(quotesApi.util.resetApiState())
        dispatch(resetNotification())
      }
    }
  }, [dispatch, quotesApi.util])

  const location = useLocation()
  const showLocationPicker = useMemo(
    () => shouldShowLocationPicker(location, isMobile, allowedPaths),
    [location, isMobile]
  )

  return (
    <>
      {showLocationPicker && <AuroraLocationPicker isAdmin={isAdmin} />}
      <Routes>
        <Route path='/' element={<Quotes />} />
        <Route path='/:code' element={<QuoteDetails />} />
      </Routes>
      <Snackbar handleClose={() => dispatch(setNotification({ open: false, message: '' }))} />
      <Contingency<RootState> codes={['ALL']} types={['dialog', 'loadingModal']} dispatch={dispatch} />
      <QuoteModalContainer />
      <DiscountFooter />
    </>
  )
}

export default App
