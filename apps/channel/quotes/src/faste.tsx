import { store } from '@gc/features-common-quotes'
import { getFasteModule } from '@gc/shared/faste-app-starter'
import { getIsAdmin } from '@gc/utils' // Import the utility function
import { StrictMode } from 'react'
import { Provider } from 'react-redux'

import App from './app/app'

const FASTE = getFasteModule((moduleProps) => {
  const isAdmin = getIsAdmin(moduleProps)

  const { route } = moduleProps
  return (
    <StrictMode>
      <Provider store={store}>
        <App base={route.path} admin={isAdmin} />
      </Provider>
    </StrictMode>
  )
})
export default FASTE
