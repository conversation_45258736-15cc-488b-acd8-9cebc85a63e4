import '@testing-library/jest-dom'

import { setUpStore } from '@gc/redux-store'
import { render, screen } from '@testing-library/react'
import { Provider } from 'react-redux'
import { MemoryRouter } from 'react-router-dom'

import App from './app'

// Mock components and hooks
jest.mock('@gc/features-common-advance-application', () => ({
  ...jest.requireActual('@gc/features-common-advance-application'),
  useAppDispatch: () => jest.fn(), // stubbed dispatch
  AdvanceApplication: () => <div data-testid='advance-application' />,
  AdvanceApplicationModalContainer: () => <div data-testid='modal-container' />
}))

jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  Snackbar: () => <div data-testid='snackbar' />,
  Contingency: ({ dispatch }: { dispatch: unknown }) => <div data-testid='contingency' data-dispatch={!!dispatch} />
}))

describe('Advance Application App component', () => {
  const renderWithRouter = () => {
    return render(
      <Provider store={setUpStore()}>
        <MemoryRouter initialEntries={['/']}>
          <App base={''} />
        </MemoryRouter>
      </Provider>
    )
  }

  it('should render App component', () => {
    const { baseElement } = renderWithRouter()
    expect(baseElement).toBeTruthy()
  })
  it('should render Snackbar component', () => {
    renderWithRouter()
    expect(screen.getByTestId('snackbar')).toBeInTheDocument()
  })

  it('should render Modal Container component', () => {
    renderWithRouter()
    expect(screen.getByTestId('modal-container')).toBeInTheDocument()
  })
  it('should render Contingency component', () => {
    renderWithRouter()
    expect(screen.getByTestId('contingency')).toBeInTheDocument()
  })

  it('should render Contingency with dispatch prop', () => {
    renderWithRouter()
    expect(screen.getByTestId('contingency')).toHaveAttribute('data-dispatch', 'true')
  })
})
