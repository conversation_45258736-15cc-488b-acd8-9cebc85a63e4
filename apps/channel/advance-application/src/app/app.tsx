/* eslint-disable @nx/enforce-module-boundaries */
import './app.module.scss'

import { Contingency, Snackbar } from '@gc/components'
import {
  AdvanceApplication,
  AdvanceApplicationModalContainer,
  RootState,
  useAppDispatch
} from '@gc/features-common-advance-application'
import { handleAdminDispatch, useAdmin } from '@gc/hooks'
import { type ReactElement } from 'react'
import { Route, Routes } from 'react-router-dom'

const App = ({ base: _base, admin }: { base: string; admin?: boolean }): ReactElement => {
  const dispatch = useAppDispatch()
  const isAdmin = useAdmin(admin)

  handleAdminDispatch(dispatch, isAdmin)

  return (
    <>
      <Routes>
        <Route path='/' element={<AdvanceApplication />} />
      </Routes>
      <Snackbar />
      <AdvanceApplicationModalContainer />
      <Contingency<RootState> codes={['ALL']} types={['dialog', 'loadingModal']} dispatch={dispatch} />
    </>
  )
}

export default App
