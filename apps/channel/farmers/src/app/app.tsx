import './app.module.scss'

import { Contingency, DeliveryDetailsMobile, FarmersReturnDetailsMobile, Snackbar } from '@gc/components'
import { GLOBAL_APP } from '@gc/constants'
import { FarmerProfile, FarmersModalContainer, MyFarmers } from '@gc/features-common-farmers'
import { AuroraLocationPicker } from '@gc/features-common-location-picker'
import { handleAdminDispatch, useAdmin, useAppSessionData, useIsMobile, useUpsertAppSessionData } from '@gc/hooks'
import { GlobalRootState, setNotification, useGlobalDispatch } from '@gc/redux-store'
import { shouldShowLocationPicker } from '@gc/utils'
import get from 'lodash/get'
import isNil from 'lodash/isNil'
import { type ReactElement, useEffect, useMemo } from 'react'
import { Route, Routes, useLocation } from 'react-router-dom'

const allowedPaths = ['/']

const App = ({ admin }: { admin?: boolean }): ReactElement => {
  const dispatch = useGlobalDispatch()
  const isMobile = useIsMobile()
  const isAdmin = useAdmin(admin)
  const [updatedAppSessionData] = useUpsertAppSessionData()
  const cachedAppNotification = get(useAppSessionData(), GLOBAL_APP)?.notification

  handleAdminDispatch(dispatch, isAdmin)
  const location = useLocation()

  const showLocationPicker = useMemo(
    () => shouldShowLocationPicker(location, isMobile, allowedPaths),
    [location, isMobile]
  )

  const mobileRoutes = useMemo(() => {
    return isMobile ? (
      <>
        <Route path='/:farmerId/returns/:code' element={<FarmersReturnDetailsMobile />} />
        <Route path='/:farmerId/deliveries/:code' element={<DeliveryDetailsMobile />} />
      </>
    ) : null
  }, [isMobile])

  useEffect(() => {
    if (!isNil(cachedAppNotification)) {
      dispatch(setNotification(cachedAppNotification))
    }

    return () => {
      if (window.location.pathname.endsWith('farmers')) return

      // If there is a notification, we are resetting it
      if (!isNil(cachedAppNotification)) {
        updatedAppSessionData(GLOBAL_APP, { notification: undefined })
      }
    }
  }, [cachedAppNotification, dispatch, updatedAppSessionData])
  return (
    <>
      {showLocationPicker && <AuroraLocationPicker isAdmin={isAdmin} />}
      <Routes>
        <Route path='/' element={<MyFarmers />} />
        <Route path='/:code' element={<FarmerProfile />} />
        {mobileRoutes}
      </Routes>
      <Snackbar />
      <Contingency<GlobalRootState> codes={['ALL']} types={['dialog', 'loadingModal']} dispatch={dispatch} />
      <FarmersModalContainer />
    </>
  )
}

export default App
