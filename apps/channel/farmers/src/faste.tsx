import { setupStore } from '@gc/features-common-farmers'
import { getFasteModule } from '@gc/shared/faste-app-starter'
import { getIsAdmin } from '@gc/utils'
import { StrictMode } from 'react'
import { Provider } from 'react-redux'

import App from './app/app'

const FASTE = getFasteModule((moduleProps) => {
  const isAdmin = getIsAdmin(moduleProps)

  return (
    <StrictMode>
      <Provider store={setupStore()}>
        <App admin={isAdmin} />
      </Provider>
    </StrictMode>
  )
})

export default FASTE
