import './app.module.scss'

import { Contingency, Snackbar } from '@gc/components'
import { NBLocationPicker } from '@gc/features-nb-location-picker'
import {
  AgentOrders,
  Orders,
  OrdersModalContainer,
  ProductReview,
  ProductShipments,
  RootState,
  useAppDispatch,
  ViewCartAndReview,
  ViewEditOrder
} from '@gc/features-nb-orders'
import type { ReactElement } from 'react'
import { Route, Routes, useLocation } from 'react-router-dom'

const App = (): ReactElement => {
  const dispatch = useAppDispatch()
  const location = useLocation()
  const allowedPaths = ['/']
  const currentPath = window.location.pathname

  const { state } = window.history

  return (
    <>
      {allowedPaths.includes(location.pathname) && <NBLocationPicker />}

      <Routes>
        <Route path='/' element={currentPath === '/agent-orders' ? <AgentOrders /> : <Orders />} />
        <Route path='/cart' element={<ViewCartAndReview />} />
        <Route path='/review' element={state?.usage === 'seedOrder' ? <ViewCartAndReview /> : <ProductReview />} />
        <Route path='/:code/' element={<ViewEditOrder />} />
        <Route path='/:code/productShipment' element={<ProductShipments />} />
      </Routes>
      <Snackbar />
      <Contingency<RootState> codes={['ALL']} types={['dialog', 'loadingModal']} dispatch={dispatch} />
      <OrdersModalContainer />
    </>
  )
}

export default App
