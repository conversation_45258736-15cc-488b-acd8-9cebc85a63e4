import { store } from '@gc/features-nb-orders'
import { getFasteModule } from '@gc/shared/faste-app-starter'
import { StrictMode } from 'react'
import { Provider } from 'react-redux'

import App from './app/app'

const FASTE = getFasteModule(
  () => {
    return (
      <StrictMode>
        <Provider store={store}>
          <App />
        </Provider>
      </StrictMode>
    )
  },
  { app: 'mycrop', env: 'stable', profile: 'orders' }
)
export default FASTE
