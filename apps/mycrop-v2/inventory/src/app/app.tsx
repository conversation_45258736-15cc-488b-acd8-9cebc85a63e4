import './app.module.scss'

import { Inventory, Shipment, ViewShipment } from '@gc/features-nb-inventory'
import type { ReactElement } from 'react'
import { Route, Routes } from 'react-router-dom'

const App = (): ReactElement => {
  return (
    <Routes>
      <Route path='/' element={<Inventory />} />
      <Route path='/shipments' element={<Shipment />} />
      <Route path='/shipments/:code' element={<ViewShipment />} />
    </Routes>
  )
}

export default App
