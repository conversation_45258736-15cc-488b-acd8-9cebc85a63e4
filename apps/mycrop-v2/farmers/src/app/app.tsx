import './app.module.scss'

import {
  FarmerOrderInfo,
  FarmersModalContainer,
  LicenseSearch,
  NbFarmerInfo,
  NbMyFarmers
} from '@gc/features-common-farmers'
import { NBLocationPicker } from '@gc/features-nb-location-picker'
import { Route, Routes, useLocation } from 'react-router-dom'

const App = () => {
  const location = useLocation()
  const allowedPaths = ['/']

  return (
    <>
      {allowedPaths.includes(location.pathname) && <NBLocationPicker />}
      <Routes>
        <Route path='/' element={<NbMyFarmers />} />
        <Route path='/farmer-info' element={<NbFarmerInfo />} />
        <Route path='/order-details' element={<FarmerOrderInfo />} />
        <Route path='/license-search' element={<LicenseSearch />} />
      </Routes>
      <FarmersModalContainer />
    </>
  )
}

export default App
