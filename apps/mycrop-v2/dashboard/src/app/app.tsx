import './app.module.scss'

import { Dashboard, DashboardModalContainer } from '@gc/features-nb-dashboard'
import { NBLocationPicker } from '@gc/features-nb-location-picker'
import type { ReactElement } from 'react'
import { Route, Routes, useLocation } from 'react-router-dom'

const App = (): ReactElement => {
  const location = useLocation()
  const allowedPaths = ['/']
  return (
    <>
      {allowedPaths.includes(location.pathname) && <NBLocationPicker />}

      <Routes>
        <Route path='/' element={<Dashboard />} />
      </Routes>
      <DashboardModalContainer />
    </>
  )
}

export default App
