import './app.module.scss'

import { Dashboard, FarmerInfo, MyFarmers } from '@gc/features-common-farmers'
import type { ReactElement } from 'react'
import { Route, Routes } from 'react-router-dom'

const App = (): ReactElement => {
  return (
    <Routes>
      <Route path='/' element={<Dashboard />} />
      <Route path='/my-farmers' element={<MyFarmers />} />
      <Route path='/farmer-info' element={<FarmerInfo />} />
    </Routes>
  )
}

export default App
