{"name": "seedsman-farmers", "$schema": "node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/seedsman/farmers/src", "projectType": "application", "open": {"executor": "nx:run-commands", "options": {"commands": ["node ./scripts/openBrowser.js https://mychannel-np.seedsmansource.com/farmers/my-farmers"]}, "configurations": {"local": {"commands": ["sleep 3", "node ./scripts/openBrowser.js https://mychannel-np.seedsmansource.com/farmers/my-farmers"], "parallel": false}}}, "tags": ["scope:seedsman-farmers", "type:app"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/seedsman/farmers", "outputFileName": "bundle", "baseHref": "/", "main": "apps/seedsman/farmers/src/faste.tsx", "tsConfig": "apps/seedsman/farmers/tsconfig.app.json", "assets": [], "scripts": [], "vendorChunk": false, "webpackConfig": "webpack.config.js"}, "configurations": {"local": {"main": "apps/seedsman/farmers/src/local.ts", "extractLicenses": false, "optimization": false, "sourceMap": true}, "development": {"extractLicenses": false, "optimization": false, "sourceMap": true}, "production": {"fileReplacements": [{"replace": "apps/seedsman/farmers/src/environments/environment.ts", "with": "apps/seedsman/farmers/src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": false}, "browser": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true, "main": "apps/seedsman/farmers/src/browser.tsx", "outputFileName": "main"}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "seedsman-farmers:build", "port": 4200, "allowedHosts": "all"}, "configurations": {"local": {"buildTarget": "seedsman-farmers:build:local", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "development": {"buildTarget": "seedsman-farmers:build:development", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "production": {"buildTarget": "seedsman-farmers:build:production", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "browser": {"buildTarget": "seedsman-farmers:build:browser", "port": 1233}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/seedsman/farmers/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/seedsman/farmers/jest.config.ts", "passWithNoTests": true}}, "deploy": {"executor": "nx:run-commands", "options": {"cwd": "tools/scripts", "command": "./spectrum-deploy.sh seedsman/farmers seedsman-farmers {args.ENV}", "parallel": true}}, "webpack-bundle-analyzer": {"executor": "nx:run-commands", "options": {"command": "nx build --stats-json --skip-nx-cache --configuraion=production && webpack-bundle-analyzer dist/apps/seedsman/farmers/stats.json"}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventional"}}}}