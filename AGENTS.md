# AGENTS.md - Development Guide

## Build/Lint/Test Commands

- **Build all**: `npm run build` or `nx run-many --target=build --configuration=development --parallel=5`
- **Lint all**: `npm run lint` or `nx run-many --target=lint --parallel=5 --exclude 'storybook-host'`
- **Test all**: `npm run test` or `nx run-many --target=test --parallel=5 --exclude components,storybook-host -- --silent`
- **Single project test**: `nx run <project-name>:test` (e.g., `nx run channel-farmers:test`)
- **Single project build**: `nx run <project-name>:build:development` (e.g., `nx run channel-farmers:build:development`)
- **Single project lint**: `nx run <project-name>:lint` (e.g., `nx run channel-farmers:lint`)

## Code Style Guidelines

- **TypeScript**: Use interfaces over types, avoid `any`, prefer `unknown`, use PascalCase for types/interfaces
- **React**: Functional components only, custom hooks for reusable logic, proper memoization with useMemo/useCallback
- **Imports**: Auto-sorted with simple-import-sort plugin, use @gc/\* aliases from tsconfig paths
- **Formatting**: Prettier config - single quotes, no semicolons, 2 spaces, 120 char width, no trailing commas
- **Naming**: camelCase for variables/functions, PascalCase for components/types, UPPER_CASE for constants
- **Error Handling**: No console.log (only warn/error allowed), implement Error Boundaries, handle async errors
- **Testing**: Use React Testing Library, write unit tests for components, test user interactions

## Commit Format

`country/type/taskNumber(scope): subject` (e.g., `us/feat/123456(quotes): Added new button`)

## Project Structure

NX monorepo with apps/ (channel, mycrop, australia, seedsman) and libs/ (shared components, hooks, utils, types)
