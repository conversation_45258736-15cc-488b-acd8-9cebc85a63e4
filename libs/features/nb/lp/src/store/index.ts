import { buildStore, mergeWithGlobalReducers } from '@gc/redux-store'
import { useDispatch } from 'react-redux'

const rootReducer = mergeWithGlobalReducers({})

export const setUpStore = (preloadedState?: Partial<RootState>) =>
  buildStore(
    { preloadedState, reducer: rootReducer },
    {
      useGlobalMiddleware: true,
      injectAcsCommonApi: true,
      middlewareOpts: { serializableCheck: false }
    }
  )

export type RootState = ReturnType<typeof rootReducer>
export type AppStore = ReturnType<typeof setUpStore>
export type AppDispatch = AppStore['dispatch']
export const useAppDispatch: () => AppDispatch = useDispatch
