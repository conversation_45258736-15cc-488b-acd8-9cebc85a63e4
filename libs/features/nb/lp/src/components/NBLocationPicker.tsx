import { LocationPicker } from '@gc/features-common-location-picker'
import { useAcsCommonQueries } from '@gc/redux-store'

export function NBLocationPicker() {
  const { useGetDealerAccountHierarchyQuery } = useAcsCommonQueries()
  const { data: dealerHierarchy, isError, refetch } = useGetDealerAccountHierarchyQuery()
  return <LocationPicker dealerHierarchy={dealerHierarchy} isError={isError} refetch={refetch} />
}

export default NBLocationPicker
