import * as reduxStore from '@gc/redux-store'
import { setUpStore } from '@gc/redux-store'
import { AccountHierarchy } from '@gc/types'
import { render } from '@gc/utils'
import { MemoryRouter as Router } from 'react-router-dom'

import NBLocationPicker from './NBLocationPicker'

type mockResponseType = {
  data: {
    baseAccount: AccountHierarchy
    selectedAccount: AccountHierarchy
    includeSubAccounts: boolean
  }
  isError: boolean
}

const mockResponse: mockResponseType = {
  data: {
    baseAccount: {
      accountName: 'PHIL LEHMAN',
      children: [
        {
          accountName: 'LAWES AG SERVICE',
          uId: '',
          irdId: '31',
          sapAccountId: '2',
          level: 2
        }
      ],
      irdId: '89607',
      sapAccountId: '1',
      level: 1
    },
    selectedAccount: {
      accountName: 'PHIL LEHMAN',
      irdId: '89607',
      sapAccountId: '1',
      level: 1,
      uId: ''
    },
    includeSubAccounts: false
  },
  isError: false
}

jest.mock('@gc/redux-store', () => ({
  ...jest.requireActual('@gc/redux-store'),
  useAcsCommonQueries: () => ({ useGetDealerAccountHierarchyQuery: () => mockResponse })
}))

const translations: { [key: string]: string } = {
  'common.try_again.label': 'Try again',
  'common.try_again_msg.label': 'Loading Error',
  'location_picker.all_locations.label': 'All Locations'
}

jest.mock('react-i18next', () => ({
  // this mock makes sure any components using the translate hook can use it without a warning being shown
  useTranslation: () => {
    return {
      t: (str: string) => translations[str] ?? str
    }
  }
}))

jest.mock('@gc/hooks', () => {
  const originalModule = jest.requireActual('@gc/hooks')
  const mockAppSessionData = {}
  const mockUpsertAppSessionData = jest.fn()

  return {
    ...originalModule,
    useIsMobile: jest.fn(() => true),
    useScreenRes: jest.fn(() => 375),
    useIsSmallMobile: jest.fn(),
    useAppSession: () => [mockAppSessionData, mockUpsertAppSessionData],
    useMemoizedTranslation: () => (key: string) => translations[key] ?? key
  }
})

describe('NBLocationPicker', () => {
  let mockStore: ReturnType<typeof setUpStore>
  const mockDispatch = jest.fn()
  beforeEach(() => {
    jest.clearAllMocks()
    mockStore = setUpStore(undefined, {
      injectConfigDataApi: true,
      injectOrdersApi: true,
      injectInventoryApi: true
    })
    jest.spyOn(reduxStore, 'useGlobalDispatch').mockReturnValue(mockDispatch)
  })
  it('should render successfully', () => {
    const { baseElement } = render(
      <Router>
        <NBLocationPicker />
      </Router>,
      { store: mockStore }
    )
    expect(baseElement).toBeTruthy()
  })
})
