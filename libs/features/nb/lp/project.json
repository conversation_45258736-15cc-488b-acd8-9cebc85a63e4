{"name": "features-nb-location-picker", "$schema": "../../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/features/nb/lp/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/features/nb/lp/jest.config.ts"}}}}