.insights {
  width: auto;
  height: 92px;
  padding-bottom: 24px;
  @media (max-width: 768px) {
    height: auto;
    padding-top: 20px;
    padding-bottom: 0;
  }
}

.insightsbar {
  display: flex;
  width: auto;
  height: 68px;
  align-items: center;
  justify-content: space-between;
  @media (max-width: 768px) {
    height: auto;
  }
}

.actionbar {
  width: auto;
  height: 40px;
  gap: 16px;
  display: flex;
}

.scorecards {
  height: 68px;
  gap: 8px;
  display: flex;
  flex-direction: row;
  @media (max-width: 768px) {
    flex-direction: column;
    width: 100%;
    height: auto;
    gap: 20px;
  }
}

.list {
  display: flex;
  justify-content: end;
  width: 100%;
  height: 100%;
  gap: 16px;
  padding: 0;
  @media (max-width: 768px) {
    width: 100%;
  }
}

.itemWrapper {
  background: #ffffff;
  display: flex;
  width: 340px;
  height: 68px;
  min-height: 48px;
  padding-top: 12px;
  padding-right: 16px;
  padding-bottom: 12px;
  padding-left: 16px;
  @media (max-width: 768px) {
    width: 100%;
  }
}
