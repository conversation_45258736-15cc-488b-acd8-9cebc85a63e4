.is-loading {
  padding: 200px 100px;
}

.grid {
  padding: 0px !important;
}

.container {
  position: relative;

  @media (min-width: 1024px) {
    padding: 40px 80px;
  }
}

.tab_outer_container {
  @media (min-width: 1024px) {
    width: 100%;
    padding: 0 80px;
    box-sizing: border-box;
  }
}

.header {
  @media (min-width: 1024px) {
    margin-bottom: 48px;
  }
}

@media (min-width: 720px) {
  :global(.lmnt-table__cell-content) {
    min-height: 48px;
    height: auto !important;
  }
}

.mktyr-caption {
  font-size: 14px;
}

.card-no-border {
  border: none !important;
}
.card-mobile {
  border-top: 1px solid rgb(224, 224, 224) !important;
  margin: 20px 0;
  border-bottom: 1px solid rgb(224, 224, 224) !important;
  border-left: 0 !important;
  border-right: 0 !important;
}
.card {
  border-radius: 0 !important;
  min-height: 160px;
  .mdc-typography--headline3 {
    font-weight: 700 !important;
    font-style: normal !important;
    font-size: 14px;
    padding: 10px 0;
  }
  .typography-text {
    font-weight: 400 !important;
    font-style: normal !important;
    font-size: 14px;
  }
}
.card-title {
  margin: -6px 0px -12px;
  div {
    font-size: 16px !important;
    font-weight: 500 !important;
  }
}
.card-body {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px 0;
  font-size: 14px;
  font-weight: 400;
  .maintext {
    font-size: 14px !important;
    font-weight: 700 !important;
    padding: 10px 0;
    font-style: normal;
    letter-spacing: normal;
  }
}
