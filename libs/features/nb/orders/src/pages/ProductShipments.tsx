import { Card, CardContent } from '@element/react-card'
import { TypoCaption, TypoDisplay, TypoSubtitle } from '@element/react-typography'
import { Badge, Band, List as ListComponent, LoadingAndContingencySection, TopAppBar } from '@gc/components'
import map from 'lodash/map'
import { useCallback, useEffect, useMemo } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

import { useOrdersLobConfig } from '../hooks'
import styles from './ProductShipments.module.scss'

export function ProductShipments() {
  const location = useLocation()
  const order = location.state?.orderDetails
  const product = location.state?.product
  const consignments = location.state?.consignments

  const navigate = useNavigate()

  const { viewOrderPageConfig, orderStatusThemeConfig } = useOrdersLobConfig()

  useEffect(() => {
    window.scrollTo(0, 0)
  }, [])

  const handleClose = useCallback(() => {
    navigate('..', { relative: 'path', state: { orderDetails: order } })
  }, [navigate, order])

  const getStyledListItems = useCallback(
    (consignment: any) => {
      const properties = viewOrderPageConfig?.productListExpandableColumns.filter(
        (item: any) => !['shipmentId', 'statusDisplay'].includes(item.accessor)
      )
      return map(properties, (row: any) => {
        return {
          row: row,
          trailingBlock: <TypoCaption>{consignment[row.accessor]}</TypoCaption>,
          primaryText: <TypoSubtitle level={2}>{row.header}</TypoSubtitle>
        }
      })
    },
    [viewOrderPageConfig?.productListExpandableColumns]
  )

  const renderProductName = useMemo(() => {
    return (
      <Card className={styles['card-title']}>
        <CardContent>
          <TypoDisplay level={5} className={styles['order-id']}>
            {product.productDesc}
          </TypoDisplay>
          <TypoCaption>
            {product.delivered}/{product.quantity} units shipped
          </TypoCaption>
          <TypoCaption>{product.pending} units pending</TypoCaption>
        </CardContent>
      </Card>
    )
  }, [product])

  const getColor = useCallback((status: string) => orderStatusThemeConfig[status], [orderStatusThemeConfig])

  const renderProductList = useMemo(() => {
    const totalShipments = consignments.length
    if ((consignments?.length || 0) > 0) {
      return consignments?.map((consignment: any, ind: number) => {
        return (
          <Card className={styles['card']}>
            <CardContent>
              <>
                <Band
                  containerClassName='lmnt-theme-secondary-50-bg header'
                  placement='list'
                  primaryText={`Shipment ${consignment?.shipmentId || ''}`}
                  secondaryText1={{
                    children: `${ind + 1} of ${totalShipments}`
                  }}
                  trailingTextProps={{
                    children: (
                      <Badge labelText={consignment?.statusDisplay} themeColor={getColor(consignment?.statusDisplay)} />
                    )
                  }}
                />
                <ListComponent
                  divider
                  className={styles['product-list-mobile']}
                  listItemClassName={styles['product-list-item']}
                  items={getStyledListItems(consignment)}
                />
              </>
            </CardContent>
          </Card>
        )
      })
    }
  }, [consignments, getColor, getStyledListItems])

  return (
    <div className={styles['product-shipment-container']}>
      <TopAppBar
        title={'Product Shipments'}
        leadingIconButtonProps={{
          icon: 'arrow_back',
          onClick: () => handleClose()
        }}
      />
      {renderProductName}
      <LoadingAndContingencySection
        hasError={false}
        isLoading={false}
        hasData={consignments.length > 0}
        noDataHeader={'No Data Present'}
        noDataDescription={''}
      >
        {renderProductList}
      </LoadingAndContingencySection>
    </div>
  )
}

export default ProductShipments
