import { ActionMenu<PERSON><PERSON>on, Header, TopAppBar } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { useAppSessionData, useMemoizedTranslation, useModal } from '@gc/hooks'
import { getModal, setProductFilterOptions, useGlobalDispatch } from '@gc/redux-store'
import { Facet } from '@gc/types'
import { getFasteStoreKey } from '@gc/utils'
import get from 'lodash/get'
import noop from 'lodash/noop'
import { useCallback, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import MediaQuery from 'react-responsive'

import { AgentOrderList } from '../components'
import { useOrdersLobConfig } from '../hooks'
import filtersList from '../mocks/filtersList.json'
import filters from '../mocks/filtersSeedOrderCreate.json'
import styles from './Orders.module.scss'

export function AgentOrders() {
  const t = useMemoizedTranslation()
  const dispatch = useGlobalDispatch()
  const { openModal } = useModal()
  const modal = useSelector(getModal)
  const { ordersPageConfig } = useOrdersLobConfig()
  const appSessionData = useAppSessionData()
  const fasteStoreKey = getFasteStoreKey('orders', 'orders')

  const [openSearch, setOpenSearch] = useState(false)
  const [searchTerm, setSearchTerm] = useState(get(appSessionData, `${fasteStoreKey}.searchTerm`, '') as string)

  const handleOpenSearch = () => setOpenSearch(true)
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)
  const handleCancelSearch = () => setSearchTerm('')
  const handleCloseSearch = () => {
    setSearchTerm('')
    setOpenSearch(false)
  }
  const handleFarmerOrder = () => {
    openModal({ name: 'SELECT_FARMER', props: { usage: 'order' } })
  }
  const handleDealerOrder = () => {
    openModal({ name: 'SELECT_PRODUCTS', props: { usage: 'dealerOrder' } })
  }
  const handleSupplierSale = () => {
    openModal({ name: 'SELECT_SUPPLIER', props: { usage: 'supplierSale', type: 'supplier' } })
  }
  const handleFarmerSale = () => {
    openModal({ name: 'SELECT_FARMER', props: { usage: 'farmerSale' } })
  }
  const handleSeedOrder = () => {
    dispatch(
      setProductFilterOptions((filters || []) as Facet[], [
        {
          category: 'favorite',
          title: t('common.favorite.label'),
          switchLabel: t('common.favorites_switch.label'),
          filterType: 'switch',
          options: [{ label: t('common.favorite_only_option.label'), value: t('common.favorite_only_option.label') }],
          selectedOptions: []
        }
      ])
    )
    openModal({ name: 'SELECT_PRODUCTS', props: { usage: 'seedOrder' } })
  }

  const handleCreateOrder = useCallback(
    (name: any) => name && openModal({ name: name, props: { usage: 'order' } }),
    [openModal]
  )

  const mapHandlers = (value: string) => {
    if (value === 'farmerOrder') return handleFarmerOrder
    if (value === 'dealerOrder') return handleDealerOrder
    if (value === 'supplierSale') return handleSupplierSale
    if (value === 'farmerSale') return handleFarmerSale
    if (value === 'seedOrder') return handleSeedOrder
    return noop
  }

  useEffect(() => {
    dispatch(setProductFilterOptions(filtersList.facets || [], []))
  }, [dispatch, t])

  const renderButtons = (
    buttonConfig: {
      label: string
      value: string
      isDisabled?: (a?: object) => boolean
    }[]
  ) => {
    return buttonConfig.map((button) => ({
      label: t(button.label),
      value: button.value,
      onClick: mapHandlers(button.value)
    }))
  }

  const showDesktopActionButtons = ordersPageConfig?.desktopButtons?.length > 0 || false
  const showMobileActionButtons = (ordersPageConfig?.mobileButtons?.length > 0 && !modal?.open) || false

  return (
    <>
      <MediaQuery maxWidth={IS_MOBILE}>
        <TopAppBar
          leadingContent={openSearch || searchTerm !== '' ? 'search' : 'title'}
          title='Agent orders'
          trailingIconButtonProps={[{ icon: 'search', onClick: handleOpenSearch }]}
          searchProps={{
            searchTerm,
            onChange: handleSearch,
            onClear: handleCancelSearch,
            closeSearchButtonProps: { onClick: handleCloseSearch }
          }}
        />
        {showMobileActionButtons && (
          <ActionMenuButton
            leadingIcon='add'
            buttonLabel={t(ordersPageConfig.actionButtonLabel)}
            actionItems={renderButtons(ordersPageConfig.mobileButtons).filter(Boolean)}
          />
        )}
      </MediaQuery>

      <div className={styles.container}>
        <div className={styles.header}>
          <MediaQuery minWidth={IS_DESKTOP}>
            <Header
              title='Agent orders'
              moreActions={
                showDesktopActionButtons
                  ? {
                      variant: 'filled',
                      buttonLabel: t(ordersPageConfig.actionButtonLabel).toUpperCase(),
                      leadingIcon: 'add',
                      listItems: renderButtons(ordersPageConfig.desktopButtons)
                    }
                  : undefined
              }
            />
          </MediaQuery>
        </div>
        <div>
          <AgentOrderList
            fasteStoreKey={fasteStoreKey}
            tableTitle='Your Orders'
            searchTerm={searchTerm}
            handleCreateOrder={handleCreateOrder}
          />
        </div>
      </div>
    </>
  )
}

export default AgentOrders
