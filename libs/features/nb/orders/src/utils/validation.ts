import { NbProduct } from '@gc/types'

type TranslationFn = (key: string, options?: { [key: string]: string | number }) => string

export enum ValidationType {
  QTY_MAX = 'qty_max',
  QTY_MULTIPLE = 'qty_multiple'
}

const validateDecrement = (totalQuantity: number, product: NbProduct, t: TranslationFn) => {
  return null
}

const validateIncrement = (totalQuantity: number, product: NbProduct, t: TranslationFn) => {
  if (product.availableQty && totalQuantity > product.availableQty) {
    return {
      type: ValidationType.QTY_MAX,
      message: t('orders.quantity_exceeds_available.label', {
        availableQty: product.availableQty
      })
    }
  }
  return null
}

export const validateQuantity = (
  quantity: number,
  product: NbProduct,
  selectedProducts: { product: NbProduct; quantity: number }[],
  t: TranslationFn
) => {
  const productQtyNbr = Number(product.quantity)
  if (quantity === productQtyNbr) {
    return null
  }

  if (product.orderMultiple && quantity % product.orderMultiple !== 0) {
    return {
      type: ValidationType.QTY_MULTIPLE,
      message: t('orders.quantity_must_be_multiple.label', {
        orderMultiple: product.orderMultiple
      })
    }
  }

  const totalQuantity = selectedProducts.reduce((acc, item) => {
    if (item.product.code === product.code) {
      return acc + item.quantity
    }
    return acc
  }, 0)
  return quantity < productQtyNbr
    ? validateDecrement(totalQuantity, product, t)
    : validateIncrement(totalQuantity, product, t)
}
