/* eslint-disable @nx/enforce-module-boundaries */
import { Grid, GridCol, GridRow } from '@element/react-grid'
import { Typo<PERSON>aption, TypoSubtitle } from '@element/react-typography'
import { Badge, HeaderType, List, LoadingAndContingencySection, MessageWithAction, Table } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { useMemoizedTranslation } from '@gc/hooks'
import { NbOrderList } from '@gc/types'
import { ReactNode, useCallback, useMemo } from 'react'
import MediaQuery from 'react-responsive'
import { useNavigate } from 'react-router-dom'

import { useOrdersLobConfig } from '../hooks'
import agentOrdersData from '../mocks/agentOrderlist.json'
import styles from './OrderList.module.scss'

export type AgentOrdersListProps = {
  tableTitle: string
  searchTerm?: string
  fasteStoreKey: string
  actionItems?: {
    value: string
    label: string
    onClick: () => void
  }[]
  handleCreateOrder: (name: string) => void
}

export function AgentOrderList({
  fasteStoreKey,
  tableTitle,
  searchTerm = '',
  handleCreateOrder
}: Readonly<AgentOrdersListProps>) {
  const t = useMemoizedTranslation()
  const navigate = useNavigate()
  const { ordersListMobileConfig, ordersPageConfig, orderStatusThemeConfig, agentOrdersListColumns } =
    useOrdersLobConfig()

  // Use mock data from JSON file
  const mockAgentOrdersData = agentOrdersData

  const searchKeys = agentOrdersListColumns
    .filter((item: { accessor: string; header: string; searchable?: boolean }) => item.searchable)
    .map((item: { accessor: string; header: string; searchable?: boolean }) => item.accessor)

  // Use mock data instead of API
  const data = mockAgentOrdersData
  const isDataLoading = false
  const hasError = false

  const hasNoData = data.length === 0
  const tabConfig = useMemo(
    () => ordersPageConfig?.tabs.find((tab: { label: string }) => tab.label === 'Orders'),
    [ordersPageConfig?.tabs]
  )

  const hasNoDataButtons =
    tabConfig?.loadingContingencyConfig?.noDataButtonProps ||
    tabConfig?.loadingContingencyConfig?.noDataSecondaryButtonProps

  const noDataProps = hasNoDataButtons
    ? {
        ...(tabConfig?.loadingContingencyConfig?.noDataButtonProps && {
          noDataButtonProps: {
            leadingIcon: 'add',
            label: t(tabConfig.loadingContingencyConfig.noDataButtonProps.label),
            variant: 'text',
            onClick: () => handleCreateOrder(tabConfig.loadingContingencyConfig.noDataButtonProps.modalToOpen)
          }
        }),
        ...(tabConfig?.loadingContingencyConfig?.noDataSecondaryButtonProps && {
          noDataSecondaryButtonProps: {
            leadingIcon: 'add',
            label: t(tabConfig.loadingContingencyConfig.noDataSecondaryButtonProps.label),
            variant: 'text',
            onClick: () => handleCreateOrder(tabConfig.loadingContingencyConfig.noDataSecondaryButtonProps.modalToOpen)
          }
        })
      }
    : {}

  const getColor = useCallback((status: string) => orderStatusThemeConfig[status], [orderStatusThemeConfig])

  const configureHeaders = () => {
    const parsedHeaders = agentOrdersListColumns.map((item: { accessor: string; header: string }) => {
      const result = {
        header: item.header,
        accessor: item.accessor,
        disableSortBy: false,
        align: [
          'shipped',
          'scheduled',
          'toShip',
          'dealerOrder',
          'stockTransferQty',
          'returnedQty',
          'replantQty',
          'rollover'
        ].includes(item.accessor)
          ? 'right'
          : 'left'
      } as HeaderType<NbOrderList> & {
        sortable?: boolean
        onLinkClick?: (data: NbOrderList) => void
      }

      // Set default sort for product column
      if (item.accessor === 'acronym') {
        result.sortable = true
      }

      return result
    })
    return parsedHeaders
  }

  const dataToListItem = (order: NbOrderList) => {
    type OrderHeaderParam = keyof NbOrderList

    const { orderHeader, overlineText, primaryText, secondaryText, trailingBlock } =
      ordersListMobileConfig?.listItemParams || {}

    const getOrderHeader = (key: OrderHeaderParam) => (
      <>
        <TypoCaption>{`${t('orders.order.label')} ${order[key]}`}</TypoCaption>
        <br />
      </>
    )

    const getTextBlock = (key: OrderHeaderParam) =>
      order[key] && (
        <div className={styles['overline-text-wrapper']}>
          <Badge labelText={order[key] as string} themeColor={getColor(order[key] as string)} />
        </div>
      )

    const getSecondaryText = (key: OrderHeaderParam, header: ReactNode) => (
      <>
        {header}
        {key === 'orderSummary' ? (
          <TypoCaption>{order.orderSummary?.length + ' products' || ''}</TypoCaption>
        ) : (
          <TypoCaption>{(order[key] as string) || ''}</TypoCaption>
        )}
      </>
    )

    const getTrailingBlock = (key: OrderHeaderParam) => key && <TypoCaption>Created {order[key] as string}</TypoCaption>

    const orderHeaderContent = getOrderHeader(orderHeader as OrderHeaderParam)

    return {
      code: order?.orderNumber,
      overlineText: getTextBlock(overlineText as OrderHeaderParam),
      primaryText: <TypoSubtitle level={2}>{(order[primaryText as OrderHeaderParam] as string) || ''}</TypoSubtitle>,
      secondaryText: getSecondaryText(secondaryText as OrderHeaderParam, orderHeaderContent),
      trailingBlock: getTrailingBlock(trailingBlock as OrderHeaderParam)
    }
  }

  const handleViewOrderDetails = useCallback(
    (code: string) => {
      const order = data.filter((order) => order?.orderNumber === code)[0]

      navigate(`/${code}`, {
        state: { orderDetails: order }
      })
    },
    [data, navigate]
  )

  return (
    <Grid className={styles.grid}>
      <GridRow className={styles.content}>
        <GridCol
          desktopCol={12}
          phoneCol={4}
          tabletCol={8}
          verticalAlign={!data.length || hasError ? 'middle' : 'top'}
          className={hasNoData || hasError ? styles['container-contingency'] : ''}
        >
          <LoadingAndContingencySection
            hasError={hasError}
            isLoading={isDataLoading}
            hasData={!hasNoData}
            errorHeader={t(tabConfig.loadingContingencyConfig.errorHeader)}
            errorDescription={t(tabConfig.loadingContingencyConfig.errorDescription)}
            loadingMessage={t(tabConfig.loadingContingencyConfig.loadingMessage)}
            noDataHeader={t(tabConfig.loadingContingencyConfig.noDataHeader)}
            noDataDescription={t(tabConfig.loadingContingencyConfig.noDataDescription)}
            {...noDataProps}
          >
            <>
              <MediaQuery minWidth={IS_DESKTOP}>
                <Table<NbOrderList>
                  paginated
                  searchable
                  enableCsvDownload
                  title={tableTitle}
                  data={data}
                  headers={configureHeaders()}
                  fasteStoreKey={fasteStoreKey}
                  className={styles.orders_table}
                  noContentMessage={
                    <MessageWithAction
                      messageHeader={t('common.no_results_message_header_label')}
                      messageDescription={t('common.no_results_message_description')}
                      iconProps={{
                        icon: 'info',
                        variant: 'filled-secondary',
                        className: 'gc-icon-info'
                      }}
                    />
                  }
                />
              </MediaQuery>
              <MediaQuery maxWidth={IS_MOBILE}>
                <div className={styles['order-list-mobile']}>
                  {data.length > 0 ? (
                    <List<NbOrderList>
                      divider={true}
                      data={data}
                      filterProps={{ filters: ordersListMobileConfig?.filters }}
                      sortProps={{ options: ordersListMobileConfig?.options }}
                      searchTerm={searchTerm}
                      searchKeys={searchKeys}
                      fasteStoreKey={fasteStoreKey}
                      dataToListItem={dataToListItem}
                      onAction={handleViewOrderDetails}
                    />
                  ) : (
                    <MessageWithAction
                      messageHeader={t('common.no_results_message_header_label')}
                      messageDescription={t('common.no_results_message_description')}
                      iconProps={{
                        icon: 'info',
                        variant: 'filled-secondary',
                        className: 'gc-icon-info'
                      }}
                    />
                  )}
                </div>
              </MediaQuery>
            </>
          </LoadingAndContingencySection>
        </GridCol>
      </GridRow>
    </Grid>
  )
}

export default AgentOrderList
