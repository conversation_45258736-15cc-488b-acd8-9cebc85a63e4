.OrderedProductsTable {
  background-color: #f5f5f5 !important; /* Apply to the entire table */
  border: none;
}

.OrderedProductsTable th,
.OrderedProductsTable td {
  background-color: #f5f5f5 !important; /* Apply to header and cells */
  padding-right: 8px;
}

.OrderedProductsTable tr {
  background-color: #f5f5f5 !important; /* Apply to rows */
}

.hide_pagination {
  :global(.mdc-data-table__pagination) {
    display: none !important;
    opacity: 0 !important;
  }
}

.product-name-column {
  color: red;
}

.OrderedProductsTable tr:last-child {
  border-bottom: 0;
}
.total-val {
  padding: 20px 8px 20px 16px;
}
