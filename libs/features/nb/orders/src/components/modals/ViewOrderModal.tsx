import { Button } from '@element/react-button'
import { Card, CardContent, CardTitle } from '@element/react-card'
import { Icon } from '@element/react-icon'
import { List, ListItem } from '@element/react-list'
import { TypoBody, TypoCaption, TypoDisplay, TypoSubtitle } from '@element/react-typography'
import { ActionMenuButton, Badge, Band, List as ListComponent, TopAppBar } from '@gc/components'
import { useMemoizedTranslation, useModal } from '@gc/hooks'
import { getModal } from '@gc/redux-store'
import { OrderSummary, Usage } from '@gc/types'
import classNames from 'classnames'
import _, { noop } from 'lodash'
import map from 'lodash/map'
import { ReactNode, useCallback, useMemo } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'

import { useOrdersLobConfig } from '../../hooks'
import styles from './ViewOrderModal.module.scss'

type ViewOrderMobileProps = {
  title: string
  usage?: Usage
  order: any
  crop?: string
  distributorBlock?: ReactNode
  shipToBlock?: ReactNode
  purchaseOrder?: ReactNode
}

export function ViewOrderModal({ ...props }: ViewOrderMobileProps) {
  const { order, crop } = props
  const { openModal } = useModal()
  const t = useMemoizedTranslation()
  const navigate = useNavigate()

  const { viewOrderPageConfig, viewOrderMetaBlockConfig } = useOrdersLobConfig()
  const modal = useSelector(getModal)

  const getTitle = useMemo(() => {
    return t(viewOrderPageConfig?.title)
  }, [t, viewOrderPageConfig])

  const handleClose = useCallback(() => {
    navigate('..')
  }, [navigate])

  const summaryInfo = useMemo(
    () => ({
      title: `${crop} Summary`,
      totalTitle: 'Total',
      parameter: 'quantity'
    }),
    [crop]
  )

  const handleViewProductShipment = useCallback(
    (product: any) => {
      navigate('productShipment', {
        state: { consignments: product?.delivery || [], orderDetails: order, product }
      })
    },
    [navigate, order]
  )

  const handleCancelOrder = useCallback(() => {
    openModal({ name: 'ABANDON_ORDER', props: { type: 'cancel', usage: props?.usage || 'order' } })
  }, [openModal, props?.usage])

  const mapHandlers = (value: string) => {
    if (value === 'duplicate') return noop
    if (value === 'edit') return noop
    if (value === 'cancelOrder') return handleCancelOrder
    return noop
  }
  const renderButtons = (
    buttonConfig: {
      label: string
      value: string
      isDisabled?: (a?: object) => boolean
    }[]
  ) => {
    return buttonConfig.map((button) => ({
      label: t(button.label),
      value: button.value,
      onClick: mapHandlers(button.value)
    }))
  }

  const getDynamicText = (config: any, row: any) => {
    return config?.map((block: any, index: number) => {
      switch (block.type) {
        case 'label':
        case 'static':
          return <span key={index}>{block.value} </span>
        case 'dynamic': {
          const resolvedValue = block.path.split('.').reduce((acc: any, key: string) => acc?.[key], row)
          return <span key={index}>{resolvedValue ?? ''} </span>
        }
        case 'icon':
          return <Icon iconSize='xsmall' className={styles['icon-align']} icon={block.value} />
        default:
          return null
      }
    })
  }

  const getStyledListItems = useCallback(
    (orderSummary: Array<OrderSummary>) => {
      return map(orderSummary, (row: OrderSummary) => {
        const { primaryText, secondaryTextBlocks, secondaryTextBlocks2, trailingBlock } =
          viewOrderPageConfig?.mobileProductListconfig || {}
        const secondaryText = (
          <>
            <TypoCaption>{getDynamicText(secondaryTextBlocks || [], row)}</TypoCaption>
            {secondaryTextBlocks2 && (
              <>
                <br />
                <TypoCaption className={classNames(styles.line_height_22)}>
                  {getDynamicText(secondaryTextBlocks2 || [], row)}
                </TypoCaption>
              </>
            )}
          </>
        )
        return {
          row: row,
          trailingBlock: <TypoCaption>{getDynamicText(trailingBlock || [], row)}</TypoCaption>,
          primaryText: <TypoSubtitle level={2}>{getDynamicText(primaryText || [], row)}</TypoSubtitle>,
          secondaryText: secondaryText
        }
      })
    },
    [viewOrderPageConfig?.mobileProductListconfig]
  )

  const renderOrderID = useMemo(() => {
    return (
      <Card className={styles['card-title']}>
        <CardContent>
          {order?.statusDisplay && <Badge labelText={order.statusDisplay} />}
          <TypoDisplay level={5} className={styles['order-id']}>
            Order {order?.orderNumber}
          </TypoDisplay>
          <TypoCaption>Created {order?.purchaseOrderDate}</TypoCaption>
        </CardContent>
      </Card>
    )
  }, [order])

  const renderProductList = useMemo(() => {
    if ((order?.orderSummary?.length || 0) > 0) {
      const cropProducts = order?.orderSummary?.map((item: any) => ({ ...item, crop }))
      const groupedProducts = _.groupBy(cropProducts || [], 'crop')
      return Object.entries(groupedProducts).map(([crop, orderSummary]) => {
        return (
          <Card className={styles['card']}>
            <CardContent>
              <CardTitle
                data-testid='title'
                className={`${styles['card-title']} ${styles['list-title']}`}
                primaryText={'Products'}
              />
              <>
                <Band
                  containerClassName='lmnt-theme-secondary-50-bg header'
                  placement='list'
                  primaryText={`${order?.orderSummary?.length} ${crop} Products`}
                />
                <ListComponent
                  divider
                  className={styles['product-list-mobile']}
                  listItemClassName={styles['product-list-item']}
                  items={getStyledListItems(orderSummary)}
                  onAction={(_code: string, product: any) =>
                    crop.toLowerCase() === 'crop protection' ? handleViewProductShipment(product) : undefined
                  }
                />
                {crop.toLowerCase() !== 'crop protection' && (
                  <List className={`${styles.footer_container}`} trailingBlockType='meta'>
                    <ListItem
                      noHover
                      className={styles['product-footer-list-item']}
                      overlineText={summaryInfo?.title}
                    />
                    <ListItem
                      noHover
                      className={styles['product-footer-list-item']}
                      primaryText={
                        <TypoBody bold level={2}>
                          {summaryInfo?.totalTitle}
                        </TypoBody>
                      }
                      trailingBlock={
                        <TypoBody bold level={2}>
                          Total SSU
                        </TypoBody>
                      }
                    ></ListItem>
                  </List>
                )}
              </>
            </CardContent>
          </Card>
        )
      })
    }
  }, [order?.orderSummary, crop, getStyledListItems, handleViewProductShipment, summaryInfo])

  return (
    <div className={styles['view-order-container']}>
      <TopAppBar
        title={getTitle}
        leadingIconButtonProps={{
          icon: 'arrow_back',
          onClick: () => handleClose()
        }}
        trailingBlock={
          props.usage === 'seedOrder' ? (
            <Button
              variant='outlined'
              buttonSize='small'
              // eslint-disable-next-line no-console
              onClick={() => console.log('edit')}
              themeColor='secondary'
              label={t('common.edit.label')}
            />
          ) : undefined
        }
      />
      {!modal?.open && viewOrderPageConfig.mobileButtons && (
        <ActionMenuButton
          leadingIcon='add'
          buttonLabel={t('common.actions.label')}
          actionItems={renderButtons(viewOrderPageConfig.mobileButtons).filter(Boolean)}
        />
      )}
      {renderOrderID}
      {viewOrderMetaBlockConfig?.showDistributorBlock && props?.distributorBlock}
      {viewOrderMetaBlockConfig?.showShipToBlock && props?.shipToBlock}
      {viewOrderMetaBlockConfig?.showPurchaseOrderBlock && props?.purchaseOrder}
      {renderProductList}
    </div>
  )
}

export default ViewOrderModal
