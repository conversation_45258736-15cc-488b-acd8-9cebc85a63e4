import { Grid, GridCol, GridRow } from '@element/react-grid'
import { Badge, HeaderType, Table } from '@gc/components'
import { ProductDeliverydetails } from '@gc/types'
import { fasteRoute, getFasteStoreKey } from '@gc/utils'
import { ReactNode, useCallback } from 'react'
import { useTranslation } from 'react-i18next'

import { useOrdersLobConfig } from '../hooks'
import styles from './ProductLisExpandableTemplate.module.scss'

export interface ProductLisExpandableTemplateProps {
  tableData: any
}

export function ProductLisExpandableTemplate({ tableData }: ProductLisExpandableTemplateProps) {
  const { t } = useTranslation()
  const fasteStoreKey = getFasteStoreKey('orders', 'select-products')
  const { viewOrderPageConfig, orderStatusThemeConfig } = useOrdersLobConfig()

  type OrderedProductsHeader = {
    accessor?: string | ((a: ProductDeliverydetails) => ReactNode)
    header: string
    searchable?: boolean
    filterable?: boolean
    disableSortBy?: boolean
    displayType?: string
    displayRoute?: string
    align?: string
    widthPercentage: number
    displayTemplate?: (status: string) => ReactNode
  }

  const onClickTrackOrder = useCallback((rowData: ProductDeliverydetails, route: string) => {
    fasteRoute(route)
  }, [])

  const getColor = useCallback((status: string) => orderStatusThemeConfig[status], [orderStatusThemeConfig])

  const generateHeaders = (headers: OrderedProductsHeader[]) => {
    return headers.map((column) => {
      switch (column.accessor) {
        case 'statusDisplay':
          return {
            header: t(column.header),
            accessor: column.accessor,
            align: column?.align || 'end',
            displayTemplate: (_value: string) =>
              _value ? <Badge labelText={_value} themeColor={getColor(_value)} /> : '',
            widthPercentage: 10
          } as HeaderType<ProductDeliverydetails>
        case 'trackingId':
          return {
            header: t(column.header),
            accessor: column.accessor,
            widthPercentage: 10,
            ...(column?.displayType === 'link' && {
              displayType: column.displayType,
              onLinkClick: (_value: string, row: ProductDeliverydetails) =>
                onClickTrackOrder(row, column?.displayRoute || '/')
            })
          } as HeaderType<ProductDeliverydetails>
        default:
          return column
      }
    })
  }

  return (
    <Grid className={styles.expanded_table_row} fullWidth={true} columnGap='40px'>
      <GridRow>
        <GridCol desktopCol={12}>
          <Table<ProductDeliverydetails>
            className={`${styles.hide_pagination} ${styles.OrderedProductsTable}`}
            noHover={true}
            paginated
            headers={generateHeaders(viewOrderPageConfig?.productListExpandableColumns as OrderedProductsHeader[])}
            data={tableData}
            noContentMessage={'NO Data Present'}
            fasteStoreKey={fasteStoreKey}
          />
        </GridCol>
      </GridRow>
    </Grid>
  )
}

export default ProductLisExpandableTemplate
