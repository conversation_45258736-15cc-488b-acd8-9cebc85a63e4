/* eslint-disable react-hooks/rules-of-hooks */
import { usePortalConfig, useSelectedAccount } from '@gc/hooks'
import { useNbOrdersQueries } from '@gc/redux-store'
import { PortalKey } from '@gc/types'

export const useOrders = (selectedAccounts: any) => {
  const { portalKey } = usePortalConfig()
  const { lob } = useSelectedAccount()
  const { useGetNbOrdersQuery, useGetCPOrdersQuery } = useNbOrdersQueries()

  switch (portalKey) {
    case PortalKey.MyCrop:
    case PortalKey.MyCropV2:
      if (lob === 'cp') return useGetCPOrdersQuery(selectedAccounts)
      return useGetNbOrdersQuery(selectedAccounts)
    default:
      throw new Error(`The portal parameter portalKey is invalid: ${portalKey}.`)
  }
}

export default useOrders
