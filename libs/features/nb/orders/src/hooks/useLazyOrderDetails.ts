/* eslint-disable react-hooks/rules-of-hooks */
import { usePortalConfig, useSelectedAccount } from '@gc/hooks'
import { useNbOrdersQueries } from '@gc/redux-store'
import { PortalKey } from '@gc/types'

export const useLazyOrderDetails = () => {
  const { portalKey } = usePortalConfig()
  const { lob } = useSelectedAccount()
  const { useLazyGetNbOrderDetailsQuery, useLazyGetCPOrderDetailsQuery } = useNbOrdersQueries()

  switch (portalKey) {
    case PortalKey.MyCrop:
    case PortalKey.MyCropV2:
      if (lob === 'cp') return useLazyGetCPOrderDetailsQuery()
      return useLazyGetNbOrderDetailsQuery()
    default:
      throw new Error(`The portal parameter portalKey is invalid: ${portalKey}.`)
  }
}

export default useLazyOrderDetails
