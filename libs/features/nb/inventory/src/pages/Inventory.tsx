import { <PERSON><PERSON>, Ta<PERSON>, <PERSON>AppBar } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { useAppSessionData, useMemoizedTranslation } from '@gc/hooks'
import { getFasteStoreKey } from '@gc/utils'
import get from 'lodash/get'
import { useCallback, useMemo, useState } from 'react'
import MediaQuery from 'react-responsive'

import { ShipmentsList } from '../components'
import { useInventoryLobConfig } from '../hooks'
import styles from './Inventory.module.scss'

export function Inventory() {
  const t = useMemoizedTranslation()

  const { inventoryTabsConfig } = useInventoryLobConfig()

  const appSessionData = useAppSessionData()
  const fasteStoreKey = getFasteStoreKey('inventory', 'inventory')

  const [openSearch, setOpenSearch] = useState(false)
  const [searchTerm, setSearchTerm] = useState(get(appSessionData, `${fasteStoreKey}.searchTerm`, '') as string)

  const handleOpenSearch = () => setOpenSearch(true)
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)
  const handleCancelSearch = () => setSearchTerm('')
  const handleCloseSearch = () => {
    setSearchTerm('')
    setOpenSearch(false)
  }

  const mapTabComponents = useCallback(
    (value: string) => {
      switch (value) {
        case 'shipments':
          return <ShipmentsList tableTitle={t('inventory.shipments.shipments.label')} searchTerm={searchTerm} />
        case 'deliveries':
          return <div>Deliveries comp</div>
        default:
          return <div>Placeholder for {value} component</div>
      }
    },
    [t, searchTerm]
  )

  const tabConfig = useMemo(() => inventoryTabsConfig?.tabs || [], [inventoryTabsConfig?.tabs])

  const renderTabs = useCallback(() => {
    return tabConfig.map((tab: { label: string; value: string }) => ({
      label: t(tab.label),
      component: mapTabComponents(tab.value)
    }))
  }, [tabConfig, t, mapTabComponents])

  return (
    <>
      <MediaQuery maxWidth={IS_MOBILE}>
        <TopAppBar
          leadingContent={openSearch || searchTerm !== '' ? 'search' : 'title'}
          title={t('inventory.inventory.label')}
          trailingIconButtonProps={[{ icon: 'search', onClick: handleOpenSearch }]}
          searchProps={{
            searchTerm,
            onChange: handleSearch,
            onClear: handleCancelSearch,
            closeSearchButtonProps: { onClick: handleCloseSearch }
          }}
        />
      </MediaQuery>
      <div className={styles.container}>
        <div className={styles.header}>
          <MediaQuery minWidth={IS_DESKTOP}>
            <Header title={t('inventory.inventory.label')} />
          </MediaQuery>
        </div>
        <MediaQuery maxWidth={IS_MOBILE}>
          <div>
            <Tabs tabs={renderTabs()} />
          </div>
        </MediaQuery>
      </div>
    </>
  )
}

export default Inventory
