import { Card, CardContent, CardTitle } from '@element/react-card'
import { Button } from '@element/react-components'
import { List, ListItem } from '@element/react-list'
import { TypoBody, TypoCaption, TypoDisplay, TypoSubtitle } from '@element/react-typography'
import { ActionMenuButton, Badge, Band, List as ListComponent, TopAppBar } from '@gc/components'
import { IS_MOBILE } from '@gc/constants'
import { useMemoizedTranslation } from '@gc/hooks'
import { ShippedProducts } from '@gc/types'
import { fasteRoute } from '@gc/utils'
import { isEmpty, noop } from 'lodash'
import map from 'lodash/map'
import { useCallback, useEffect, useMemo, useState } from 'react'
import MediaQuery from 'react-responsive'
import { useLocation } from 'react-router-dom'

import { useInventoryLobConfig } from '../hooks'
import styles from './ViewShipment.module.scss'

export function ViewShipment() {
  const location = useLocation()
  const previousPageShipmentInfo = location.state?.shipmentInfo
  const [shipment, setShipment] = useState(previousPageShipmentInfo)

  const t = useMemoizedTranslation()
  const { misc, shipmentsListActions } = useInventoryLobConfig()

  useEffect(() => {
    if (isEmpty(previousPageShipmentInfo)) {
      const fetchData = async () => {
        // const { data: singleOrderData } = await getOrderDetails({ orderId: code })
        // setShipment(singleOrderData)
        setShipment([])
      }
      fetchData()
    }
  }, [previousPageShipmentInfo])

  const getTitle = useMemo(() => {
    return t(misc?.ShipmentsDetailsTitle)
  }, [t, misc])

  const handleClose = useCallback(() => {
    fasteRoute('/inventory')
  }, [])

  const summaryInfo = useMemo(
    () => ({
      title: `Product Summary`,
      totalTitle: 'Total',
      parameter: 'quantity'
    }),
    []
  )

  const mapHandlers = (value: string) => {
    if (value === 'printBOL') return noop
    if (value === 'addIssue') return noop
    return noop
  }
  const renderButtons = (
    buttonConfig: {
      label: string
      value: string
      isDisabled?: (a?: object) => boolean
    }[]
  ) => {
    return buttonConfig
      .filter((i: { value: string }) => shipment?.deliveryStatus === 'Received' || i.value !== 'printBOL')
      .map((button) => ({
        label: t(button.label),
        value: button.value,
        onClick: mapHandlers(button.value)
      }))
  }

  const getColor = useCallback(
    (status: string) =>
      ({
        Open: 'orange',
        Scheduled: 'green',
        'Goods Issued': 'green',
        Received: 'green',
        Canceled: 'red'
      })[status],
    []
  )

  const getStyledListItems = useCallback(
    (shippedProducts: Array<ShippedProducts>) => {
      return map(shippedProducts, (row: ShippedProducts) => {
        const secondaryText = <TypoCaption>{shipment.type}</TypoCaption>
        return {
          row: row,
          trailingBlock: <TypoCaption>{row.quantity} SSU</TypoCaption>,
          primaryText: <TypoSubtitle level={2}>{row.productName}</TypoSubtitle>,
          secondaryText: secondaryText
        }
      })
    },
    [shipment.type]
  )

  const renderDeliveryID = useMemo(() => {
    return (
      <Card className={styles['card-title']}>
        <CardContent>
          <Badge labelText={shipment.deliveryStatus} themeColor={getColor(shipment.deliveryStatus)} />
          <TypoDisplay level={5} className={styles['order-id']}>
            Delivery {shipment.deliveryId}
          </TypoDisplay>
          <TypoCaption>Shipment {shipment.shipmentId}</TypoCaption>
          <Button variant='outlined' leadingIcon='my_location' className={styles['track-button']}>
            {t('inventory.track_shipment.label')}
          </Button>
        </CardContent>
      </Card>
    )
  }, [getColor, shipment.deliveryId, shipment.deliveryStatus, shipment.shipmentId, t])

  const renderShipTo = useMemo(() => {
    const shipTo = shipment?.shippingDetails?.shipTo || {}
    return (
      <Card className={styles['card']}>
        <CardContent>
          <CardTitle
            data-testid='ship-to-title'
            className={`${styles['card-title']} ${styles['list-title']}`}
            primaryText={'Ship To'}
          />
          <div className={styles['card-body']}>
            {shipTo?.locationName} <br /> {shipTo?.addressline1}
            {shipTo?.addressline2 && (
              <>
                <br /> {shipTo.addressline2}
              </>
            )}
            <br />
            {shipTo?.city}, {shipTo?.state}, {shipTo?.postalCode}
          </div>
        </CardContent>
      </Card>
    )
  }, [shipment])

  const renderShipFrom = useMemo(() => {
    const shipFrom = shipment?.shippingDetails?.shipFrom || {}
    return (
      <Card className={styles['card']}>
        <CardContent>
          <CardTitle
            data-testid='ship-from-title'
            className={`${styles['card-title']} ${styles['list-title']}`}
            primaryText={'Ship From'}
          />
          <div className={styles['card-body']}>
            {shipFrom?.warehouseName} <br /> {shipFrom?.addressline1}
            {shipFrom?.addressline2 && (
              <>
                <br /> {shipFrom.addressline2}
              </>
            )}
            <br />
            {shipFrom?.city}, {shipFrom?.state}, {shipFrom?.postalCode}
          </div>
        </CardContent>
      </Card>
    )
  }, [shipment])

  const renderProductList = useMemo(() => {
    const shippingProduts = shipment?.shippingDetails?.shippedProducts || []
    if ((shippingProduts?.length || 0) > 0) {
      return (
        <Card className={styles['card']}>
          <CardContent>
            <CardTitle
              data-testid='title'
              className={`${styles['card-title']} ${styles['list-title']}`}
              primaryText={'Products in Delivery'}
            />
            <>
              <Band
                containerClassName='lmnt-theme-secondary-50-bg header'
                placement='list'
                primaryText={`${shippingProduts.length}  Products`}
              />
              <ListComponent
                divider
                className={styles['product-list-mobile']}
                listItemClassName={styles['product-list-item']}
                items={getStyledListItems(shippingProduts)}
              />
              <List className={`${styles.footer_container}`} trailingBlockType='meta'>
                <ListItem noHover className={styles['product-footer-list-item']} overlineText={summaryInfo?.title} />
                <ListItem
                  noHover
                  className={styles['product-footer-list-item']}
                  primaryText={
                    <TypoBody bold level={2}>
                      {summaryInfo?.totalTitle}
                    </TypoBody>
                  }
                  trailingBlock={
                    <TypoBody bold level={2}>
                      Total SSU
                    </TypoBody>
                  }
                ></ListItem>
              </List>
            </>
          </CardContent>
        </Card>
      )
    }
  }, [shipment?.shippingDetails?.shippedProducts, getStyledListItems, summaryInfo?.title, summaryInfo?.totalTitle])

  return (
    <MediaQuery maxWidth={IS_MOBILE}>
      <div className={styles['view-order-container']}>
        <TopAppBar
          title={getTitle}
          leadingIconButtonProps={{
            icon: 'arrow_back',
            onClick: () => handleClose()
          }}
        />
        <ActionMenuButton
          leadingIcon='add'
          buttonLabel={t('common.actions.label')}
          actionItems={renderButtons(shipmentsListActions).filter(Boolean)}
        />
        {renderDeliveryID}
        {renderShipTo}
        {renderShipFrom}
        {renderProductList}
      </div>
    </MediaQuery>
  )
}

export default ViewShipment
