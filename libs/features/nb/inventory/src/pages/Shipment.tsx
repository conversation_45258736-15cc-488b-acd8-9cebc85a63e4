import { Header } from '@gc/components'
import { IS_DESKTOP } from '@gc/constants'
import MediaQuery from 'react-responsive'

import { ShipmentsList } from '../components'
import { useInventoryLobConfig } from '../hooks'
import styles from './Shipment.module.scss'

export function Shipment() {
  const { misc } = useInventoryLobConfig()
  return (
    <MediaQuery minWidth={IS_DESKTOP}>
      <div className={styles.container}>
        <div className={styles.header}>
          <Header title='Shipments' />
        </div>
        <div>
          <ShipmentsList tableTitle={misc.shipmentsTableTitle} />
        </div>
      </div>
    </MediaQuery>
  )
}

export default Shipment
