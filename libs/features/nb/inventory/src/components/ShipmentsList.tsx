import { Typo<PERSON>aption, TypoSubtitle } from '@element/react-components'
import { Grid, GridCol, GridRow } from '@element/react-grid'
import { Badge, List, LoadingAndContingencySection, MessageWithAction, Table, TableMenu } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { useMemoizedTranslation } from '@gc/hooks'
import { ShipmentsListTable, TableRow } from '@gc/types'
import { getFasteStoreKey } from '@gc/utils'
import { ReactNode, useCallback, useEffect, useState } from 'react'
import MediaQuery from 'react-responsive'
import { useNavigate } from 'react-router-dom'

import { useInventoryLobConfig } from '../hooks'
import mockShipmentsListData from '../mocks/shipments.json'
import { EXPANDABLE_ROW_TEMPLATE } from '../utils'
import ShipmentsExpandableTemplate from './ShipmentsExpandableTemplate'
import styles from './ShipmentsList.module.scss'

type ShipmentsHeader = {
  accessor?: string | ((a: ShipmentsListTable) => ReactNode)
  header: string
  searchable?: boolean
  filterable?: boolean
  disableSortBy?: boolean
  displayType?: string
  align?: string
  displayTemplate?: (status: string) => ReactNode
}

export type ShipmentsListProps = {
  tableTitle: string
  searchTerm?: string
}

export function ShipmentsList({ tableTitle, searchTerm = '' }: Readonly<ShipmentsListProps>) {
  const t = useMemoizedTranslation()
  const {
    shipmentsListActions,
    isShipmentsListExpandableColumn,
    shipmentsLoadingContingencyConfig,
    shipmentsColumns,
    shipmentsListMobileConfig
  } = useInventoryLobConfig()
  const searchKeys = shipmentsColumns.filter((item: any) => item.searchable).map((item: any) => item.accessor)
  const navigate = useNavigate()

  const fasteStoreKey = getFasteStoreKey('inventory', 'inventory')
  const [data, setData] = useState<ShipmentsListTable[]>([])
  const [isDataLoading, setDataLoading] = useState(true)
  const hasNoData = data.length === 0
  const hasError = false

  useEffect(() => {
    const loadData = () => {
      setTimeout(() => {
        setData(
          mockShipmentsListData.map((item) => ({
            ...item,
            type: item.type as 'Package' | 'Bulk'
          }))
        )
        setDataLoading(false)
      }, 1000)
    }

    loadData()
  }, [])

  const shipmentsListActionHandlers = useCallback((value: string) => {
    const actionsMap: Record<string, () => void> = {
      // eslint-disable-next-line no-console
      printBOL: () => console.log('print bol'),
      // eslint-disable-next-line no-console
      addIssue: () => console.log('add issue')
    }
    actionsMap[value]?.()
  }, [])

  const getshipmentsListActions = useCallback(
    (status: string) =>
      shipmentsListActions
        .filter((i: { value: string }) => status === 'Received' || i.value !== 'printBOL')
        .map((item: { label: string; value: string }) => ({
          ...item,
          label: t(item.label),
          onClick: () => shipmentsListActionHandlers(item.value)
        })),
    [shipmentsListActions, shipmentsListActionHandlers, t]
  )

  const getColor = useCallback(
    (status: string) =>
      ({
        Open: 'orange',
        Scheduled: 'green',
        'Goods Issued': 'green',
        Received: 'green',
        Canceled: 'red'
      })[status],
    []
  )

  const configureHeaders = (headers: ShipmentsHeader[]) => {
    const parsedHeaders = headers.map((item) => {
      const result = item
      if (item.accessor === 'deliveryStatus') {
        result.displayType = 'custom'
        result.displayTemplate = (status: string) =>
          status ? <Badge labelText={status} themeColor={getColor(status)} /> : ''
      }
      return result
    })
    parsedHeaders.push({
      header: t('common.actions.label'),
      accessor: (_data: ShipmentsListTable) => (
        <TableMenu<ShipmentsListTable> listItems={getshipmentsListActions(_data.deliveryStatus)} currentRow={_data} />
      ),
      disableSortBy: true,
      align: 'center'
    })
    return parsedHeaders
  }

  function TableExpandedRowTemplate({ row }: Readonly<{ row: TableRow<ShipmentsListTable> }>) {
    if (isShipmentsListExpandableColumn?.value === EXPANDABLE_ROW_TEMPLATE) {
      return (
        <div id='orders_expansion_panel' className={styles.orders_expansion_panel}>
          <ShipmentsExpandableTemplate shippingDetails={row.original.shippingDetails || {}} />
        </div>
      )
    }
    return null
  }

  const isExpandableRow = isShipmentsListExpandableColumn?.expandable

  const dataToListItem = (shipment: ShipmentsListTable) => {
    type ShipmentHeaderParam = keyof ShipmentsListTable

    const { orderHeader, overlineText, primaryText, secondaryText, trailingBlock } =
      shipmentsListMobileConfig?.listItemParams || {}

    const getHeader = (key: ShipmentHeaderParam) => (
      <>
        <TypoCaption>{`${t('inventory.shipments.shipment.label')} ${shipment[key]}`}</TypoCaption>
        <br />
      </>
    )

    const getTextBlock = (key: ShipmentHeaderParam) =>
      shipment[key] && (
        <div className={styles['overline-text-wrapper']}>
          <Badge labelText={shipment[key] as string} themeColor={getColor(shipment[key] as string)} />
        </div>
      )

    const getSecondaryText = (key: ShipmentHeaderParam, header: ReactNode) => (
      <>
        {header}
        <TypoCaption>To: {(shipment[key] as string) || ''}</TypoCaption>
      </>
    )

    const getTrailingBlock = (key: ShipmentHeaderParam) =>
      key && <TypoCaption>Created {shipment[key] as string}</TypoCaption>

    const orderHeaderContent = getHeader(orderHeader as ShipmentHeaderParam)

    return {
      code: shipment?.deliveryId,
      overlineText: getTextBlock(overlineText as ShipmentHeaderParam),
      primaryText: (
        <TypoSubtitle level={2}>
          {t('deliveries.delivery.label')} {(shipment[primaryText as ShipmentHeaderParam] as string) || ''}
        </TypoSubtitle>
      ),
      secondaryText: getSecondaryText(secondaryText as ShipmentHeaderParam, orderHeaderContent),
      trailingBlock: getTrailingBlock(trailingBlock as ShipmentHeaderParam)
    }
  }

  const handleViewShipmentDetails = useCallback(
    (code: string) => {
      const shipment = data.filter((shipment) => shipment?.deliveryId === code)[0]

      navigate(`/shipments/${code}`, {
        state: { shipmentInfo: shipment }
      })
    },
    [data, navigate]
  )

  return (
    <Grid className={styles.grid}>
      <GridRow className={styles.content}>
        <GridCol
          desktopCol={12}
          phoneCol={4}
          tabletCol={8}
          verticalAlign={!data.length || hasError ? 'middle' : 'top'}
          className={hasNoData || hasError ? styles['container-contingency'] : ''}
        >
          <LoadingAndContingencySection
            hasError={hasError}
            isLoading={isDataLoading}
            hasData={!hasNoData}
            errorHeader={t(shipmentsLoadingContingencyConfig.errorHeader)}
            errorDescription={t(shipmentsLoadingContingencyConfig.errorDescription)}
            loadingMessage={t(shipmentsLoadingContingencyConfig.loadingMessage)}
            noDataHeader={t(shipmentsLoadingContingencyConfig.noDataHeader)}
            noDataDescription={t(shipmentsLoadingContingencyConfig.noDataDescription)}
          >
            <MediaQuery minWidth={IS_DESKTOP}>
              <Table<ShipmentsListTable>
                paginated
                searchable
                enableCsvDownload
                title={t(tableTitle)}
                data={data}
                headers={configureHeaders(shipmentsColumns as ShipmentsHeader[])}
                fasteStoreKey={fasteStoreKey}
                className={styles.orders_table}
                {...(isExpandableRow ? { expandedRowTemplate: TableExpandedRowTemplate } : {})}
                noContentMessage={
                  <MessageWithAction
                    messageHeader={t('common.no_matching_results_message_header_label')}
                    messageDescription={t('common.no_matching_result_desc.label')}
                    iconProps={{
                      icon: 'info',
                      variant: 'filled-secondary',
                      className: 'gc-icon-info'
                    }}
                  />
                }
              />
            </MediaQuery>
            <MediaQuery maxWidth={IS_MOBILE}>
              <div className={styles['order-list-mobile']}>
                {data.length > 0 ? (
                  <List<ShipmentsListTable>
                    divider={true}
                    data={data}
                    filterProps={{ filters: shipmentsListMobileConfig?.filters }}
                    sortProps={{ options: shipmentsListMobileConfig?.options }}
                    searchTerm={searchTerm}
                    searchKeys={searchKeys}
                    fasteStoreKey={fasteStoreKey}
                    dataToListItem={dataToListItem}
                    onAction={handleViewShipmentDetails}
                  />
                ) : (
                  <MessageWithAction
                    messageHeader={t('common.no_matching_results_message_header_label')}
                    messageDescription={t('common.no_matching_result_desc.label')}
                    iconProps={{
                      icon: 'info',
                      variant: 'filled-secondary',
                      className: 'gc-icon-info'
                    }}
                  />
                )}
              </div>
            </MediaQuery>
          </LoadingAndContingencySection>
        </GridCol>
      </GridRow>
    </Grid>
  )
}

export default ShipmentsList
