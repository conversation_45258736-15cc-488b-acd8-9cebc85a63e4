import { createContext, ReactNode, useCallback, useContext, useEffect, useMemo, useState } from 'react'

interface SearchContextType {
  searchTerm: string
  debouncedSearchTerm: string
  openSearch: boolean
  setSearchTerm: (term: string) => void
  setOpenSearch: (open: boolean) => void
  handleOpenSearch: () => void
  handleCloseSearch: () => void
  handleCancelSearch: () => void
  handleSearch: (event: React.ChangeEvent<HTMLInputElement>) => void
}

const SearchContext = createContext<SearchContextType | null>(null)

export function useSearchContext() {
  const context = useContext(SearchContext)
  if (!context) {
    throw new Error('useSearchContext must be used within a SearchProvider')
  }
  return context
}

interface SearchProviderProps {
  children: ReactNode
}

export function SearchProvider({ children }: SearchProviderProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [openSearch, setOpenSearch] = useState(false)
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')

  // Update debounced term when search term changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchTerm])

  const handleOpenSearch = useCallback(() => {
    setOpenSearch(true)
  }, [])

  const handleCloseSearch = useCallback(() => {
    setOpenSearch(false)
  }, [])

  const handleCancelSearch = useCallback(() => {
    setSearchTerm('')
    setOpenSearch(false)
  }, [])

  const handleSearch = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
  }, [])

  const value = useMemo(
    () => ({
      searchTerm,
      debouncedSearchTerm,
      openSearch,
      setSearchTerm,
      setOpenSearch,
      handleOpenSearch,
      handleCloseSearch,
      handleCancelSearch,
      handleSearch
    }),
    [searchTerm, debouncedSearchTerm, openSearch, handleOpenSearch, handleCloseSearch, handleCancelSearch, handleSearch]
  )

  return <SearchContext.Provider value={value}>{children}</SearchContext.Provider>
}
