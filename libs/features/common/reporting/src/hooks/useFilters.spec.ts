import { ReportCategoryOptions, ReportFilterOptions, type UserReport } from '@gc/types'
import { renderHook } from '@testing-library/react'

import * as constants from '../config/reportColumnConstants'
import { useFilters } from './useFilters'

// Helper type to satisfy strict selectedFilters element structure
type SelectedFilterItem = NonNullable<UserReport['selectedFilters']>[number]

describe('useFilters', () => {
  const spyGetMappedFilterId = jest.spyOn(constants, 'getMappedFilterId')

  const baseReport = (overrides?: Partial<UserReport>): UserReport =>
    ({
      id: 'r1',
      name: 'Report',
      description: '',
      reportCategory: ReportCategoryOptions.OrderByProduct,
      selectedFields: [],
      selectedFilters: [],
      ...overrides
    }) as unknown as UserReport

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('returns empty object when userReport is undefined', () => {
    const { result } = renderHook(() => useFilters(undefined))
    expect(result.current).toEqual({})
    expect(spyGetMappedFilterId).not.toHaveBeenCalled()
  })

  it('returns empty object when no selected filters', () => {
    const report = baseReport({ selectedFilters: [] })
    const { result } = renderHook(() => useFilters(report))
    expect(result.current).toEqual({})
    expect(spyGetMappedFilterId).not.toHaveBeenCalled()
  })

  it('maps SWITCH filter string "true"/"false" to boolean true/false', () => {
    const report = baseReport({
      selectedFilters: [
        {
          filterId: 'status',
          fieldId: 'status',
          filterType: ReportFilterOptions.SWITCH,
          selectedOptions: ['true']
        } as SelectedFilterItem,
        {
          filterId: 'active',
          fieldId: 'active',
          filterType: ReportFilterOptions.SWITCH,
          selectedOptions: ['false']
        } as SelectedFilterItem
      ]
    })
    spyGetMappedFilterId.mockImplementation((id: string) => id)
    const { result } = renderHook(() => useFilters(report))
    expect(spyGetMappedFilterId).toHaveBeenCalledTimes(2)
    expect(result.current).toEqual({ status: true, active: false })
  })

  it('passes through non-switch filter selectedOptions as-is (using ENUM type)', () => {
    const report = baseReport({
      selectedFilters: [
        {
          filterId: 'brands',
          fieldId: 'brands',
          filterType: ReportFilterOptions.ENUM,
          selectedOptions: ['Bayer', 'Dekalb']
        } as SelectedFilterItem,
        {
          filterId: 'salesYear',
          fieldId: 'salesYear',
          filterType: ReportFilterOptions.ENUM,
          selectedOptions: ['2025']
        } as SelectedFilterItem
      ]
    })
    spyGetMappedFilterId.mockImplementation((id: string) => id)
    const { result } = renderHook(() => useFilters(report))
    expect(result.current).toEqual({
      brands: ['Bayer', 'Dekalb'],
      salesYear: ['2025']
    })
  })

  it('uses getMappedFilterId to map filter ids with report category provided', () => {
    const report = baseReport({
      selectedFilters: [
        {
          filterId: 'status',
          fieldId: 'status',
          filterType: ReportFilterOptions.SWITCH,
          selectedOptions: ['true']
        } as SelectedFilterItem
      ]
    })

    spyGetMappedFilterId.mockReturnValue('mappedStatus')

    const { result } = renderHook(() => useFilters(report))

    expect(spyGetMappedFilterId).toHaveBeenCalledWith('status', ReportCategoryOptions.OrderByProduct)
    expect(result.current).toEqual({ mappedStatus: true })
  })

  it('handles mixed filter types together', () => {
    const report = baseReport({
      selectedFilters: [
        {
          filterId: 'status',
          fieldId: 'status',
          filterType: ReportFilterOptions.SWITCH,
          selectedOptions: ['false']
        } as SelectedFilterItem,
        {
          filterId: 'brands',
          fieldId: 'brands',
          filterType: ReportFilterOptions.ENUM,
          selectedOptions: ['Bayer']
        } as SelectedFilterItem
      ]
    })
    spyGetMappedFilterId.mockImplementation((id: string) => id)

    const { result } = renderHook(() => useFilters(report))

    expect(result.current).toEqual({
      status: false,
      brands: ['Bayer']
    })
  })

  it('memoizes result: unchanged when inputs unchanged', () => {
    const report = baseReport({
      selectedFilters: [
        {
          filterId: 'status',
          fieldId: 'status',
          filterType: ReportFilterOptions.SWITCH,
          selectedOptions: ['true']
        } as SelectedFilterItem
      ]
    })
    spyGetMappedFilterId.mockImplementation((id: string) => id)

    const { result, rerender } = renderHook(({ r }) => useFilters(r), {
      initialProps: { r: report }
    })

    const first = result.current
    rerender({ r: report })
    expect(result.current).toBe(first)
  })

  it('updates memoized result when selectedFilters changes', () => {
    const report1 = baseReport({
      selectedFilters: [
        {
          filterId: 'status',
          fieldId: 'status',
          filterType: ReportFilterOptions.SWITCH,
          selectedOptions: ['true']
        } as SelectedFilterItem
      ]
    })
    const report2 = baseReport({
      selectedFilters: [
        {
          filterId: 'status',
          fieldId: 'status',
          filterType: ReportFilterOptions.SWITCH,
          selectedOptions: ['false']
        } as SelectedFilterItem
      ]
    })
    spyGetMappedFilterId.mockImplementation((id: string) => id)

    const { result, rerender } = renderHook(({ r }) => useFilters(r), {
      initialProps: { r: report1 }
    })

    expect(result.current).toEqual({ status: true })
    rerender({ r: report2 })
    expect(result.current).toEqual({ status: false })
  })

  it('handles malformed filter data gracefully', () => {
    const report = baseReport({
      selectedFilters: [
        // missing selectedOptions for SWITCH - should result in false
        { filterId: 'status', fieldId: 'status', filterType: ReportFilterOptions.SWITCH } as SelectedFilterItem,
        // empty selectedOptions for ENUM
        {
          filterId: 'brands',
          fieldId: 'brands',
          filterType: ReportFilterOptions.ENUM,
          selectedOptions: []
        } as SelectedFilterItem,
        // undefined selectedOptions for ENUM
        {
          filterId: 'salesYear',
          fieldId: 'salesYear',
          filterType: ReportFilterOptions.ENUM,
          selectedOptions: undefined
        } as SelectedFilterItem
      ]
    })
    spyGetMappedFilterId.mockImplementation((id: string) => id)

    const { result } = renderHook(() => useFilters(report))

    expect(result.current).toEqual({
      status: false,
      brands: [],
      salesYear: undefined
    })
  })
})
