/* eslint-disable @nx/enforce-module-boundaries */
import * as hooksPkg from '@gc/hooks'
import * as storePkg from '@gc/redux-store'
import {
  type ChannelOrder,
  ReportCategoryOptions,
  SelectedAccount,
  type SelectedReportField,
  type StockOrder,
  type UserReport
} from '@gc/types'
import { act, renderHook } from '@testing-library/react'

import * as columnConstants from '../config/reportColumnConstants'
import * as useCumulativeDataModule from './useCumulativeData'
import * as useFiltersModule from './useFilters'
import { transformOrdersToEntries, useOrdersByProductData } from './useOrderByProducts'

// Local test-only types to model progressive loading args our hook expects from the query hook
interface ProgressiveArgs {
  updatePartialData: (batch: ChannelOrder[]) => void
  reqBody: Record<string, unknown>
  skip?: boolean
}

// Minimal QueryOptions shape used in this spec to avoid relying on external types
interface QueryOptions {
  keepPreviousData?: boolean
  skip?: boolean
}

describe('transformOrdersToEntries', () => {
  const baseSelected = (overrides?: Partial<SelectedReportField>): SelectedReportField =>
    ({
      fieldId: 'code',
      displayName: 'Code',
      dataType: 'string',
      isFilterable: false,
      displayOrder: 1,
      accessor: undefined,
      isHidden: false,
      ...overrides
    }) as unknown as SelectedReportField

  const makeOrder = (code: string, entries: Array<{ entryNumber: number; product?: { name?: string } }> = []) =>
    ({
      code,
      entries
    }) as unknown as ChannelOrder

  it('returns [] when orders or selectedFields are empty', () => {
    expect(transformOrdersToEntries([], [])).toEqual([])
    const orders = [makeOrder('O1', [{ entryNumber: 1 }])]
    expect(transformOrdersToEntries(orders, [])).toEqual([])
    expect(transformOrdersToEntries([], [baseSelected()])).toEqual([])
  })

  it("extracts order-level fields using accessor or fieldId and keeps under root keys, entry fields nested under 'entry'", () => {
    const orders = [
      makeOrder('ORD-1', [
        { entryNumber: 10, product: { name: 'Corn' } },
        { entryNumber: 11, product: { name: 'Soy' } }
      ])
    ]

    const fields: SelectedReportField[] = [
      baseSelected({ fieldId: 'code' }), // order level
      baseSelected({ fieldId: 'entry.product.name' }) // entry level
    ]

    const out = transformOrdersToEntries(orders, fields)
    expect(out).toHaveLength(2)
    expect(out[0]).toEqual({
      code: 'ORD-1',
      entry: { 'product.name': 'Corn' },
      id: columnConstants.generateEntryId('ORD-1', 10)
    })
    expect(out[1]).toEqual({
      code: 'ORD-1',
      entry: { 'product.name': 'Soy' },
      id: columnConstants.generateEntryId('ORD-1', 11)
    })
  })

  it('uses accessor override when provided', () => {
    const orders = [makeOrder('O-2', [{ entryNumber: 5, product: { name: 'Wheat' } }])]
    const fields: SelectedReportField[] = [
      baseSelected({ fieldId: 'code', accessor: 'code' }),
      baseSelected({ fieldId: 'entry.product.name', accessor: 'entry.product.name' })
    ]
    const out = transformOrdersToEntries(orders, fields)
    expect(out[0]).toMatchObject({
      code: 'O-2',
      entry: { 'product.name': 'Wheat' }
    })
  })

  it('supports multiple entries per order', () => {
    const orders = [makeOrder('O-3', [{ entryNumber: 1 }, { entryNumber: 2 }, { entryNumber: 3 }])]
    const fields: SelectedReportField[] = [
      baseSelected({ fieldId: 'code' }),
      baseSelected({ fieldId: 'entry.entryNumber' })
    ]
    const out = transformOrdersToEntries(orders, fields)
    expect(out.map((x) => x.id)).toEqual([
      columnConstants.generateEntryId('O-3', 1),
      columnConstants.generateEntryId('O-3', 2),
      columnConstants.generateEntryId('O-3', 3)
    ])
  })

  it('mixes order-level and entry-level fields', () => {
    const orders = [makeOrder('O-4', [{ entryNumber: 9, product: { name: 'Alfalfa' } }])]
    const fields: SelectedReportField[] = [
      baseSelected({ fieldId: 'code' }),
      baseSelected({ fieldId: 'entry.product.name' })
    ]
    const out = transformOrdersToEntries(orders, fields)
    expect(out[0]).toEqual({
      code: 'O-4',
      entry: { 'product.name': 'Alfalfa' },
      id: columnConstants.generateEntryId('O-4', 9)
    })
  })
})

describe('useOrdersByProductData', () => {
  const baseReport = (overrides?: Partial<UserReport>): UserReport =>
    ({
      id: 'r1',
      name: 'Report',
      description: '',
      reportCategory: ReportCategoryOptions.OrderByProduct,
      selectedFields: [],
      selectedFilters: [],
      ...overrides
    }) as unknown as UserReport

  const mockUseSelectedAccount = jest.spyOn(hooksPkg, 'useSelectedAccount')
  const mockUseGetAllOrdersV2 = jest.spyOn(storePkg, 'useGetAllOrdersV2')
  const mockUseFilters = jest.spyOn(useFiltersModule, 'useFilters')
  const mockUseCumulativeData = jest.spyOn(useCumulativeDataModule, 'useCumulativeData')

  beforeEach(() => {
    mockUseSelectedAccount.mockReturnValue({ sapAccountId: 'ACC-1' } as SelectedAccount)
    mockUseFilters.mockReturnValue({})
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  function setupCumulative(initial: Array<Record<string, unknown>> = []) {
    const state = { data: [...initial] }
    const update = jest.fn((items: Record<string, unknown>[]) => {
      state.data.push(...items)
    })

    const reset = jest.fn(() => {
      state.data.length = 0
    })

    mockUseCumulativeData.mockReturnValue({
      cumulativeData: state.data,
      updateCumulativeData: update,
      resetCumulativeData: reset
    })

    return { state, update, reset }
  }

  function makeQuery(
    overrides?: Partial<{
      data: (ChannelOrder | StockOrder)[] | undefined
      isLoading: boolean
      isFetching: boolean
      isError: boolean
      refetch: jest.Mock
    }>
  ) {
    const base = {
      data: undefined as unknown as (ChannelOrder | StockOrder)[] | undefined,
      isLoading: false,
      isFetching: false,
      isError: false,
      refetch: jest.fn()
    }
    const value = { ...base, ...overrides }
    mockUseGetAllOrdersV2.mockReturnValue(value as unknown as ReturnType<typeof storePkg.useGetAllOrdersV2>)
    return value
  }

  it('initial loading state when no data yet', () => {
    setupCumulative([])
    makeQuery({ isLoading: true, data: undefined })

    const { result } = renderHook(() => useOrdersByProductData(baseReport()))
    expect(result.current.data).toEqual([])
    expect(result.current.isDataLoading).toBe(true)
    expect(result.current.isLoading).toBe(true)
    expect(result.current.hasData).toBe(false)
  })

  it('progressive loading: updatePartialData transforms and appends to cumulative data', () => {
    const cumu = setupCumulative([])
    const ordersBatch: ChannelOrder[] = [
      { code: 'A', entries: [{ entryNumber: 1 }, { entryNumber: 2 }] },
      { code: 'B', entries: [{ entryNumber: 1 }] }
    ] as unknown as ChannelOrder[]

    // return a live object so updatePartialData is captured
    let capturedArgs: ProgressiveArgs | null = null
    mockUseGetAllOrdersV2.mockImplementation((args: unknown) => {
      capturedArgs = args as ProgressiveArgs
      return {
        isLoading: false,
        isFetching: false,
        data: undefined,
        refetch: jest.fn(),
        isError: false
      } as unknown as ReturnType<typeof storePkg.useGetAllOrdersV2>
    })

    const userReport = baseReport({
      selectedFields: [
        {
          fieldId: 'code',
          displayName: 'Code',
          dataType: 'string',
          isFilterable: false,
          isRequired: false,
          isHidden: false
        }
      ]
    })
    renderHook(() => useOrdersByProductData(userReport))

    // simulate progressive callback from query
    act(() => {
      // eslint-disable-next-line @typescript-eslint/no-extra-semi
      ;(capturedArgs as ProgressiveArgs).updatePartialData(ordersBatch)
    })

    // It should have transformed 3 entries and called updateCumulativeData once
    expect(cumu.update).toHaveBeenCalledTimes(1)
    const pushed = cumu.update.mock.calls[0][0]
    expect(pushed).toHaveLength(3)
    expect(pushed[0].id).toBe(columnConstants.generateEntryId('A', 1))
    expect(pushed[1].id).toBe(columnConstants.generateEntryId('A', 2))
    expect(pushed[2].id).toBe(columnConstants.generateEntryId('B', 1))
  })

  it('uses filters and sapAccountId in request body', () => {
    setupCumulative([])
    mockUseFilters.mockReturnValue({ status: 'OPEN' })
    let captured: ProgressiveArgs | null = null
    mockUseGetAllOrdersV2.mockImplementation((args: unknown) => {
      captured = args as ProgressiveArgs
      return {
        isLoading: false,
        isFetching: false,
        data: [],
        refetch: jest.fn(),
        isError: false
      } as unknown as ReturnType<typeof storePkg.useGetAllOrdersV2>
    })
    renderHook(() => useOrdersByProductData(baseReport()))
    // narrow captured to ProgressiveArgs for type-safety in assertions
    const prog = captured as unknown as ProgressiveArgs
    expect(prog.reqBody?.agents).toEqual(['ACC-1'])
    expect(prog.reqBody?.status).toBe('OPEN')
    expect(prog.reqBody?.pageSize).toBe(50)
  })

  it('reset cumulative data when starting a new query (isLoading true and no data)', () => {
    const { reset } = setupCumulative([{ id: 'x' }])
    makeQuery({ isLoading: true, data: undefined })
    renderHook(() => useOrdersByProductData(baseReport()))
    // effect runs after mount
    expect(reset).toHaveBeenCalled()
  })

  it('skip conditions when account missing or userReport undefined', () => {
    setupCumulative([])
    mockUseSelectedAccount.mockReturnValueOnce({ sapAccountId: '' } as unknown as SelectedAccount)

    let receivedOptions: QueryOptions | null = null
    mockUseGetAllOrdersV2.mockImplementation((_args: unknown, opts?: QueryOptions) => {
      receivedOptions = opts ?? null
      return {
        isLoading: false,
        isFetching: false,
        data: [],
        refetch: jest.fn(),
        isError: false
      } as unknown as ReturnType<typeof storePkg.useGetAllOrdersV2>
    })

    renderHook(() => useOrdersByProductData(undefined, { keepPreviousData: true }))
    expect((receivedOptions as QueryOptions | null)?.skip).toBe(true)
  })

  it('refetch delegates to query refetch', () => {
    setupCumulative([])
    const q = makeQuery({ isLoading: false, data: [] })
    const { result } = renderHook(() => useOrdersByProductData(baseReport()))
    act(() => {
      result.current.refetch()
    })
    expect(q.refetch).toHaveBeenCalled()
  })

  it('loading flags reflect fetching states', () => {
    setupCumulative([])
    makeQuery({ isLoading: false, isFetching: true, data: [] })
    const { result } = renderHook(() => useOrdersByProductData(baseReport()))

    expect(result.current.isLoading).toBe(false)
    expect(result.current.isFetching).toBe(true)
    expect(result.current.isDataLoading).toBe(true)
  })

  it('error flag is passed through', () => {
    setupCumulative([])
    makeQuery({ isError: true })
    const { result } = renderHook(() => useOrdersByProductData(baseReport()))
    expect(result.current.isError).toBe(true)
  })
})
