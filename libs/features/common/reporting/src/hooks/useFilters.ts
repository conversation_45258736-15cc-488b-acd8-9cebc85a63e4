import { ReportFilterOptions, UserReport } from '@gc/types'
import { useMemo } from 'react'

import { getMappedFilterId } from '../config/reportColumnConstants'

/**
 * Hook to extract and format filters from user report
 * @param userReport - The user report containing selected filters
 * @returns Formatted filters object
 */
export function useFilters(userReport: UserReport | undefined) {
  const selectedFilters = userReport?.selectedFilters
  const reportCategory = userReport?.reportCategory

  return useMemo(() => {
    const filters: Record<string, string | boolean | string[] | boolean[] | undefined> = {}

    if (selectedFilters && reportCategory) {
      selectedFilters.forEach((filter) => {
        const filterId = getMappedFilterId(filter.filterId, reportCategory)

        // Transform 'true' to true and 'false' to false for switch filters
        if (filter.filterType === ReportFilterOptions.SWITCH) {
          filters[filterId] = filter.selectedOptions?.[0] === 'true' ? true : false
        } else {
          filters[filterId] = filter.selectedOptions
        }
      })
    }

    return filters
  }, [selectedFilters, reportCategory])
}
