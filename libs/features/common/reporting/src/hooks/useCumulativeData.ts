import { useCallback, useEffect, useRef, useState } from 'react'

/**
 * Generic hook for managing cumulative data with deduplication
 * Useful for progressive loading scenarios where data is loaded in batches
 */
export function useCumulativeData<T extends Record<string, unknown>>(
  generateId: (item: T) => string = (item) => item.id as string
) {
  const [cumulativeData, setCumulativeData] = useState<T[]>([])

  // Stabilize the generateId reference for use in stable callbacks
  const generateIdRef = useRef(generateId)
  useEffect(() => {
    generateIdRef.current = generateId
  }, [generateId])

  // Stable function identities across rerenders; logic reads latest refs
  const updateCumulativeData = useCallback((newData: T[]) => {
    setCumulativeData((prevData) => {
      const gen = generateIdRef.current

      // Existing ids from current state
      const existingIds = new Set(prevData.map(gen))
      const seen = new Set(existingIds)
      const uniqueNewData: T[] = []

      for (const item of newData) {
        // Normalize null/undefined entries to a safe placeholder object
        const safeItem = (item ?? ({} as T)) as unknown as T
        const id = gen(safeItem)
        if (!seen.has(id)) {
          seen.add(id)
          uniqueNewData.push(safeItem)
        }
      }

      return [...prevData, ...uniqueNewData]
    })
  }, [])

  const resetCumulativeData = useCallback(() => {
    setCumulativeData([])
  }, [])

  return {
    cumulativeData,
    updateCumulativeData,
    resetCumulativeData
  }
}
