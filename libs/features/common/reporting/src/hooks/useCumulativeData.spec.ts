import { act, renderHook } from '@testing-library/react'

import { useCumulativeData } from './useCumulativeData'

describe('useCumulativeData', () => {
  type Item = { id?: string; code?: string; extra?: unknown }

  it('Initial state: cumulativeData starts as empty array', () => {
    const { result } = renderHook(() => useCumulativeData<Item>())
    expect(result.current.cumulativeData).toEqual([])
  })

  it('Adding unique data: appends new unique items', () => {
    const { result } = renderHook(() => useCumulativeData<Item>())

    act(() => {
      result.current.updateCumulativeData([{ id: '1' }, { id: '2' }])
    })
    expect(result.current.cumulativeData).toEqual([{ id: '1' }, { id: '2' }])

    act(() => {
      result.current.updateCumulativeData([{ id: '3' }])
    })
    expect(result.current.cumulativeData).toEqual([{ id: '1' }, { id: '2' }, { id: '3' }])
  })

  it('Duplicate prevention: filters out duplicates based on default generateId (item.id)', () => {
    const { result } = renderHook(() => useCumulativeData<Item>())

    act(() => {
      result.current.updateCumulativeData([{ id: '1' }, { id: '2' }])
    })
    act(() => {
      result.current.updateCumulativeData([{ id: '2' }, { id: '3' }])
    })
    expect(result.current.cumulativeData).toEqual([{ id: '1' }, { id: '2' }, { id: '3' }])
  })

  it('Custom ID generation: deduplicates using custom generateId', () => {
    const { result } = renderHook(() => useCumulativeData<Item>((item) => String(item.code)))

    act(() => {
      result.current.updateCumulativeData([{ code: 'A' }, { code: 'B' }])
    })
    act(() => {
      result.current.updateCumulativeData([{ code: 'B' }, { code: 'C' }])
    })
    expect(result.current.cumulativeData).toEqual([{ code: 'A' }, { code: 'B' }, { code: 'C' }])
  })

  it('Reset functionality: clears cumulativeData', () => {
    const { result } = renderHook(() => useCumulativeData<Item>())

    act(() => {
      result.current.updateCumulativeData([{ id: '1' }, { id: '2' }])
    })
    expect(result.current.cumulativeData.length).toBe(2)

    act(() => {
      result.current.resetCumulativeData()
    })
    expect(result.current.cumulativeData).toEqual([])
  })

  it('Callback stability: update/reset functions stable across rerenders when deps unchanged', () => {
    const { result, rerender } = renderHook(() => useCumulativeData<Item>())

    const update1 = result.current.updateCumulativeData
    const reset1 = result.current.resetCumulativeData

    rerender()

    expect(result.current.updateCumulativeData).toBe(update1)
    expect(result.current.resetCumulativeData).toBe(reset1)
  })

  it('Edge cases: handles empty arrays gracefully', () => {
    const { result } = renderHook(() => useCumulativeData<Item>())

    act(() => {
      result.current.updateCumulativeData([])
    })
    expect(result.current.cumulativeData).toEqual([])
  })

  it('Edge cases: handles null/undefined items safely with custom id generator', () => {
    // Provide a tolerant id generator to avoid runtime error
    const { result } = renderHook(() => useCumulativeData<Item>((item) => String(item?.id ?? item?.code ?? '')))

    act(() => {
      // @ts-expect-error testing malformed inputs
      result.current.updateCumulativeData([null, undefined])
    })

    // Both will map to same id '', first kept, second filtered. But actual objects are null/undefined - they won't pass generateId safely if accessed.
    // Since our custom generator guards, they are treated but still included once.
    expect(result.current.cumulativeData.length).toBe(1)

    // Ensure we can still add valid items afterward
    act(() => {
      result.current.updateCumulativeData([{ id: 'x' }])
    })
    expect(result.current.cumulativeData.some((x) => (x as Item).id === 'x')).toBe(true)
  })
})
