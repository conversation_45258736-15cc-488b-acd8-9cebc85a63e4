/* eslint-disable @nx/enforce-module-boundaries */
import { useSelectedAccount } from '@gc/hooks'
import { useGetAllOrdersV2 } from '@gc/redux-store'
import { ChannelOrder, SelectedReportField, StockOrder, UserReport } from '@gc/types'
import { QueryOptions } from '@gc/utils'
import { get, isEmpty } from 'es-toolkit/compat'
import { useCallback, useEffect, useMemo } from 'react'

import { generateEntryId, hasEntryPrefix, removeEntryPrefix } from '../config/reportColumnConstants'
import { useCumulativeData } from './useCumulativeData'
import { useFilters } from './useFilters'

/**
 * Transform orders into a flat list of entries for ordersByProduct reports
 * Each entry includes order-level fields and entry-specific fields
 */
export function transformOrdersToEntries(
  orders: ChannelOrder[] | StockOrder[],
  selectedFields: SelectedReportField[]
): Record<string, unknown>[] {
  if (!orders.length || !selectedFields.length) {
    return []
  }

  const entries: Record<string, unknown>[] = []

  const selectedEntryFields = selectedFields.filter((field) => hasEntryPrefix(field.fieldId))
  const selectedOrderFields = selectedFields.filter((field) => !hasEntryPrefix(field.fieldId))

  orders.forEach((order) => {
    // Create order fields object from selectedOrderFields
    const orderFields = selectedOrderFields.reduce(
      (acc, field) => {
        const accessor = field.accessor ?? field.fieldId
        // Order-level fields should be stored under their accessor key at top-level
        acc[accessor] = get(order, accessor)
        return acc
      },
      {} as Record<string, unknown>
    )

    // Transform each entry in the order
    const orderEntries = Array.isArray(order.entries) ? order.entries : []
    orderEntries.forEach((entry) => {
      const entryFields = selectedEntryFields.reduce(
        (acc, field) => {
          const accessor = field.accessor ?? field.fieldId
          // Remove the 'entry.' prefix from the accessor if it exists and use the remaining as a flattened key
          const flatKey = hasEntryPrefix(accessor) ? removeEntryPrefix(accessor) : accessor
          // Place as a literal flattened property name e.g. {'product.name': 'Corn'}
          acc[flatKey] = get(entry, flatKey)
          return acc
        },
        {} as Record<string, unknown>
      )

      // Combine order and entry fields
      entries.push({
        ...orderFields,
        entry: entryFields,
        // Use entry number as unique identifier for each seed product
        id: generateEntryId(order.code, entry.entryNumber)
      })
    })
  })

  return entries
}

/**
 * Hook specifically for Orders by Product report data
 * Uses cumulative data management for progressive loading
 */
export function useOrdersByProductData(userReport: UserReport | undefined, options?: QueryOptions) {
  const { sapAccountId } = useSelectedAccount()
  const filters = useFilters(userReport)
  const selectedFields = useMemo(() => userReport?.selectedFields ?? [], [userReport])

  // Use cumulative data hook with entry ID generation
  const { cumulativeData, updateCumulativeData, resetCumulativeData } = useCumulativeData<Record<string, unknown>>(
    (item) => item.id as string
  )

  // Update cumulative data callback - transforms orders to entries
  const updatePartialData = useCallback(
    (newOrders: Record<string, unknown>[]) => {
      // Ensure type safety - the actual type should be determined by the API response
      const transformedEntries = transformOrdersToEntries(newOrders as ChannelOrder[] | StockOrder[], selectedFields)
      updateCumulativeData(transformedEntries)
    },
    [selectedFields, updateCumulativeData]
  )

  // Fetch orders with progressive loading
  const ordersResponse = useGetAllOrdersV2(
    {
      updatePartialData,
      reqBody: { pageSize: 50, agents: [sapAccountId], ...filters }
    },
    {
      skip: isEmpty(sapAccountId) || !userReport,
      ...options
    }
  )

  // Reset cumulative data when starting new query
  useEffect(() => {
    if (ordersResponse.isLoading && !ordersResponse.data) {
      resetCumulativeData()
    }
  }, [ordersResponse.isLoading, ordersResponse.data, resetCumulativeData])

  // Use cumulative data for progressive loading, fallback to transformed response data
  const data = useMemo(() => {
    if (cumulativeData.length > 0) {
      return cumulativeData
    }

    // Transform the response data if no cumulative data exists yet
    const responseData = ordersResponse.data ?? []
    return transformOrdersToEntries(responseData, selectedFields)
  }, [ordersResponse.data, cumulativeData, selectedFields])

  const hasData = useMemo(() => data.length > 0, [data])
  // Match spec expectations: treat no-data + (isLoading || isFetching) as "data is loading"
  const isDataLoading = useMemo(
    () => data.length === 0 && (ordersResponse.isLoading || ordersResponse.isFetching),
    [data, ordersResponse.isLoading, ordersResponse.isFetching]
  )
  const isLoadingMoreData = ordersResponse.isFetching && data.length > 0

  const refetch = useCallback(() => {
    ordersResponse.refetch()
  }, [ordersResponse])

  return {
    data,
    hasData,
    isDataLoading,
    isLoadingMoreData,
    isError: ordersResponse.isError,
    isLoading: ordersResponse.isLoading,
    isFetching: ordersResponse.isFetching,
    refetch
  }
}
