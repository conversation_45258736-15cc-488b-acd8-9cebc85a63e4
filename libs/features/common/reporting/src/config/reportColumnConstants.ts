import { ReportCategoryIds, ReportCategoryOptions } from '@gc/types'
import { TFunction } from 'i18next'

import { ColumnAlignment, ColumnIds, DataType, DisplayTemplate, EntryFieldIds, FilterIds } from '../types'

/**
 * Centralized constants for report column special cases
 * This file contains all the special handling rules for different column types,
 * making it easy to maintain and extend column-specific behavior.
 */

export const REPORT_TABLE_TITLES: Record<ReportCategoryIds, (t: TFunction) => string> = {
  [ReportCategoryOptions.OrderByProduct]: (t: TFunction) => t('reports.order_by_product.title')
}

export const REPORT_TABLE_PREFIXES: Record<ReportCategoryIds, string> = {
  [ReportCategoryOptions.OrderByProduct]: 'orders'
}

export const RULE_IDS = {
  COLUMN_WIDTHS: 'COLUMN_WIDTHS',
  COLUMN_ALIGNMENTS: 'COLUMN_ALIGNMENTS',
  DISPLAY_TEMPLATES: 'DISPLAY_TEMPLATES',
  DATA_TYPE_FORMATTING: 'DATA_TYPE_FORMATTING',
  FILTER_MAPPINGS: 'FILTER_MAPPINGS',
  FIELD_TRANSFORMATIONS: 'FIELD_TRANSFORMATIONS',
  IDENTIFIER_FIELDS: 'IDENTIFIER_FIELDS'
} as const

/**
 * Typed shape for rule group settings
 */
export interface RuleGroup {
  [RULE_IDS.COLUMN_WIDTHS]: Record<string, number>
  [RULE_IDS.COLUMN_ALIGNMENTS]: Record<string, string[]>
  [RULE_IDS.DISPLAY_TEMPLATES]: Record<string, DisplayTemplate>
  [RULE_IDS.DATA_TYPE_FORMATTING]: Record<string, string[]>
  [RULE_IDS.FILTER_MAPPINGS]: Record<string, string>
  [RULE_IDS.FIELD_TRANSFORMATIONS]: Record<string, string>
  [RULE_IDS.IDENTIFIER_FIELDS]: string[]
}

type ReportColumnRulesIDs = 'base' | ReportCategoryIds

export type ReportColumnRules = {
  [K in ReportColumnRulesIDs]: K extends 'base' ? RuleGroup : Partial<RuleGroup>
}

/**
 * Main constants object containing all report column special cases with category-based structure
 */
export const REPORT_COLUMN_RULES: ReportColumnRules = {
  /**
   * Base configuration - used as fallback for all categories
   */
  base: {
    /**
     * Column width overrides for columns that need specific widths (in percentage)
     * Add new entries here when certain columns need more or less space
     */
    [RULE_IDS.COLUMN_WIDTHS]: {
      // Future additions:
      // 'description': 25,
      // 'address': 20,
    } as Record<string, number>,

    /**
     * Column alignment rules grouped by alignment type
     * Determines how content is aligned within each column
     */
    [RULE_IDS.COLUMN_ALIGNMENTS]: {
      CENTER: [ColumnIds.STATUS, ColumnIds.SALES_YEAR, ColumnIds.GROWER, EntryFieldIds.PACKAGE_SIZE_CODE],
      RIGHT: []
      // LEFT is the default, no need to specify
    } as Record<string, string[]>,

    /**
     * Special display components/templates by field ID
     * Determines which component or formatting to use for specific fields
     */
    [RULE_IDS.DISPLAY_TEMPLATES]: {
      [ColumnIds.CODE]: DisplayTemplate.CODE,
      [ColumnIds.STATUS]: DisplayTemplate.BADGE
    } as Record<string, DisplayTemplate>,

    /**
     * Data type based default formatting rules
     * Used for automatic formatting when no specific template is defined
     */
    [RULE_IDS.DATA_TYPE_FORMATTING]: {
      NUMBER_TYPES: [DataType.NUMBER, DataType.CURRENCY],
      DATE_TYPES: [DataType.DATE],
      BOOLEAN_TYPES: [DataType.BOOLEAN]
    } as Record<string, string[]>,

    /**
     * Filter ID mappings for transforming filter names
     * Maps filter field IDs to their corresponding API parameter names
     */
    [RULE_IDS.FILTER_MAPPINGS]: {
      [ColumnIds.STATUS]: FilterIds.STATUS
    } as Record<string, string>,

    /**
     * Field transformation rules
     * Contains patterns and prefixes used in data transformation
     */
    [RULE_IDS.FIELD_TRANSFORMATIONS]: {
      ENTRY_PREFIX: 'entry.',
      ID_GENERATION_SEPARATOR: '-'
    } as Record<string, string>,

    /**
     * Identifier field priority for mobile lists and data extraction
     * Order matters - first available field will be used as identifier
     */
    [RULE_IDS.IDENTIFIER_FIELDS]: ['id', 'code'] as string[]
  },

  /**
   * OrderByProduct category-specific overrides
   * These override the base configuration for OrderByProduct reports
   */
  [ReportCategoryOptions.OrderByProduct]: {}
} as const

/**
 * Internal resolver function to get rule with category-specific override or base fallback
 * @param key - The rule group key to resolve
 * @param category - The report category to check for overrides
 * @returns The resolved rule group (category override or base)
 */
function resolveRule<K extends keyof RuleGroup>(key: K, category: ReportCategoryIds): RuleGroup[K] {
  const categoryRules = REPORT_COLUMN_RULES[category] as Partial<RuleGroup> | undefined
  return (categoryRules?.[key] as RuleGroup[K]) ?? REPORT_COLUMN_RULES.base[key]
}

/**
 * Helper function to get column width for a specific field
 * @param fieldId - The field ID to get width for
 * @param category - The report category for rule resolution
 * @returns Width percentage or undefined if no special width is defined
 */
export function getColumnWidth(fieldId: string, category: ReportCategoryIds): number | undefined {
  return resolveRule(RULE_IDS.COLUMN_WIDTHS, category)[fieldId]
}

/**
 * Helper function to get column alignment for a specific field
 * @param fieldId - The field ID to get alignment for
 * @param dataType - The data type for default alignment fallback
 * @param category - The report category for rule resolution
 * @returns Column alignment ('left', 'center', or 'right')
 */
export function getColumnAlignment(fieldId: string, dataType: string, category: ReportCategoryIds): ColumnAlignment {
  const alignments = resolveRule(RULE_IDS.COLUMN_ALIGNMENTS, category)
  const dataTypeFormatting = resolveRule(RULE_IDS.DATA_TYPE_FORMATTING, category)

  // Check for specific field alignments
  if (alignments.CENTER.includes(fieldId)) {
    return ColumnAlignment.CENTER
  }
  if (alignments.RIGHT.includes(fieldId)) {
    return ColumnAlignment.RIGHT
  }

  // Default alignment based on data type
  if (dataTypeFormatting.NUMBER_TYPES.includes(dataType)) {
    return ColumnAlignment.CENTER
  }

  // Boolean types should be centered
  if (dataTypeFormatting.BOOLEAN_TYPES.includes(dataType)) {
    return ColumnAlignment.CENTER
  }

  return ColumnAlignment.LEFT
}

/**
 * Helper function to get display template type for a specific field
 * @param fieldId - The field ID to get template for
 * @param category - The report category for rule resolution
 * @returns Display template type or 'default' if no special template is defined
 */
export function getDisplayTemplate(fieldId: string, category: ReportCategoryIds): DisplayTemplate {
  return resolveRule(RULE_IDS.DISPLAY_TEMPLATES, category)[fieldId] || DisplayTemplate.DEFAULT
}

/**
 * Helper function to check if a field should use number formatting
 * @param dataType - The data type to check
 * @param category - Optional report category for rule resolution (defaults to base)
 * @returns True if the data type should use number formatting
 */
export function shouldUseNumberFormatting(dataType: string, category?: ReportCategoryIds): boolean {
  const dataTypeFormatting = category
    ? resolveRule(RULE_IDS.DATA_TYPE_FORMATTING, category)
    : REPORT_COLUMN_RULES.base.DATA_TYPE_FORMATTING
  return dataTypeFormatting.NUMBER_TYPES.includes(dataType)
}

/**
 * Helper function to check if a field should use date formatting
 * @param dataType - The data type to check
 * @param category - Optional report category for rule resolution (defaults to base)
 * @returns True if the data type should use date formatting
 */
export function shouldUseDateFormatting(dataType: string, category?: ReportCategoryIds): boolean {
  const dataTypeFormatting = category
    ? resolveRule(RULE_IDS.DATA_TYPE_FORMATTING, category)
    : REPORT_COLUMN_RULES.base.DATA_TYPE_FORMATTING
  return dataTypeFormatting.DATE_TYPES.includes(dataType)
}

export function shouldUseBooleanFormatting(dataType: string, category?: ReportCategoryIds): boolean {
  const dataTypeFormatting = category
    ? resolveRule(RULE_IDS.DATA_TYPE_FORMATTING, category)
    : REPORT_COLUMN_RULES.base.DATA_TYPE_FORMATTING
  return dataTypeFormatting.BOOLEAN_TYPES.includes(dataType)
}

/**
 * Helper function to get mapped filter ID
 * @param filterId - The original filter ID
 * @param category - The report category for rule resolution
 * @returns Mapped filter ID or the original if no mapping exists
 */
export function getMappedFilterId(filterId: string, category: ReportCategoryIds): string {
  return resolveRule(RULE_IDS.FILTER_MAPPINGS, category)[filterId] || filterId
}

/**
 * Helper function to check if a field has the entry prefix
 * @param fieldId - The field ID to check
 * @param category - Optional report category for rule resolution (defaults to base)
 * @returns True if the field ID starts with the entry prefix
 */
export function hasEntryPrefix(fieldId: string, category?: ReportCategoryIds): boolean {
  const fieldTransformations = category
    ? resolveRule(RULE_IDS.FIELD_TRANSFORMATIONS, category)
    : REPORT_COLUMN_RULES.base.FIELD_TRANSFORMATIONS
  return fieldId.startsWith(fieldTransformations.ENTRY_PREFIX)
}

/**
 * Helper function to remove entry prefix from field ID
 * @param fieldId - The field ID to process
 * @param category - Optional report category for rule resolution (defaults to base)
 * @returns Field ID without the entry prefix
 */
export function removeEntryPrefix(fieldId: string, category?: ReportCategoryIds): string {
  const fieldTransformations = category
    ? resolveRule(RULE_IDS.FIELD_TRANSFORMATIONS, category)
    : REPORT_COLUMN_RULES.base.FIELD_TRANSFORMATIONS
  return fieldId.replace(fieldTransformations.ENTRY_PREFIX, '')
}

/**
 * Helper function to generate unique ID for order entries
 * @param orderCode - The order code
 * @param entryNumber - The entry number
 * @param category - Optional report category for rule resolution (defaults to base)
 * @returns Generated unique ID
 */
export function generateEntryId(orderCode: string, entryNumber: string | number, category?: ReportCategoryIds): string {
  const fieldTransformations = category
    ? resolveRule(RULE_IDS.FIELD_TRANSFORMATIONS, category)
    : REPORT_COLUMN_RULES.base.FIELD_TRANSFORMATIONS
  return `${orderCode}${fieldTransformations.ID_GENERATION_SEPARATOR}${entryNumber}`
}

/**
 * Helper function to get identifier field from a data object
 * @param item - The data object to extract identifier from
 * @param category - Optional report category for rule resolution (defaults to base)
 * @returns The identifier value or empty string if none found
 */
export function getItemIdentifier(item: Record<string, unknown>, category?: ReportCategoryIds): string {
  const identifierFields = category
    ? resolveRule(RULE_IDS.IDENTIFIER_FIELDS, category)
    : REPORT_COLUMN_RULES.base.IDENTIFIER_FIELDS

  for (const field of identifierFields) {
    if (field in item && item[field] != null) {
      return String(item[field])
    }
  }

  // Fallback to the first available property value
  const firstValue = Object.values(item)[0]
  return String(firstValue || '')
}

export function getPrefixFromConstants(reportCategory: ReportCategoryIds): string {
  return REPORT_TABLE_PREFIXES[reportCategory]
}
