/* eslint-disable sonarjs/no-small-switch */
/* eslint-disable @nx/enforce-module-boundaries */
import { TypoLink } from '@element/react-typography'
import { Badge, HeaderType } from '@gc/components'
import { useMemoizedTranslation } from '@gc/hooks'
import { ReportCategoryIds, ReportCategoryOptions, UserReport } from '@gc/types'
import { goToPrefixCode } from '@gc/utils'
import { TFunction } from 'i18next'
import { useMemo } from 'react'

import { ReportRowData, ReportTableColumnConfig } from '../components/report-template/ReportTemplate'
import { DisplayTemplate } from '../types'
import {
  getPrefixFromConstants,
  REPORT_COLUMN_RULES,
  REPORT_TABLE_TITLES,
  RULE_IDS,
  RuleGroup,
  shouldUseBooleanFormatting,
  shouldUseDateFormatting,
  shouldUseNumberFormatting
} from './reportColumnConstants'

export interface ReportTypeConfig<T extends ReportRowData = ReportRowData> {
  columns: ReportTableColumnConfig<T>[]
  title: string
  options: {
    enableSearch: boolean
    enableCsvDownload: boolean
    enablePagination: boolean
  }
}

/**
 * Helper function to merge base rules with category-specific overrides
 */
function mergeRuleGroup<K extends keyof RuleGroup>(
  ruleKey: K,
  reportCategory: ReportCategoryIds,
  categoryConfig?: Partial<RuleGroup>
): RuleGroup[K] {
  const baseRule = REPORT_COLUMN_RULES.base[ruleKey]
  const categoryRule = categoryConfig?.[ruleKey]

  if (!categoryRule) {
    return baseRule
  }

  // Merge objects (for most rule types)
  if (typeof baseRule === 'object' && typeof categoryRule === 'object' && !Array.isArray(baseRule)) {
    return { ...baseRule, ...categoryRule } as RuleGroup[K]
  }

  // For arrays and other types, category overrides completely
  return categoryRule as RuleGroup[K]
}

/**
 * Get display template based on field ID and data type
 */
function getDisplayTemplate(
  fieldId: string,
  dataType: string,
  reportCategory: ReportCategoryIds,
  categoryConfig?: Partial<RuleGroup>
): HeaderType<ReportRowData>['displayTemplate'] {
  const prefix = getPrefixFromConstants(reportCategory)

  // Get merged display templates (base + category overrides)
  const displayTemplates = mergeRuleGroup(RULE_IDS.DISPLAY_TEMPLATES, reportCategory, categoryConfig)
  const templateType = displayTemplates[fieldId] || DisplayTemplate.DEFAULT

  // If the template is a function, return it directly
  if (typeof templateType === 'function') {
    return templateType
  }

  switch (templateType) {
    case DisplayTemplate.CODE:
      return (code: string) =>
        code ? (
          <TypoLink data-testid='typo-link' onClick={() => goToPrefixCode(code, prefix)}>
            {code}
          </TypoLink>
        ) : (
          ''
        )

    case DisplayTemplate.BADGE:
      return (statusText: string) => (statusText ? <Badge data-testid='badge' labelText={`${statusText}`} /> : '')

    default:
      // Default formatting based on data type
      if (shouldUseNumberFormatting(dataType)) {
        return (value: unknown) => (value != null ? Number(value).toLocaleString() : '')
      }
      if (shouldUseDateFormatting(dataType)) {
        return (value: unknown) => (value ? new Date(String(value)).toLocaleDateString() : '')
      }
      if (shouldUseBooleanFormatting(dataType)) {
        return (value: boolean) => <Badge data-testid='badge' labelText={`${value ? 'Yes' : 'No'}`} />
      }

      return undefined
  }
}

/**
 * Get column alignment based on field ID and data type
 */
function getColumnAlignment(
  fieldId: string,
  dataType: string,
  reportCategory: ReportCategoryIds,
  categoryConfig?: Partial<RuleGroup>
): 'left' | 'center' | 'right' {
  // Get merged alignment rules (base + category overrides)
  const alignments = mergeRuleGroup(RULE_IDS.COLUMN_ALIGNMENTS, reportCategory, categoryConfig)
  const dataTypeFormatting = mergeRuleGroup(RULE_IDS.DATA_TYPE_FORMATTING, reportCategory, categoryConfig)

  // Check for specific field alignments
  if (alignments.CENTER?.includes(fieldId)) {
    return 'center'
  }
  if (alignments.RIGHT?.includes(fieldId)) {
    return 'right'
  }

  // Default alignment based on data type
  if (dataTypeFormatting.NUMBER_TYPES?.includes(dataType)) {
    return 'center'
  }

  // Boolean types should be centered
  if (dataTypeFormatting.BOOLEAN_TYPES?.includes(dataType)) {
    return 'center'
  }

  return 'left'
}

// Column widths are now managed in reportColumnConstants.ts
// This local constant is kept for backwards compatibility but uses the centralized constants

/**
 * Calculate width percentages for all columns, accounting for special columns first
 */
function calculateColumnWidths(
  fieldIds: string[],
  reportCategory: ReportCategoryIds,
  categoryConfig?: Partial<RuleGroup>
): Record<string, number> {
  const widths: Record<string, number> = {}

  // Get merged column width rules (base + category overrides)
  const columnWidthRules = mergeRuleGroup(RULE_IDS.COLUMN_WIDTHS, reportCategory, categoryConfig)

  // First pass: Calculate total width used by special columns
  let specialColumnsWidth = 0
  const specialFields: string[] = []
  const regularFields: string[] = []

  fieldIds.forEach((fieldId) => {
    const columnWidth = columnWidthRules[fieldId]
    if (columnWidth) {
      specialFields.push(fieldId)
      specialColumnsWidth += columnWidth
    } else {
      regularFields.push(fieldId)
    }
  })

  // Second pass: Distribute remaining width among regular columns
  const remainingWidth = 100 - specialColumnsWidth
  const regularColumnWidth = regularFields.length > 0 ? Math.floor(remainingWidth / regularFields.length) : 0

  // Handle rounding - give extra width to the last regular column if needed
  const totalAssigned = specialColumnsWidth + regularColumnWidth * regularFields.length
  const extraWidth = 100 - totalAssigned

  // Assign widths
  specialFields.forEach((fieldId) => {
    const columnWidth = columnWidthRules[fieldId]
    if (columnWidth) {
      widths[fieldId] = columnWidth
    }
  })

  regularFields.forEach((fieldId, index) => {
    // Give extra width to the last regular column to reach exactly 100%
    const isLastRegularField = index === regularFields.length - 1
    widths[fieldId] = regularColumnWidth + (isLastRegularField ? extraWidth : 0)
  })

  return widths
}

/**
 * Generate dynamic columns based on user's selected fields
 */
function generateDynamicColumns(
  userReport: UserReport,
  categoryConfig?: Partial<RuleGroup>
): ReportTableColumnConfig<ReportRowData>[] {
  if (!userReport.selectedFields || userReport.selectedFields.length === 0) {
    return []
  }

  // Sort fields by display order
  const sortedFields = [...userReport.selectedFields]
    .filter((field) => !field.isHidden)
    .sort((a, b) => {
      const orderA = a.displayOrder ?? 999
      const orderB = b.displayOrder ?? 999
      return orderA - orderB
    })

  // Calculate width percentages for all columns
  const fieldIds = sortedFields.map((field) => field.fieldId)
  const reportCategory = userReport.reportCategory
  const columnWidths = calculateColumnWidths(fieldIds, reportCategory, categoryConfig)

  return sortedFields.map((field, index) => {
    const displayTemplate = getDisplayTemplate(field.fieldId, field.dataType, reportCategory, categoryConfig)
    const align = getColumnAlignment(field.fieldId, field.dataType, reportCategory, categoryConfig)

    return {
      align,
      displayTemplate,
      id: field.fieldId,
      header: field.displayName,
      filterable: field.isFilterable,
      accessor: (field.accessor ?? field.fieldId) as keyof ReportRowData,
      // Use calculated width percentage
      widthPercentage: columnWidths[field.fieldId],
      // First field gets default sort
      defaultSort: index === 0 ? ('desc' as const) : undefined
    }
  })
}

/**
 * Dynamic configuration for report types based on user selections
 */
export const REPORT_TYPE_CONFIGS: Record<
  ReportCategoryIds,
  (userReport: UserReport, t: TFunction, categoryConfig?: Partial<RuleGroup>) => ReportTypeConfig
> = {
  [ReportCategoryOptions.OrderByProduct]: (
    userReport: UserReport,
    t: TFunction,
    categoryConfig?: Partial<RuleGroup>
  ) => {
    return {
      columns: generateDynamicColumns(userReport, categoryConfig),
      title: REPORT_TABLE_TITLES[userReport.reportCategory](t),
      options: {
        enableSearch: true,
        enableCsvDownload: true,
        enablePagination: true
      }
    }
  }
}

/**
 * Hook to get report column configuration using static definitions
 * @param userReport - User report configuration
 * @returns Report column configuration
 */
export function useReportColumnConfiguration(
  userReport: UserReport,
  categoryConfig?: Partial<RuleGroup>
): ReportTypeConfig {
  const t = useMemoizedTranslation()

  return useMemo(() => {
    const reportCategory = userReport.reportCategory
    const configFn = REPORT_TYPE_CONFIGS[reportCategory]

    return (
      configFn?.(userReport, t, categoryConfig) ?? {
        columns: [],
        title: '',
        options: {
          enableSearch: false,
          enableCsvDownload: false,
          enablePagination: false
        }
      }
    )
  }, [userReport, t, categoryConfig])
}

/**
 * Example usage demonstrating how to pass category-specific configurations
 *
 * This approach uses the base rules from reportColumnConstants.ts and merges them
 * with category-specific overrides passed to the hook.
 */
/* Example usage (commented out):

import { asyncHelpers } from './reportColumnConstants'

// Option 1: Static category-specific config
const staticReportConfig: Record<ReportCategoryIds, Partial<RuleGroup>> = {
  [ReportCategoryOptions.OrderByProduct]: {
    [RULE_IDS.DISPLAY_TEMPLATES]: {
      grossPrice: (value, order) => `$${value}`,
      productName: DisplayTemplate.CODE
    },
    [RULE_IDS.COLUMN_WIDTHS]: {
      productName: 25,
      grossPrice: 15
    }
  }
}

// Option 2: Dynamic loading with async helpers
const loadReportConfig = async (reportCategory: ReportCategoryIds) => {
  const grossPriceTemplate = await asyncHelpers.getDisplayTemplate('grossPrice', reportCategory)

  return {
    [reportCategory]: {
      [RULE_IDS.DISPLAY_TEMPLATES]: {
        grossPrice: grossPriceTemplate
      },
      [RULE_IDS.COLUMN_WIDTHS]: {
        productName: 25,
        grossPrice: 15
      }
    }
  }
}

// Usage in component
export function ReportWithMergedConfig({ userReport }: { userReport: UserReport }) {
  // Option 1: Use static config
  const config1 = useReportColumnConfiguration(userReport, staticReportConfig)

  // Option 2: Use dynamic config
  const [reportConfig, setReportConfig] = useState<Record<ReportCategoryIds, Partial<RuleGroup>>>()

  useEffect(() => {
    loadReportConfig(userReport.reportCategory).then(setReportConfig)
  }, [userReport.reportCategory])

  const config2 = useReportColumnConfiguration(userReport, reportConfig)

  return <ReportTemplate config={config2} data={[]} />
}

// How the merging works:
// 1. Base rules from REPORT_COLUMN_RULES.base are used as foundation
// 2. Category-specific overrides are merged on top
// 3. For objects: { ...baseRule, ...categoryRule }
// 4. For arrays: categoryRule completely replaces baseRule
// 5. Missing category configs fall back to base rules
*/
