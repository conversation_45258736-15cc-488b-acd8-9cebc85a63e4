/* eslint-disable @nx/enforce-module-boundaries */
import { type ReportCategoryIds, ReportCategoryOptions } from '@gc/types'

import { ColumnAlignment, DataType, DisplayTemplate } from '../types'
import {
  generateEntryId,
  getColumnAlignment,
  getColumnWidth,
  getDisplayTemplate,
  getItemIdentifier,
  getMappedFilterId,
  getPrefixFromConstants,
  hasEntryPrefix,
  removeEntryPrefix,
  REPORT_COLUMN_RULES,
  shouldUseBooleanFormatting,
  shouldUseDateFormatting,
  shouldUseNumberFormatting
} from './reportColumnConstants'

describe('reportColumnConstants helpers', () => {
  const CAT: ReportCategoryIds = ReportCategoryOptions.OrderByProduct

  describe('getColumnWidth', () => {
    it('returns undefined for fields without specific width', () => {
      expect(getColumnWidth('unknownField', CAT)).toBeUndefined()
    })

    it('returns specific width when defined in rules', () => {
      const fieldId = 'description'
      const original = REPORT_COLUMN_RULES.base.COLUMN_WIDTHS[fieldId]
      REPORT_COLUMN_RULES.base.COLUMN_WIDTHS[fieldId] = 25
      try {
        expect(getColumnWidth(fieldId, CAT)).toBe(25)
      } finally {
        if (original === undefined) {
          delete REPORT_COLUMN_RULES.base.COLUMN_WIDTHS[fieldId]
        } else {
          REPORT_COLUMN_RULES.base.COLUMN_WIDTHS[fieldId] = original
        }
      }
    })
  })

  describe('getColumnAlignment', () => {
    it('uses specific CENTER/RIGHT alignment rules', () => {
      expect(getColumnAlignment('status', DataType.STRING, CAT)).toBe(ColumnAlignment.CENTER)
    })

    it('centers number and boolean types by default', () => {
      expect(getColumnAlignment('any', DataType.NUMBER, CAT)).toBe(ColumnAlignment.CENTER)
      expect(getColumnAlignment('any', DataType.BOOLEAN, CAT)).toBe(ColumnAlignment.CENTER)
    })

    it('defaults to LEFT', () => {
      expect(getColumnAlignment('any', DataType.STRING, CAT)).toBe(ColumnAlignment.LEFT)
    })
  })

  describe('getDisplayTemplate', () => {
    it('returns CODE template for code field, BADGE for status, DEFAULT otherwise', () => {
      expect(getDisplayTemplate('code', CAT)).toBe(DisplayTemplate.CODE)
      expect(getDisplayTemplate('status', CAT)).toBe(DisplayTemplate.BADGE)
      expect(getDisplayTemplate('anything-else', CAT)).toBe(DisplayTemplate.DEFAULT)
    })
  })

  describe('formatting helpers', () => {
    it('shouldUseNumberFormatting true for NUMBER and CURRENCY', () => {
      expect(shouldUseNumberFormatting(DataType.NUMBER)).toBe(true)
      expect(shouldUseNumberFormatting(DataType.CURRENCY)).toBe(true)
      expect(shouldUseNumberFormatting(DataType.STRING)).toBe(false)
    })

    it('shouldUseDateFormatting only for DATE', () => {
      expect(shouldUseDateFormatting(DataType.DATE)).toBe(true)
      expect(shouldUseDateFormatting(DataType.STRING)).toBe(false)
    })

    it('shouldUseBooleanFormatting only for BOOLEAN', () => {
      expect(shouldUseBooleanFormatting(DataType.BOOLEAN)).toBe(true)
      expect(shouldUseBooleanFormatting(DataType.STRING)).toBe(false)
    })
  })

  describe('getMappedFilterId', () => {
    it('returns mapped filter id when mapping exists', () => {
      expect(getMappedFilterId('status', CAT)).toBe('statusCodes')
    })

    it('returns original when mapping does not exist', () => {
      expect(getMappedFilterId('nonExisting', CAT)).toBe('nonExisting')
    })
  })

  describe('entry prefix helpers', () => {
    it('hasEntryPrefix detects entry. prefix', () => {
      expect(hasEntryPrefix('entry.product.name')).toBe(true)
      expect(hasEntryPrefix('product.name')).toBe(false)
    })

    it('removeEntryPrefix removes entry. prefix', () => {
      expect(removeEntryPrefix('entry.product.name')).toBe('product.name')
      expect(removeEntryPrefix('product.name')).toBe('product.name')
    })
  })

  describe('generateEntryId', () => {
    it('generates orderCode-entryNumber format', () => {
      expect(generateEntryId('ORD', 5)).toBe('ORD-5')
      expect(generateEntryId('ORD', '10')).toBe('ORD-10')
    })
  })

  describe('getItemIdentifier', () => {
    it('returns first available identifier from priority list', () => {
      expect(getItemIdentifier({ id: 'X', anything: 1 })).toBe('X')
      expect(getItemIdentifier({ code: 'C-1' })).toBe('C-1')
    })

    it('falls back to first object value when none present', () => {
      expect(getItemIdentifier({ name: 'N1', value: 2 })).toBe('N1')
      expect(getItemIdentifier({})).toBe('')
    })
  })

  describe('getPrefixFromConstants', () => {
    it('returns correct prefix per category', () => {
      expect(getPrefixFromConstants(ReportCategoryOptions.OrderByProduct)).toBe('orders')
    })
  })

  describe('rule resolution behavior', () => {
    it('category rules override base for objects, base used when no override', () => {
      const baseAlign = REPORT_COLUMN_RULES.base.COLUMN_ALIGNMENTS
      expect(baseAlign.CENTER).toEqual(expect.arrayContaining(['status']))

      expect(getColumnAlignment('status', DataType.STRING, ReportCategoryOptions.OrderByProduct)).toBe(
        ColumnAlignment.CENTER
      )
      expect(getColumnWidth('nonSpecified', ReportCategoryOptions.OrderByProduct)).toBeUndefined()
    })
  })
})
