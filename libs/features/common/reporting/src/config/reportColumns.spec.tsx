/* eslint-disable sonarjs/no-nested-functions */
/* eslint-disable @nx/enforce-module-boundaries */
import * as hooks from '@gc/hooks'
import { ReportCategoryOptions, RowData, type UserReport } from '@gc/types'
import * as utils from '@gc/utils'
import { renderHook } from '@testing-library/react'
import React from 'react'

import { ReportRowData } from '../components/report-template/ReportTemplate'
import {
  type ReportTypeConfig
  // internal utilities under test via named re-export pattern
} from './reportColumns'
// Re-import the module under test as a whole to access non-exported utilities through helpers section below.
// We will duplicate minimal logic expectations by driving through exported API.
// For display template and alignment we indirectly verify via generated columns.
import * as reportColumnsModule from './reportColumns'

// Mocks
jest.mock('@gc/hooks', () => ({
  useMemoizedTranslation: jest.fn(() => (k: string) => k)
}))
jest.mock('@gc/utils', () => ({
  goToPrefixCode: jest.fn()
}))
jest.mock('@element/react-typography', () => ({
  TypoLink: ({ children, onClick }: { children: React.ReactNode; onClick: () => void }) => (
    <a data-testid='typo-link' onClick={onClick} href='/'>
      {children}
    </a>
  )
}))
jest.mock('@gc/components', () => ({
  Badge: ({ labelText }: { labelText: string }) => <span data-testid='badge'>{labelText}</span>
}))

// Helper to build a UserReport quickly
const makeReport = (overrides?: Partial<UserReport>): UserReport =>
  ({
    id: 'r1',
    name: 'Report',
    description: '',
    reportCategory: ReportCategoryOptions.OrderByProduct,
    selectedFields: [],
    selectedFilters: [],
    ...overrides
  }) as unknown as UserReport

describe('reportColumns - generate columns and configuration', () => {
  const useMemoizedTranslation = hooks.useMemoizedTranslation as jest.Mock
  const goToPrefixCode = utils.goToPrefixCode as jest.Mock

  beforeEach(() => {
    jest.clearAllMocks()
    useMemoizedTranslation.mockReturnValue((k: string) => k)
  })

  describe('generateDynamicColumns and calculateColumnWidths (via REPORT_TYPE_CONFIGS)', () => {
    it('returns empty columns for empty selectedFields', () => {
      const report = makeReport({ selectedFields: [] })
      const configFn = reportColumnsModule.REPORT_TYPE_CONFIGS[ReportCategoryOptions.OrderByProduct]
      const cfg = configFn(report, useMemoizedTranslation())
      expect(cfg.columns).toEqual([])
    })

    it('sorts by displayOrder, filters hidden, assigns widths to 100% total, and sets defaultSort for first column', () => {
      const report = makeReport({
        selectedFields: [
          {
            fieldId: 'status',
            displayName: 'Status',
            dataType: 'string',
            displayOrder: 3,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          },
          {
            fieldId: 'code',
            displayName: 'Code',
            dataType: 'string',
            displayOrder: 1,
            isFilterable: true,
            isRequired: false,
            isHidden: false
          },
          {
            fieldId: 'salesYear',
            displayName: 'Sales Year',
            dataType: 'number',
            displayOrder: 2,
            isFilterable: true,
            isRequired: false,
            isHidden: false
          },
          {
            fieldId: 'hiddenField',
            displayName: 'Hidden',
            dataType: 'string',
            displayOrder: 4,
            isFilterable: false,
            isRequired: false,
            isHidden: true
          }
        ]
      })
      const configFn = reportColumnsModule.REPORT_TYPE_CONFIGS[ReportCategoryOptions.OrderByProduct]
      const cfg = configFn(report, useMemoizedTranslation())
      const ids = cfg.columns.map((c) => c.id)
      expect(ids).toEqual(['code', 'salesYear', 'status']) // sorted and hidden removed

      // widths sum to 100
      const widthSum = cfg.columns.reduce((s, c) => s + (c.widthPercentage ?? 0), 0)
      expect(widthSum).toBe(100)

      // defaultSort on first column only
      expect(cfg.columns[0].defaultSort).toBe('desc')
      expect(cfg.columns[1].defaultSort).toBeUndefined()
      expect(cfg.columns[2].defaultSort).toBeUndefined()
    })
  })

  describe('display template selection and alignment (via generateDynamicColumns)', () => {
    it('applies CODE display template for code and BADGE for status; boolean/number use defaults', () => {
      const report = makeReport({
        selectedFields: [
          {
            fieldId: 'code',
            displayName: 'Code',
            dataType: 'string',
            displayOrder: 1,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          },
          {
            fieldId: 'status',
            displayName: 'Status',
            dataType: 'string',
            displayOrder: 2,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          },
          {
            fieldId: 'qty',
            displayName: 'Qty',
            dataType: 'number',
            displayOrder: 3,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          },
          {
            fieldId: 'isActive',
            displayName: 'Active',
            dataType: 'boolean',
            displayOrder: 4,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          }
        ]
      })

      const cfg = reportColumnsModule.REPORT_TYPE_CONFIGS[ReportCategoryOptions.OrderByProduct](
        report,
        useMemoizedTranslation()
      )

      // code template -> TypoLink
      const codeCol = cfg.columns.find((c) => c.id === 'code')
      expect(codeCol?.displayTemplate).toBeDefined()

      const CodeCell = codeCol?.displayTemplate

      // render function should call goToPrefixCode on click
      const codeEl = CodeCell?.('ORD-1', {} as unknown as RowData<ReportRowData>) as React.ReactElement
      expect(codeEl?.props['data-testid']).toBe('typo-link')
      codeEl?.props.onClick()
      expect(goToPrefixCode).toHaveBeenCalledWith('ORD-1', 'orders')

      // status template -> Badge
      const statusCol = cfg.columns.find((c) => c.id === 'status')
      const StatusCell = statusCol?.displayTemplate
      const statusEl = StatusCell?.('Open', {} as unknown as RowData<ReportRowData>) as React.ReactElement
      expect(statusEl?.props['data-testid']).toBe('badge')
      expect(statusEl?.props.children).toBeUndefined()
      expect(statusEl?.props.labelText).toBe('Open')

      // number -> number formatting function defined
      const qtyCol = cfg.columns.find((c) => c.id === 'qty')
      const QtyCell = qtyCol?.displayTemplate
      expect(QtyCell?.(12345, {} as unknown as RowData<ReportRowData>)).toBe('12,345')

      // boolean -> Badge yes/no
      const activeCol = cfg.columns.find((c) => c.id === 'isActive')
      const ActiveCell = activeCol?.displayTemplate
      const t1 = ActiveCell?.(true, {} as unknown as RowData<ReportRowData>) as React.ReactElement
      expect(t1?.props['data-testid']).toBe('badge')
      expect(t1?.props.labelText).toBe('Yes')
      const t2 = ActiveCell?.(false, {} as unknown as RowData<ReportRowData>) as React.ReactElement
      expect(t2?.props.labelText).toBe('No')
    })

    it('alignment rules: status centered, number and boolean centered, others left', () => {
      const report = makeReport({
        selectedFields: [
          {
            fieldId: 'status',
            displayName: 'Status',
            dataType: 'string',
            displayOrder: 1,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          },
          {
            fieldId: 'qty',
            displayName: 'Qty',
            dataType: 'number',
            displayOrder: 2,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          },
          {
            fieldId: 'flag',
            displayName: 'Flag',
            dataType: 'boolean',
            displayOrder: 3,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          },
          {
            fieldId: 'note',
            displayName: 'Note',
            dataType: 'string',
            displayOrder: 4,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          }
        ]
      })

      const cfg = reportColumnsModule.REPORT_TYPE_CONFIGS[ReportCategoryOptions.OrderByProduct](
        report,
        useMemoizedTranslation()
      )
      const getAlign = (id: string) => cfg.columns.find((c) => c.id === id)?.align
      expect(getAlign('status')).toBe('center')
      expect(getAlign('qty')).toBe('center')
      expect(getAlign('flag')).toBe('center')
      expect(getAlign('note')).toBe('left')
    })
  })

  describe('useReportColumnConfiguration hook', () => {
    it('returns config for report category and memoizes by deps', () => {
      const report = makeReport({
        selectedFields: [
          {
            fieldId: 'code',
            displayName: 'Code',
            dataType: 'string',
            displayOrder: 1,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          }
        ]
      })

      const { result, rerender } = renderHook(({ ur }) => reportColumnsModule.useReportColumnConfiguration(ur), {
        initialProps: { ur: report }
      })

      const first = result.current
      expect(first.title).toBe('reports.order_by_product.title')
      expect(first.columns.length).toBe(1)

      // rerender with same object ref => memoized
      rerender({ ur: report })
      expect(result.current).toBe(first)

      // change prop => recompute
      const report2 = makeReport({
        selectedFields: [
          {
            fieldId: 'code',
            displayName: 'Code',
            dataType: 'string',
            displayOrder: 1,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          },
          {
            fieldId: 'status',
            displayName: 'Status',
            dataType: 'string',
            displayOrder: 2,
            isFilterable: false,
            isRequired: false,
            isHidden: false
          }
        ]
      })
      rerender({ ur: report2 })
      expect(result.current).not.toBe(first)
      expect(result.current.columns.length).toBe(2)
    })

    it('falls back to empty configuration if no category config function exists (defensive)', () => {
      // Temporarily patch config map
      const original = { ...reportColumnsModule.REPORT_TYPE_CONFIGS }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      reportColumnsModule.REPORT_TYPE_CONFIGS[ReportCategoryOptions.OrderByProduct] = undefined as any

      try {
        const report = makeReport()
        const { result } = renderHook(() => reportColumnsModule.useReportColumnConfiguration(report))
        expect(result.current).toEqual<ReportTypeConfig>({
          columns: [],
          title: '',
          options: { enableSearch: false, enableCsvDownload: false, enablePagination: false }
        })
      } finally {
        Object.assign(reportColumnsModule.REPORT_TYPE_CONFIGS, original)
      }
    })
  })
})
