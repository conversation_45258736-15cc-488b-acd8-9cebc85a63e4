// eslint-disable-next-line @nx/enforce-module-boundaries
import { MessageWithAction } from '@gc/components'

import styles from './DesktopViewRequired.module.scss'

export function DesktopViewRequired() {
  return (
    <div className={styles.centeredMessage}>
      <MessageWithAction
        messageHeader='Desktop View Required'
        messageDescription='This reporting feature can only be displayed on desktop devices. Please switch to a desktop view to access reports.'
        iconProps={{ icon: 'desktop_windows' }}
      />
    </div>
  )
}
