import { ReportCategoryIds, ReportCategoryOptions, UserReport } from '@gc/types'
import { ComponentType } from 'react'

import { OrderByProductTemplate } from './orderByProduct'

// Common props interface for all report templates
export interface ReportTemplateProps {
  fasteStoreKey?: string
  userReport: UserReport
}

// Template registry mapping report categories to their components
export const REPORT_TEMPLATES: Partial<Record<ReportCategoryIds, ComponentType<ReportTemplateProps>>> = {
  [ReportCategoryOptions.OrderByProduct]: OrderByProductTemplate
}

// Export individual templates for direct imports
export { OrderByProductTemplate } from './orderByProduct'
