/* eslint-disable @nx/enforce-module-boundaries */
import { HeaderProps } from '@gc/components'
import { useMemoizedTranslation, useModal } from '@gc/hooks'
import { UserReport } from '@gc/types'
import { useCallback } from 'react'

import { RuleGroup } from '../../config/reportColumnConstants'
import { useReportColumnConfiguration } from '../../config/reportColumns'
import { useOrdersByProductData } from '../../hooks/useOrderByProducts'
import { ReportTemplate } from '../report-template/ReportTemplate'
import { ReportTemplateHeader } from '../report-template/ReportTemplateHeader'
import { ReportTemplateProps } from './index'

// Add any configs for the order by product report here
const ORDER_BY_PRODUCT_COLUMN_CONFIG: Partial<RuleGroup> = {
  /**
   * Special display components/templates by field ID
   * Determines which component or formatting to use for specific fields
   */
  // [RULE_IDS.DISPLAY_TEMPLATES]: {
  //   [ColumnIds.GROSS_PRICE]: (_, order: ChannelOrder) => {
  //     return ''
  //   }
  // } as Record<string, DisplayTemplate>
}

function useHeaderConfig(userReport: UserReport): HeaderProps {
  const { openModal } = useModal()
  const t = useMemoizedTranslation()

  const handleDuplicateReport = useCallback(() => {
    window.alert('Duplicate report!')
  }, [])

  const handleEditReport = useCallback(() => {
    // eslint-disable-next-line no-console
    console.log('edit report')

    openModal({
      name: 'CREATE_REPORT',
      props: {
        isEditMode: true,
        userReport: userReport
      }
    })
  }, [openModal, userReport])

  return {
    title: userReport.reportName,
    buttonProps: [
      {
        variant: 'filled',
        label: t('common.duplicate.label', { defaultValue: 'Duplicate' }),
        onClick: handleDuplicateReport
      },
      {
        variant: 'filled',
        label: t('common.edit.label', { defaultValue: 'Edit' }),
        onClick: handleEditReport
      }
    ]
  }
}

export function OrderByProductTemplate({ userReport, fasteStoreKey }: Readonly<ReportTemplateProps>) {
  const { columns, title, options } = useReportColumnConfiguration(userReport, ORDER_BY_PRODUCT_COLUMN_CONFIG)
  const headerProps = useHeaderConfig(userReport)

  // Fetch data using the specialized hook
  const {
    refetch,
    isError,
    isLoading,
    isFetching,
    isLoadingMoreData,
    data: reportData
  } = useOrdersByProductData(userReport)

  return (
    <>
      <ReportTemplateHeader headerProps={headerProps} />

      <ReportTemplate
        title={title}
        refetch={refetch}
        data={reportData}
        columns={columns}
        isError={isError}
        isLoading={isLoading}
        isFetching={isFetching}
        fasteStoreKey={fasteStoreKey}
        isLoadingMoreData={isLoadingMoreData}
        {...options}
      />
    </>
  )
}

export default OrderByProductTemplate
