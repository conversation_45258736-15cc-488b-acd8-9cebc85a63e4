/* eslint-disable @nx/enforce-module-boundaries */
import '@testing-library/jest-dom'

import { HeaderProps } from '@gc/components'
import { mockPropsToLowerCase } from '@gc/shared/test'
import { render, screen } from '@gc/utils'

import { ReportTemplateHeader } from './ReportTemplateHeader'

// Mock the Header component from @gc/components
const mockHeader = jest.fn()
jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  Header: jest.fn((props: HeaderProps) => {
    mockHeader(props)
    return <div data-testid='header' {...mockPropsToLowerCase(props as unknown as Record<string, unknown>)} />
  })
}))

describe('ReportTemplateHeader', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  const mockHeaderProps: HeaderProps = {
    title: 'Test Report Title',
    secText1: 'Secondary text 1',
    secText2: 'Secondary text 2',
    buttonProps: [
      {
        label: 'Test Button',
        onClick: jest.fn()
      }
    ]
  }

  it('renders the Header component when headerProps is provided', () => {
    render(<ReportTemplateHeader headerProps={mockHeaderProps} />)

    const header = screen.getByTestId('header')
    expect(header).toBeInTheDocument()
    expect(header).toHaveAttribute('title', 'Test Report Title')
    expect(header).toHaveAttribute('sectext1', 'Secondary text 1')
    expect(header).toHaveAttribute('sectext2', 'Secondary text 2')
  })

  it('applies the correct CSS class to the wrapper div', () => {
    const { container } = render(<ReportTemplateHeader headerProps={mockHeaderProps} />)

    const headerWrapper = container.firstChild as HTMLElement
    expect(headerWrapper).toHaveClass('header')
  })

  it('passes all headerProps to the Header component', () => {
    const complexHeaderProps: HeaderProps = {
      title: 'Complex Report',
      secText1: 'Subtitle',
      buttonProps: [
        { label: 'Export', onClick: jest.fn() },
        { label: 'Share', onClick: jest.fn() }
      ],
      trailingIcon: <span data-testid='trailing-icon'>Icon</span>
    }

    render(<ReportTemplateHeader headerProps={complexHeaderProps} />)

    // Verify the mocked Header component was called with the correct props
    expect(mockHeader).toHaveBeenCalledWith(complexHeaderProps)
    // Note: In React StrictMode, components may render twice, so we just verify it was called at least once
    expect(mockHeader).toHaveBeenCalled()

    // Verify the header renders
    const header = screen.getByTestId('header')
    expect(header).toBeInTheDocument()
    expect(header).toHaveAttribute('title', 'Complex Report')
    expect(header).toHaveAttribute('sectext1', 'Subtitle')
  })

  it('returns null when headerProps is null', () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { container } = render(<ReportTemplateHeader headerProps={null as any} />)

    expect(container.firstChild).toBeNull()
  })

  it('returns null when headerProps is undefined', () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { container } = render(<ReportTemplateHeader headerProps={undefined as any} />)

    expect(container.firstChild).toBeNull()
  })

  it('renders with minimal headerProps (only title)', () => {
    const minimalProps: HeaderProps = {
      title: 'Minimal Title'
    }

    render(<ReportTemplateHeader headerProps={minimalProps} />)

    const header = screen.getByTestId('header')
    expect(header).toBeInTheDocument()
    expect(header).toHaveAttribute('title', 'Minimal Title')
  })
})
