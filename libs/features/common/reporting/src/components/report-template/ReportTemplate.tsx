/* eslint-disable @nx/enforce-module-boundaries */
import { GridList, HeaderType, MessageWithAction, Table } from '@gc/components'
import { GridListRefetch, useLoadingContingency, useMemoizedTranslation } from '@gc/hooks'
import { ChannelOrder } from '@gc/types'
import { useMemo } from 'react'

import styles from '../report-list/ReportsList.module.scss'

/**
 * Base interface for all report data items
 * Ensures common properties needed for report functionality
 */
export interface ReportDataItem {
  /** Unique identifier - can be id, code, or other identifying property */
  id?: string | number
  code?: string
  [key: string]: unknown
}

/**
 * Union type for all known report data types
 * Add new specific types here as they are implemented
 */
export type ReportRowData = ChannelOrder | ReportDataItem

export interface ReportTableColumnConfig<T> extends Omit<HeaderType<T>, 'editProps' | 'accessor'> {
  accessor: keyof T | ((data: T) => React.ReactNode)
}

export interface ReportTableTemplateProps<T> {
  data: T[]
  title: string
  columns: ReportTableColumnConfig<T>[]
  isError?: boolean
  isLoading?: boolean
  isFetching?: boolean
  isLoadingMoreData?: boolean
  fasteStoreKey?: string
  enableSearch?: boolean
  enablePagination?: boolean
  enableCsvDownload?: boolean
  refetch?: GridListRefetch<T>
  customSearchFn?: (item: T, searchStr: string) => boolean
}

export function ReportTemplate<T extends ReportRowData = ReportRowData>({
  title,
  columns,
  refetch,
  data = [],
  fasteStoreKey,
  customSearchFn,
  isError = false,
  isLoading = false,
  isFetching = false,
  isLoadingMoreData = false,
  enableSearch = true,
  enablePagination = true,
  enableCsvDownload = true
}: Readonly<ReportTableTemplateProps<T>>) {
  const t = useMemoizedTranslation()

  const displayLoading = isLoading && !isLoadingMoreData

  const contingency = useLoadingContingency<T>({
    data,
    isError,
    isFetching,
    isLoading: false,
    errorHeader: t('reports.error_msg_header.label'),
    errorDescription: t('reports.error_msg_description.label'),
    noDataHeader: t('reports.no_reports_message_header.label'),
    noDataDescription: t('reports.no_reports_message.description'),
    refetch: refetch as GridListRefetch<T>
  })

  const headers = useMemo(
    (): HeaderType<T>[] =>
      columns.map((col) => ({
        id: col.id,
        align: col.align,
        header: col.header,
        filterable: col.filterable,
        defaultSort: col.defaultSort,
        displayType: col.displayType,
        disableSortBy: col.disableSortBy,
        displayTemplate: col.displayTemplate,
        widthPercentage: col.widthPercentage,
        accessor: typeof col.accessor === 'function' ? col.accessor : (col.accessor as string)
      })),
    [columns]
  )

  const defaultSearchFn = useMemo(
    () =>
      customSearchFn ||
      ((item: T, searchStr: string) => {
        return Object.values(item).some((value) => String(value).toLowerCase().includes(searchStr.toLowerCase()))
      }),
    [customSearchFn]
  )

  return (
    <GridList contingency={contingency}>
      <Table<T>
        data={data}
        title={title}
        layout='flex'
        headers={headers}
        searchable={enableSearch}
        isSkeletonLoading={displayLoading}
        customSearchFn={defaultSearchFn}
        paginated={enablePagination}
        fasteStoreKey={fasteStoreKey}
        className={styles.reports_table}
        enableCsvDownload={enableCsvDownload}
        noContentMessage={
          <MessageWithAction
            messageHeader={t('common.no_results_message_header_label')}
            messageDescription={t('common.no_results_message_description')}
            iconProps={{
              icon: 'info',
              variant: 'filled-secondary',
              className: 'gc-icon-info'
            }}
          />
        }
      />
    </GridList>
  )
}
