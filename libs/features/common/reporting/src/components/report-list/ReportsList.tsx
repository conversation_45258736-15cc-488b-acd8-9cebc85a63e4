/* eslint-disable @nx/enforce-module-boundaries */
import { GridList, HeaderType, MessageWithAction, Table, TableMenu } from '@gc/components'
import { useLoadingContingency, useMemoizedTranslation, useSelectedAccount, useUpdateSortBy } from '@gc/hooks'
import { useGetUserReportsQuery } from '@gc/redux-store'
import { UserReport } from '@gc/types'
import { fasteRoute, getDateFromUTC, hitsOnData } from '@gc/utils'
import { capitalize } from 'es-toolkit/compat'
import { useMemo } from 'react'

import { usePerformanceMonitorWrapper } from '../../utils/performanceMonitor'
import styles from './ReportsList.module.scss'

function getReportActions(_data: UserReport) {
  return ['Duplicate', 'Edit', 'Delete'].map((action) => ({
    value: action,
    label: action,
    onClick: () => {
      // eslint-disable-next-line no-console
      console.log(action)
    }
  }))
}

const goToReportDetails = (reportId: string) => {
  fasteRoute(`/reporting/${reportId}`, { reportId })
}

export interface ReportsListProps {
  tableTitle: string
  fasteStoreKey: string
}

function useReportsData() {
  const { sapAccountId } = useSelectedAccount()

  const {
    data = [],
    isError,
    isFetching,
    isLoading,
    refetch: refetchReports
  } = useGetUserReportsQuery(sapAccountId, { pageSize: 50 }, { skip: !sapAccountId })

  const reports = useMemo(
    () =>
      data.map((report) => ({
        ...report,
        createdAt: getDateFromUTC(report.createdAt),
        lastModifiedAt: getDateFromUTC(report.lastModifiedAt)
      })),
    [data]
  )

  return { reports, isLoading, isFetching, isError, refetchReports }
}

export function ReportsList({ tableTitle, fasteStoreKey }: Readonly<ReportsListProps>) {
  const t = useMemoizedTranslation()

  const headers = useReportsHeaders()
  const { reports, isLoading, isFetching, isError, refetchReports } = useReportsData()

  const contingency = useLoadingContingency({
    isError,
    isLoading,
    isFetching,
    data: reports,
    errorHeader: t('reports.error_msg_header.label'),
    errorDescription: t('reports.error_msg_description.label'),
    noDataHeader: t('reports.no_reports_message_header.label'),
    noDataDescription: t('reports.no_reports_message.description'),
    refetch: refetchReports
  })

  const searchFun = (report: UserReport, searchStr: string) => {
    return hitsOnData(searchStr, [report.reportName, report.reportType, report.reportCategory, report.userId])
  }

  useUpdateSortBy({ fasteStoreKey })
  usePerformanceMonitorWrapper('ReportsList', { disabled: false })

  return (
    <GridList contingency={contingency}>
      <Table<UserReport>
        paginated
        searchable
        enableCsvDownload
        title={tableTitle}
        data={reports}
        headers={headers}
        className={styles.reports_table}
        customSearchFn={searchFun}
        fasteStoreKey={fasteStoreKey}
        noContentMessage={
          <MessageWithAction
            messageHeader={t('common.no_results_message_header_label')}
            messageDescription={t('common.no_results_message_description')}
            iconProps={{
              icon: 'info',
              variant: 'filled-secondary',
              className: 'gc-icon-info'
            }}
          />
        }
      />
    </GridList>
  )
}

function useReportsHeaders() {
  const t = useMemoizedTranslation()

  return useMemo(
    (): HeaderType<UserReport>[] => [
      // Report Name
      {
        id: 'reportName',
        header: t('reports.report_name.label'),
        accessor: 'reportName',
        displayType: 'link',
        defaultSort: 'desc',
        widthPercentage: 20,
        onLinkClick: (report: UserReport) => goToReportDetails(report.reportId)
      },
      // Report Category
      {
        id: 'reportCategoryDisplayName',
        header: t('reports.report_category.label'),
        accessor: 'reportCategoryDisplayName',
        align: 'center',
        displayTemplate: (displayName: string) => displayName
      },
      // Created By (User ID)
      {
        id: 'createdBy',
        header: t('common.created_by.label'),
        accessor: 'createdBy'
      },
      // Report Type
      {
        id: 'reportType',
        header: t('reports.report_type.label'),
        displayTemplate: (type: string) => capitalize(type),
        accessor: 'reportType',
        filterable: true,
        widthPercentage: 20
      },
      // Actions
      {
        header: t('common.actions.label'),
        accessor: (data: UserReport) => <TableMenu<UserReport> listItems={getReportActions(data)} currentRow={data} />,
        disableSortBy: true,
        excludeFromDownload: true,
        align: 'center'
      }
    ],
    [t]
  )
}
