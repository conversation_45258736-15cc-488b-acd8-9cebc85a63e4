.reports_table {
  :global(.lmnt-table__cell--nestable) {
    padding: 0px;
  }

  tr:has(button[id='arrow_up']) {
    background: #f5f5f5;
  }

  tr > :nth-child(1) {
    width: 56px;
    padding-right: 0px;
  }
}

// Loading spinner animation
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

:global(.spinner) {
  animation: spin 1s linear infinite;
}
