/* eslint-disable @nx/enforce-module-boundaries */
import { createEditModalNames, getModals, ModalContainer, ModalState } from '@gc/components'
import { resolutions } from '@gc/constants'
import { getModal } from '@gc/redux-store'
import React from 'react'
import isEqual from 'react-fast-compare'
import { useSelector } from 'react-redux'

import styles from './ReportingModalContainer.module.scss'

const modals: Record<string, ModalState> = {
  ...getModals(createEditModalNames)
}

export function ReportingModalContainer() {
  const modal = useSelector(getModal)

  const getModalSize = (ModalBody: ModalState['modalBody'], screenRes: number) => {
    if (screenRes <= resolutions.M719) {
      return 'fullscreen'
    }

    return screenRes >= resolutions.M1023 ? 'medium' : 'max'
  }

  if (!modal) return null

  return (
    <ModalContainer
      modals={modals}
      className={styles.create_report_modal}
      getModalSize={getModalSize}
      open={modal?.open ?? false}
      modalName={modal?.name}
      modalProps={modal?.props}
    />
  )
}

export default React.memo(ReportingModalContainer, isEqual)
