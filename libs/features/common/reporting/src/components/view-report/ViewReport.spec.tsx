/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable @typescript-eslint/no-explicit-any */
import '@testing-library/jest-dom'

import { ReportCategoryOptions, UserReport } from '@gc/types'
import { render, screen, waitFor } from '@gc/utils'

import { setUpStore } from '../../store'
import { ViewReport } from './ViewReport'

// Mock data
const mockUserReport: UserReport = {
  reportName: 'Test Report',
  reportCategory: ReportCategoryOptions.OrderByProduct,
  reportCategoryDisplayName: 'Orders by Product',
  reportConfigurationVersion: '1.0',
  reportId: 'test-report-id',
  reportType: 'live',
  version: '1.0.0',
  selectedFilters: [
    {
      filterType: 'enum',
      fieldId: 'orderCode',
      filterId: 'test-filter-id',
      selectedOptions: ['test-option']
    }
  ],
  selectedFields: [
    {
      fieldId: 'orderCode',
      accessor: 'code',
      displayName: 'Order Code',
      dataType: 'string',
      isFilterable: true,
      isRequired: false,
      isHidden: false
    },
    {
      fieldId: 'entry.quantity',
      accessor: 'entry.quantity',
      displayName: 'Quantity',
      dataType: 'number',
      isFilterable: true,
      isRequired: false,
      isHidden: false
    }
  ],
  userId: 'test-user',
  createdAt: '2023-01-01',
  lastModifiedAt: '2023-01-01'
}

// Mocks
jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  CenterLoader: () => <div data-testid='center-loader'>Loading...</div>,
  LoadingAndContingencySection: ({
    children,
    data,
    hasData,
    hasError,
    isLoading,
    errorConfig,
    noDataConfig,
    loadingConfig,
    onRetry
  }: any) => {
    if (isLoading) {
      return <div data-testid='loading-section'>{loadingConfig?.component}</div>
    }
    if (hasError) {
      // For this test suite, when hasError is true, we still want a deterministic DOM.
      // Render the same structure but also render children when data exists to not block findByTestId in other tests.
      return (
        <div data-testid='error-section'>
          <div data-testid='error-header'>{errorConfig?.header}</div>
          <div data-testid='error-description'>{errorConfig?.description}</div>
          <button data-testid='retry-button' onClick={onRetry}>
            {errorConfig?.buttonProps?.label}
          </button>
        </div>
      )
    }
    if (!hasData) {
      return (
        <div data-testid='no-data-section'>
          <div data-testid='no-data-header'>{noDataConfig?.header}</div>
          <div data-testid='no-data-description'>{noDataConfig?.description}</div>
        </div>
      )
    }
    return <div data-testid='content-section'>{data && children(data)}</div>
  }
}))

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useIsFirstRender: jest.fn(() => false),
  useMemoizedTranslation: jest.fn(
    () => (key: string, options?: { defaultValue?: string }) => options?.defaultValue || key
  ),
  useSelectedAccount: jest.fn(() => ({ sapAccountId: '**********' }))
}))

jest.mock('@gc/redux-store', () => ({
  ...jest.requireActual('@gc/redux-store'),
  useGetUserReportQuery: jest.fn()
}))

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: jest.fn(() => ({ reportId: 'test-report-id' }))
}))

jest.mock('../templates', () => ({
  ...jest.requireActual('../templates'),
  REPORT_TEMPLATES: {
    [require('@gc/types').ReportCategoryOptions.OrderByProduct]: (props: any) => {
      const React = require('react')
      const toKebab = (obj: any) =>
        Object.fromEntries(
          Object.entries(obj || {}).map(([k, v]) => [
            // eslint-disable-next-line sonarjs/no-nested-functions
            String(k).replace(/[A-Z]/g, (m: string) => `-${m.toLowerCase()}`),
            v
          ])
        )
      return React.createElement(
        'div',
        { 'data-testid': 'report-template-renderer', ...toKebab(props) },
        'Report Template Content'
      )
    }
  }
}))

// Override only the hooks from ViewReport we need to control in tests.
// Keep the real component so we exercise its logic.
jest.mock('./ViewReport', () => {
  const actual = jest.requireActual('./ViewReport')
  return {
    ...actual,
    useErrorConfig: jest.fn(() => ({
      header: 'Unable to Load Report',
      description: 'We encountered an issue loading this report. Please try again.',
      buttonProps: { label: 'Try Again' }
    })),
    useNoDataConfig: jest.fn(() => ({
      header: 'No Data Available',
      description: 'No data available for this report.'
    }))
  }
})

jest.mock('../../utils/performanceMonitor', () => ({
  usePerformanceMonitor: jest.fn(),
  logPerformanceSummary: jest.fn()
}))

// Get mocked functions
const { useGetUserReportQuery } = jest.mocked(require('@gc/redux-store'))
const { useIsFirstRender } = jest.mocked(require('@gc/hooks'))
const { logPerformanceSummary } = jest.mocked(require('../../utils/performanceMonitor'))

describe('ViewReport', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Default successful mocks
    useGetUserReportQuery.mockReturnValue({
      data: mockUserReport,
      isLoading: false,
      isFetching: false,
      isError: false,
      error: null,
      refetch: jest.fn()
    })
  })

  const renderViewReport = (isMobile = false) => {
    return render(<ViewReport />, {
      store: setUpStore(),
      width: isMobile ? 375 : undefined
    })
  }

  it('renders loading state when user report is loading', () => {
    useGetUserReportQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      isFetching: false,
      isError: false,
      error: null,
      refetch: jest.fn()
    })

    renderViewReport()

    expect(screen.getByTestId('loading-section')).toBeInTheDocument()
    expect(screen.getByTestId('center-loader')).toBeInTheDocument()
  })

  it('renders error state when user report query fails', () => {
    useGetUserReportQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      isFetching: false,
      isError: true,
      error: { message: 'Failed to fetch' },
      refetch: jest.fn()
    })

    renderViewReport()

    expect(screen.getByTestId('error-section')).toBeInTheDocument()
    expect(screen.getByTestId('error-header')).toHaveTextContent('Unable to Load Report')
    expect(screen.getByTestId('error-description')).toHaveTextContent(
      'We encountered an issue loading this report. Please try again.'
    )
    expect(screen.getByTestId('retry-button')).toHaveTextContent('Try Again')
  })

  it('renders report template renderer when data is available', async () => {
    renderViewReport()

    // content-section indicates the LoadingAndContingencySection rendered its children
    expect(screen.getByTestId('content-section')).toBeInTheDocument()

    // Ensure we are not in error/no-data path by shaping the mock accordingly (done in beforeEach)

    // Wait for the template to render
    const renderer = await screen.findByTestId('report-template-renderer', {}, { timeout: 3000 })
    expect(renderer).toBeInTheDocument()
    expect(screen.getByText('Report Template Content')).toBeInTheDocument()
  })

  it('passes correct props to CategoryTemplateRenderer', async () => {
    useGetUserReportQuery.mockReturnValue({
      data: mockUserReport,
      isLoading: false,
      isFetching: false,
      isError: false,
      error: null,
      refetch: jest.fn()
    })

    renderViewReport()

    const renderer = await screen.findByTestId('report-template-renderer', {}, { timeout: 3000 })

    await waitFor(() => {
      expect(renderer).toHaveAttribute('user-report')
    })
  })

  it('handles retry functionality', () => {
    const mockRefetch = jest.fn()

    // Make the hook return our mock refetch so the click assertion is meaningful.
    ;(useGetUserReportQuery as jest.Mock).mockReturnValue({
      data: mockUserReport,
      isLoading: false,
      isFetching: false,
      isError: true,
      error: { message: 'Failed to fetch' },
      refetch: mockRefetch
    })

    renderViewReport()

    const retryButton = screen.getByTestId('retry-button')
    retryButton.click()

    expect(mockRefetch).toHaveBeenCalledTimes(1)
  })

  it('skips user report query when sapAccountId or reportId is empty', () => {
    const { useSelectedAccount } = jest.mocked(require('@gc/hooks'))
    const { useParams } = jest.mocked(require('react-router-dom'))

    useSelectedAccount.mockReturnValue({ sapAccountId: '' })
    useParams.mockReturnValue({ reportId: '' })

    renderViewReport()

    expect(useGetUserReportQuery).toHaveBeenCalledWith('', '', { skip: true })
  })

  it('renders container with correct id and className', () => {
    const { container } = renderViewReport()

    const viewReportContainer = container.querySelector('#view-report-container')
    expect(viewReportContainer).toBeInTheDocument()
    expect(viewReportContainer).toHaveClass('container')
  })

  it('logs performance summary when not first render', async () => {
    useIsFirstRender.mockReturnValue(false)

    renderViewReport()

    await waitFor(() => {
      expect(logPerformanceSummary).toHaveBeenCalled()
    })
  })

  it('does not log performance summary on first render', () => {
    useIsFirstRender.mockReturnValue(true)

    render(<ViewReport />, { store: setUpStore() })

    expect(logPerformanceSummary).not.toHaveBeenCalled()
  })

  it('shows loading when report data is not available and no error', () => {
    // Simulate no data and not error but initial user report loading state
    // eslint-disable-next-line prettier/prettier
    (useGetUserReportQuery as jest.Mock).mockReturnValue({
      data: undefined,
      isLoading: true,
      isFetching: false,
      isError: false,
      error: null,
      refetch: jest.fn()
    })

    renderViewReport()

    expect(screen.getByTestId('loading-section')).toBeInTheDocument()
  })

  it('creates valid data object when user report exists', () => {
    renderViewReport()

    expect(screen.getByTestId('content-section')).toBeInTheDocument()
    // The content section being rendered indicates validatedData was created successfully
  })
})
