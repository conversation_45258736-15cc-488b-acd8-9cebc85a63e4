/* eslint-disable @nx/enforce-module-boundaries */
import { CenterLoader, LoadingAndContingencySection } from '@gc/components'
import { useIsFirstRender, useMemoizedTranslation, useSelectedAccount } from '@gc/hooks'
import { useGetUserReportQuery } from '@gc/redux-store'
import { ReportCategoryIds, UserReport } from '@gc/types'
import { isEmpty } from 'es-toolkit/compat'
import { ComponentType, Suspense, useEffect, useMemo } from 'react'
import { useParams } from 'react-router-dom'

import { logPerformanceSummary, usePerformanceMonitor } from '../../utils/performanceMonitor'
import { REPORT_TEMPLATES, ReportTemplateProps } from '../templates'
import styles from './ViewReport.module.scss'

/**
 * Hook to generate error configuration for LoadingAndContingencySection
 * @param t - Translation function
 * @returns Error configuration object
 */
export function useErrorConfig(t: (key: string, options?: { defaultValue?: string }) => string) {
  return useMemo(
    () => ({
      className: styles['error-container'],
      header: t('reports.error_loading_report.header', { defaultValue: 'Unable to Load Report' }),
      description: t('reports.error_loading_report.message', {
        defaultValue: 'We encountered an issue loading this report. Please try again.'
      }),
      iconProps: {
        icon: 'error',
        variant: 'filled-secondary',
        className: 'gc-icon-error'
      },
      buttonProps: {
        label: t('reports.error_loading_report.button', { defaultValue: 'Try Again' })
      }
    }),
    [t]
  )
}

/**
 * Hook to generate no data configuration for LoadingAndContingencySection
 * @param t - Translation function
 * @returns No data configuration object
 */
export function useNoDataConfig(t: (key: string, options?: { defaultValue?: string }) => string) {
  return useMemo(
    () => ({
      className: styles['no-data-container'],
      header: t('reports.no_data_available.header', { defaultValue: 'No Data Available' }),
      description: t('reports.no_data_available.message', {
        defaultValue: 'No data available for this report.'
      }),
      iconProps: {
        icon: 'info',
        variant: 'filled-secondary',
        className: 'gc-icon-info'
      }
    }),
    [t]
  )
}

/**
 * Type definition for validated view report data
 */
export type ViewReportData = {
  userReport: UserReport
}

export function ViewReport() {
  usePerformanceMonitor('ViewReport')

  const isFirstRender = useIsFirstRender()
  const t = useMemoizedTranslation()
  const { sapAccountId } = useSelectedAccount()
  const { reportId = '' } = useParams<{ reportId: string }>()

  // Fetch user report configuration
  const {
    data: userReport,
    isLoading: isUserReportLoading,
    isError: isUserReportError,
    error: _userReportError,
    refetch: refetchUserReport
  } = useGetUserReportQuery(sapAccountId, reportId, { skip: isEmpty(sapAccountId) || isEmpty(reportId) })

  const hasData = !!userReport
  const isError = isUserReportError
  const fasteStoreKey = userReport ? `report-${userReport.reportId}` : undefined

  // Only show full page loader when user report is loading, not when data is loading
  const isLoading = isUserReportLoading || (!hasData && !isError)

  // Prepare validated data for render prop - show UI as soon as we have user report config
  const validatedData: ViewReportData | undefined = useMemo(() => {
    if (userReport) {
      return { userReport }
    }
    return undefined
  }, [userReport])

  // Configuration objects from extracted utilities
  const errorConfig = useErrorConfig(t)
  const noDataConfig = useNoDataConfig(t)

  const ReportTemplateComponent = useMemo(() => {
    if (!userReport?.reportCategory) return undefined
    return REPORT_TEMPLATES[userReport.reportCategory as ReportCategoryIds] as ComponentType<ReportTemplateProps>
  }, [userReport?.reportCategory])

  useEffect(() => {
    if (isFirstRender) return
    logPerformanceSummary()
  })

  return (
    <Suspense fallback={<CenterLoader />}>
      <div id='view-report-container' className={styles.container}>
        <LoadingAndContingencySection<ViewReportData>
          data={validatedData}
          hasData={hasData}
          hasError={isError}
          isLoading={isLoading}
          onRetry={refetchUserReport}
          errorConfig={errorConfig}
          noDataConfig={noDataConfig}
          loadingConfig={{
            className: 'loading-container',
            component: <CenterLoader />
          }}
        >
          {({ userReport: validUserReport }) => {
            if (!ReportTemplateComponent) {
              return null
            }

            return <ReportTemplateComponent userReport={validUserReport} fasteStoreKey={fasteStoreKey} />
          }}
        </LoadingAndContingencySection>
      </div>
    </Suspense>
  )
}
