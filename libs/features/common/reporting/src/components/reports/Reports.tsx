/* eslint-disable @nx/enforce-module-boundaries */
import { Header } from '@gc/components'
import { useMemoizedTranslation, useModal, useSelectedAccount } from '@gc/hooks'
import { useGetUserReportsQuery } from '@gc/redux-store'
import { getFasteStoreKey } from '@gc/utils'
import { useCallback } from 'react'

import { SearchProvider } from '../../contexts/SearchContext'
import { ReportsList } from '../report-list/ReportsList'
import styles from './Reports.module.scss'

function ReportsContent() {
  const { openModal } = useModal()
  const t = useMemoizedTranslation()

  const { sapAccountId } = useSelectedAccount()
  const fasteStoreKey = getFasteStoreKey('reports', 'reports')

  const { refetch: refetchReports } = useGetUserReportsQuery(sapAccountId, { pageSize: 50 }, { skip: !sapAccountId })

  const handleCloseCreateReport = useCallback(() => {
    refetchReports()
  }, [refetchReports])

  const handleCreateReport = useCallback(() => {
    openModal({
      name: 'CREATE_REPORT',
      props: {
        onCloseTrigger: handleCloseCreateReport
      }
    })
  }, [handleCloseCreateReport, openModal])

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Header
          title={t('reports.label')}
          buttonProps={[
            {
              onClick: handleCreateReport,
              variant: 'filled',
              label: t('reports.create_report.label'),
              leadingIcon: 'add'
            }
          ]}
        />
      </div>

      <ReportsList fasteStoreKey={fasteStoreKey} tableTitle={t('reports.available_reports.label')} />
    </div>
  )
}

export function Reports() {
  return (
    <SearchProvider>
      <ReportsContent />
    </SearchProvider>
  )
}
