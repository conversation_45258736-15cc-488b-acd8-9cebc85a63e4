/* eslint-disable @typescript-eslint/no-explicit-any */
import '@testing-library/jest-dom'

import { mockPropsToLowerCase } from '@gc/shared/test'
import { render, screen } from '@gc/utils'

import { setUpStore } from '../../store'
import { Reports } from './Reports'

// Mocks
jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  Header: (props: any) => <div data-testid='header' {...mockPropsToLowerCase(props)} />,
  MessageWithAction: (props: any) => <div data-testid='message-with-action' {...mockPropsToLowerCase(props)} />
}))

// Mock the ReportsList component specifically
jest.mock('../report-list/ReportsList', () => ({
  ...jest.requireActual('../report-list/ReportsList'),
  ReportsList: (props: any) => <div data-testid='reports-list' {...mockPropsToLowerCase(props)} />
}))

// Mock the API query to return data instead of loading
jest.mock('@gc/redux-store', () => ({
  ...jest.requireActual('@gc/redux-store'),
  useGetUserReportsQuery: () => ({
    data: [
      {
        reportName: 'Test Report',
        reportTypeId: 'test-type',
        userId: 'test-user',
        createdAt: '2023-01-01',
        lastModifiedAt: '2023-01-01'
      }
    ],
    isLoading: false,
    isFetching: false,
    isError: false,
    refetch: jest.fn()
  }),
  useGetModal: () => ({ open: false }),
  usePreviousModals: () => []
}))

jest.mock('@gc/constants', () => ({
  ...jest.requireActual('@gc/constants'),
  IS_DESKTOP: 1024,
  IS_MOBILE: 1023
}))

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useMemoizedTranslation: () => (key: string) => key,
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  useModal: () => ({
    openModal: jest.fn(),
    closeModal: jest.fn()
  }),
  useSearch: () => [
    '', // searchTerm
    false, // openSearch
    {
      handleOpenSearch: jest.fn(),
      handleCloseSearch: jest.fn(),
      handleCancelSearch: jest.fn(),
      handleSearch: jest.fn()
    }
  ]
}))

jest.mock('@gc/utils', () => ({
  ...jest.requireActual('@gc/utils'),
  getFasteStoreKey: jest.fn(() => 'mocked-faste-store-key')
}))

describe('Reports', () => {
  beforeEach(() => {
    // Default to desktop
    global.innerWidth = 1200

    // Mock IntersectionObserver
    global.IntersectionObserver = jest.fn().mockImplementation(() => ({
      observe: jest.fn(),
      disconnect: jest.fn(),
      unobserve: jest.fn()
    }))
  })

  const renderReports = (isMobile = false) => render(<Reports />, { store: setUpStore(), width: isMobile ? 500 : 1200 })

  it('renders Header and ReportsList on desktop', () => {
    renderReports()

    expect(screen.getByTestId('header')).toBeInTheDocument()
    expect(screen.getByTestId('reports-list')).toBeInTheDocument()
    expect(screen.queryByTestId('message-with-action')).not.toBeInTheDocument()
  })

  it('passes correct props to Header', () => {
    renderReports()
    const header = screen.getByTestId('header')
    expect(header).toHaveAttribute('title', 'reports.label')
    expect(header).toHaveAttribute('buttonProps')
  })

  it('passes correct props to ReportsList', () => {
    renderReports()
    const reportsList = screen.getByTestId('reports-list')
    expect(reportsList).toHaveAttribute('fasteStoreKey', 'mocked-faste-store-key')
    expect(reportsList).toHaveAttribute('tableTitle', 'reports.available_reports.label')
  })
})
