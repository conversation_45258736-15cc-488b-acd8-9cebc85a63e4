import { ChannelOrder, SelectedReportField } from '@gc/types'
import { get, set } from 'lodash'

import { generateEntryId, hasEntryPrefix, removeEntryPrefix } from '../config/reportColumnConstants'

/**
 * Transform orders into a flat list of entries for ordersByProduct reports
 * Each entry includes order-level fields and entry-specific fields
 */
export function transformOrdersToEntries(
  orders: ChannelOrder[],
  selectedFields: SelectedReportField[]
): Record<string, unknown>[] {
  const entries: Record<string, unknown>[] = []

  const selectedEntryFields = selectedFields.filter((field) => hasEntryPrefix(field.fieldId))
  const selectedOrderFields = selectedFields.filter((field) => !hasEntryPrefix(field.fieldId))

  orders.forEach((order) => {
    // Create order fields object from selectedOrderFields
    const orderFields = selectedOrderFields.reduce(
      (acc, field) => {
        const accessor = field.accessor ?? field.fieldId
        set(acc, accessor, get(order, accessor))
        return acc
      },
      {} as Record<string, unknown>
    )

    // Transform each entry in the order
    order.entries?.forEach((entry) => {
      const entryFields = selectedEntryFields.reduce(
        (acc, field) => {
          const accessor = field.accessor ?? field.fieldId

          // Remove the 'entry.' prefix from the accessor if it exists
          const key = hasEntryPrefix(accessor) ? removeEntryPrefix(accessor) : accessor
          set(acc, key, get(entry, key))
          return acc
        },
        {} as Record<string, unknown>
      )

      // Combine order and entry fields
      entries.push({
        ...orderFields,
        entry: entryFields,
        // Use entry number as unique identifier for each seed product
        id: generateEntryId(order.code, entry.entryNumber)
      })
    })
  })

  return entries
}
