/* eslint-disable no-console */
import { useIsFirstRender } from '@gc/hooks'
import { useEffect, useRef } from 'react'

/**
 * Performance monitoring utilities for tracking component rerenders and performance
 * Only active in development environments
 */

interface PerformanceMetrics {
  componentName: string
  renderCount: number
  averageRenderTime: number
  lastRenderTime: number
}

interface PerformanceMonitorOptions {
  disabled?: boolean
  slowRenderThreshold?: number
  highRenderCountThreshold?: number
  /** Minimum time between warnings for the same component (default: 5000ms) */
  warningThrottleMs?: number
  /** Maximum warnings per minute per component (default: 6) */
  maxWarningsPerMinute?: number
}

interface WarningThrottleState {
  lastWarningTime: number
  warningCount: number
  pendingWarnings: Array<{ type: 'slow' | 'rerender'; message: string; timestamp: number }>
}

const performanceMetrics = new Map<string, PerformanceMetrics>()
const warningThrottleState = new Map<string, WarningThrottleState>()

// Check if we're in development environment
const isDevEnvironment = process.env.NODE_ENV === 'development'

// Default throttling configuration
const DEFAULT_WARNING_THROTTLE_MS = 5000 // 5 seconds between warnings per component
const DEFAULT_MAX_WARNINGS_PER_MINUTE = 6 // Maximum 6 warnings per minute per component

/**
 * Throttled warning system to prevent excessive console output
 */
function throttledWarning(
  componentName: string,
  type: 'slow' | 'rerender',
  message: string,
  options: PerformanceMonitorOptions
) {
  if (!isDevEnvironment) return

  const { warningThrottleMs = DEFAULT_WARNING_THROTTLE_MS, maxWarningsPerMinute = DEFAULT_MAX_WARNINGS_PER_MINUTE } =
    options

  const now = Date.now()
  const throttleKey = `${componentName}-${type}`

  let state = warningThrottleState.get(throttleKey)
  if (!state) {
    state = {
      lastWarningTime: 0,
      warningCount: 0,
      pendingWarnings: []
    }
    warningThrottleState.set(throttleKey, state)
  }

  // Reset warning count if more than a minute has passed
  if (now - state.lastWarningTime > 60000) {
    state.warningCount = 0
    state.pendingWarnings = []
  }

  // Check if we've exceeded the maximum warnings per minute
  if (state.warningCount >= maxWarningsPerMinute) {
    // Add to pending warnings but don't log immediately
    state.pendingWarnings.push({ type, message, timestamp: now })

    // Batch log pending warnings every throttle interval
    if (now - state.lastWarningTime > warningThrottleMs) {
      const pendingCount = state.pendingWarnings.length
      if (pendingCount > 0) {
        console.warn(
          `⚠️ Performance warnings throttled for ${componentName} (${pendingCount} warnings suppressed in the last minute)`
        )
        state.pendingWarnings = []
        state.lastWarningTime = now
      }
    }
    return
  }

  // Check if enough time has passed since the last warning
  if (now - state.lastWarningTime < warningThrottleMs) {
    // Add to pending warnings
    state.pendingWarnings.push({ type, message, timestamp: now })
    return
  }

  // Log the warning and any pending warnings
  console.warn(message)
  state.warningCount++
  state.lastWarningTime = now

  // Log any pending warnings if there are some
  if (state.pendingWarnings.length > 0) {
    const pendingOfSameType = state.pendingWarnings.filter((w) => w.type === type)
    if (pendingOfSameType.length > 0) {
      console.warn(`📝 ${pendingOfSameType.length} similar warnings occurred in the last ${warningThrottleMs}ms`)
    }
    state.pendingWarnings = state.pendingWarnings.filter((w) => w.type !== type)
  }
}

/**
 * Hook to monitor component render performance
 * @param componentName - Name of the component being monitored
 * @param options - Configuration options for the monitor
 * @returns Performance metrics for the component
 */
export function usePerformanceMonitor(componentName: string, options: PerformanceMonitorOptions = {}) {
  const { disabled = false, slowRenderThreshold = 16, highRenderCountThreshold = 20 } = options
  const renderCountRef = useRef(0)
  const renderStartTime = useRef(0)

  // Always initialize refs, but skip timing in non-dev environments
  const shouldMonitor = isDevEnvironment && !disabled

  if (shouldMonitor) {
    // Start timing before render
    renderStartTime.current = performance.now()
    renderCountRef.current += 1
  }

  useEffect(() => {
    if (!shouldMonitor) return

    // Calculate render time after render completes
    const renderTime = performance.now() - renderStartTime.current

    const existingMetrics = performanceMetrics.get(componentName)
    const newMetrics: PerformanceMetrics = {
      componentName,
      renderCount: renderCountRef.current,
      lastRenderTime: renderTime,
      averageRenderTime: existingMetrics
        ? (existingMetrics.averageRenderTime * (existingMetrics.renderCount - 1) + renderTime) /
          existingMetrics.renderCount
        : renderTime
    }

    performanceMetrics.set(componentName, newMetrics)

    // Log performance warnings for slow renders (throttled)
    if (renderTime > slowRenderThreshold) {
      throttledWarning(
        componentName,
        'slow',
        `🐌 Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`,
        options
      )
    }

    // Log excessive rerenders (throttled)
    if (renderCountRef.current > highRenderCountThreshold && renderCountRef.current % 10 === 0) {
      throttledWarning(
        componentName,
        'rerender',
        `🔄 High rerender count in ${componentName}: ${renderCountRef.current} renders`,
        options
      )
    }
  })

  return {
    renderCount: shouldMonitor ? renderCountRef.current : 0,
    getMetrics: () => (shouldMonitor ? performanceMetrics.get(componentName) : undefined)
  }
}

/**
 * Get all performance metrics for debugging
 * Only returns data in development environment
 */
export function getAllPerformanceMetrics() {
  if (!isDevEnvironment) {
    return []
  }
  return Array.from(performanceMetrics.values())
}

/**
 * Reset performance metrics (useful for testing)
 * Only works in development environment
 */
export function resetPerformanceMetrics() {
  if (!isDevEnvironment) {
    return
  }
  performanceMetrics.clear()
}

/**
 * Reset warning throttle state (useful for testing)
 * Only works in development environment
 */
export function resetWarningThrottleState() {
  if (!isDevEnvironment) {
    return
  }
  warningThrottleState.clear()
}

/**
 * Get current warning throttle statistics
 * Only returns data in development environment
 */
export function getWarningThrottleStats() {
  if (!isDevEnvironment) {
    return {}
  }

  const stats: Record<string, { warningCount: number; pendingCount: number; lastWarningTime: number }> = {}
  warningThrottleState.forEach((state, key) => {
    stats[key] = {
      warningCount: state.warningCount,
      pendingCount: state.pendingWarnings.length,
      lastWarningTime: state.lastWarningTime
    }
  })

  return stats
}

/**
 * Log performance summary to console
 * Only logs in development environment
 */
export function logPerformanceSummary() {
  if (!isDevEnvironment) {
    return
  }

  const metrics = getAllPerformanceMetrics()
  if (metrics.length === 0) {
    console.log('📊 No performance metrics collected')
    return
  }

  console.group('📊 Performance Summary')
  metrics.forEach((metric) => {
    console.log(`${metric.componentName}:`, {
      renders: metric.renderCount,
      avgTime: `${metric.averageRenderTime.toFixed(2)}ms`,
      lastTime: `${metric.lastRenderTime.toFixed(2)}ms`
    })
  })
  console.groupEnd()
}

export function usePerformanceMonitorWrapper(componentName: string, options: PerformanceMonitorOptions = {}) {
  const isFirstRender = useIsFirstRender()
  const shouldMonitor = isDevEnvironment && !options.disabled
  const { renderCount, getMetrics } = usePerformanceMonitor(componentName, options)

  useEffect(() => {
    if (!shouldMonitor || isFirstRender) return
    logPerformanceSummary()
  }, [shouldMonitor, isFirstRender, renderCount])

  return {
    renderCount,
    getMetrics
  }
}
