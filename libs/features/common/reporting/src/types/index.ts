/**
 * Centralized constants for report column special cases
 * This file contains all the special handling rules for different column types,
 * making it easy to maintain and extend column-specific behavior.
 */

// eslint-disable-next-line @nx/enforce-module-boundaries
import { HeaderType } from '@gc/components'

import { ReportRowData } from '../components/report-template/ReportTemplate'

export const ColumnAlignment = {
  LEFT: 'left',
  CENTER: 'center',
  RIGHT: 'right'
} as const
export type ColumnAlignment = (typeof ColumnAlignment)[keyof typeof ColumnAlignment]

export const DisplayTemplate = {
  CODE: 'code',
  BADGE: 'badge',
  CURRENCY: 'currency',
  DEFAULT: 'default'
} as const
export type DisplayTemplate =
  | (typeof DisplayTemplate)[keyof typeof DisplayTemplate]
  | HeaderType<ReportRowData>['displayTemplate']

export const DataType = {
  STRING: 'string',
  NUMBER: 'number',
  CURRENCY: 'currency',
  DATE: 'date',
  BOOLEAN: 'boolean'
} as const

export const ColumnIds = {
  CODE: 'code',
  STATUS: 'status',
  GROWER: 'grower',
  SALES_YEAR: 'salesYear',
  GROSS_PRICE: 'grossPrice',
  NET_PRICE: 'netPrice',
  TOTAL_DISCOUNT: 'totalDiscount'
}

export const EntryFieldIds = {
  PACKAGE_SIZE_CODE: 'entry.packageSizeCode'
}

export const ProductFieldIds = {
  PRODUCT_NAME: 'product.name'
}

export const FilterIds = {
  STATUS: 'statusCodes'
  // Future additions:
  // 'documentType': 'documentTypes',
} as const
