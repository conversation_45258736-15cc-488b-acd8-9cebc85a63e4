import { buildStore, mergeWithGlobalReducers } from '@gc/redux-store'
import { useDispatch } from 'react-redux'

const rootReducer = mergeWithGlobalReducers({})

export const setUpStore = (preloadedState?: RootState) =>
  buildStore(
    { preloadedState, reducer: rootReducer },
    {
      injectConsignmentsApi: true,
      injectConfigDataApi: true,
      useGlobalMiddleware: true,
      injectProductsApi: true,
      middlewareOpts: { serializableCheck: false }
    }
  )

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof rootReducer>
export type AppStore = ReturnType<typeof setUpStore>
export type AppDispatch = AppStore['dispatch']

export const store = setUpStore()
export const useAppDispatch: () => AppDispatch = useDispatch // Export a hook that can be reused to resolve types
