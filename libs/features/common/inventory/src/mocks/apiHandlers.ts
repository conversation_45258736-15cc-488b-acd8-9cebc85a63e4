/* eslint-disable @nx/enforce-module-boundaries */
import { consignmentTypes } from '@gc/constants'
import { configDataServices, consignmentServices } from '@gc/shared/test'
import _ from 'es-toolkit/compat'
import { http, HttpResponse } from 'msw'

import { seedConsignments, seedGrowthConsignments } from './shipments'
import { transfers } from './transfers'

const inventoryServices = [
  http.post(/\/consignments/, async ({ request }) => {
    const body = await request.clone().json()
    const consignmentType = _.get(body, 'consignmentType', [])

    if (_.isEqual(consignmentType, consignmentTypes.SEED_GROWTH)) {
      return HttpResponse.json(seedGrowthConsignments)
    }

    if (_.isEqual(consignmentType, consignmentTypes.TRANSFER)) {
      return HttpResponse.json(transfers)
    }

    return HttpResponse.json(seedConsignments)
  })
]

export const handlers = [...inventoryServices, ...configDataServices, ...consignmentServices]
