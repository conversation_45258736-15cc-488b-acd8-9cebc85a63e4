import { consignmentTypes } from '@gc/constants'
import { useLocale, useSelectedAccount } from '@gc/hooks'
import { Consignment, Locale } from '@gc/types'
import { getDateFromUTC, toDateObject } from '@gc/utils'
import { TFunction } from 'i18next'
import { useTranslation } from 'react-i18next'

import { useConsignmentTypeData, UseConsignmentTypeDataOptions } from './useConsignmentTypeData'

export type FormattedConsignment = Consignment & {
  _createdOnDateTime: Date
  formattedCreatedOnDate: string
}

export function transformTransferConsignments(
  consignments: Consignment[],
  sapAccountId: string,
  locale: Locale,
  t: TFunction
): FormattedConsignment[] {
  return consignments.map((transfer: Consignment) => ({
    ...transfer,
    transferType: sapAccountId === transfer.agent?.uid ? t('common.ship_to.label') : t('common.ship_from.label'),
    seedProName: sapAccountId === transfer.agent?.uid ? transfer.grower?.name : transfer.agent?.name,
    _createdOnDateTime: toDateObject(transfer.createdOnDateTime),
    formattedCreatedOnDate: getDateFromUTC(transfer.createdOnDateTime, locale)
  }))
}

export function useTransferListData({ updatePartialConsignments }: UseConsignmentTypeDataOptions<Consignment>) {
  const locale = useLocale()
  const { t } = useTranslation()
  const { sapAccountId } = useSelectedAccount()

  const {
    data,
    error,
    isFetching,
    isLoading,
    isSuccess,
    refetch: refetchTransferList
  } = useConsignmentTypeData<FormattedConsignment>(consignmentTypes.TRANSFER, {
    reqBody: { agents: [`${sapAccountId}`] },
    updatePartialConsignments,
    transformResponse: (consignments: Consignment[]) =>
      transformTransferConsignments(consignments, sapAccountId, locale, t)
  })

  const refetch = () => {
    if (isFetching || isLoading) return
    refetchTransferList()
  }

  return { data, error, isFetching, isLoading, isSuccess, refetch }
}
