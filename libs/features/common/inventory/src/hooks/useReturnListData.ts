import { consignmentTypes } from '@gc/constants'
import { useLocale, useSelectedAccount } from '@gc/hooks'
import { Consignment, Locale } from '@gc/types'
import { getDateFromUTC, toDateObject } from '@gc/utils'

import { useConsignmentTypeData, UseConsignmentTypeDataOptions } from './useConsignmentTypeData'

export type FormattedReturnConsignment = Consignment & {
  _plannedShipDate: Date
  formattedPlannedShipDate: string
  formattedShipFrom: string
}

export function transformReturnConsignments(consignments: Consignment[], locale: Locale) {
  return consignments.map((delivery: Consignment) => ({
    ...delivery,
    _plannedShipDate: toDateObject(delivery?.plannedShipDate),
    formattedPlannedShipDate: getDateFromUTC(delivery?.plannedShipDate, locale),
    formattedShipFrom: `${delivery?.fromLocation?.locationCode} - ${delivery?.fromLocation?.locationName}`
  }))
}

export function useReturnListData(
  consignmentType: keyof typeof consignmentTypes,
  { updatePartialConsignments }: UseConsignmentTypeDataOptions<Consignment>
) {
  const locale = useLocale()
  const { sapAccountId } = useSelectedAccount()

  const {
    data,
    error,
    isFetching,
    isLoading,
    isSuccess,
    refetch: refetchReturns
  } = useConsignmentTypeData<Consignment>(consignmentType, {
    reqBody: { agents: [`${sapAccountId}`] },
    updatePartialConsignments,
    transformResponse: (consignments: Consignment[]) => transformReturnConsignments(consignments, locale)
  })

  const refetch = () => {
    if (isFetching || isLoading) return
    refetchReturns()
  }

  return { data, error, isFetching, isLoading, isSuccess, refetch }
}
