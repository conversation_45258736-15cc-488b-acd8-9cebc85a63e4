import { consignmentTypes } from '@gc/constants'
import { useLocale, useMemoizedTranslation, usePortal, useSeedGrowthShipToPartner } from '@gc/hooks'
import { Consignment, FarmerShippingAddress, FormattedConsignment, Locale } from '@gc/types'
import { getDateFromUTC, toDateObject } from '@gc/utils'
import { isUndefined, uniqBy } from 'es-toolkit/compat'
import { TFunction } from 'i18next'
import { useCallback, useMemo } from 'react'

import { useConsignmentTypeData, UseConsignmentTypeDataOptions } from './useConsignmentTypeData'

// Memoized helper functions to prevent unnecessary re-computations
const getShipToPartyCityAndStateCallback = (
  shipToPartnerAddresses: FarmerShippingAddress[],
  shipToPartyId: string
): string => {
  const shipToPartyAddress = shipToPartnerAddresses.find(
    (address: FarmerShippingAddress) => address.sourceId === shipToPartyId
  )
  return shipToPartyAddress
    ? `${shipToPartyAddress?.cityTown?.toUpperCase()}, ${shipToPartyAddress?.stateProvinceCode?.toUpperCase()}`
    : ''
}

const getShipToPartyAddressCallback = (shipToPartnerAddresses: FarmerShippingAddress[], shipToPartyId: string) => {
  const shipToPartyAddress = shipToPartnerAddresses.find(
    (address: FarmerShippingAddress) => address.sourceId === shipToPartyId
  )
  return {
    locationName: shipToPartyAddress?.name ?? '',
    address: {
      line1: shipToPartyAddress?.address1Text ?? '',
      postalCode: shipToPartyAddress?.postalCode ?? '',
      town: shipToPartyAddress?.cityTown?.toUpperCase() ?? '',
      region: {
        isocodeShort: shipToPartyAddress?.stateProvinceCode?.toUpperCase() ?? ''
      }
    }
  }
}

// Keep original functions for backward compatibility
export const getShipToPartyCityAndState = getShipToPartyCityAndStateCallback
export const getShipToPartyAddress = getShipToPartyAddressCallback

export function transformSeedConsignments(
  consignments: Consignment[],
  t: TFunction<string>,
  locale: Locale,
  consignmentType: keyof typeof consignmentTypes = consignmentTypes.SHIPMENT
): FormattedConsignment[] {
  return consignments.map((consignment: Consignment) => {
    const locationCode = consignment.toLocation?.locationCode
    const locationName = consignment.toLocation?.locationName
    const formattedShipTo =
      !isUndefined(locationCode) && !isUndefined(locationName) ? `${locationCode} - ${locationName}` : ''
    const translatedLOB = consignment.lineOfBusiness
      ? t(`common.line_of_business.${consignment.lineOfBusiness?.toLocaleLowerCase()}.label`)
      : ''

    return {
      ...consignment,
      formattedShipTo,
      _plannedShipDate: toDateObject(consignment.plannedShipDate),
      formattedPlannedShipDate: getDateFromUTC(consignment.plannedShipDate, locale),
      _goodsIssuedDate: toDateObject(consignment?.goodsIssuedDate ?? ''),
      formattedGoodsIssuedDate: getDateFromUTC(consignment?.goodsIssuedDate ?? '', locale),
      translatedLOB,
      ...(consignmentType === consignmentTypes.SEED_GROWTH ? { lineOfBusiness: 'SEED_GROWTH' } : {})
    }
  })
}

export function useShipmentListData({
  shipmentOptions,
  seedGrowthOptions
}: {
  shipmentOptions?: UseConsignmentTypeDataOptions<FormattedConsignment>
  seedGrowthOptions?: UseConsignmentTypeDataOptions<FormattedConsignment>
}) {
  const locale = useLocale()
  const t = useMemoizedTranslation()
  const { isMyCropCentre } = usePortal()
  const shipToPartnerAddresses = useSeedGrowthShipToPartner()

  // Memoized transform functions to prevent recreation on every render
  const shipmentTransformResponse = useCallback(
    (consignments: Consignment[]) => transformSeedConsignments(consignments, t, locale),
    [t, locale]
  )

  const consignmentType = useMemo(
    () => (isMyCropCentre ? consignmentTypes.SHIPMENT : consignmentTypes.SEED_GROWTH),
    [isMyCropCentre]
  )

  const seedGrowthTransformResponse = useCallback(
    (consignments: Consignment[]) => transformSeedConsignments(consignments, t, locale, consignmentType),
    [t, locale, consignmentType]
  )

  //////////////////////////
  // SHIPMENT
  //////////////////////////
  const shipmentConsignments = useConsignmentTypeData(consignmentTypes.SHIPMENT, {
    ...shipmentOptions,
    transformResponse: shipmentTransformResponse
  })

  //////////////////////////
  // SEED GROWTH
  //////////////////////////
  const seedGrowthConsignments = useConsignmentTypeData(consignmentType, {
    ...seedGrowthOptions,
    transformResponse: seedGrowthTransformResponse
  })

  //////////////////////////
  // SEED GROWTH DATA TRANSFORMATION
  //////////////////////////
  const seedGrowthConsignmentsData = useMemo(() => {
    if (!shipToPartnerAddresses || !seedGrowthConsignments?.data) {
      return seedGrowthConsignments?.data
    }

    // Memoized transformation to prevent unnecessary re-computations
    return seedGrowthConsignments.data.map((consignment: FormattedConsignment) => ({
      ...consignment,
      formattedShipTo: consignment.shipToParty
        ? getShipToPartyCityAndStateCallback(shipToPartnerAddresses, consignment.shipToParty)
        : '',
      toLocation: getShipToPartyAddressCallback(shipToPartnerAddresses, consignment?.shipToParty ?? '')
    }))
  }, [shipToPartnerAddresses, seedGrowthConsignments?.data])

  //////////////////////////
  // MEMOIZED DATA
  //////////////////////////
  const memoizedData = useMemo(() => {
    const shipmentData = shipmentConsignments.data ?? []
    const seedGrowthData = seedGrowthConsignmentsData ?? []

    if (shipmentData.length === 0 && seedGrowthData.length === 0) {
      return []
    }

    return uniqBy([...shipmentData, ...seedGrowthData], 'code')
  }, [shipmentConsignments.data, seedGrowthConsignmentsData])

  const data = useMemo(
    () => (isMyCropCentre ? shipmentConsignments?.data : memoizedData),
    [isMyCropCentre, shipmentConsignments?.data, memoizedData]
  )

  ///////////////////////////////
  // STATUS CHECKS
  ///////////////////////////////
  const error = useMemo(
    () => shipmentConsignments.error ?? seedGrowthConsignments.error,
    [shipmentConsignments.error, seedGrowthConsignments.error]
  )

  const isError = useMemo(
    () => shipmentConsignments.isError || seedGrowthConsignments.isError,
    [shipmentConsignments.isError, seedGrowthConsignments.isError]
  )

  const isFetching = useMemo(
    () => shipmentConsignments.isFetching || seedGrowthConsignments.isFetching,
    [shipmentConsignments.isFetching, seedGrowthConsignments.isFetching]
  )

  const isLoading = useMemo(
    () => shipmentConsignments.isLoading || seedGrowthConsignments.isLoading,
    [shipmentConsignments.isLoading, seedGrowthConsignments.isLoading]
  )

  const isSuccess = useMemo(
    () => shipmentConsignments.isSuccess && seedGrowthConsignments.isSuccess,
    [shipmentConsignments.isSuccess, seedGrowthConsignments.isSuccess]
  )

  // Memoized refetch function to prevent recreation on every render
  const refetch = useCallback(() => {
    if (isFetching || isLoading) return

    const promises = [shipmentConsignments.refetch(), seedGrowthConsignments.refetch()]

    Promise.allSettled(promises).then((res) => {
      for (const r of res) {
        if (r.status === 'rejected') {
          console.error('error', r.reason)
        }
      }
    })
  }, [isFetching, isLoading, shipmentConsignments.refetch, seedGrowthConsignments.refetch])

  return {
    data,
    error,
    isFetching,
    isLoading,
    isSuccess,
    refetch,
    isError
  } as const
}
