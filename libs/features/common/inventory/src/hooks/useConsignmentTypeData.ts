import { consignmentTypes } from '@gc/constants'
import { useGcPortalConfig, useIsMobile, useSelectedAccount } from '@gc/hooks'
import { useConsignmentsQueries } from '@gc/redux-store'
import { GetAllConsignmentsOptions, GetAllConsignmentsReqBody } from '@gc/rtk-queries'
import { Consignment } from '@gc/types'
import { isEmpty } from 'lodash'
import { useMemo } from 'react'

export type UseConsignmentTypeDataOptions<ConsignmentType extends Consignment> = Omit<
  GetAllConsignmentsOptions<ConsignmentType>,
  'reqBody' | 'isMobile'
> & {
  reqBody?: Omit<GetAllConsignmentsReqBody, 'consignmentType'>
}

export function useConsignmentTypeData<ConsignmentType extends Consignment>(
  consignmentType: keyof typeof consignmentTypes,
  queryOptions?: UseConsignmentTypeDataOptions<ConsignmentType>
) {
  const gcPortalConfig = useGcPortalConfig()
  const sapAccountId = useSelectedAccount().sapAccountId

  const { useGetAllConsignmentsQuery } = useConsignmentsQueries<ConsignmentType>()

  const isMobile = useIsMobile()
  const { reqBody: finalReqBody = {}, ...options } = queryOptions ?? {}
  const { salesYear: salesYears, pageSize, documentTypes } = gcPortalConfig

  //

  const consignmentTypeKey: keyof typeof documentTypes = useMemo(() => {
    if (consignmentType === consignmentTypes.TRANSFER) return 'transfers'
    if (consignmentType === consignmentTypes.RETURN) return 'returns'
    if (consignmentType === consignmentTypes.AGENTRETURN) return 'agencyReturns'
    if (consignmentType === consignmentTypes.FARMERRETURN) return 'farmerReturns'
    return 'shipments'
  }, [consignmentType])

  const extraReqBody = useMemo(() => {
    return consignmentType !== consignmentTypes.SEED_GROWTH ? { salesYears } : {}
  }, [consignmentType, salesYears])

  return useGetAllConsignmentsQuery({
    isMobile,
    ...options,
    reqBody: {
      consignmentType,
      pageSize: pageSize[consignmentTypeKey] ?? 50,
      documentTypes: isEmpty(sapAccountId) ? [] : documentTypes[consignmentTypeKey],
      growers: isEmpty(sapAccountId) ? [] : [`${sapAccountId}`],
      ...(isEmpty(sapAccountId) ? {} : extraReqBody),
      ...finalReqBody
    }
  })
}
