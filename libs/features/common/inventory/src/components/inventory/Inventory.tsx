import { TabBar } from '@element/react-tabs'
import { Header, MessageWithAction, TopAppBar } from '@gc/components'
import { consignmentTypes, IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import {
  useEntitlement,
  useGcPortalConfig,
  useIsMobile,
  useMemoizedTranslation,
  usePortal,
  useSearch,
  useTabs
} from '@gc/hooks'
import { fasteRoute, getFasteStoreKey } from '@gc/utils'
import { memo, useEffect, useMemo } from 'react'
import MediaQuery from 'react-responsive'

import ReturnList from '../returns/return-list/ReturnList'
import SeedProductsList from '../seed-product/SeedProductsList'
import ShipmentList from '../shipments/shipment-list/ShipmentList'
import TransferList from '../transfers/transfer-list/TransferList'
import styles from './Inventory.module.scss'

const InventoryTabBar = memo(
  ({
    currentTab,
    handleTabActivated,
    ComponentTabs: InventoryTabs
  }: {
    currentTab: number
    handleTabActivated: (index: number) => void
    ComponentTabs: () => JSX.Element[] | undefined
  }) => {
    return (
      <TabBar
        clusterAlign='start'
        clustered={true}
        elevated={false}
        stacked={false}
        variant='surface'
        className={styles['tabs']}
        activeTabIndex={currentTab}
        onTabActivated={handleTabActivated}
      >
        <InventoryTabs />
      </TabBar>
    )
  },
  (prevProps, nextProps) => {
    return prevProps.currentTab === nextProps.currentTab
  }
)

export function Inventory() {
  const { isMyCropCentre } = usePortal()
  const isMobile = useIsMobile()
  const t = useMemoizedTranslation()
  const gcPortalConfig = useGcPortalConfig()
  const { hasInventoryReadAccess } = useEntitlement()
  const inventoryTabs: string[] = gcPortalConfig.inventoryTabs
  const { currentTab, handleTabActivated, ComponentTabs: InventoryTabs } = useTabs(inventoryTabs)

  useEffect(() => {
    if (isMobile && isMyCropCentre && currentTab === 0) {
      handleTabActivated(1) // Set the initial tab to 'Shipments' which is TAB 1
    }
  }, [isMobile, isMyCropCentre, currentTab, handleTabActivated])

  const storeKey = useMemo(
    () => (currentTab === 0 ? 'inventory' : inventoryTabs[currentTab]),
    [currentTab, inventoryTabs]
  )
  const fasteStoreKey = getFasteStoreKey('inventory', storeKey)
  const [searchTerm, openSearch, { handleOpenSearch, handleCancelSearch, handleCloseSearch, handleSearch }] =
    useSearch(fasteStoreKey)

  const InventoryTabList = useMemo(() => {
    switch (currentTab) {
      case 0:
        return hasInventoryReadAccess ? (
          <SeedProductsList
            searchTerm={searchTerm}
            tableTitle={t('inventory.seed_products.label')}
            fasteStoreKey={getFasteStoreKey('inventory', 'inventory')}
          />
        ) : (
          <MessageWithAction
            messageHeader={t('common.coming_soon.label')}
            className={styles.contingency_container}
            messageDescription={t('common.coming_soon.desc.label')}
            iconProps={{
              icon: 'info',
              variant: 'filled-secondary',
              className: 'gc-icon-info'
            }}
          />
        )
      case 1:
        return (
          <ShipmentList
            tableTitle={t('inventory.shipments.your_shipments.label')}
            farmerSapId={[]}
            searchTerm={searchTerm}
            fasteStoreKey={fasteStoreKey}
          />
        )
      case 2:
        return (
          <ReturnList
            consignmentType={consignmentTypes.RETURN}
            tableTitle={t('returns.your.returns.label')}
            searchTerm={searchTerm}
            fasteStoreKey={fasteStoreKey}
          />
        )
      case 3:
        return (
          <TransferList
            tableTitle={t('inventory.transfers.your_transfers.label')}
            searchTerm={searchTerm}
            fasteStoreKey={fasteStoreKey}
          />
        )
      default:
        return null
    }
  }, [currentTab, fasteStoreKey, hasInventoryReadAccess, searchTerm, t])

  useEffect(() => {
    if (isMobile) return

    switch (currentTab) {
      case 1:
        fasteRoute('/inventory/shipments')
        break
      case 2:
        fasteRoute('/inventory/returns')
        break
      case 3:
        fasteRoute('/inventory/transfers')
        break
      default:
        break
    }
  }, [currentTab, isMobile])

  return (
    <div className={styles.container}>
      <MediaQuery minWidth={IS_DESKTOP}>
        <div className={styles.header}>
          <Header title='Overview' />
        </div>
      </MediaQuery>

      <MediaQuery maxWidth={IS_MOBILE}>
        <TopAppBar
          title={t('inventory.inventory.label')}
          leadingContent={openSearch || searchTerm !== '' ? 'search' : 'title'}
          trailingIconButtonProps={[{ icon: 'search', onClick: handleOpenSearch }]}
          searchProps={{
            searchTerm,
            onChange: handleSearch,
            onClear: handleCancelSearch,
            closeSearchButtonProps: { onClick: handleCloseSearch }
          }}
        />
        <InventoryTabBar
          currentTab={currentTab}
          handleTabActivated={handleTabActivated}
          ComponentTabs={InventoryTabs}
        />
      </MediaQuery>

      <div>{InventoryTabList}</div>
    </div>
  )
}

export default Inventory
