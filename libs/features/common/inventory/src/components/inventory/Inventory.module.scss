.grid {
  padding: 0px !important;
}

.container {
  @media (min-width: 1024px) {
    padding: 48px 80px;
    position: relative;
  }
  @media (max-width: 719px) {
    margin-bottom: 24px;
  }
}

.tabs {
  @media (min-width: 1024px) {
    padding-left: 80px;
  }
}

.header {
  @media (min-width: 1024px) {
    margin-bottom: 48px;
  }
}
.top_bar_select_account {
  @media (max-width: 719px) {
    position: relative !important;
    margin-top: -30px !important;
    width: 89% !important;
    margin-bottom: 10px !important;
  }
}

.aurora_top_bar {
  @media (max-width: 719px) {
    position: relative !important;
    margin-top: -25px !important;
    width: 89% !important;
    z-index: unset !important;
  }
}
@media (min-width: 720px) {
  :global(.lmnt-table__cell-content) {
    min-height: 48px;
    height: auto !important;
  }
}

.contingency_container {
  margin-top: calc(100vh / 4);
  position: relative;
  @media (max-width: 719px) {
    width: 90%;
    padding: 0 16px;
  }
}
