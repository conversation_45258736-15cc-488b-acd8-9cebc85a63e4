import { getModals, LocationPickerModal, ModalContainer, ModalState, selectAccountModalNames } from '@gc/components'
import { resolutions } from '@gc/constants'
import { useScreenRes } from '@gc/hooks'
import { getModal } from '@gc/redux-store'
import classNames from 'classnames'
import SelectAccountModal from 'libs/shared/components/src/features/modals/select-account/SelectAccountModal'
import React from 'react'
import isEqual from 'react-fast-compare'
import { useSelector } from 'react-redux'

import styles from './InventoryModalContainer.module.scss'

const modals: Record<string, ModalState> = {
  ...getModals(selectAccountModalNames),
  SELECT_ACCOUNT: { modalBody: SelectAccountModal },
  SELECT_LOCATION: { modalBody: LocationPickerModal }
}

export function InventoryModalContainer() {
  const modal = useSelector(getModal)
  const screenRes = useScreenRes()
  const getModalSize = () => {
    if (screenRes <= resolutions.M719) {
      return 'fullscreen'
    }
    return 'medium'
  }
  const isLocationPickerModal = modal?.name === 'SELECT_LOCATION'
  if (!modal) return null

  return (
    <ModalContainer
      modals={modals}
      className={classNames(styles.inventory_modal, { [styles.location_picker_modal]: isLocationPickerModal })}
      getModalSize={getModalSize}
      open={modal?.open ?? false}
      modalName={modal?.name}
      modalProps={modal?.props}
    />
  )
}

export default React.memo(InventoryModalContainer, isEqual)
