import { setupStore } from '@gc/features-common-farmers'
import * as hooks from '@gc/hooks'
import { channelArrowPortalConfig as mockPortalConfig } from '@gc/shared/test'
import { fasteRoute, render, screen } from '@gc/utils'
import { Provider } from 'react-redux'

import Inventory from './Inventory'

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  usePortalConfig: () => mockPortalConfig,
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig,
  useEntitlement: () => ({ hasInventoryReadAccess: true })
}))

jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  Header: () => <div data-testid='Header'>Header</div>,
  TopAppBar: ({ title }: { title: string }) => <div data-testid='TopAppBar'>{title}</div>
}))

jest.mock('@gc/utils', () => ({
  ...jest.requireActual('@gc/utils'),
  fasteRoute: jest.fn(),
  getFasteStoreKey: (section: string, key: string) => `${section}-${key}`
}))

// Mock other components
jest.mock('../returns/return-list/ReturnList', () => () => <div data-testid='ReturnList'>ReturnList</div>)
jest.mock('../shipments/shipment-list/ShipmentList', () => () => <div data-testid='ShipmentList'>ShipmentList</div>)
jest.mock('../transfers/transfer-list/TransferList', () => () => <div data-testid='TransferList'>TransferList</div>)
jest.mock('../seed-product/SeedProductsList', () => () => <div data-testid='SeedProductsList'>SeedProductsList</div>)

describe('Inventory Component', () => {
  const store = setupStore()

  beforeEach(() => {
    jest.clearAllMocks()
    jest.restoreAllMocks()
  })

  const renderWithProvider = (ui: React.ReactElement) => {
    return render(<Provider store={store}>{ui}</Provider>)
  }

  it('renders without crashing', () => {
    const { baseElement } = renderWithProvider(<Inventory />)
    expect(baseElement).toBeTruthy()
  })

  it('matches snapshots', () => {
    const useTabsSpy = jest.spyOn(hooks, 'useTabs')
    const base = { ComponentTabs: () => [], setCurrentTab: jest.fn(), handleTabActivated: jest.fn(), previousTab: 0 }
    useTabsSpy.mockReturnValue({
      ...base,
      currentTab: 0,
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      resetCurrentTab: () => {}
    })

    const { container, rerender } = renderWithProvider(<Inventory />)
    expect(container).toMatchSnapshot()

    useTabsSpy.mockReturnValue({
      ...base,
      currentTab: 1,
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      resetCurrentTab: () => {}
    })
    rerender(
      <Provider store={store}>
        <Inventory />
      </Provider>
    )
    expect(container).toMatchSnapshot()

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    useTabsSpy.mockReturnValue({ ...base, currentTab: 2, resetCurrentTab: () => {} })
    rerender(
      <Provider store={store}>
        <Inventory />
      </Provider>
    )
    expect(container).toMatchSnapshot()

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    useTabsSpy.mockReturnValue({ ...base, currentTab: 3, resetCurrentTab: () => {} })
    rerender(
      <Provider store={store}>
        <Inventory />
      </Provider>
    )
    expect(container).toMatchSnapshot()
  })

  it('calls fasteRoute with correct path when non-mobile and currentTab is 1', () => {
    window.history.pushState({ tab: 1 }, '')

    renderWithProvider(<Inventory />)
    expect(fasteRoute).toHaveBeenCalledWith('/inventory/shipments')
  })

  it('calls fasteRoute with correct path when non-mobile and currentTab is 2', () => {
    window.history.pushState({ tab: 2 }, '')

    renderWithProvider(<Inventory />)
    expect(fasteRoute).toHaveBeenCalledWith('/inventory/returns')
  })

  it('calls fasteRoute with correct path when non-mobile and currentTab is 3', () => {
    window.history.pushState({ tab: 3 }, '')

    renderWithProvider(<Inventory />)
    expect(fasteRoute).toHaveBeenCalledWith('/inventory/transfers')
  })

  it('does not call fasteRoute when on mobile, even if currentTab is not 0', () => {
    window.history.pushState({ tab: 1 }, '')

    jest.spyOn(hooks, 'useIsMobile').mockReturnValue(true)
    renderWithProvider(<Inventory />)
    expect(fasteRoute).not.toHaveBeenCalled()
  })

  it('renders correct component based on the active tab', () => {
    const useTabsSpy = jest.spyOn(hooks, 'useTabs')
    const base = { ComponentTabs: () => [], setCurrentTab: jest.fn(), handleTabActivated: jest.fn(), previousTab: 0 }
    useTabsSpy.mockReturnValue({
      ...base,
      currentTab: 0,
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      resetCurrentTab: () => {}
    })

    const { rerender } = renderWithProvider(<Inventory />)

    // Default is tab 0 -> SeedProductsList should render
    expect(screen.getByTestId('SeedProductsList')).toBeTruthy()

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    useTabsSpy.mockReturnValue({ ...base, currentTab: 1, resetCurrentTab: () => {} })
    rerender(
      <Provider store={store}>
        <Inventory />
      </Provider>
    )
    expect(screen.getByTestId('ShipmentList')).toBeTruthy()

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    useTabsSpy.mockReturnValue({ ...base, currentTab: 2, resetCurrentTab: () => {} })
    rerender(
      <Provider store={store}>
        <Inventory />
      </Provider>
    )
    expect(screen.getByTestId('ReturnList')).toBeTruthy()

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    useTabsSpy.mockReturnValue({ ...base, currentTab: 3, resetCurrentTab: () => {} })
    rerender(
      <Provider store={store}>
        <Inventory />
      </Provider>
    )
    expect(screen.getByTestId('TransferList')).toBeInTheDocument()
  })

  it('calls fasteRoute with correct path when non-mobile and currentTab is 1', () => {
    const { rerender } = renderWithProvider(<Inventory />)
    window.history.pushState({ tab: 1 }, '')

    rerender(
      <Provider store={store}>
        <Inventory />
      </Provider>
    )
    expect(fasteRoute).toHaveBeenCalledWith('/inventory/shipments')
  })

  it('calls fasteRoute with correct path when non-mobile and currentTab is 2', () => {
    window.history.pushState({ tab: 2 }, '')
    const { rerender } = renderWithProvider(<Inventory />)
    rerender(
      <Provider store={store}>
        <Inventory />
      </Provider>
    )
    expect(fasteRoute).toHaveBeenCalledWith('/inventory/returns')
  })

  it('calls fasteRoute with correct path when non-mobile and currentTab is 3', () => {
    window.history.pushState({ tab: 3 }, '')
    const { rerender } = renderWithProvider(<Inventory />)
    rerender(
      <Provider store={store}>
        <Inventory />
      </Provider>
    )
    expect(fasteRoute).toHaveBeenCalledWith('/inventory/transfers')
  })

  it('does not call fasteRoute when on mobile, even if currentTab is not 0', () => {
    window.history.pushState({ tab: 1 }, '')

    jest.spyOn(hooks, 'useIsMobile').mockReturnValue(true)
    renderWithProvider(<Inventory />) // Use renderWithProvider to wrap with Provider
    expect(fasteRoute).not.toHaveBeenCalled()
  })
})
