import { Header } from '@gc/components'
import { IS_DESKTOP } from '@gc/constants'
import { useIsMobile } from '@gc/hooks'
import { fasteRoute, getFasteStoreKey } from '@gc/utils'
import { useEffect } from 'react'
import MediaQuery from 'react-responsive'

import styles from './StockTake.module.scss'
import StockTakeProductList from './StockTakeProductList'

export function StockTake() {
  const isMobile = useIsMobile()
  const fasteStoreKey = getFasteStoreKey('quotes', 'select-products')

  useEffect(() => {
    if (isMobile) {
      fasteRoute('/inventory')
    }
  }, [isMobile])
  return (
    <div className={styles['container']}>
      <MediaQuery minWidth={IS_DESKTOP}>
        <div className={styles.header}>
          <Header title='Stock Take' />
        </div>
        <StockTakeProductList fasteStoreKey={fasteStoreKey} tableTitle='Products' searchTerm='' />
      </MediaQuery>
    </div>
  )
}

export default StockTake
