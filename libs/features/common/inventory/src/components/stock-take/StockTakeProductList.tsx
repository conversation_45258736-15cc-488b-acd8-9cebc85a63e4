import { List, LoadingAndContingencySection, MessageWithAction, Table } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import {
  useCreateStockTakeOrder,
  useGcPortalConfig,
  useIsMobile,
  useMemoizedTranslation,
  usePortalConfig,
  useUpdateCartCache,
  useUpdateSortBy
} from '@gc/hooks'
import { getProductFilters, setContingency, useGlobalDispatch, useProductsQueries } from '@gc/redux-store'
import { Cart, DomainDefGcPortalConfig, Entry, Product, ProductWithPrice } from '@gc/types'
import { getMatchingCartEntryIndex, getMatchingCartEntryUsingProduct, hitsOnData } from '@gc/utils'
import { isUndefined } from 'es-toolkit'
import _ from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import MediaQuery from 'react-responsive'

import { useStockTakeHeader } from '../header/useStockTakeHeader'
import styles from './StockTakeProductList.module.scss'

type ProductRow = Product & { quantity: number | undefined }

export const getSortingList = (t: (key: string) => string) => {
  return [
    { label: t('common.availability_high_low.label'), columnName: 'available', sortingType: 'desc' },
    { label: t('common.availability_low_high.label'), columnName: 'available', sortingType: 'asc' },
    { label: t('products.name_a-z.label'), columnName: 'name', sortingType: 'asc' },
    { label: t('products.name_z-a.label'), columnName: 'name', sortingType: 'desc' }
  ]
}

export interface StockStakeProps {
  tableTitle: string
  searchTerm?: string
  fasteStoreKey: string
}

export function StockTakeProductList({ fasteStoreKey, tableTitle, searchTerm = '' }: Readonly<StockStakeProps>) {
  const isMobile = useIsMobile()
  const dispatch = useGlobalDispatch()
  const t = useMemoizedTranslation()
  const portalConfig = usePortalConfig()
  const gcPortalConfig = useGcPortalConfig()
  const { useGetProductListQuery } = useProductsQueries()
  const filterList = useSelector(getProductFilters)
  const [updateCartCache] = useUpdateCartCache()
  const [isCreateCart, setIsCreateCart] = useState(true)
  const { data: cart } = useCreateStockTakeOrder({ isCreateCart: isCreateCart, setIsCreateCart: setIsCreateCart })
  const cropList: DomainDefGcPortalConfig['cropList'] = gcPortalConfig?.cropList

  const [products, setProducts] = useState<Product[]>([])

  const productQueryFilters = useMemo<{ [key: string]: string[] }>(() => {
    if (filterList.length === 0) return {}

    return filterList.reduce((acc: { [key: string]: string[] }, filter) => {
      if (filter.filterType !== 'switch' && filter.selectedOptions.length > 0) {
        acc[filter.category] = filter.selectedOptions.map((option) => option?.value ?? '')
      }
      return acc
    }, {})
  }, [filterList])

  const searchQuery = useMemo(() => {
    return {
      query: { filters: productQueryFilters, cartId: cart?.code },
      pageSize: portalConfig?.gcPortalConfig.productsPageSize,
      isMobile: isMobile
    }
  }, [productQueryFilters, cart?.code, portalConfig?.gcPortalConfig.productsPageSize, isMobile])

  const {
    data: result,
    error,
    isLoading,
    isFetching,
    refetch: refetchProduct
  } = useGetProductListQuery(searchQuery, { skip: !cart?.code })

  const hasError = !isUndefined(error)

  const isDataLoading = useMemo(() => {
    return products.length === 0 && (isFetching || isLoading)
  }, [isFetching, isLoading, products.length])

  const hasNoData = useMemo(() => {
    return products.length === 0 && searchTerm.trim().length === 0
  }, [products, searchTerm])

  useEffect(() => {
    if (!isLoading && result) {
      setProducts(result.products)
    }
  }, [hasError, isLoading, result])

  const getUpdatedEntry = (product: Product, qty: number | undefined) => {
    const quantity = qty && Number(qty)
    return {
      cropCode: cropList.find((crop) => crop.cropName === product.crop)?.cropCode || '',
      cropName: product.crop,
      product: { code: product.code, name: product.name },
      quantity,
      totalPricePerUnit: { ...product.price, formattedValue: '', priceType: '' }
    }
  }

  const updateQuantity = (quantity: string, product: ProductRow, onlyCache = true) => {
    const updateEntries = (entries: Entry[]) => {
      const matchingEntry = getMatchingCartEntryUsingProduct(entries, product as unknown as ProductWithPrice, true)
      const entry = getUpdatedEntry(product, Number(quantity))
      if (!matchingEntry && Number(quantity) === 0) {
        return undefined
      }
      if (!matchingEntry) {
        entries.push(entry)
      } else {
        matchingEntry.quantity = entry.quantity
      }
      return { ...entry, entryNumber: matchingEntry?.entryNumber }
    }

    dispatch(setContingency())
    updateCartCache((draft: Cart) => {
      draft.draftEntries = draft.draftEntries ?? []
      const entry = updateEntries(draft.draftEntries)

      // onBlur handle
      if (!onlyCache && entry) {
        if (entry.quantity === 0) {
          updateCartCache((draft: Cart) => {
            const matchingIndex = getMatchingCartEntryIndex(draft.entries, entry, true)
            if (matchingIndex !== -1) {
              const { entryNumber, quantity } = draft.entries[matchingIndex]
              if (!_.isNumber(entryNumber) || (_.isNumber(quantity) && quantity === 0)) {
                const entries =
                  draft.entries.length > 1
                    ? draft.entries.map((e, index) => {
                        if (index > matchingIndex) {
                          e.entryNumber = e.entryNumber ? e.entryNumber - 1 : e.entryNumber
                        }
                        return e
                      })
                    : [...draft.entries]
                entries.splice(matchingIndex, 1)
                draft.entries = entries
              }
            }
            return draft
          })
        }
      }
      return draft
    })
  }

  const headerData = useStockTakeHeader({ t, updateQuantity })

  const getTableData = useCallback(() => {
    const entries = cart?.draftEntries
    const data = (products ?? [])?.map((product: Product) => {
      const matchingCartEntry = getMatchingCartEntryUsingProduct(
        entries,
        product as unknown as ProductWithPrice,
        true
      ) as Entry
      return {
        ...product,
        quantity: matchingCartEntry?.quantity || 0
      }
    })
    return data
  }, [cart?.draftEntries, products])

  useUpdateSortBy({ fasteStoreKey, sortBy: [{ id: 'name', desc: true, order: 1 }] })

  const searchFun = useCallback((product: Product, searchStr: string) => {
    const res = hitsOnData(searchStr, [product.name, product.code])
    return res
  }, [])

  return (
    <div className={styles.container}>
      <LoadingAndContingencySection
        errorDescription={t('product.api_error_description_msg')}
        errorHeader={t('product.stock_take.product_api_error_header_msg')}
        loadingMessage={t('products.loading_products_message.label')}
        noDataDescription={t('product.stock_take.no_data_description.msg')}
        noDataHeader={t('product.stock_take.no_data_header.msg')}
        hasError={hasError}
        isLoading={isDataLoading}
        hasData={!hasNoData}
        onRetry={refetchProduct}
      >
        <>
          <MediaQuery minWidth={IS_DESKTOP}>
            <Table<ProductRow>
              title={tableTitle}
              layout={'standard'}
              className={`${styles.table}`}
              paginated
              searchable
              editable
              data={getTableData()}
              headers={headerData}
              customSearchFn={searchFun}
              fasteStoreKey={fasteStoreKey}
              noContentMessage={
                <MessageWithAction
                  messageHeader={t('common.no_results_message_header_label')}
                  messageDescription={t('common.no_results_message_description')}
                  iconProps={{
                    icon: 'info',
                    variant: 'filled-secondary',
                    className: 'gc-icon-info'
                  }}
                />
              }
            />
          </MediaQuery>
          <MediaQuery maxWidth={IS_MOBILE}>
            <div className={styles['order-list-mobile']}>
              {products.length > 0 ? (
                <List<ProductRow>
                  divider={true}
                  data={getTableData()}
                  searchTerm={searchTerm}
                  searchFn={searchFun}
                  fasteStoreKey={fasteStoreKey}
                />
              ) : (
                <MessageWithAction
                  messageHeader={t('common.no_results_message_header_label')}
                  messageDescription={t('common.no_results_message_description')}
                  iconProps={{
                    icon: 'info',
                    variant: 'filled-secondary',
                    className: 'gc-icon-info'
                  }}
                />
              )}
            </div>
          </MediaQuery>
        </>
      </LoadingAndContingencySection>
    </div>
  )
}

export default StockTakeProductList
