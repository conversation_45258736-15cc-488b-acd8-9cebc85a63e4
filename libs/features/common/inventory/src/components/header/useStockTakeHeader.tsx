// eslint-disable-next-line @nx/enforce-module-boundaries
import { Badge, HeaderType } from '@gc/components'
import { Product } from '@gc/types'
import { TFunction } from 'i18next'

type ProductRow = Product & { quantity: number | undefined }

interface UseHeaderDataProps {
  t: TFunction<'translation', undefined>
  updateQuantity: (quantity: string, product: ProductRow, onlyCache: boolean) => void
}

export function useStockTakeHeader({ t }: UseHeaderDataProps) {
  const auStockTakeHeader: HeaderType<ProductRow>[] = [
    {
      header: t('product.material_number.label'),
      accessor: 'code',
      id: 'code',
      searchable: true,
      displayTemplate: (_value, product: ProductRow) => {
        const productCode = product?.code ? product?.code.slice(-8) : ''
        return <div>{productCode}</div>
      },
      widthPercentage: 8,
      align: 'start'
    },
    {
      header: t('common.product_name.label'),
      accessor: 'name',
      id: 'name',
      searchable: true,
      defaultSort: 'desc',
      defaultSortOrder: 1,
      sortType: (x: any, y: any) => {
        const a: string = x.original.name
        const b: string = y.original.name
        return a.localeCompare(b)
      },
      displayTemplate: (_value: string, product: ProductRow) => {
        const onExclusion = product?.canView === true && product?.canOrder === false
        return (
          <>
            {product.name}&nbsp;
            {!!product.quantity && product.quantity > 0 && <Badge labelText={t('common.added.label')} />}&nbsp;
            {onExclusion && <Badge themeColor='danger' labelText={t('common.on_exclusion.label')} />}
          </>
        )
      },
      filterable: true
    },
    {
      header: t('product.qty_available.label'),
      accessor: 'available',
      id: 'available',
      defaultSort: 'desc',
      defaultSortOrder: 1,
      sortType: (x: any, y: any) => {
        const a: string = x.original.name
        const b: string = y.original.name
        return a.localeCompare(b)
      },
      displayTemplate: (_value: string, product: ProductRow) => _value,
      filterable: true
    },
    {
      header: t('product.qty_reported.label'),
      accessor: 'availableQty',
      id: 'availableQty',
      editProps: {
        editType: 'textfield' as const,
        textfieldProps: {
          type: 'number',
          isWholeNumber: true,
          placeholder: '0',
          onChange: () => {
            // Intentionally left as a no-op. This can be implemented if future functionality requires handling changes.
            return
          }
        }
      },
      widthPercentage: 8.5,
      align: 'end'
    }
  ]

  return auStockTakeHeader
}
