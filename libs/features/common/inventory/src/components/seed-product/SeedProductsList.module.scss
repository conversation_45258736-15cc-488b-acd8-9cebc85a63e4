.seed_products_table {
  tr:has(i[id='arrow_down']) {
    background: #f5f5f5;
  }

  tr:has(div[id='orders_expansion_panel']) {
    border-top: none !important;
  }
  .orders_expansion_panel {
    width: 100%;
    background-color: #f5f5f5;
  }
  :global(.lmnt-table__cell--nestable) {
    padding: 0px;
  }
}

.seed_products-mobile {
  width: 100%;

  :global(.lmnt-list-item--wrap) {
    min-height: 85px !important;
  }

  :global(.mdc-list-item__overline-text) {
    max-height: 60px !important;
  }

  .overline-text-wrapper {
    margin: -15px 0px;
    text-transform: none;
  }
}
