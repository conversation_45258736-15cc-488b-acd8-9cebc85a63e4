/* eslint-disable @nx/enforce-module-boundaries */
import { Icon } from '@element/react-icon'
import { Typo<PERSON>aption, TypoLink, TypoSubtitle } from '@element/react-typography'
import { Badge, getCropIcon, GridList, HeaderType, List, MessageWithAction, Table } from '@gc/components'
import { interpunct, IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import {
  useDataSource,
  useGcPortalConfig,
  useIsMobile,
  useLoadingContingency,
  useLocale,
  useLocation,
  useMemoizedTranslation,
  usePageSize,
  useSelectedAccount,
  useUpdateSortBy
} from '@gc/hooks'
import { useConsignmentsQueries } from '@gc/redux-store'
import { InventoryProduct, Locale } from '@gc/types'
import { fasteRoute, formatNumber, hitsOnData } from '@gc/utils'
import { useCallback, useEffect, useMemo } from 'react'
import MediaQuery from 'react-responsive'

import styles from './SeedProductsList.module.scss'

type ThemeColor = 'gray' | 'success' | 'danger'
type StatusText = 'Even' | 'Long' | 'Short'

interface BadgeStatus {
  badgePrefix: string
  themeColor: ThemeColor
  statusText: StatusText
  displayValue: number
}

function getBadgeStatus(statusValue: number): BadgeStatus {
  if (statusValue === 0) {
    return {
      badgePrefix: '',
      themeColor: 'gray',
      statusText: 'Even',
      displayValue: statusValue
    }
  }
  if (statusValue < 0) {
    return {
      badgePrefix: '+',
      themeColor: 'success',
      statusText: 'Long',
      displayValue: Math.abs(statusValue)
    }
  }
  return {
    badgePrefix: '-',
    themeColor: 'danger',
    statusText: 'Short',
    displayValue: statusValue
  }
}

function getBadgeLabel(statusValue: number, locale: Locale): string {
  const { badgePrefix, statusText, displayValue } = getBadgeStatus(statusValue)
  return `${badgePrefix}${displayValue ? formatNumber(displayValue, locale, true) : ''} ${statusText}`
}

export const SeedProductBadge = ({ statusValue, locale }: Readonly<{ statusValue: number; locale: Locale }>) => {
  const { themeColor } = getBadgeStatus(statusValue)
  const labelText = getBadgeLabel(statusValue, locale)
  return <Badge themeColor={themeColor} labelText={labelText} />
}

const useSeedProductsListHeaders = () => {
  const t = useMemoizedTranslation()
  const locale = useLocale()

  return useMemo<HeaderType<InventoryProduct>[]>(
    () => [
      {
        header: t('common.product.type.label'),
        accessor: 'product.crop',
        filterable: true,
        isHidden: true
      },
      // Product Name
      {
        header: t('common.product_name.label'),
        accessor: 'product.name',
        id: 'product.name',
        filterable: false,
        downloadAccessor: 'product.name',
        displayTemplate: (_value: string, rowData: InventoryProduct) => (
          <TypoLink onClick={() => goToSeedProductDetails(rowData?.product.code)}>{rowData?.product.name}</TypoLink>
        )
      },
      // Status
      {
        header: t('common.status.label'),
        accessor: 'statusText',
        filterable: true,
        sortType: (x: { original: { remainingToShip: number } }, y: { original: { remainingToShip: number } }) => {
          const a: number = x.original?.remainingToShip
          const b: number = y.original?.remainingToShip
          return a - b
        },
        downloadAccessor: (rowData: InventoryProduct) => getBadgeLabel(rowData.remainingToShip, locale),
        displayTemplate: (_value: string, rowData: InventoryProduct) => {
          return _value ? <SeedProductBadge statusValue={rowData.remainingToShip} locale={locale} /> : ''
        }
      },
      // unrestricted
      {
        header: t('common.unrestricted.label'),
        accessor: 'unrestricted',
        align: 'right',
        displayType: 'decimal'
      },
      // restricted
      {
        header: t('common.restricted.label'),
        accessor: 'restricted',
        align: 'right',
        displayType: 'decimal'
      },
      // confirmed
      {
        header: t('common.confirmed.label'),
        accessor: 'confirmed',
        align: 'right',
        displayType: 'decimal'
      },
      // unconfirmed
      {
        header: t('common.unconfirmed.label'),
        accessor: 'unconfirmed',
        align: 'right',
        displayType: 'decimal'
      },
      // Rem. to deliver
      {
        header: t('common.remaining_to_deliver.label'),
        accessor: 'remainingToDeliver',
        align: 'right',
        displayTemplate: (value: string, rowData: InventoryProduct) =>
          formatNumber(!rowData.product.isPackagingMaterial ? value : 0, locale)
      }
    ],
    [locale, t]
  )
}

const goToSeedProductDetails = (code: string) => {
  fasteRoute(`/inventory/seed-products/${code}`, { code })
}

export interface SeedProductsListProps {
  tableTitle: string
  searchTerm?: string
  fasteStoreKey: string
}

export function SeedProductsList({ tableTitle, fasteStoreKey, searchTerm = '' }: Readonly<SeedProductsListProps>) {
  const isMobile = useIsMobile()
  const t = useMemoizedTranslation()
  const { sapAccountId } = useSelectedAccount()
  const [location] = useLocation()
  const locale = useLocale()
  const headers = useSeedProductsListHeaders()
  const { useGetInventoryOverviewQuery } = useConsignmentsQueries()

  const portalConfig = useGcPortalConfig()
  const seedYear = portalConfig?.seedYear

  const {
    data = [],
    isError,
    isFetching,
    isLoading,
    isSuccess,
    refetch: refetchSeedProducts
  } = useGetInventoryOverviewQuery({
    isMobile,
    reqBody: {
      //we need to change these props with dynamic values
      lob: 'SEED',
      seedYear: seedYear,
      agents: [`${sapAccountId}`],
      pageSize: usePageSize('seedProducts')
    },
    updatePartialInventory: (products = []) => {
      setInventoryProducts((prevProducts) => [...prevProducts, ...products])
    }
  })

  const { dataSource: inventoryProducts, setTmpData: setInventoryProducts } = useDataSource<InventoryProduct>(
    data,
    isSuccess,
    isFetching
  )
  const contingency = useLoadingContingency({
    isError,
    isLoading,
    isFetching,
    data: inventoryProducts,
    refetch: refetchSeedProducts,
    errorHeader: t('inventory.seed_products.api_error_header_msg.label'),
    errorDescription: t('common.error_msg_description.label'),
    noDataHeader: t('inventory.seed_products.no_data_header.msg'),
    noDataDescription: t('inventory.seed_products.no_data_description.msg')
  })

  const customSearchFun = useCallback((entry: InventoryProduct, searchStr: string) => {
    return hitsOnData(searchStr, [
      entry.product?.name,
      entry.status,
      entry.unrestricted,
      entry.restricted,
      entry.confirmed,
      entry.unconfirmed,
      entry.remainingToDeliver
    ])
  }, [])

  const dataToListItem = useCallback(
    (inventoryProduct: InventoryProduct) => {
      return {
        status: inventoryProduct.status,
        crop: inventoryProduct.product.crop,
        code: inventoryProduct.product.code,
        leadingBlock: (
          <Icon
            variant='filled-secondary'
            className={'lmnt-theme-secondary-100-bg'}
            icon={<img alt='' src={getCropIcon(inventoryProduct.product.crop)} />}
          />
        ),
        overlineText: inventoryProduct.status && (
          <div className={styles['overline-text-wrapper']}>
            <SeedProductBadge statusValue={inventoryProduct.remainingToShip} locale={locale} />
          </div>
        ),
        primaryText: <TypoSubtitle level={2}>{`${inventoryProduct.product.name}`}</TypoSubtitle>,
        secondaryText: (
          <>
            <TypoCaption>{`${formatNumber(inventoryProduct.unrestricted, locale)} ${t('common.unrestricted.label')}`}</TypoCaption>{' '}
            {interpunct}
            <TypoCaption>{`${formatNumber(inventoryProduct.restricted, locale)} ${t('common.restricted.label')}`}</TypoCaption>
          </>
        )
      }
    },
    [t, locale]
  )

  // Map inventoryProducts to add statusText for filtering
  const inventoryProductsWithStatusText = inventoryProducts.map((item) => ({
    ...item,
    statusText: getBadgeStatus(item.remainingToShip).statusText
  }))

  useUpdateSortBy({ fasteStoreKey, sortBy: [{ id: 'code', desc: false, order: 1 }] })

  useEffect(() => {
    if (location.selectedAccount && location.selectedAccount.sapAccountId !== sapAccountId) {
      refetchSeedProducts()
    }
  }, [location.selectedAccount, sapAccountId, refetchSeedProducts])

  return (
    <GridList contingency={contingency}>
      <>
        <MediaQuery minWidth={IS_DESKTOP}>
          <Table
            paginated
            searchable
            enableCsvDownload
            data={inventoryProductsWithStatusText}
            title={tableTitle}
            headers={headers}
            customSearchFn={customSearchFun}
            fasteStoreKey={fasteStoreKey}
            noContentMessage={<NoDataMessage />}
            className={styles.seed_products_table}
            hiddenColumns={['product.crop']}
          />
        </MediaQuery>

        <MediaQuery maxWidth={IS_MOBILE}>
          <div className={styles['seed_products-mobile']}>
            {inventoryProductsWithStatusText.length > 0 ? (
              <List<InventoryProduct>
                divider={true}
                onAction={goToSeedProductDetails}
                data={inventoryProductsWithStatusText}
                searchTerm={searchTerm}
                searchFn={customSearchFun}
                fasteStoreKey={fasteStoreKey}
                leadingBlockType='icon'
                dataToListItem={dataToListItem}
                filterProps={{
                  filters: [
                    { title: t('common.product.type.label'), accessor: 'product.crop' },
                    { title: t('common.status.label'), accessor: 'statusText' }
                  ]
                }}
              />
            ) : (
              <MessageWithAction
                messageHeader={t('common.no_results_message_header_label')}
                messageDescription={t('common.no_results_message_description')}
                iconProps={{
                  icon: 'info',
                  variant: 'filled-secondary',
                  className: 'gc-icon-info'
                }}
              />
            )}
          </div>
        </MediaQuery>
      </>
    </GridList>
  )
}

const NoDataMessage = () => {
  const t = useMemoizedTranslation()

  return (
    <MessageWithAction
      messageHeader={t('common.no_matching_results_message_header_label')}
      messageDescription={t('common.no_matching_results_description.msg')}
      iconProps={{
        icon: 'info',
        variant: 'filled-secondary',
        className: 'gc-icon-info'
      }}
    />
  )
}

export default SeedProductsList
