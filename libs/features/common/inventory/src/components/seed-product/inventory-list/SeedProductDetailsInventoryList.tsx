import { Typo<PERSON>aption, TypoSubtitle } from '@element/react-typography'
import { Badge, GridList, HeaderType, List, MessageWithAction, Table } from '@gc/components'
import { interpunct, IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import {
  useDataSource,
  useGcPortalConfig,
  useLoadingContingency,
  useLocale,
  useMemoizedTranslation,
  usePageSize,
  useSelectedAccount,
  useUpdateSortBy
} from '@gc/hooks'
import { useConsignmentsQueries } from '@gc/redux-store'
import { Locale, SeedProductInventory, TableRow } from '@gc/types'
import { formatNumber, getFasteStoreKey, hitsOnData } from '@gc/utils'
import { useCallback, useMemo } from 'react'
import MediaQuery from 'react-responsive'

import styles from './SeedProductDetailsInventoryList.module.scss'

const SeedProductInventoryBadge = ({
  statusValue,
  statusText
}: {
  statusValue: string | number
  statusText: string
}) => {
  return <Badge themeColor={'danger'} labelText={`${statusValue} ${statusText}`} />
}

const useSeedProductsDetailsInventoryListHeaders = (locale: Locale) => {
  const t = useMemoizedTranslation()

  return useMemo<HeaderType<SeedProductInventory>[]>(
    () => [
      {
        defaultSort: 'asc',
        defaultSortOrder: 1,
        header: t('common.warehouse.label'),
        accessor: 'location',
        downloadAccessor: (row: SeedProductInventory) =>
          row.location ? `${row.location.locationCode} - ${row.location.locationName}` : '',
        displayTemplate: (location: { locationCode: string; locationName: string }) =>
          location ? `${location.locationCode} - ${location.locationName}` : '',
        sortType(a: TableRow<SeedProductInventory>, b: TableRow<SeedProductInventory>) {
          const aLocation = a.original.location
          const bLocation = b.original.location
          return aLocation?.locationCode?.localeCompare(bLocation?.locationCode ?? '')
        }
      },
      {
        header: t('common.batch.label'),
        accessor: 'batchNumber',
        id: 'batchNumber',
        displayTemplate: (batchNumber: string, row: SeedProductInventory) => {
          return row.restricted ? (
            <div className={styles['overline-text-wrapper']}>
              <TypoCaption className={styles['batch-number']}>{batchNumber}</TypoCaption>
              <SeedProductInventoryBadge
                statusValue={formatNumber(row.restricted, locale)}
                statusText={t('common.restricted.label')}
              />
            </div>
          ) : (
            <TypoCaption>{batchNumber}</TypoCaption>
          )
        }
      },
      {
        header: t('common.seed_size.label'),
        accessor: 'seedSize'
      },
      {
        header: t('common.staged.label'),
        accessor: 'staged',
        align: 'right',
        displayType: 'decimal',
        downloadAccessor: (row: SeedProductInventory) => formatNumber(row.staged, locale)
      },
      {
        header: t('common.unrestricted.label'),
        accessor: 'unrestricted',
        align: 'right',
        displayType: 'decimal'
      }
    ],
    [t, locale]
  )
}

export interface SeedProductDetailsInventoryListProps {
  seedProductCode: string
}

export function SeedProductDetailsInventoryList({ seedProductCode }: Readonly<SeedProductDetailsInventoryListProps>) {
  const t = useMemoizedTranslation()
  const locale = useLocale()
  const headers = useSeedProductsDetailsInventoryListHeaders(locale)
  const fasteStoreKey = getFasteStoreKey('inventorySeedProduct', 'inventories')
  const { useGetSeedProductInventoriesQuery } = useConsignmentsQueries()
  const { sapAccountId } = useSelectedAccount()

  const gcPortalConfig = useGcPortalConfig()
  const seedYear = gcPortalConfig?.seedYear
  const pageSize = usePageSize('seedProductInventories')

  const {
    data = [],
    isError,
    isLoading,
    isFetching,
    isSuccess,
    refetch: refetchInventories
  } = useGetSeedProductInventoriesQuery({
    reqBody: { pageSize, productCode: seedProductCode, lob: 'SEED', agents: [`${sapAccountId}`], seedYear: seedYear },
    updatePartialData: (inventories: SeedProductInventory[] = []) => {
      setTmpData((prev) => {
        return [...prev, ...inventories]
      })
    },
    transformResponse: (inventories: SeedProductInventory[]) => {
      return inventories.map((inventory) => {
        return {
          ...inventory,
          code: `${inventory.batchNumber}-${inventory.product.code}`
        }
      })
    }
  })

  const { dataSource: inventories, setTmpData } = useDataSource<SeedProductInventory>(data, isSuccess, isFetching)
  const contingency = useLoadingContingency({
    isError,
    isLoading,
    isFetching,
    data: inventories,
    refetch: refetchInventories,
    errorHeader: t('inventory.seed_products.api_error_header_msg.label'),
    errorDescription: t('common.error_msg_description.label'),
    noDataHeader: t('inventory.seed_products.no_data_header.msg'),
    noDataDescription: t('inventory.seed_products.no_data_description.msg'),
    loadingMessage: t('common.loading.label')
  })

  const customSearchFun = useCallback((entry: SeedProductInventory, searchStr: string) => {
    const { locationCode, locationName } = entry.location ?? {}
    const warehouse = locationCode ? `${locationCode} - ${locationName}` : locationName

    return hitsOnData(searchStr, [
      warehouse,
      locationCode,
      locationName,
      entry.product?.code,
      entry.batchNumber,
      entry.seedSize,
      entry.staged,
      entry.unrestricted
    ])
  }, [])

  const dataToListItem = useCallback(
    (seedProductInventory: SeedProductInventory) => {
      const seedSize = seedProductInventory.seedSize ?? ''
      const primaryText = seedSize
        ? `${seedProductInventory.batchNumber}${interpunct}${seedSize}`
        : seedProductInventory.batchNumber

      return {
        code: seedProductInventory.batchNumber,
        overlineText: seedProductInventory.restricted && (
          <div className={styles['overline-text-wrapper']}>
            <SeedProductInventoryBadge
              statusValue={formatNumber(seedProductInventory.restricted, locale)}
              statusText={t('common.restricted.label')}
            />
          </div>
        ),
        primaryText: <TypoSubtitle bold level={2}>{`${primaryText}`}</TypoSubtitle>,
        secondaryText: (
          <>
            <TypoCaption>{`${formatNumber(seedProductInventory.staged, locale)} ${t('common.staged.label')}`}</TypoCaption>
            {interpunct}
            <TypoCaption>{`${formatNumber(seedProductInventory.unrestricted ?? 0, locale)} ${t('common.unrestricted.label')}`}</TypoCaption>
            <br />
            <TypoCaption>{`${t('common.warehouse.label')}: ${seedProductInventory.location?.locationCode} - ${seedProductInventory.location?.locationName}`}</TypoCaption>
          </>
        )
      }
    },
    [t, locale]
  )

  useUpdateSortBy({ fasteStoreKey, sortBy: [{ id: 'location', desc: false, order: 1 }] })

  return (
    <GridList contingency={contingency}>
      <>
        <MediaQuery minWidth={IS_DESKTOP}>
          <Table
            paginated
            searchable
            enableCsvDownload
            data={inventories}
            title={t('inventory.inventory.label')}
            headers={headers}
            customSearchFn={customSearchFun}
            fasteStoreKey={fasteStoreKey}
            noContentMessage={<NoDataMessage />}
            className={styles.inventory_table}
          />
        </MediaQuery>

        <MediaQuery maxWidth={IS_MOBILE}>
          <div className={styles['seed_products-mobile']}>
            {inventories && inventories?.length > 0 ? (
              <List<SeedProductInventory>
                divider={true}
                data={inventories}
                searchFn={customSearchFun}
                fasteStoreKey={fasteStoreKey}
                dataToListItem={dataToListItem}
              />
            ) : (
              <MessageWithAction
                messageHeader={t('common.no_results_message_header_label')}
                messageDescription={t('common.no_results_message_description')}
                iconProps={{
                  icon: 'info',
                  variant: 'filled-secondary',
                  className: 'gc-icon-info'
                }}
              />
            )}
          </div>
        </MediaQuery>
      </>
    </GridList>
  )
}
const NoDataMessage = () => {
  const t = useMemoizedTranslation()

  return (
    <MessageWithAction
      messageHeader={t('common.no_matching_results_message_header_label')}
      messageDescription={t('common.no_matching_results_description.msg')}
      iconProps={{
        icon: 'info',
        variant: 'filled-secondary',
        className: 'gc-icon-info'
      }}
    />
  )
}
export default SeedProductDetailsInventoryList
