.overline-text-wrapper {
  margin: -15px 0px;
  text-transform: capitalize;
}

.deliveries_table {
  tr:has(button[id='arrow_up']) {
    background: #f5f5f5;
  }

  :global(.lmnt-table__cell--nestable) {
    padding: 0px;
  }
  tr > :nth-child(0) {
    width: 86px;
    padding-right: 0px;
  }
}

.delivery-list-mobile {
  width: 100%;
  :global(.lmnt-list-item--wrap) {
    min-height: 85px !important;
  }
  :global(.mdc-list-divider) {
    padding: 0px 16px !important;
  }
}
