.former_orders_table {
  tr:has(button[id='arrow_up']) {
    background: #f5f5f5;
  }

  :global(.lmnt-table__cell--nestable) {
    padding: 0px;
  }
  tr > :nth-child(0) {
    width: 86px;
    padding-right: 0px;
  }
}

.seed_products-mobile {
  width: 100%;

  :global(.lmnt-list-item--wrap) {
    min-height: 85px !important;
  }

  :global(.mdc-list-item__overline-text) {
    max-height: 60px !important;
  }

  .overline-text-wrapper {
    margin: -15px 0px;
    text-transform: none;
  }
}
