/* eslint-disable @typescript-eslint/no-extra-semi */
import { actAwait, fireEvent, render, screen } from '@gc/utils'
import { http, HttpResponse } from 'msw'
import { MemoryRouter } from 'react-router-dom'

import server from '../../../mocks/server'
import { setUpStore } from '../../../store'
import SeedProductDetailsInventoryList from './SeedProductDetailsInventoryList'

const mockInventories = {
  inventories: [
    {
      location: {
        locationCode: 'WH001',
        locationName: 'Warehouse 1'
      },
      product: {
        code: '000000000089231108',
        name: 'Product 1'
      },
      batchNumber: 'BATCH001',
      seedSize: 'M',
      staged: 100,
      unrestricted: 500,
      restricted: 50
    },
    {
      location: {
        locationCode: 'WH002',
        locationName: 'Warehouse 2'
      },
      product: {
        code: '000000000089231108',
        name: 'Product 1'
      },
      batchNumber: 'BATCH002',
      seedSize: 'L',
      staged: 150,
      unrestricted: 750,
      restricted: 25
    }
  ]
}

const mockPortalConfig = {
  gcPortalConfig: {
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ],
    seedYear: '2024'
  }
}

const translations: { [key: string]: string } = {
  'common.try_again.label': 'Try again',
  'common.data_load_failed.label': 'Data load failed',
  'common.error_msg_description.label': 'You might be able to fix this by refreshing this page.',
  'inventory.seed_products.api_error_header_msg.label': 'Error loading data',
  'inventory.seed_products.no_data_header.msg': 'No data available',
  'inventory.seed_products.no_data_description.msg': 'No data available for the selected filters.',
  'inventory.inventory.api_error_header_msg.label': 'Error loading data',
  'common.warehouse.label': 'Warehouse',
  'common.batch.label': 'Batch',
  'common.seed_size.label': 'Seed Size',
  'common.staged.label': 'Staged',
  'common.unrestricted.label': 'Unrestricted',
  'common.loading.label': 'Loading...',
  'common.no_matching_results_message_header_label': 'No Results Found',
  'common.no_matching_results_description': 'No matching results found.'
}

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: (str: string) => translations[str] ?? str
    }
  }
}))

jest.mock('@gc/hooks', () => {
  const originalModule = jest.requireActual('@gc/hooks')
  return {
    ...originalModule,
    useIsMobile: jest.fn(() => false),
    useIsSmallMobile: jest.fn(() => false),
    useScreenRes: jest.fn(() => 1024),
    useMemoizedTranslation: () => (key: string) => translations[key] ?? key
  }
})
jest.mock('@gc/hooks/useFasteStore.ts', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore.ts'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  usePortalConfig: jest.fn(() => mockPortalConfig),
  useGcPortalConfig: jest.fn(() => mockPortalConfig.gcPortalConfig),
  usePageSize: () => 50
}))
// Mock useQuerySeedProducts hook
jest.mock('../seed-product-details/SeedProductDetails', () => ({
  useQuerySeedProducts: jest.fn()
}))

describe('SeedProductDetailsInventoryList', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  const renderComponent = () => {
    return render(
      <MemoryRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true
        }}
      >
        <SeedProductDetailsInventoryList seedProductCode='000000000089231108' />
      </MemoryRouter>,
      { store: setUpStore() }
    )
  }

  describe('rendering', () => {
    it('should render loading state initially', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/overview/, async () => {
          return HttpResponse.json(mockInventories)
        })
      )
      const { getByText } = renderComponent()
      expect(getByText('Loading...')).toBeTruthy()
    })

    it('should render inventory list when data is loaded', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/overview/, () => {
          return HttpResponse.json(mockInventories)
        })
      )
      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('WH001 - Warehouse 1')).toBeTruthy()
      expect(getByText('BATCH001')).toBeTruthy()
      expect(getByText('WH002 - Warehouse 2')).toBeTruthy()
      expect(getByText('BATCH002')).toBeTruthy()
    })

    it('should render error state when API fails', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/overview/, () => {
          return HttpResponse.error()
        })
      )

      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('Error loading data')).toBeTruthy()
      expect(getByText('Try again')).toBeTruthy()
    })

    it('should render no data message when inventory is empty', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/overview/, () => {
          return HttpResponse.json([])
        })
      )

      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('No data available')).toBeTruthy()
      expect(getByText('No data available for the selected filters.')).toBeTruthy()
    })

    it('should render table headers correctly', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/overview/, () => {
          return HttpResponse.json(mockInventories)
        })
      )

      renderComponent()

      await actAwait(100)

      expect(screen.getByText('Warehouse')).toBeTruthy()
      expect(screen.getByText('Batch')).toBeTruthy()
      expect(screen.getByText('Seed Size')).toBeTruthy()
      expect(screen.getByText('Staged')).toBeTruthy()
      expect(screen.getByText('Unrestricted')).toBeTruthy()
    })
  })

  describe('interactions', () => {
    it('should filter data when search is used', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/overview/, () => {
          return HttpResponse.json(mockInventories)
        })
      )
      const { getByPlaceholderText, queryByText } = renderComponent()
      await actAwait(100)
      const searchInput = getByPlaceholderText('common.search.label')
      fireEvent.change(searchInput, { target: { value: 'BATCH001' } })
      expect(queryByText('BATCH001')).toBeTruthy()
      expect(queryByText('BATCH002')).toBeNull()
    })
    it('should handle refresh when error occurs', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/overview/, async () => {
          return HttpResponse.error()
        })
      )

      const { getByText, queryByText } = renderComponent()
      await actAwait(1000)

      server.use(
        http.post(/\/inventory\/000000000089231108\/overview/, async () => {
          return HttpResponse.json(mockInventories)
        })
      )

      expect(getByText('Error loading data')).toBeTruthy()

      const retryButton = getByText('Try again')
      fireEvent.click(retryButton)
      await actAwait(500)
      expect(queryByText('Error loading data')).toBeNull()
    })
  })
})
