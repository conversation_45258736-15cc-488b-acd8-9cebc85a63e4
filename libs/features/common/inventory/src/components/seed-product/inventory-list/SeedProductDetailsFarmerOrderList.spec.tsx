/* eslint-disable @typescript-eslint/no-extra-semi */
import { actAwait, fasteRoute, fireEvent, render, screen, waitFor } from '@gc/utils'
import { delay, http, HttpResponse } from 'msw'
import { MemoryRouter } from 'react-router-dom'

import server from '../../../mocks/server'
import { setUpStore } from '../../../store'
import SeedProductDetailsFarmerOrderList from './SeedProductDetailsFarmerOrderList'

const mockFarmerOrders = {
  orders: [
    {
      order: {
        orderNumber: '001',
        code: 'ORDER-001',
        growerInfo: {
          name: '<PERSON>',
          uid: 'JF001'
        }
      },
      orderId: '001',
      ordered: 100,
      confirmed: 80,
      unconfirmed: 20,
      delivered: 50,
      remainingToDeliver: 30,
      returned: 0
    },
    {
      order: {
        code: 'ORDER-002',
        growerInfo: {
          name: '<PERSON>',
          uid: 'JF002'
        }
      },
      orderId: '002',
      ordered: 150,
      confirmed: 120,
      unconfirmed: 30,
      delivered: 80,
      remainingToDeliver: 40,
      returned: 5
    }
  ]
}

const mockPortalConfig = {
  gcPortalConfig: {
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ],
    seedYear: '2024'
  }
}

jest.mock('@gc/redux-store', () => ({
  ...jest.requireActual('@gc/redux-store'),
  usePortalConfig: () => mockPortalConfig,
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig,
  useSelectedAccount: () => ({ sapAccountId: '**********' })
}))

const translations: { [key: string]: string } = {
  'common.try_again.label': 'Try again',
  'common.data_load_failed.label': 'Data load failed',
  'common.error_msg_description.label': 'You might be able to fix this by refreshing this page.',
  'inventory.farmer_orders.api_error_header_msg.label': 'Error loading data',
  'inventory.seed_product_farmer_order.no_data_header.msg': 'No Orders Found',
  'inventory.seed_product_farmer_order.no_data_description.msg': 'No orders available for this product.',
  'common.farmer_name.label': 'Farmer Name',
  'orders.order_id.label': 'Order ID',
  'orders.ordered_quantity.label': 'Ordered',
  'common.confirmed.label': 'Confirmed',
  'common.unconfirmed.label': 'Unconfirmed',
  'common.delivered.label': 'Delivered',
  'common.remaining_to_deliver.label': 'Remaining to Deliver',
  'common.returned.label': 'Returned',
  'common.loading.label': 'Loading...',
  'orders.order.label': 'Order',
  'common.farmer.header.label': 'Farmer',
  'common.no_matching_results_message_header_label': 'No Results Found',
  'common.no_matching_results_description': 'No matching results found.'
}

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: (str: string) => translations[str] ?? str
    }
  }
}))

jest.mock('@gc/hooks', () => {
  const originalModule = jest.requireActual('@gc/hooks')
  return {
    ...originalModule,
    useIsMobile: jest.fn(() => false),
    useIsSmallMobile: jest.fn(() => false),
    useScreenRes: jest.fn(() => 1024),
    useMemoizedTranslation: () => (key: string) => translations[key] ?? key
  }
})
jest.mock('@gc/hooks/useFasteStore.ts', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore.ts'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  usePortalConfig: jest.fn(() => mockPortalConfig),
  useGcPortalConfig: jest.fn(() => mockPortalConfig.gcPortalConfig),
  usePageSize: () => 50
}))

jest.mock('@gc/utils', () => ({
  ...jest.requireActual('@gc/utils'),
  fasteRoute: jest.fn()
}))

describe('SeedProductDetailsFarmerOrderList', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  const renderComponent = (width = 1024) => {
    return render(
      <MemoryRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true
        }}
      >
        <SeedProductDetailsFarmerOrderList seedProductCode='000000000089231108' isPackagingMaterial={false} />
      </MemoryRouter>,
      { store: setUpStore(), width }
    )
  }

  describe('rendering', () => {
    it('should render loading state initially', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/orders/, async () => {
          return HttpResponse.json(mockFarmerOrders)
        })
      )
      const { getByText } = renderComponent()
      expect(getByText('Loading...')).toBeTruthy()
    })

    it('should render farmer orders list when data is loaded', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/orders/, () => {
          return HttpResponse.json(mockFarmerOrders)
        })
      )
      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('John Farmer')).toBeTruthy()
      expect(getByText('001')).toBeTruthy()
      expect(getByText('Jane Farmer')).toBeTruthy()
      expect(getByText('common.pending.label')).toBeTruthy()
    })

    it('should render error state when API fails', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/orders/, () => {
          return HttpResponse.error()
        })
      )

      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('Error loading data')).toBeTruthy()
      expect(getByText('Try again')).toBeTruthy()
    })

    it('should render no data message when orders list is empty', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/orders/, () => {
          return HttpResponse.json({ farmerOrders: [] })
        })
      )

      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('No Orders Found')).toBeTruthy()
      expect(getByText('No orders available for this product.')).toBeTruthy()
    })

    it('should render table headers correctly', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/orders/, () => {
          return HttpResponse.json(mockFarmerOrders)
        })
      )

      renderComponent()

      await actAwait(100)

      expect(screen.getByText('Farmer Name')).toBeTruthy()
      expect(screen.getByText('Order ID')).toBeTruthy()
      expect(screen.getByText('Ordered')).toBeTruthy()
      expect(screen.getByText('Confirmed')).toBeTruthy()
      expect(screen.getByText('Unconfirmed')).toBeTruthy()
      expect(screen.getByText('Delivered')).toBeTruthy()
      expect(screen.getByText('Remaining to Deliver')).toBeTruthy()
      expect(screen.getByText('Returned')).toBeTruthy()
    })

    it('should render mobile view when width is less than 600', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/orders/, () => {
          return HttpResponse.json(mockFarmerOrders)
        })
      )

      const { getByText } = renderComponent(600)

      await waitFor(() => {
        expect(getByText('Order 001')).toBeTruthy()
        expect(getByText('80 Confirmed')).toBeTruthy()
        expect(getByText('20 Unconfirmed')).toBeTruthy()
        expect(getByText('Farmer: John Farmer')).toBeTruthy()
      })
    })

    it('should handle undefined grower name', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/orders/, () => {
          return HttpResponse.json({
            orders: [
              {
                ...mockFarmerOrders.orders[0],
                order: {
                  ...mockFarmerOrders.orders[0].order,
                  growerInfo: undefined
                }
              }
            ]
          })
        })
      )

      const { getByText } = renderComponent(600)

      await waitFor(() => {
        expect(getByText('Order 001')).toBeTruthy()
        expect(getByText('80 Confirmed')).toBeTruthy()
        expect(getByText('20 Unconfirmed')).toBeTruthy()
        expect(getByText('Farmer:')).toBeTruthy()
      })
    })
  })

  describe('interactions', () => {
    it('should filter data when search is used', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/orders/, () => {
          return HttpResponse.json(mockFarmerOrders)
        })
      )
      const { getByPlaceholderText, queryByText } = renderComponent()
      await actAwait(100)
      const searchInput = getByPlaceholderText('common.search.label')
      fireEvent.change(searchInput, { target: { value: 'John Farmer' } })
      expect(queryByText('Jane Farmer')).toBeNull()
    })

    it('should navigate to order details when order ID is clicked', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/orders/, () => {
          return HttpResponse.json(mockFarmerOrders)
        })
      )
      const { getByText } = renderComponent()
      await actAwait(100)

      const orderLink = getByText('001')
      fireEvent.click(orderLink)
      await actAwait(100)
      expect(fasteRoute).toHaveBeenCalledWith('/orders/ORDER-001', { code: 'ORDER-001' })
    })

    it('should handle refresh when error occurs', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/orders/, async () => {
          return HttpResponse.error()
        })
      )

      const { getByText } = renderComponent()
      await actAwait(300)

      server.use(
        http.post(/\/inventory\/000000000089231108\/orders/, async () => {
          await delay(1000)
          return HttpResponse.json(mockFarmerOrders)
        })
      )

      const retryButton = getByText('Try again')
      fireEvent.click(retryButton)
      await actAwait(100)
      expect(getByText('Loading...')).toBeTruthy()
    })
  })
})
