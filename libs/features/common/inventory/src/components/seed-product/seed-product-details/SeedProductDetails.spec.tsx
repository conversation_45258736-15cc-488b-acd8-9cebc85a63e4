import { actAwait, fasteRoute, fireEvent, render, screen } from '@gc/utils'
import { http, HttpResponse } from 'msw'
import { MemoryRouter } from 'react-router-dom'

import server from '../../../mocks/server'
import { setUpStore } from '../../../store'
import SeedProductDetails from './SeedProductDetails'

const mockSeedProduct = {
  product: {
    code: '000000000089231108',
    crop: 'Corn',
    isPackagingMaterial: false,
    name: 'Product 1'
  },
  confirmed: 2050,
  remainingToDeliver: 84,
  restricted: 17464350,
  unrestricted: 295510,
  scheduled: 150,
  inTransit: 50,
  remainingToShip: -100,
  farmerOrders: 200,
  stockOrders: 100,
  unconfirmed: 138,
  status: 'Short'
}

const mockPortalConfig = {
  gcPortalConfig: {
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ],
    seedYear: '2024',
    inventoryProductDetailsTabs: ['Inventory', 'Farmer Orders', 'Deliveries']
  }
}

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ code: '000000000089231108' })
}))

const translations: { [key: string]: string } = {
  'common.try_again.label': 'Try again',
  'common.data_load_failed.label': 'Data load failed',
  'common.error_msg_description.label': 'You might be able to fix this by refreshing this page.',
  'inventory.seed_product_details.api_error_header_msg.label': 'Error loading data',
  'inventory.seed_product_details.no_data_description.msg': 'No data available for the selected filters.',
  'inventory.seed_product_details.no_data_header.msg': 'No data available',
  'inventory.loading_seed_product.msg': 'Loading Seed Product Details',
  'inventory.seed_product_your_shed.label': 'Your Shed',
  'inventory.seed_product_remaining_to_deliver.label': 'Remaining to Deliver',
  'inventory.seed_product_remaining_to_ship.label': 'Remaining to Ship',
  'inventory.inventory.label': 'Inventory',
  'orders.orders.label': 'Orders',
  'orders.farmer_orders.label': 'Farmer Orders',
  'orders.ordered_quantity.label': 'Ordered Quantity',
  'orders.confirmed.label': 'Confirmed',
  'orders.unconfirmed.label': 'Unconfirmed',
  'orders.delivered.label': 'Delivered',
  'orders.returned.label': 'Returned',
  'common.product.detail.label': 'Product Details',
  'common.relative_maturity.label': 'Relative Maturity',
  'common.warehouse.label': 'Warehouse',
  'common.staged.label': 'Staged',
  'common.unrestricted.label': 'Unrestricted',
  'common.restricted.label': 'Restricted',
  'inventory.shipments.shipments.label': 'Shipments',
  'common.scheduled.label': 'Scheduled',
  'common.in_transit.label': 'In Transit',
  'common.remaining_to_ship.label': 'Remaining to Ship',
  'common.remaining_to_deliver_title.label': 'Remaining to Deliver'
}

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: (str: string) => translations[str] ?? str
    }
  }
}))

jest.mock('@gc/hooks', () => {
  const originalModule = jest.requireActual('@gc/hooks')
  return {
    ...originalModule,
    useIsMobile: jest.fn(() => false),
    useIsSmallMobile: jest.fn(() => false),
    useScreenRes: jest.fn(() => 1024),
    useMemoizedTranslation: () => (key: string) => translations[key] ?? key
  }
})

jest.mock('@gc/hooks/useFasteStore.ts', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore.ts'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  usePortalConfig: jest.fn(() => mockPortalConfig),
  useGcPortalConfig: jest.fn(() => mockPortalConfig.gcPortalConfig),
  usePageSize: () => 50
}))

jest.mock('../inventory-list/SeedProductDetailsInventoryList', () => ({
  __esModule: true,
  default: () => <div>Inventory List</div>
}))

jest.mock('../inventory-list/SeedProductDetailsDeliveryList', () => ({
  __esModule: true,
  default: () => <div>Delivery List</div>
}))

describe('SeedProductDetails', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  const renderComponent = () => {
    return render(
      <MemoryRouter initialEntries={['/inventory/seed-product/000000000089231108']}>
        <SeedProductDetails />
      </MemoryRouter>,
      { store: setUpStore() }
    )
  }

  describe('rendering', () => {
    it('should render loading state initially', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108/, async () => {
          return HttpResponse.json(mockSeedProduct)
        })
      )

      const { getByText } = renderComponent()
      expect(getByText('Loading Seed Product Details')).toBeTruthy()
    })

    it('should render seed product details when data is loaded', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108/, () => {
          return HttpResponse.json(mockSeedProduct)
        })
      )

      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('Product 1')).toBeTruthy()
      expect(getByText('Your Shed')).toBeTruthy()
      expect(getByText('Shipments')).toBeTruthy()
      expect(getByText('Orders')).toBeTruthy()
      expect(getByText('+100 Long')).toBeTruthy()
    })

    it('should render seed product details with long status', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108/, () => {
          return HttpResponse.json({ ...mockSeedProduct, status: 'Long', remainingToShip: 1500 })
        })
      )

      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('Product 1')).toBeTruthy()
      expect(getByText('Your Shed')).toBeTruthy()
      expect(getByText('Shipments')).toBeTruthy()
      expect(getByText('Orders')).toBeTruthy()
      expect(getByText('-1,500 Short')).toBeTruthy()
    })
    it('should render seed product details with Even status', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108/, () => {
          return HttpResponse.json({ ...mockSeedProduct, status: 'Long', remainingToShip: 0 })
        })
      )

      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('Product 1')).toBeTruthy()
      expect(getByText('Your Shed')).toBeTruthy()
      expect(getByText('Shipments')).toBeTruthy()
      expect(getByText('Orders')).toBeTruthy()
      expect(getByText('Even')).toBeTruthy()
    })

    it('should render error state when API fails', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108/, () => {
          return HttpResponse.error()
        })
      )

      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('Data load failed')).toBeTruthy()
      expect(getByText('Try again')).toBeTruthy()
    })

    it('should render product details after loading', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108/, () => {
          return HttpResponse.json(mockSeedProduct)
        })
      )

      renderComponent()

      await actAwait(100)

      expect(screen.getByText('Your Shed')).toBeInTheDocument()
      expect(screen.getByText('Shipments')).toBeInTheDocument()
      expect(screen.getByText('Orders')).toBeInTheDocument()
    })

    it('should render all tabs', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108/, () => {
          return HttpResponse.json(mockSeedProduct)
        })
      )

      renderComponent()

      await actAwait(100)

      expect(screen.getByRole('tab', { name: 'INVENTORY' })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'FARMER ORDERS' })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'DELIVERIES' })).toBeInTheDocument()
    })
  })
  describe('interactions', () => {
    it('should switch tabs when clicked', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108/, () => {
          return HttpResponse.json(mockSeedProduct)
        })
      )

      const { getByText } = renderComponent()

      await actAwait(100)

      const farmerOrdersTab = getByText('FARMER ORDERS')
      fireEvent.click(farmerOrdersTab)

      // Verify tab content changed
      expect(getByText('FARMER ORDERS')).toBeTruthy()
    })

    it('should navigate back when back button is clicked', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108/, () => {
          return HttpResponse.json(mockSeedProduct)
        })
      )

      const { container } = renderComponent()

      await actAwait(100)

      const backButton: HTMLButtonElement | null = container.querySelector('[icon="arrow_back"]')
      if (backButton) {
        fireEvent.click(backButton)
        expect(fasteRoute).toHaveBeenCalledWith('/inventory')
      }
    })
  })
})
