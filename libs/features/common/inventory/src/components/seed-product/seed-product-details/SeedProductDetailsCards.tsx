import { GridCol } from '@element/react-grid'
import { CustomDetailsCard } from '@gc/components'
import { interpunct } from '@gc/constants'
import { useLocale, useMemoizedTranslation } from '@gc/hooks'
import { formatNumber } from '@gc/utils'
import { useMemo } from 'react'

import { useSeedProduct } from './SeedProductDetails'

export function SeedProductDetailsShedCard({ cols }: Readonly<{ cols: number }>) {
  const locale = useLocale()
  const t = useMemoizedTranslation()
  const { seedProduct, isPackagingMaterial } = useSeedProduct()

  const { remainingToDeliver, restricted, unrestricted, recalled } = useMemo(() => {
    const _remainingToDeliver = isPackagingMaterial ? 0 : (seedProduct?.remainingToDeliver ?? 0)

    return {
      remainingToDeliver: formatNumber(_remainingToDeliver, locale, true),
      restricted: formatNumber(seedProduct?.restricted ?? 0, locale),
      unrestricted: formatNumber(seedProduct?.unrestricted ?? 0, locale),
      recalled: formatNumber((seedProduct?.restricted ?? 0) + (seedProduct?.unrestricted ?? 0), locale)
    }
  }, [seedProduct, locale, isPackagingMaterial])

  const items = useMemo(() => {
    return [
      {
        label: t('inventory.seed_product_on_hand.label'),
        value: recalled
      },
      {
        label: `${t('common.restricted.label')} / ${t('common.recalled.label')}`,
        value: restricted
      }
    ]
  }, [t, recalled, restricted])

  return (
    <GridCol desktopCol={cols} tabletCol={cols} phoneCol={cols}>
      <CustomDetailsCard
        items={items}
        title={t('inventory.seed_product_your_shed.label')}
        subtitle={`${remainingToDeliver} ${t('common.remaining_to_deliver_title.label')}`}
        footerItem={{ label: t('common.unrestricted.label'), value: unrestricted }}
      />
    </GridCol>
  )
}

export function SeedProductDetailsShipmentsCard({ cols }: Readonly<{ cols: number }>) {
  const locale = useLocale()
  const t = useMemoizedTranslation()
  const { seedProduct } = useSeedProduct()

  const { scheduled, inTransit, remainingToShip } = useMemo(() => {
    const remainingToShip = (seedProduct?.remainingToShip ?? 0) < 0 ? 0 : seedProduct?.remainingToShip

    return {
      remainingToShip: formatNumber(remainingToShip, locale),
      scheduled: formatNumber(seedProduct?.scheduled ?? 0, locale),
      inTransit: formatNumber(seedProduct?.inTransit ?? 0, locale)
    }
  }, [seedProduct, locale])

  const items = useMemo(() => {
    return [
      {
        label: t('common.scheduled.label'),
        value: scheduled
      },
      {
        label: t('common.in_transit.label'),
        value: inTransit
      }
    ]
  }, [t, scheduled, inTransit])

  return (
    <GridCol desktopCol={cols} tabletCol={cols} phoneCol={cols}>
      <CustomDetailsCard
        items={items}
        title={t('inventory.shipments.shipments.label')}
        footerItem={{
          label: t('common.remaining_to_ship.label'),
          value: remainingToShip
        }}
      />
    </GridCol>
  )
}

export function SeedProductDetailsOrdersCard({ cols }: Readonly<{ cols: number }>) {
  const locale = useLocale()
  const t = useMemoizedTranslation()
  const { seedProduct, isPackagingMaterial } = useSeedProduct()

  const { farmerOrders, stockOrders, confirmed, unconfirmed, totalOrders } = useMemo(() => {
    const _farmersOrders = seedProduct?.farmerOrders ?? 0
    const _stockOrders = seedProduct?.stockOrders ?? 0
    const _totalOrders = !isPackagingMaterial ? _farmersOrders + _stockOrders : _stockOrders

    return {
      farmerOrders: formatNumber(_farmersOrders, locale),
      stockOrders: formatNumber(_stockOrders, locale),
      totalOrders: formatNumber(_totalOrders, locale),
      confirmed: formatNumber(seedProduct?.confirmed ?? 0, locale),
      unconfirmed: formatNumber(seedProduct?.unconfirmed ?? 0, locale)
    }
  }, [
    seedProduct?.farmerOrders,
    seedProduct?.stockOrders,
    seedProduct?.confirmed,
    seedProduct?.unconfirmed,
    isPackagingMaterial,
    locale
  ])

  const items = useMemo(() => {
    return [
      {
        label: t('common.farmer.header.label'),
        value: !isPackagingMaterial ? farmerOrders : 0
      },
      {
        label: t('common.stock.label'),
        value: stockOrders
      }
    ]
  }, [t, isPackagingMaterial, farmerOrders, stockOrders])

  return (
    <GridCol desktopCol={cols} tabletCol={cols} phoneCol={cols}>
      <CustomDetailsCard
        items={items}
        title={t('orders.orders.label')}
        subtitle={`${confirmed} ${t('common.confirmed.label')} ${interpunct} ${unconfirmed} ${t('common.unconfirmed.label')}`}
        footerItem={{
          label: `${t('common.total.label')} ${t('orders.ordered_quantity.label')}`,
          value: totalOrders
        }}
      />
    </GridCol>
  )
}
