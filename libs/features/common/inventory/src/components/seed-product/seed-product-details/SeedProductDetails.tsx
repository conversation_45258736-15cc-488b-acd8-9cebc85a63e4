import { Divider } from '@element/react-divider'
import { Grid, GridCol, GridRow } from '@element/react-grid'
import { TabBar } from '@element/react-tabs'
import { LoadingAndContingencySection, TopAppBar } from '@gc/components'
import { IS_DESKTOP, resolutions } from '@gc/constants'
import {
  useGcPortalConfig,
  useIsMobile,
  useIsSmallMobile,
  useMemoizedTranslation,
  useScreenRes,
  useSelectedAccount,
  useTabs
} from '@gc/hooks'
import { useConsignmentsQueries } from '@gc/redux-store'
import { SeedProduct } from '@gc/types'
import { fasteRoute } from '@gc/utils'
import { useMemo } from 'react'
import MediaQuery from 'react-responsive'
import { useParams } from 'react-router-dom'

import styles from './SeedProductDetails.module.scss'
import {
  SeedProductDetailsOrdersCard,
  SeedProductDetailsShedCard,
  SeedProductDetailsShipmentsCard
} from './SeedProductDetailsCards'
import {
  SeedProductDetailsContingency,
  SeedProductDetailsHeader,
  SeedProductDetailsTabList
} from './SeedProductDetailsComponents'

const handleClickBack = () => {
  fasteRoute('/inventory')
}

export function useSeedProduct() {
  const { code = '' } = useParams()
  const { sapAccountId } = useSelectedAccount()
  const { useGetSeedProductDetailsQuery } = useConsignmentsQueries()

  const {
    data: seedProduct,
    isError,
    isFetching,
    isLoading,
    refetch: refetchSeedProductDetails
  } = useGetSeedProductDetailsQuery({
    reqBody: { pageSize: 50, productCode: code, lob: 'SEED', agents: [`${sapAccountId}`], seedYear: '2025' },
    transformResponse: (data: SeedProduct) => {
      return data
    }
  })

  const hasError = !!isError
  const isPackagingMaterial = useMemo(() => seedProduct?.product.isPackagingMaterial ?? false, [seedProduct])

  return { seedProduct, hasError, isError, isFetching, isLoading, refetchSeedProductDetails, isPackagingMaterial }
}

export function SeedProductDetails() {
  const t = useMemoizedTranslation()
  const isMobile = useIsMobile()
  const isSmallMobile = useIsSmallMobile()
  const gcPortalConfig = useGcPortalConfig()
  const { hasError, isLoading, isFetching, seedProduct, refetchSeedProductDetails, isPackagingMaterial } =
    useSeedProduct()

  const {
    currentTab,
    handleTabActivated,
    resetCurrentTab,
    ComponentTabs: InventoryProductDetailsTabs
  } = useTabs(gcPortalConfig?.inventoryProductDetailsTabs)

  const isDataLoading = useMemo(() => {
    return seedProduct === undefined && (isFetching || isLoading)
  }, [seedProduct, isFetching, isLoading])

  const hasData = useMemo(() => {
    return seedProduct !== undefined
  }, [seedProduct])

  const res = useScreenRes()
  const cols = res > resolutions.M1023 ? 3 : 4

  return (
    <div className={styles.container}>
      {isMobile && (
        <TopAppBar
          title={t('common.product.detail.label')}
          leadingIconButtonProps={{
            icon: 'arrow_back',
            onClick: () => {
              resetCurrentTab()
              handleClickBack()
            }
          }}
        />
      )}

      <div className={!hasData || hasError ? styles.contingency : ''}>
        <LoadingAndContingencySection
          hasError={hasError}
          hasData={hasData}
          isLoading={isDataLoading}
          errorDescription={t('common.error_msg_description.label')}
          errorHeader={t('inventory.seed_product_details.api_error_header_msg.label')}
          loadingMessage={t('inventory.loading_seed_product.msg')}
          noDataDescription={t('inventory.seed_product_details.no_data_description.msg')}
          noDataHeader={t('inventory.seed_product_details.no_data_header.msg')}
          errorComponent={<SeedProductDetailsContingency refetchSeedProductDetails={refetchSeedProductDetails} />}
        >
          <>
            <SeedProductDetailsHeader seedProduct={seedProduct} refetchSeedProductDetails={refetchSeedProductDetails} />

            <Grid className={styles.cards_container}>
              <GridRow>
                <SeedProductDetailsShedCard cols={cols} />
                <SeedProductDetailsShipmentsCard cols={cols} />
                <SeedProductDetailsOrdersCard cols={cols} />

                {/* Filler column for desktop */}
                <MediaQuery minWidth={IS_DESKTOP}>
                  <GridCol desktopCol={cols} tabletCol={cols} phoneCol={cols}></GridCol>
                </MediaQuery>
              </GridRow>
            </Grid>

            {isSmallMobile && <Divider />}

            <TabBar
              clustered={true}
              clusterAlign='start'
              elevated={false}
              variant='surface'
              activeTabIndex={currentTab}
              stacked={false}
              onTabActivated={handleTabActivated}
            >
              <InventoryProductDetailsTabs />
            </TabBar>
            <SeedProductDetailsTabList
              currentTab={currentTab}
              seedProductCode={seedProduct?.product.code ?? ''}
              isPackagingMaterial={isPackagingMaterial}
            />
          </>
        </LoadingAndContingencySection>
      </div>
    </div>
  )
}

export default SeedProductDetails
