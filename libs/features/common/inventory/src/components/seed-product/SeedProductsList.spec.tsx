import { actAwait, fireEvent, formatNumber, render } from '@gc/utils'
import { within } from '@testing-library/dom'
import { http, HttpResponse } from 'msw'

import server from '../../mocks/server'
import { setUpStore } from '../../store'
import SeedProductsList from './SeedProductsList'

const mockData = {
  inventoryProducts: [
    {
      confirmed: 2050,
      product: {
        code: '000000000089231108',
        crop: 'Corn',
        isPackagingMaterial: false,
        name: 'Product 1'
      },
      remainingToDeliver: 84,
      remainingToShip: -100,
      restricted: 17464350,
      status: 'Long',
      unconfirmed: 138,
      unrestricted: 0
    },
    {
      confirmed: 96,
      product: {
        code: '000000000012279704',
        crop: 'Corn',
        isPackagingMaterial: false,
        name: 'Product 2'
      },
      remainingToDeliver: 33,
      remainingToShip: 1500,
      restricted: 0,
      status: 'Short',
      unconfirmed: 9,
      unrestricted: 295510
    },
    {
      confirmed: 96,
      product: {
        code: '000000000012279704',
        crop: 'Corn',
        isPackagingMaterial: false,
        name: 'Product 3o'
      },
      remainingToDeliver: 33,
      remainingToShip: 0,
      restricted: 0,
      status: 'Even',
      unconfirmed: 9,
      unrestricted: 295510
    }
  ]
}
const mockPortalConfig = {
  gcPortalConfig: {
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ],
    seedYear: '2024',
    inventoryProductDetailsTabs: ['Inventory', 'Former Orders', 'Deliveries']
  }
}

const translations: { [key: string]: string } = {
  'common.cancel.label': 'Cancel',
  'common.apply.label': 'Apply',
  'common.edit.label': 'Edit',
  'common.add_products.label': 'Add Products',
  'common.try_again.label': 'Try again',
  'common.product.label': 'Products',
  'common.search.label': 'Search',
  'inventory.seed_products.no_data_header.msg': 'No data available',
  'inventory.seed_products.no_data_description.msg': 'No data available for the selected filters.',
  'common.error_msg_description.label': '	You might be able to fix this by refreshing this page.'
}

jest.mock('react-i18next', () => ({
  // this mock makes sure any components using the translate hook can use it without a warning being shown
  useTranslation: () => {
    return {
      t: (str: string) => translations[str] ?? str
    }
  }
}))

jest.mock('@gc/hooks', () => {
  const originalModule = jest.requireActual('@gc/hooks')
  const mockAppSessionData = {}
  const mockUpsertAppSessionData = jest.fn()

  return {
    ...originalModule,
    useIsMobile: jest.fn(() => true),
    useScreenRes: jest.fn(() => 375),
    useIsSmallMobile: jest.fn(),
    useAppSession: () => [mockAppSessionData, mockUpsertAppSessionData],
    useMemoizedTranslation: () => (key: string) => translations[key] ?? key
  }
})
jest.mock('@gc/hooks/useFasteStore.ts', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore.ts'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  usePortalConfig: jest.fn(() => mockPortalConfig),
  useGcPortalConfig: jest.fn(() => mockPortalConfig.gcPortalConfig),
  usePageSize: () => 50
}))

describe('SeedProductsList', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('should render no data message when no products are available', async () => {
    server.use(
      http.post(/\/overview/, () => {
        return HttpResponse.json([])
      })
    )
    const { getByText } = render(<SeedProductsList fasteStoreKey='inventory' tableTitle='Overview' />, {
      store: setUpStore()
    })
    await actAwait(100)
    expect(getByText('No data available')).toBeTruthy()
    expect(getByText('No data available for the selected filters.')).toBeTruthy()
  })

  it('should render the list of seed products', async () => {
    server.use(
      http.post(/\/overview/, () => {
        return HttpResponse.json(mockData)
      })
    )
    const { getByText, getAllByText } = render(<SeedProductsList fasteStoreKey='inventory' tableTitle='Overview' />, {
      store: setUpStore()
    })

    await actAwait(100)

    expect(getByText('Product 1')).toBeTruthy()
    expect(getByText('Product 2')).toBeTruthy()
    expect(getByText('+100 Long')).toBeTruthy()
    expect(getByText('-1,500 Short')).toBeTruthy()
    expect(getAllByText('Even')).toHaveLength(2)
  })

  it('should filter products based on search string', async () => {
    server.use(
      http.post(/\/overview/, () => {
        return HttpResponse.json(mockData)
      })
    )

    const { getByPlaceholderText, getByText, queryByText } = render(
      <SeedProductsList fasteStoreKey='inventory' tableTitle='Overview' />,
      {
        store: setUpStore()
      }
    )
    await actAwait(100)
    const searchInput = getByPlaceholderText('Search')

    fireEvent.change(searchInput, { target: { value: 'Product 1' } })
    await actAwait(100)
    expect(getByText('Product 1')).toBeTruthy()
    expect(queryByText('Product 2')).toBeNull()
  })

  it('should render remaining to deliver as 0 when product is a packaging material', async () => {
    server.use(
      http.post(/\/overview/, () => {
        return HttpResponse.json({
          inventoryProducts: [
            {
              ...mockData.inventoryProducts[0],
              product: { ...mockData.inventoryProducts[0].product, isPackagingMaterial: true }
            }
          ]
        })
      })
    )

    const { getByText, getAllByRole } = render(<SeedProductsList fasteStoreKey='inventory' tableTitle='Overview' />, {
      store: setUpStore()
    })

    const product = mockData.inventoryProducts[0]
    const restricted = formatNumber(product.restricted, undefined, true)
    const confirmed = formatNumber(product.confirmed, undefined, true)
    const unrestricted = formatNumber(product.unrestricted, undefined, true)

    await actAwait(100)
    expect(getByText('Product 1')).toBeTruthy()

    const cells = getAllByRole('cell')
    expect(cells[0]).toHaveTextContent(product.product.name)
    expect(cells[1]).toHaveTextContent(`+${Math.abs(product.remainingToShip)} Long`)
    expect(cells[2]).toHaveTextContent(unrestricted)
    expect(cells[3]).toHaveTextContent(restricted)
    expect(cells[4]).toHaveTextContent(confirmed)
    expect(cells[5]).toHaveTextContent(product.unconfirmed.toString())
    expect(cells[6]).toHaveTextContent('0')
  })
  it('should sort products by remainingToShip when clicking on status', async () => {
    server.use(
      http.post(/\/overview/, () => {
        return HttpResponse.json(mockData)
      })
    )
    render(<SeedProductsList fasteStoreKey='inventory' tableTitle='Overview' />, {
      store: setUpStore()
    })
    await actAwait(100)
    // Simulate sorting by clicking the table header for statusText
    const statusHeader = document.querySelector('th[id="statusText"]')
    if (statusHeader) {
      fireEvent.click(statusHeader)
      await actAwait(100)
      // Check the order of product names in the table after sorting
      const productRows = Array.from(document.querySelectorAll('table tbody tr'))
      const productNames = productRows
        .map((row) => {
          const cell = row.querySelector('td')
          return cell ? cell.textContent?.trim() : null
        })
        .filter(Boolean)
      expect(productNames).toEqual(['Product 1', 'Product 3o', 'Product 2'])
    } else {
      throw new Error('Status header not found for sorting')
    }
  })
  it('should filter products using the status filter chip', async () => {
    server.use(
      http.post(/\/overview/, () => {
        return HttpResponse.json(mockData)
      })
    )
    const { getByText, queryByText } = render(<SeedProductsList fasteStoreKey='inventory' tableTitle='Overview' />, {
      store: setUpStore()
    })
    await actAwait(100)

    // Find the status filter chip by its test id or label
    const statusChip = document.querySelector('[data-testid="common.status.label"]')
    expect(statusChip).toBeTruthy()
    if (statusChip) {
      fireEvent.click(statusChip)
      await actAwait(100)

      // Click on the 'Short' status chip (should now be visible as a filter option)
      const shortChip = getByText('Short')
      fireEvent.click(shortChip)
      await actAwait(100)

      // Apply the filter (if there is an apply button)
      const applyButton = getByText('Apply')
      fireEvent.click(applyButton)
      await actAwait(100)

      // Only 'Product 1' should be visible after filtering by 'Short'
      const table = document.querySelector('[data-testid="table"]')
      expect(table).toBeTruthy()
      if (table) {
        const tbody = table.querySelector('tbody')
        expect(tbody).toBeTruthy()
        if (tbody) {
          // After filtering by "Short" status chip, assert that "Product 2" is present and "Product 1" is not
          expect(within(tbody).getByText(/Product 2/)).toBeInTheDocument()
          expect(within(tbody).queryByText(/Product 1/)).not.toBeInTheDocument()
          expect(within(tbody).queryByText(/Product 3o/)).not.toBeInTheDocument()
        }
      }
      expect(queryByText('Product 3o')).toBeNull()
    }
  })

  it('should filter products using the status filter chip', async () => {
    server.use(
      http.post(/\/overview/, () => {
        return HttpResponse.json(mockData)
      })
    )
    const { getByText, queryByText } = render(<SeedProductsList fasteStoreKey='inventory' tableTitle='Overview' />, {
      store: setUpStore()
    })
    await actAwait(100)

    // Find the status filter chip by its test id or label
    const statusChip = document.querySelector('[data-testid="common.status.label"]')
    expect(statusChip).toBeTruthy()
    if (statusChip) {
      fireEvent.click(statusChip)
      await actAwait(100)

      // Apply the filter (if there is an apply button)
      const applyButton = getByText('Apply')

      // Now filter by 'Long' status
      fireEvent.click(statusChip)
      await actAwait(50)
      const longChip = getByText('Long')
      fireEvent.click(longChip)
      fireEvent.click(applyButton)
      await actAwait(100)
      const table = document.querySelector('[data-testid="table"]')

      // Only 'Product 1' should be visible after filtering by 'Long'
      const tbody = table ? table.querySelector('tbody') : null
      expect(tbody).toBeTruthy()
      if (tbody) {
        expect(within(tbody).getByText(/Product 1/)).toBeInTheDocument()
        expect(within(tbody).queryByText(/Product 2/)).not.toBeInTheDocument()
        // Assert that 'Product 3o' is NOT present after filtering by 'Long'
        expect(within(tbody).queryByText(/Product 3o/)).not.toBeInTheDocument()
      }
      expect(queryByText('Product 2')).toBeNull()
    }
  })
})
