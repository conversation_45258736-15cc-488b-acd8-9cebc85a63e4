import { ActionMenuButton, ConsignmentDetailsMobile, Header } from '@gc/components'
import { useMemoizedTranslation, useShipmentActions } from '@gc/hooks'
import { getModal, useConsignmentsQueries } from '@gc/redux-store'
import { Consignment } from '@gc/types'
import { fasteRoute } from '@gc/utils'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'

const handleClickBack = () => {
  fasteRoute('/inventory')
}

export function ShipmentDetailsMobile() {
  const t = useMemoizedTranslation()
  const { code } = useParams()

  const modal = useSelector(getModal)

  const { useGetConsignmentDetailsQuery } = useConsignmentsQueries()

  const { actions: shipmentActions } = useShipmentActions()

  const {
    data: shipmentDetails,
    isLoading,
    isError,
    isFetching,
    refetch: refetchShipmentDetails
  } = useGetConsignmentDetailsQuery({
    consignmentId: code ?? ''
  })

  const getHeader = () => {
    return (
      <Header
        secText1={`${t('inventory.shipments.shipment.label')} ${shipmentDetails?.shipmentId ?? ''}`}
        secText2={
          shipmentDetails?.lineOfBusiness
            ? t(`common.line_of_business.${shipmentDetails?.lineOfBusiness?.toLocaleLowerCase()}.label`)
            : ''
        }
        overlineBadgeProps={{ labelText: shipmentDetails?.statusText as string }}
        title={`${t('deliveries.delivery.label')} ${shipmentDetails?.code}`}
      />
    )
  }

  return (
    <>
      <ConsignmentDetailsMobile
        topBarProps={{
          title: t('inventory.shipments.delivery_details.label'),
          onBackClick: handleClickBack
        }}
        contingencyStateProps={{
          isLoading: isLoading || isFetching,
          loadingMessage: t('deliveries.loading_delivery_details_message.label'),
          isError: isError,
          errorMessageActionProps: {
            messageHeader: t('common.data_load_failed.label'),
            primaryButtonProps: {
              onClick: refetchShipmentDetails
            }
          }
        }}
        header={getHeader()}
        consignmentDetails={shipmentDetails as Consignment}
        showFromAddress
        showToAddress
      />
      {!!shipmentActions.length && !modal?.open && !!shipmentDetails && shipmentDetails.lineOfBusiness !== 'SG' && (
        <ActionMenuButton
          leadingIcon='add'
          buttonLabel={t('common.actions.label')}
          actionItems={shipmentActions}
          data={shipmentDetails}
        />
      )}
    </>
  )
}

export default ShipmentDetailsMobile
