import { actAwait, render, screen } from '@gc/utils'
import { http, HttpResponse } from 'msw'

import server from '../../../mocks/server'
import { setUpStore } from '../../../store'
import ShipmentDetailsMobile from './ShipmentDetailsMobile'

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: jest.fn(),
  useParams: () => ({ code: '0801770708' })
}))

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useScreenRes: () => 5
}))

const mockPortalConfig = {
  gcPortalConfig: {
    seedYear: '2024',
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ],
    pageSize: {
      shipments: 50
    },
    documentTypes: {
      shipments: ['SHIPMENT']
    }
  },
  inventoryModule: {
    shipmentActions: [{ type: 'goodsReceive', enableStatues: ['GOODS_ISSUED'] }, { type: 'addIssue' }]
  }
}

jest.mock('../../../../../../../hooks/src/useFasteStore', () => ({
  ...jest.requireActual('../../../../../../../hooks/src/useFasteStore'),
  usePortalConfig: () => mockPortalConfig,
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig
}))

describe('ShipmentDetailsMobile', () => {
  beforeEach(() => {
    server.use(http.get(/\/consignments/, () => HttpResponse.json(mockShipment)))

    // track network requests for each test
    jest.clearAllMocks()
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('should render successfully', () => {
    const { baseElement } = render(<ShipmentDetailsMobile />, { store: setUpStore() })
    expect(baseElement).toBeTruthy()
  })

  it('should render action button for line of business of SEED', async () => {
    render(<ShipmentDetailsMobile />, { store: setUpStore() })
    await actAwait(100)

    const actionsButton = screen.getByRole('button', { name: 'add common.actions.label' })
    expect(actionsButton).toBeDefined()
  })

  it('should not render action button for line of business of SG', async () => {
    server.use(http.get(/\/consignments/, () => HttpResponse.json({ ...mockShipment, lineOfBusiness: 'SG' })))

    render(<ShipmentDetailsMobile />, { store: setUpStore() })
    await actAwait(100)

    const actionsButton = screen.queryByRole('button', { name: 'add common.actions.label' })
    expect(actionsButton).toBeNull()
  })
})

const mockShipment = {
  agent: {
    name: 'Bayer AG',
    uid: '1000000001'
  },
  code: '0801770708',
  createDate: '2025-03-19T01:29:59-05:00',
  createdOnDateTime: '2025-03-19T01:29:59-05:00',
  deleted: false,
  entries: [
    {
      batchName: 'B57GM4TVR',
      deleted: false,
      deliveryItemNumber: '000010',
      newEntry: false,
      packagingMaterial: false,
      product: {
        available: 0,
        code: '000000000012315356',
        isPackagingMaterial: false,
        name: '186-30R 80M BAG A500V',
        salesUnitOfMeasure: 'SSU'
      },
      quantity: 2,
      salesOrderEntryNumber: '10',
      salesUnitOfMeasureCode: 'SSU',
      statusText: 'Being Processed',
      storageLocation: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          formattedAddress: 'EAST. Millikin Parkway2380, Illinois, DECATUR, 62526',
          id: '8860319088663',
          line1: 'EAST. Millikin Parkway2380',
          phone: '************',
          postalCode: '62526',
          region: {
            countryIso: 'US',
            isocode: 'US-IL',
            isocodeShort: 'IL',
            name: 'Illinois'
          },
          shippingAddress: true,
          town: 'DECATUR',
          visibleInAddressBook: true
        },
        code: '8H78_WH01',
        locationCode: 'WH01',
        locationName: 'Warehouse Loc',
        plant: 'zz_invalid_DC Decatur IL-CRC M',
        type: 'SELLABLE'
      },
      transferOrderEntryNumber: '10',
      warehouse: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          formattedAddress: '2380 EAST. Millikin Parkway, Illinois, DECATUR, 62526',
          id: '8860318466071',
          line1: '2380 EAST. Millikin Parkway',
          phone: '************',
          postalCode: '62526',
          region: {
            countryIso: 'US',
            isocode: 'US-IL',
            isocodeShort: 'IL',
            name: 'Illinois'
          },
          shippingAddress: true,
          town: 'DECATUR',
          visibleInAddressBook: true
        },
        code: '8H78',
        name: 'zz_invalid_DC Decatur IL-CRC M',
        plantCode: '8H78'
      }
    }
  ],
  fromLocation: {
    address: {
      country: {
        isocode: 'US',
        name: 'United States'
      },
      defaultAddress: false,
      formattedAddress: 'EAST. Millikin Parkway2380, Illinois, DECATUR, 62526',
      id: '8860319088663',
      line1: 'EAST. Millikin Parkway2380',
      phone: '************',
      postalCode: '62526',
      region: {
        countryIso: 'US',
        isocode: 'US-IL',
        isocodeShort: 'IL',
        name: 'Illinois'
      },
      shippingAddress: true,
      town: 'DECATUR',
      visibleInAddressBook: true
    },
    code: '8H78_WH01',
    locationCode: 'WH01',
    locationName: 'Warehouse Loc',
    plant: 'zz_invalid_DC Decatur IL-CRC M',
    type: 'SELLABLE'
  },
  fromWarehouse: {
    address: {
      country: {
        isocode: 'US',
        name: 'United States'
      },
      defaultAddress: false,
      formattedAddress: '2380 EAST. Millikin Parkway, Illinois, DECATUR, 62526',
      id: '8860318466071',
      line1: '2380 EAST. Millikin Parkway',
      phone: '************',
      postalCode: '62526',
      region: {
        countryIso: 'US',
        isocode: 'US-IL',
        isocodeShort: 'IL',
        name: 'Illinois'
      },
      shippingAddress: true,
      town: 'DECATUR',
      visibleInAddressBook: true
    },
    code: '8H78',
    name: 'zz_invalid_DC Decatur IL-CRC M',
    plantCode: '8H78'
  },
  grower: {
    name: 'BRIAN HAVLIK',
    uid: '**********'
  },
  lineOfBusiness: 'SEED',
  modify: false,
  order: {
    code: 'BC-0801770708',
    isAddSameSKUInSeparateLineEnabled: false,
    roundUp: false
  },
  plannedShipDate: '2025-03-18T19:00:00-05:00',
  salesYear: '2025',
  shipToAddress: {
    country: {
      isocode: 'US',
      name: 'United States'
    },
    defaultAddress: false,
    email: '<EMAIL>',
    formattedAddress: '23625 365TH AVE, South Dakota, KIMBALL, 57355-6004',
    id: '8806185598999',
    line1: '23625 365TH AVE',
    phone: '************',
    postalCode: '57355-6004',
    region: {
      countryIso: 'US',
      isocode: 'US-SD',
      isocodeShort: 'SD',
      name: 'South Dakota'
    },
    shippingAddress: false,
    town: 'KIMBALL',
    visibleInAddressBook: true
  },
  shipToParty: '0004202686',
  shipToWarehouse: {
    address: {
      country: {
        isocode: 'US',
        name: 'United States'
      },
      defaultAddress: false,
      email: '<EMAIL>',
      formattedAddress: '23625 365th Ave, South Dakota, Kimball, 57355-6004',
      id: '8860318433303',
      line1: '23625 365th Ave',
      phone: '************',
      postalCode: '57355-6004',
      region: {
        countryIso: 'US',
        isocode: 'US-SD',
        isocodeShort: 'SD',
        name: 'South Dakota'
      },
      shippingAddress: true,
      town: 'Kimball',
      visibleInAddressBook: true
    },
    code: '8V2U',
    name: 'SM Kimball SD - Brian Havlik',
    plantCode: '8V2U'
  },
  shipmentId: '0090008569',
  status: 'GOODS_ISSUED',
  statusText: 'Goods Issued',
  toLocation: {
    address: {
      country: {
        isocode: 'US',
        name: 'United States'
      },
      defaultAddress: false,
      formattedAddress: '23625 365th Ave, South Dakota, Kimball, 57355-6004',
      id: '8860319809559',
      line1: '23625 365th Ave',
      phone: '6057304000',
      postalCode: '57355-6004',
      region: {
        countryIso: 'US',
        isocode: 'US-SD',
        isocodeShort: 'SD',
        name: 'South Dakota'
      },
      shippingAddress: true,
      town: 'Kimball',
      visibleInAddressBook: true
    },
    code: '8V2U_WH01',
    locationCode: 'WH01',
    locationName: 'SM Kimball SD - Brian Havlik',
    plant: 'SM Kimball SD - Brian Havlik',
    type: 'SELLABLE'
  },
  toWarehouse: {
    address: {
      country: {
        isocode: 'US',
        name: 'United States'
      },
      defaultAddress: false,
      email: '<EMAIL>',
      formattedAddress: '23625 365th Ave, South Dakota, Kimball, 57355-6004',
      id: '8860318433303',
      line1: '23625 365th Ave',
      phone: '************',
      postalCode: '57355-6004',
      region: {
        countryIso: 'US',
        isocode: 'US-SD',
        isocodeShort: 'SD',
        name: 'South Dakota'
      },
      shippingAddress: true,
      town: 'Kimball',
      visibleInAddressBook: true
    },
    code: '8V2U',
    name: 'SM Kimball SD - Brian Havlik',
    plantCode: '8V2U'
  }
}
