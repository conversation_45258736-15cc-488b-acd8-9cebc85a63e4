import { ButtonProps } from '@element/react-button'
import { TypoCaption, TypoSubtitle } from '@element/react-typography'
import {
  Badge,
  ConsignmentExpandedRowTemplate,
  DesktopAndMobileQuery,
  GridList,
  HeaderType,
  List,
  ListProps,
  MessageWithAction,
  Table,
  TableMenu
} from '@gc/components'
import { consignmentTypes, orderDocumentTypes, OrderStatus } from '@gc/constants'
import {
  useCheckForSelectedAccount,
  useDataSource,
  useHeaderData,
  useLoadingContingency,
  useLocale,
  useMemoizedTranslation,
  useOrderStatus,
  usePortal,
  useSelectedAccount,
  useShipmentActions,
  useUpdateSortBy
} from '@gc/hooks'
import { setContingency, useConsignmentsQueries, useGlobalDispatch } from '@gc/redux-store'
import { ConsignmentEntry, FormattedConsignment, TableRow } from '@gc/types'
import {
  compareDateObjects,
  compareDatesWithFallback,
  fasteRoute,
  getDateFromUTC,
  getThemeColor,
  goToLink,
  hitsOnData
} from '@gc/utils'
import { ThunkDispatch, UnknownAction } from '@reduxjs/toolkit'
import { useCallback, useMemo } from 'react'

import { useShipmentListData } from '../../../hooks'
import styles from './ShipmentList.module.scss'

export interface ShipmentListProps {
  farmerSapId?: string[]
  fasteStoreKey: string
  searchTerm?: string
  tableTitle: string
  dispatch?: ThunkDispatch<object, undefined, UnknownAction>
  actionItems?: {
    value: string
    label: string
    onClick: () => void
  }[]
}

export function ShipmentList({ fasteStoreKey, tableTitle, searchTerm = '' }: Readonly<ShipmentListProps>) {
  const t = useMemoizedTranslation()
  const { sapAccountId } = useSelectedAccount()
  const { isMyCropCentre } = usePortal()

  const {
    data = [],
    isError,
    isFetching,
    isLoading,
    isSuccess,
    refetch: refetchShipments
  } = useShipmentListData({
    shipmentOptions: {},
    seedGrowthOptions: {
      reqBody: {
        growers: [`${sapAccountId}`],
        documentTypes: isMyCropCentre ? orderDocumentTypes.SHIPMENT_AU : orderDocumentTypes.SEED_GROWTH
      }
    }
  })

  const { dataSource: deliveries } = useDataSource<FormattedConsignment>(data, isSuccess, isFetching)

  const searchFun = (delivery: FormattedConsignment, searchStr: string) => {
    if (!searchStr) return true

    const matchingEntry = (entry: ConsignmentEntry) => {
      const productAccesor =
        delivery.lineOfBusiness === consignmentTypes.SEED_GROWTH ? entry.orderEntry?.product.name : entry.product.name

      return hitsOnData(searchStr, [
        productAccesor ?? '',
        entry.quantity?.toString(),
        entry.salesUnitOfMeasureCode,
        entry.batchName,
        entry.seedSize ?? ''
      ])
    }
    const matchingDelivery = (delivery: FormattedConsignment) =>
      hitsOnData(searchStr, [
        delivery.code ?? '',
        delivery.shipmentId ?? '',
        delivery.status,
        delivery.order?.code ?? '',
        delivery.statusText,
        delivery.translatedLOB ?? '',
        delivery.formattedShipTo,
        delivery.formattedPlannedShipDate,
        delivery?.formattedGoodsIssuedDate,
        delivery.toLocation?.locationCode ?? '',
        delivery.toLocation?.locationName ?? '',
        delivery.toLocation?.address?.town ?? '',
        delivery.toLocation?.address?.line1 ?? '',
        delivery.toLocation?.address?.postalCode ?? '',
        delivery.toLocation?.address?.region?.isocodeShort ?? '',
        delivery.fromLocation?.locationCode ?? '',
        delivery.fromLocation?.locationName ?? '',
        delivery.fromLocation?.address?.town ?? '',
        delivery.fromLocation?.address?.line1 ?? '',
        delivery.fromLocation?.address?.postalCode ?? '',
        delivery.fromLocation?.address?.region?.isocodeShort ?? '',
        delivery.entries?.some(matchingEntry) ? searchStr : false
      ])

    return matchingDelivery(delivery)
  }

  useUpdateSortBy({ fasteStoreKey, sortBy: [{ id: 'code', desc: true, order: 1 }] })

  useCheckForSelectedAccount(refetchShipments)

  const contingency = useLoadingContingency({
    isError,
    isLoading,
    isFetching,
    data: deliveries,
    refetch: refetchShipments,
    errorConfig: {
      header: t('inventory.shipments.api_error_header_msg.label'),
      description: t('common.error_msg_description.label')
    },
    loadingConfig: {
      message: t('inventory.shipments.loading_shipments.label')
    },
    noDataConfig: {
      header: t('inventory.shipments.no_data_header.msg'),
      description: t('inventory.shipments.no_data_description.msg')
    }
  })

  return (
    <GridList contingency={contingency}>
      <DesktopAndMobileQuery
        desktopContent={
          <ShipmentListDesktop
            searchFun={searchFun}
            tableTitle={tableTitle}
            deliveries={deliveries}
            fasteStoreKey={fasteStoreKey}
          />
        }
        mobileContent={
          <ShipmentListMobile
            searchFun={searchFun}
            deliveries={deliveries}
            searchTerm={searchTerm}
            fasteStoreKey={fasteStoreKey}
          />
        }
      />
    </GridList>
  )
}

function ShipmentListMobile({
  searchFun,
  deliveries,
  searchTerm,
  fasteStoreKey
}: Readonly<
  Omit<ShipmentListProps, 'tableTitle'> & {
    deliveries: FormattedConsignment[]
    searchFun: ListProps<FormattedConsignment>['searchFn']
  }
>) {
  const t = useMemoizedTranslation()
  const locale = useLocale()
  const { checkForCancelOrderStatus } = useOrderStatus()
  const dispatch = useGlobalDispatch()

  const goToShipmentDetails = useCallback(
    (shipmentId: string) => {
      const delivery = deliveries.filter((delivery) => delivery.code === shipmentId)[0]
      checkForCancelOrderStatus(delivery?.status)
        ? dispatch(
            setContingency({
              code: 'CANCELLED_ORDER',
              displayType: 'dialog',
              dialogProps: {
                title: t('shipment.cancel.dialog.label'),
                message: t('shipment.cancel.dialog.message', {
                  code: shipmentId,
                  date: getDateFromUTC(delivery.createdOnDateTime, locale)
                }),
                open: true,
                dismissButtonLabel: t('common.close.label'),
                hideActionButton: true
              }
            })
          )
        : fasteRoute(`/inventory/shipments/${shipmentId}`)
    },
    [deliveries, checkForCancelOrderStatus, dispatch, locale, t]
  )

  const dataToListItem = (shipmentDelivery: FormattedConsignment) => ({
    code: shipmentDelivery.code,
    overlineText: shipmentDelivery.statusText && (
      <div className={styles['overline-text-wrapper']}>
        <Badge labelText={shipmentDelivery.statusText} />
      </div>
    ),
    primaryText: <TypoSubtitle level={2}>{`${t('deliveries.delivery.label')} ${shipmentDelivery.code}`}</TypoSubtitle>,
    secondaryText: (
      <>
        <TypoCaption>{`${t('inventory.shipments.shipment.label')} ${shipmentDelivery.shipmentId ?? ''}`}</TypoCaption>
        <br />
        <TypoCaption>{`${t('common.to.label')} ${shipmentDelivery?.formattedShipTo ?? ''}`}</TypoCaption>
      </>
    )
  })

  return (
    <div className={styles['order-list-mobile']}>
      <List<FormattedConsignment>
        divider={true}
        data={deliveries}
        searchTerm={searchTerm}
        searchFn={searchFun}
        fasteStoreKey={fasteStoreKey}
        dataToListItem={dataToListItem}
        onAction={goToShipmentDetails}
        filterProps={{
          filters: [
            { title: t('inventory.shipments.line_of_business.label'), accessor: 'translatedLOB' },
            {
              title: `${t('deliveries.delivery.label')} ${t('common.status.label')}`,
              accessor: 'statusText'
            },
            { title: t('common.warehouse.label'), accessor: 'shipToAddress.town' }
          ]
        }}
        sortProps={{
          options: [
            {
              label: t('common.date_new-old.label'),
              columnName: 'createdOnDateTime',
              sortingType: 'desc'
            },
            {
              label: t('common.date_old-new.label'),
              columnName: 'createdOnDateTime',
              sortingType: 'asc'
            }
          ]
        }}
      />
    </div>
  )
}

function ShipmentListDesktop({
  tableTitle,
  fasteStoreKey,
  deliveries,
  searchFun
}: Readonly<
  ShipmentListProps & {
    deliveries: FormattedConsignment[]
    searchFun: (delivery: FormattedConsignment, searchStr: string) => boolean
  }
>) {
  const t = useMemoizedTranslation()
  const { isMyCropCentre } = usePortal()
  const locale = useLocale()
  const { actions, handlePGR } = useShipmentActions()

  ///////////////////////////////
  // CONSIGNMENT TRACKING DETAILS
  ///////////////////////////////
  const { useGetConsignmentsTrackingDetailsQuery } = useConsignmentsQueries()

  // Memoize delivery numbers to prevent array recreation on every render
  const deliveryNumbers = useMemo(() => deliveries?.map((consignment) => consignment.code) ?? [], [deliveries])
  const consignmentsTrackingDetails = useGetConsignmentsTrackingDetailsQuery(
    { deliveryNumbers },
    {
      skip: !deliveryNumbers.length,
      // Add caching options
      refetchOnMountOrArgChange: 600, // 10 minutes
      refetchOnFocus: false
    }
  )

  const deliveryItems = useMemo(
    () => consignmentsTrackingDetails?.data?.items ?? [],
    [consignmentsTrackingDetails.data]
  )

  const renderShipmentsExpandedRowTemplate = useCallback(
    ({ row }: Readonly<{ row: TableRow<FormattedConsignment> }>) => {
      const buttons: ButtonProps[] = []

      ///////////////////////////////
      // GOODS RECEIVE
      ///////////////////////////////
      if (row.original.status === 'GOODS_ISSUED' && !!handlePGR) {
        buttons.push({ label: t('shipments.goods_receive.label'), onClick: () => handlePGR(row.original) })
      }

      ///////////////////////////////
      // TRACKING NUMBER
      ///////////////////////////////
      const deliveryItemIndex = deliveryItems.findIndex((item) => item.deliveryNumber === row.original.code)
      const deliveryItem = deliveryItems[deliveryItemIndex] ?? {}
      const trackingUrl = deliveryItem.trackingUrl
      if (trackingUrl) {
        buttons.push({
          label: t('inventory.track_shipment.label'),
          leadingIcon: 'my_location',
          onClick: () => goToLink(trackingUrl)
        })
      }

      return (
        <div
          id='shipment_expansion_panel'
          data-testid='shipment_expansion_panel'
          className={styles.shipment_expansion_panel}
        >
          <ConsignmentExpandedRowTemplate
            data={row.original}
            buttons={buttons}
            usage={t('inventory.shipments.shipment.label').toLocaleLowerCase()}
          />
        </div>
      )
    },
    [deliveryItems, handlePGR, t]
  )

  const headerData = useHeaderData<HeaderType<FormattedConsignment>>([
    {
      header: t('inventory.shipments.delivery_id.label'),
      accessor: 'code',
      id: 'code',
      defaultSort: 'desc',
      defaultSortOrder: 1
    },
    ...(isMyCropCentre
      ? [
          {
            header: t('inventory.shipments.line_of_business.label'),
            accessor: 'lineOfBusiness',
            displayType: 'custom',
            displayTemplate: (statusText: string) =>
              statusText ? statusText.replace(/^CP/, 'Crop Protection') : 'Crop Protection',
            align: 'left'
          },
          {
            header: t('orders.order_id.label'),
            accessor: 'order.code',
            displayType: 'custom',
            displayTemplate: (statusText: string) => (statusText ? statusText.replace(/^BC-/, '') : ''),
            align: 'left'
          },
          {
            header: t('inventory.shipments.shipped_date.label'),
            accessor: 'formattedGoodsIssuedDate',
            id: 'formattedGoodsIssuedDate',
            sortType: (a: TableRow<FormattedConsignment>, b: TableRow<FormattedConsignment>) => {
              const compare = compareDatesWithFallback(a.original.goodsIssuedDate, b.original.goodsIssuedDate)
              // return default sort if no compare available
              if (compare === 0) {
                return a.original.code.localeCompare(b.original.code)
              }
              return compare
            },
            displayTemplate: (date: string) => date,
            align: 'center'
          }
        ]
      : [
          {
            header: t('inventory.shipments.shipment_id.label'),
            accessor: 'shipmentId'
          },
          { header: t('inventory.shipments.line_of_business.label'), accessor: 'translatedLOB', filterable: true },
          {
            header: `${t('common.ship.label')} ${t('common.to.label')}`,
            accessor: 'formattedShipTo',
            filterable: true
          },
          {
            header: t('inventory.shipments.planned_ship_date.label'),
            accessor: 'formattedPlannedShipDate',
            id: '_plannedShipDate',
            sortType: (a: TableRow<FormattedConsignment>, b: TableRow<FormattedConsignment>) =>
              compareDateObjects(a.original._plannedShipDate, b.original._plannedShipDate),
            displayTemplate: (date: string) => (date ? new Date(date).toLocaleDateString(locale.code) : ''),
            align: 'center'
          }
        ]),
    {
      filterable: true,
      header: `${t('deliveries.delivery.label')} ${t('common.status.label')}`,
      accessor: 'statusText',
      displayType: 'custom',
      displayTemplate: (statusText: string) =>
        statusText ? (
          <Badge
            themeColor={isMyCropCentre ? getThemeColor(statusText as OrderStatus) : undefined}
            labelText={statusText}
          />
        ) : null
    },
    ...(!isMyCropCentre
      ? [
          {
            header: t('common.actions.label'),
            accessor: (data: FormattedConsignment) => (
              <TableMenu<FormattedConsignment>
                listItems={actions}
                currentRow={data}
                disabled={data.lineOfBusiness === 'SEED_GROWTH'}
              />
            ),
            disableSortBy: true,
            excludeFromDownload: true,
            align: 'center'
          }
        ]
      : [])
  ])

  return (
    <Table<FormattedConsignment>
      paginated
      searchable
      enableCsvDownload
      title={tableTitle}
      data={deliveries}
      className={styles.shipment_table}
      headers={headerData}
      expandedRowTemplate={renderShipmentsExpandedRowTemplate}
      customSearchFn={searchFun}
      fasteStoreKey={fasteStoreKey}
      noContentMessage={
        <MessageWithAction
          messageHeader={t('common.no_matching_results_message_header_label')}
          messageDescription={t('inventory.shipments.no_matching_results_description.msg')}
          iconProps={{
            icon: 'info',
            variant: 'filled-secondary',
            className: 'gc-icon-info'
          }}
        />
      }
    />
  )
}

export default ShipmentList
