import { ConsignmentDetailsMobile, Header } from '@gc/components'
import { useConsignmentsQueries } from '@gc/redux-store'
import { Consignment } from '@gc/types'
import { fasteRoute } from '@gc/utils'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'

export function ReturnDetailsMobile() {
  const { t } = useTranslation()
  const { code } = useParams()
  const { useGetConsignmentDetailsQuery } = useConsignmentsQueries()
  const {
    data: returnDetails,
    isLoading,
    isError,
    isFetching,
    refetch: refetchReturnDetails
  } = useGetConsignmentDetailsQuery({
    consignmentId: code ?? ''
  })

  const handleClickBack = () => {
    fasteRoute('/inventory')
  }

  const getHeader = () => {
    return (
      <Header
        secText1={`${t('inventory.shipments.shipment.label')} ${returnDetails?.shipmentId}`}
        overlineBadgeProps={{ labelText: returnDetails?.statusText as string }}
        title={`${t('deliveries.delivery.label')} ${returnDetails?.code}`}
      />
    )
  }

  return (
    <ConsignmentDetailsMobile
      topBarProps={{
        title: t('returns.view_return.label'),
        onBackClick: handleClickBack
      }}
      contingencyStateProps={{
        isLoading: isLoading || isFetching,
        isError: isError,
        loadingMessage: t('common.loading.label'),
        errorMessageActionProps: {
          messageHeader: t('common.data_load_failed.label'),
          primaryButtonProps: {
            onClick: refetchReturnDetails
          }
        }
      }}
      header={getHeader()}
      consignmentDetails={returnDetails as Consignment}
      showFromAddress
      showToAddress
    />
  )
}

export default ReturnDetailsMobile
