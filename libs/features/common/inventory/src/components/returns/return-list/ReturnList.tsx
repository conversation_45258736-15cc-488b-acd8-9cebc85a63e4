import { Grid, GridCol, GridRow } from '@element/react-grid'
import { Typo<PERSON>aption, TypoSubtitle } from '@element/react-typography'
import {
  Alert,
  Badge,
  ConsignmentExpandedRowTemplate,
  HeaderType,
  List,
  LoadingAndContingencySection,
  MessageWithAction,
  Table
} from '@gc/components'
import { consignmentTypes, IS_DESKTOP, IS_MOBILE, OrderStatus } from '@gc/constants'
import {
  useCheckForSelectedAccount,
  useDataSource,
  useLocale,
  useMemoizedTranslation,
  useOrderStatus,
  usePortal,
  useUpdateSortBy
} from '@gc/hooks'
import { setContingency, useGlobalDispatch } from '@gc/redux-store'
import { Consignment, ConsignmentEntry, ConsignmentWarehouse, TableRow } from '@gc/types'
import { compareDateObjects, fasteRoute, getDateFromUTC, getThemeColor } from '@gc/utils'
import { isUndefined } from 'es-toolkit'
import { TFunction } from 'i18next'
import { useCallback, useMemo } from 'react'
import MediaQuery from 'react-responsive'

import { FormattedReturnConsignment, useReturnListData } from '../../../hooks/useReturnListData'
import styles from './ReturnList.module.scss'

export interface ReturnListProps {
  consignmentType: keyof typeof consignmentTypes
  tableTitle: string
  searchTerm?: string
  fasteStoreKey: string
}
function useHeaderData(t: TFunction) {
  const { isMyCropCentre } = usePortal()
  return useMemo<HeaderType<Consignment>[]>(
    () => [
      {
        header: t('inventory.shipments.delivery_id.label'),
        accessor: 'code',
        id: 'code',
        defaultSort: 'desc',
        defaultSortOrder: 1
      },
      ...(isMyCropCentre
        ? [
            {
              header: t('orders.order_id.label'),
              accessor: 'order.code',
              displayTemplate: (statusText: string) => (statusText ? statusText.replace(/^BC-/, '') : ''),
              align: 'left'
            }
          ]
        : [
            {
              header: t('inventory.shipments.shipment_id.label'),
              accessor: 'shipmentId'
            }
          ]),
      { header: t('returns.pickup.location.label'), accessor: 'formattedShipFrom', filterable: true },
      {
        header: t('returns.planned.pickdate.label'),
        accessor: 'formattedPlannedShipDate',
        align: 'auto',
        id: '_plannedShipDate',
        displayTemplate: (date: string) => date,
        sortType: (a: TableRow<FormattedReturnConsignment>, b: TableRow<FormattedReturnConsignment>) =>
          compareDateObjects(a.original._plannedShipDate, b.original._plannedShipDate)
      },
      {
        header: `${t('deliveries.delivery.label')} ${t('common.status.label')}`,
        accessor: 'statusText',
        displayType: 'custom',
        displayTemplate: (statusText: string) =>
          statusText ? (
            <Badge
              themeColor={isMyCropCentre ? getThemeColor(statusText as OrderStatus) : undefined}
              labelText={statusText}
            />
          ) : null,
        filterable: true
      }
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )
}

export function ReturnList({ consignmentType, fasteStoreKey, tableTitle, searchTerm = '' }: Readonly<ReturnListProps>) {
  const t = useMemoizedTranslation()
  const locale = useLocale()
  const { checkForCancelOrderStatus } = useOrderStatus()
  const dispatch = useGlobalDispatch()
  const headerData = useHeaderData(t)
  const { isMyCropCentre } = usePortal()
  const {
    data = [],
    error,
    isFetching,
    isLoading,
    isSuccess,
    refetch: refetchReturns
  } = useReturnListData(consignmentType, {
    updatePartialConsignments: (deliveries: Consignment[] = []) => {
      setDeliveries(deliveries)
    }
  })

  const { dataSource: deliveries, setTmpData: setDeliveries } = useDataSource<Consignment>(data, isSuccess, isFetching)

  const hasError = !isUndefined(error)

  const isDataLoading = useMemo(() => {
    return deliveries.length === 0 && (isFetching || isLoading)
  }, [deliveries, isFetching, isLoading])

  const hasNoData = useMemo(() => {
    return deliveries.length === 0 && searchTerm.trim().length === 0
  }, [deliveries, searchTerm])

  const searchFun = (delivery: Consignment, searchStr: string) => {
    const hits = (value: string) => value?.toLowerCase().includes(searchStr)

    const matchWarehouse = (warehouse: ConsignmentWarehouse) => {
      const combinedCodeName = `${warehouse.locationCode} - ${warehouse.locationName}`

      return (
        hits(warehouse?.locationCode ?? '') ||
        hits(warehouse?.locationName ?? '') ||
        hits(combinedCodeName) ||
        hits(warehouse?.address?.town ?? '') ||
        hits(warehouse?.address?.line1 ?? '') ||
        hits(warehouse?.address?.postalCode ?? '') ||
        hits(warehouse?.address?.region?.isocodeShort ?? '')
      )
    }

    const matchingDelivery = (delivery: Consignment) =>
      hits(delivery.code) ||
      hits(delivery.shipmentId ?? '') ||
      hits(delivery.order.code ?? '') ||
      hits(delivery.status) ||
      hits(delivery.statusText) ||
      hits(delivery.lineOfBusiness ?? '') ||
      hits(getDateFromUTC(new Date(delivery.plannedShipDate), locale)) ||
      (!isMyCropCentre && matchWarehouse(delivery.toLocation)) ||
      matchWarehouse(delivery?.fromLocation) ||
      (delivery.entries ? delivery.entries.some(matchingEntry) : false)

    const matchingEntry = (entry: ConsignmentEntry) =>
      hits(entry.product.name) ||
      hits(entry.quantity?.toString()) ||
      hits(entry.salesUnitOfMeasureCode) ||
      hits(entry.batchName) ||
      hits(entry.seedSize ?? '')

    return matchingDelivery(delivery)
  }

  const goToReturnDetails = useCallback(
    (shipmentId: string) => {
      const delivery = deliveries.filter((transfer) => transfer.code === shipmentId)[0]
      checkForCancelOrderStatus(delivery?.status)
        ? dispatch(
            setContingency({
              code: 'CANCELLED_ORDER',
              displayType: 'dialog',
              dialogProps: {
                title: t('return.cancel.dialog.label'),
                message: t('return.cancel.dialog.message', {
                  code: shipmentId,
                  date: getDateFromUTC(delivery.createdOnDateTime, locale)
                }),
                open: true,
                dismissButtonLabel: t('common.close.label'),
                hideActionButton: true
              }
            })
          )
        : fasteRoute(`/inventory/returns/${shipmentId}`)
    },
    [checkForCancelOrderStatus, deliveries, dispatch, locale, t]
  )

  const dataToListItem = (shipmentDelivery: Consignment) => ({
    code: shipmentDelivery.code,
    trailingBlock: (
      <TypoCaption>
        {t('returns.planned.pickup.label')}
        <br />
        {getDateFromUTC(new Date(shipmentDelivery.plannedShipDate), locale)}
      </TypoCaption>
    ),
    overlineText: shipmentDelivery.statusText && (
      <div className={styles['overline-text-wrapper']}>
        <Badge labelText={shipmentDelivery.statusText} />
      </div>
    ),
    primaryText: <TypoSubtitle level={2}>{`${t('deliveries.delivery.label')} ${shipmentDelivery.code}`}</TypoSubtitle>,
    secondaryText: (
      <>
        <TypoCaption>{`${t('inventory.shipments.shipment.label')} ${shipmentDelivery.shipmentId ?? ''}`}</TypoCaption>
        <br />
        <TypoCaption>{`${t('common.to.label')} ${shipmentDelivery.toLocation?.locationCode ? shipmentDelivery.toLocation?.locationCode + ' - ' + shipmentDelivery.toLocation?.locationName : ''}`}</TypoCaption>
      </>
    )
  })

  const renderReturnsExpandedRowTemplate = useCallback(
    ({ row }: Readonly<{ row: TableRow<Consignment> }>) => {
      return (
        <div id='seedpro_returns_expansion_panel' className={styles.seedpro_returns_expansion_panel}>
          <ConsignmentExpandedRowTemplate data={row.original} usage={t('farmers.return.label').toLocaleLowerCase()} />
        </div>
      )
    },
    [t]
  )

  useUpdateSortBy({ fasteStoreKey, sortBy: [{ id: 'code', desc: true, order: 1 }] })

  useCheckForSelectedAccount(refetchReturns)

  const { loadingMessage, errorHeader, noDataHeader } = useMemo(() => {
    switch (consignmentType) {
      case consignmentTypes.AGENTRETURN:
        return {
          loadingMessage: t('inventory.returns.loading_agency_returns.label'),
          errorHeader: t('inventory.returns.agency_error_header.label'),
          noDataHeader: t('inventory.returns.agency_no_data_header.msg')
        }
      case consignmentTypes.FARMERRETURN:
        return {
          loadingMessage: t('inventory.returns.loading_farmer_returns.label'),
          errorHeader: t('inventory.returns.farmer_error_header.label'),
          noDataHeader: t('inventory.returns.farmer_no_data_header.msg')
        }
      default:
        return {
          loadingMessage: t('returns.loading_seedpro_returns_message.label'),
          errorHeader: t('inventory.returns.api_error_header_msg.label'),
          noDataHeader: t('inventory.returns.no_data_header.msg')
        }
    }
  }, [consignmentType, t])
  return (
    <Grid className={styles.grid}>
      {deliveries.length ? (
        <GridRow className={styles.content}>
          <GridCol
            desktopCol={12}
            phoneCol={4}
            tabletCol={8}
            verticalAlign={'middle'}
            className={styles.message_container}
          >
            <Alert
              className={styles.alert_message}
              type='info'
              variant='tonal'
              title={t('returns.info_title.msg')}
              description={t('returns.info_description.msg')}
            />
          </GridCol>
        </GridRow>
      ) : (
        ''
      )}

      <GridRow className={styles.content}>
        <GridCol
          desktopCol={12}
          phoneCol={4}
          tabletCol={8}
          verticalAlign={!deliveries.length || hasError ? 'middle' : 'top'}
          className={hasNoData || hasError ? styles['container-contingency'] : ''}
        >
          <LoadingAndContingencySection
            hasError={hasError}
            isLoading={isDataLoading}
            hasData={!hasNoData}
            errorDescription={t('common.error_msg_description.label')}
            errorHeader={errorHeader}
            loadingMessage={loadingMessage}
            noDataDescription={t('inventory.returns.no_data_description.msg')}
            noDataHeader={noDataHeader}
            onRetry={refetchReturns}
          >
            <>
              <MediaQuery minWidth={IS_DESKTOP}>
                <Table<Consignment>
                  title={tableTitle}
                  data={deliveries}
                  paginated
                  searchable
                  enableCsvDownload
                  className={styles.seedpro_returns_table}
                  headers={headerData}
                  expandedRowTemplate={renderReturnsExpandedRowTemplate}
                  customSearchFn={searchFun}
                  fasteStoreKey={fasteStoreKey}
                  noContentMessage={
                    <MessageWithAction
                      messageHeader={t('common.no_matching_results_message_header_label')}
                      messageDescription={t('inventory.shipments.no_matching_results_description.msg')}
                      iconProps={{
                        icon: 'info',
                        variant: 'filled-secondary',
                        className: 'gc-icon-info'
                      }}
                    />
                  }
                />
              </MediaQuery>
              <MediaQuery maxWidth={IS_MOBILE}>
                {/* Note: Uncomment this code when need action fab button for mobile
                    <ActionMenuButton
                      leadingIcon='add'
                      buttonLabel={t('common.actions.label')}
                      actionItems={[]}
                      data={deliveries}
                    /> */}
                <div className={styles['order-list-mobile']}>
                  {deliveries.length > 0 ? (
                    <List<Consignment>
                      divider={true}
                      onAction={goToReturnDetails}
                      data={deliveries}
                      searchTerm={searchTerm}
                      searchFn={searchFun}
                      fasteStoreKey={fasteStoreKey}
                      dataToListItem={dataToListItem}
                      sortProps={{
                        options: [
                          {
                            label: t('common.date_new-old.label'),
                            columnName: 'plannedShipDate',
                            sortingType: 'asc'
                          },
                          {
                            label: t('common.date_old-new.label'),
                            columnName: 'plannedShipDate',
                            sortingType: 'desc'
                          }
                        ]
                      }}
                      filterProps={{
                        filters: [
                          {
                            title: `${t('deliveries.delivery.label')} ${t('common.status.label')}`,
                            accessor: 'statusText'
                          }
                        ]
                      }}
                    />
                  ) : (
                    <MessageWithAction
                      messageHeader={t('common.no_results_message_header_label')}
                      messageDescription={t('common.no_results_message_description')}
                      iconProps={{
                        icon: 'info',
                        variant: 'filled-secondary',
                        className: 'gc-icon-info'
                      }}
                    />
                  )}
                </div>
              </MediaQuery>
            </>
          </LoadingAndContingencySection>
        </GridCol>
      </GridRow>
    </Grid>
  )
}

export default ReturnList
