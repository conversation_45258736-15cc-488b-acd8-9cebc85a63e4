import { render } from '@testing-library/react'
import { Provider } from 'react-redux'

import { store } from '../../store'
import Returns from './Returns'

// Mocks
const mockHandleTabActivated = jest.fn()
let mockCurrentTab = 0

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  usePortal: jest.fn(),
  useIsMobile: jest.fn(),
  useTabs: () => ({
    currentTab: mockCurrentTab,
    handleTabActivated: mockHandleTabActivated,
    ComponentTabs: () => (
      <button data-testid='tab-1' name='Next' onClick={mockHandleTabActivated(2)}>
        Tab 1
      </button>
    )
  })
}))

describe('Returns desktop view AU', () => {
  beforeEach(() => {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { usePortal } = require('@gc/hooks')
    usePortal.mockReturnValue({
      isMyCrop: false,
      isMyCropV2: false,
      isSMS: false,
      isArrow: false,
      isMyCropCentre: true,
      isAurora: false
    })
  })
  it('should render successfully', () => {
    const { baseElement } = render(
      <Provider store={store}>
        <Returns />
      </Provider>
    )
    expect(baseElement).toBeTruthy()
  })
})

describe('Return for AU users', () => {
  beforeEach(() => {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { usePortal, useIsMobile } = require('@gc/hooks')
    usePortal.mockReturnValue({
      isMyCrop: false,
      isMyCropV2: false,
      isSMS: false,
      isArrow: false,
      isMyCropCentre: true,
      isAurora: false
    })
    useIsMobile.mockReturnValue(false)
  })
  it('should render Agent return layout for AU users', async () => {
    mockCurrentTab = 0
    const { baseElement } = render(
      <Provider store={store}>
        <Returns />
      </Provider>
    )
    expect(baseElement).toBeTruthy()
  })
  it('should render farmer return layout for AU users', async () => {
    mockCurrentTab = 1
    const { baseElement } = render(
      <Provider store={store}>
        <Returns />
      </Provider>
    )
    expect(baseElement).toBeTruthy()
  })
  it('should render transfer return layout for AU users', async () => {
    mockCurrentTab = 2
    const { baseElement } = render(
      <Provider store={store}>
        <Returns />
      </Provider>
    )
    expect(baseElement).toBeTruthy()
  })
  it('should render default case return layout for AU users', async () => {
    mockCurrentTab = 5
    const { baseElement } = render(
      <Provider store={store}>
        <Returns />
      </Provider>
    )
    expect(baseElement).toBeTruthy()
  })
})

describe('Return for AU users for mobile view', () => {
  beforeEach(() => {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { usePortal, useIsMobile } = require('@gc/hooks')
    usePortal.mockReturnValue({
      isMyCrop: true,
      isMyCropV2: true,
      isSMS: true,
      isArrow: true,
      isMyCropCentre: true,
      isAurora: true
    })
    useIsMobile.mockReturnValue(true)
  })
  it('should render Agent return layout for AU users', async () => {
    mockCurrentTab = 0
    const { baseElement } = render(
      <Provider store={store}>
        <Returns />
      </Provider>
    )
    expect(baseElement).toBeTruthy()
  })
})
