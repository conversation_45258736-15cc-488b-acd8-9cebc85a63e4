import { TabBar } from '@element/react-tabs'
import { Header } from '@gc/components'
import { consignmentTypes, IS_DESKTOP } from '@gc/constants'
import { useGcPortalConfig, useIsMobile, usePortal, useTabs } from '@gc/hooks'
import { fasteRoute, getFasteStoreKey } from '@gc/utils'
import { useCallback, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import MediaQuery from 'react-responsive'

import ReturnList from './return-list/ReturnList'
import styles from './Returns.module.scss'

export function Returns() {
  const { t } = useTranslation()
  const { isMyCropCentre } = usePortal()

  const isMobile = useIsMobile()
  const { returnTabs } = useGcPortalConfig()
  const { currentTab, setCurrentTab, ComponentTabs: ReturnTabs } = useTabs(returnTabs)

  useEffect(() => {
    if (isMobile) {
      fasteRoute('/inventory')
    }
  }, [isMobile])
  const handleTabActivated = (index: number) => {
    window.history.replaceState({ ...window.history.state, tab: index }, '')
    setCurrentTab(index)
  }

  const renderReturns = useCallback(
    (consignmentType: keyof typeof consignmentTypes, keySuffix = '') => {
      return (
        <ReturnList
          searchTerm=''
          consignmentType={consignmentType}
          tableTitle={t('returns.returns.label')}
          fasteStoreKey={`${getFasteStoreKey('inventory', 'returns')}_${keySuffix}`}
        />
      )
    },
    [t]
  )

  const renderReturnList = useMemo(() => {
    switch (currentTab) {
      case 0:
        return renderReturns(isMyCropCentre ? consignmentTypes.AGENTRETURN : consignmentTypes.RETURN)
      case 1:
        return renderReturns(consignmentTypes.FARMERRETURN, 'farmerReturn')
      case 2:
        return renderReturns(consignmentTypes.TRANSFER, 'transfers')
      default:
        return null
    }
  }, [currentTab, isMyCropCentre, renderReturns])

  return (
    <div className={styles['container']}>
      <MediaQuery minWidth={IS_DESKTOP}>
        <div className={styles.header}>
          <Header title={t('returns.seedpro_returns.label')} />
        </div>
      </MediaQuery>
      <MediaQuery minWidth={IS_DESKTOP}>
        {isMyCropCentre && (
          <TabBar
            clustered={true}
            clusterAlign='start'
            elevated={false}
            variant='surface'
            activeTabIndex={currentTab}
            stacked={false}
            onTabActivated={handleTabActivated}
          >
            <ReturnTabs />
          </TabBar>
        )}
        <div key={currentTab}>{renderReturnList}</div>
      </MediaQuery>
    </div>
  )
}

export default Returns
