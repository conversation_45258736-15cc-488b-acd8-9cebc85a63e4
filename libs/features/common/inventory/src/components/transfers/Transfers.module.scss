.grid {
  padding: 0px !important;
}

.container {
  @media (min-width: 1024px) {
    padding: 48px 80px;
    position: relative;
  }
}

.tabs {
  @media (min-width: 1024px) {
    padding-left: 80px;
  }
}

.header {
  @media (min-width: 1024px) {
    margin-bottom: 48px;
  }
}

@media (min-width: 720px) {
  :global(.lmnt-table__cell-content) {
    min-height: 48px;
    height: auto !important;
  }
}
