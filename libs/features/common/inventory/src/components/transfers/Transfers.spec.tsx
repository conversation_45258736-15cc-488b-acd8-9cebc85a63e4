import { setUpStore } from '@gc/redux-store'
import { render } from '@testing-library/react'
import { Provider } from 'react-redux'

import Transfers from './Transfers'

let mockStore: ReturnType<typeof setUpStore>

beforeEach(() => {
  mockStore = setUpStore(
    {
      app: {}
    },
    {
      injectDiscountsApi: true,
      injectOrdersApi: true,
      injectConfigDataApi: true,
      injectProductsApi: true,
      injectConsignmentsApi: true,
      injectInventoryApi: true,
      useGlobalMiddleware: true,
      middlewareOpts: { serializableCheck: false }
    }
  )
})

// Example usage of the provider in a test
it('renders Transfers component correctly', () => {
  render(
    <Provider store={mockStore}>
      <Transfers />
    </Provider>
  )
})
