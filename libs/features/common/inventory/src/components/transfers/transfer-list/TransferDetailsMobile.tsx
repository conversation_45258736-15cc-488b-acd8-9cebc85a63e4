/* eslint-disable @nx/enforce-module-boundaries */
import { ActionMenuButton, ConsignmentDetailsMobile, Header } from '@gc/components'
import { useLocale, useTransferActions } from '@gc/hooks'
import { useConsignmentsQueries } from '@gc/redux-store'
import { Consignment } from '@gc/types'
import { fasteRoute, fetchStore, getDateFromUTC } from '@gc/utils'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'

export function TransferDetailsMobile() {
  const { t } = useTranslation()
  const { code } = useParams()
  const locale = useLocale()
  const selectedAccount = fetchStore('selectedAccount')
  const { useGetConsignmentDetailsQuery } = useConsignmentsQueries()
  const { actions: transferActions } = useTransferActions()
  const {
    data: shipmentDetails,
    isLoading,
    isFetching,
    isError,
    refetch: refetchShipmentDetails
  } = useGetConsignmentDetailsQuery({
    consignmentId: code ?? ''
  })

  const handleClickBack = () => {
    fasteRoute('/inventory')
  }

  const getHeader = () => {
    return (
      <Header
        secText1={
          shipmentDetails?.createdOnDateTime &&
          `${t('common.created.label')} ${getDateFromUTC(new Date(shipmentDetails?.createdOnDateTime), locale)}`
        }
        overlineBadgeProps={{
          labelText: shipmentDetails?.statusText as string,
          themeColor:
            selectedAccount?.sapAccountId === shipmentDetails?.grower?.uid && shipmentDetails?.status === 'GOODS_ISSUED'
              ? 'orange'
              : undefined
        }}
        title={`${t('deliveries.delivery.label')} ${shipmentDetails?.code}`}
      />
    )
  }

  return (
    <>
      <ConsignmentDetailsMobile
        topBarProps={{
          title: t('inventory.transfer_details.label'),
          onBackClick: handleClickBack
        }}
        contingencyStateProps={{
          isLoading: isLoading || isFetching,
          loadingMessage: t('inventory.loading_transfer_details_message.label'),
          isError: isError,
          errorMessageActionProps: {
            messageHeader: t('common.data_load_failed.label'),
            primaryButtonProps: {
              onClick: refetchShipmentDetails
            }
          }
        }}
        header={getHeader()}
        consignmentDetails={shipmentDetails as Consignment}
        showFromAddress
        showToAddress
        productListProps={{
          cardTitle: t('inventory.products_on_transfer.label')
        }}
      />
      {!!transferActions.length && !!shipmentDetails && (
        <ActionMenuButton
          leadingIcon='add'
          buttonLabel={t('common.actions.label')}
          actionItems={transferActions}
          data={shipmentDetails}
        />
      )}
    </>
  )
}

export default TransferDetailsMobile
