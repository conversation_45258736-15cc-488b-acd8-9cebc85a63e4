.transfer_table {
  tr:has(button[id='arrow_up']) {
    background: #f5f5f5;
  }
  tr:has(div[id='transfer_expansion_panel']) {
    border-top: none !important;
  }
  .transfer_expansion_panel {
    width: 100%;
    background-color: #f5f5f5;
  }
  :global(.lmnt-table__cell--nestable) {
    padding: 0px;
  }
  tr > :nth-child(1) {
    width: 56px;
    padding-right: 0px;
  }
  .transfer_expansion_panel {
    tr > :nth-child(1) {
      width: auto;
    }
    tr:last-child td {
      font-weight: 500;
    }
  }
}
.transfer_list_mobile {
  width: 100%;
  :global(.lmnt-list-item--wrap) {
    min-height: 85px !important;
  }
  .overline-text-wrapper {
    margin: -15px 0px;
    text-transform: capitalize;
  }
}

.grid {
  padding: 0px;
  @media (min-width: 720px) and (max-width: 1023px) {
    padding: 16px 24px;
    position: relative;
  }
}

.header {
  @media (min-width: 1024px) {
    padding: 48px 80px;
  }
}

.container-contingency {
  // TODO: This number needs to be tweaked for different devices once we have the final portal ready with any footer!
  min-height: 480px;
}

.overline-text-wrapper {
  margin: -15px 0px;
  text-transform: capitalize;
}

.bottom-sheet {
  width: 100%;

  @media (min-width: 720px) {
    width: 80%;
    left: 10% !important;
  }
}

.filter-container-mobile {
  position: absolute;
  width: -webkit-fill-available;
  height: 64px;
  line-height: 64px;
  top: 0px;
  margin: 0px 16px;
}

.delivery-list-mobile {
  width: 100%;
  :global(.lmnt-list-item--wrap) {
    min-height: 85px !important;
  }
}

.sorting-chip-container {
  float: right;
  @media (min-width: 720px) {
    margin-right: 16px;
  }
}

@media (max-width: 719px) {
  .filter-container-mobile {
    margin: 0px 16px;
  }
}

.cursor-pointer {
  cursor: pointer;
}

@media (min-width: 720px) {
  :global(.lmnt-table__cell-content) {
    min-height: 48px;
    height: auto !important;
  }
}
