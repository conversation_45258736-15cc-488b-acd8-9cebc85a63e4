import { actAwait, render, screen, within } from '@gc/utils'
import { act, fireEvent } from '@testing-library/react'
import { http, HttpResponse } from 'msw'

import server from '../../../mocks/server'
import { transfers } from '../../../mocks/transfers'
import { setUpStore } from '../../../store'
import TransferList from './TransferList'

const translations: { [key: string]: string } = {
  'common.cancel.label': 'Cancel',
  'common.apply.label': 'Apply',
  'common.edit.label': 'Edit',
  'common.status.label': 'Status',
  'common.all_filters.label': 'All Filters',
  'common.to.label': 'To',
  'common.date_new-old.label': 'Newest to Oldest',
  'common.ship_from.label': 'Ship From',
  'common.ship_to.label': 'Ship To',
  'common.created.label': 'Created',
  'common.date_created.label': 'Date Created',

  'deliveries.delivery.label': 'Delivery',
  'deliveries.delivery_date.label': 'Delivery Date',
  'deliveries.delivery_status.label': 'Delivery Status',

  'inventory.loading_transfer_message.label': 'Loading transfers...',
  'inventory.seedpro_name.label': 'SeedPro Name',
  'inventory.shipments.delivery_id.label': 'Delivery ID',
  'inventory.transfer_status.label': 'Transfer Status',
  'inventory.transfer_type.label': 'Transfer Type',
  'inventory.transfers.api_error_header_msg.label': 'Failed to load transfers',
  'inventory.transfers.line_of_business.label': 'Line of Business',
  'inventory.transfers.no_data_description.msg': 'No transfers found in the system',
  'inventory.transfers.no_data_header.msg': 'No transfers found',
  'inventory.transfers.planned_transfer_date.label': 'Planned Transfer Date',
  'inventory.transfers.transfer_id.label': 'Transfer ID'
}

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (str: string) => translations[str] ?? str
  })
}))

const mockPortalConfig = {
  ordersModule: {
    orderCancelStatuses: ['CANCELLED', 'CANCELLING']
  },
  gcPortalConfig: {
    seedYear: '2024',
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ],
    pageSize: {
      transfers: 50
    },
    documentTypes: {
      transfers: ['TRANSFER']
    }
  },
  inventoryModule: {
    transferActions: [
      { type: 'goodsIssue', enableStatues: ['STAGED'] },
      { type: 'goodsReceive', enableStatues: ['GOODS_ISSUED'] }
    ]
  }
}

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useScreenRes: () => 5
}))

jest.mock('../../../../../../../hooks/src/useFasteStore', () => ({
  ...jest.requireActual('../../../../../../../hooks/src/useFasteStore'),
  usePortalConfig: () => mockPortalConfig,
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig,
  useCheckForSelectedAccount: (refetch: () => void) => jest.fn()
}))

describe('TransferList', () => {
  const defaultProps = {
    tableTitle: 'Transfers',
    fasteStoreKey: 'test-store'
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  describe('Mobile List', () => {
    function renderMobileTransferList() {
      return render(<TransferList {...defaultProps} />, {
        store: setUpStore(),
        width: 900
      })
    }

    it('Renders the TransferList component with loading state', async () => {
      const { baseElement } = renderMobileTransferList()
      expect(baseElement).toBeTruthy()
      expect(screen.getByText('Loading transfers...')).toBeInTheDocument()
    })

    it('Renders the TransferList component with error state', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.error()))

      const { baseElement } = renderMobileTransferList()
      await act(async () => {
        await actAwait(100)
      })

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('Failed to load transfers')).toBeInTheDocument()
    })

    it('Renders the TransferList component with no data', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ data: { consignments: [] } })))

      const { baseElement } = renderMobileTransferList()
      await actAwait(100)

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('No transfers found')).toBeInTheDocument()
      expect(screen.getByText('No transfers found in the system')).toBeInTheDocument()
    })

    it('displays transfers when data is available', async () => {
      renderMobileTransferList()

      await act(async () => {
        await actAwait(100)
      })

      // Check list is rendered
      expect(screen.getAllByRole('listbox')).toBeDefined()

      // Check filters are rendered
      expect(screen.getByTestId('Filters')).toBeDefined()

      // Check list items are rendered
      expect(screen.getAllByText('Staged')).toHaveLength(4)
      expect(screen.getByText('Goods Issued')).toBeDefined()

      expect(screen.getByText('Ship From GRAIN STORAGE SYSTEMS LLC')).toBeDefined()
      expect(screen.getAllByText('Ship To JASON KANE')).toHaveLength(3)
      expect(screen.getByText('Ship From JASON KANE')).toBeDefined()
      expect(screen.getByText('Delivery 0801766484')).toBeDefined()
      expect(screen.getByText('Delivery 0801766483')).toBeDefined()
      expect(screen.getByText('Delivery 0801766479')).toBeDefined()
      expect(screen.getByText('Delivery 0801766464')).toBeDefined()
      expect(screen.getByText('Delivery 0801766462')).toBeDefined()

      expect(screen.getAllByText('Created 01/06/2025')).toHaveLength(5)
    })

    it('filters transfers by status', async () => {
      renderMobileTransferList()
      await act(async () => {
        await actAwait()
      })

      const filtersButton = screen.getByTestId('Filters')
      expect(filtersButton).toBeDefined()

      await act(async () => fireEvent.click(filtersButton))

      const goodsIssuedOptions = screen.getAllByRole('option', { name: 'Goods Issued' })
      expect(goodsIssuedOptions.length).toBeGreaterThan(0)
      const goodsIssuedOption = goodsIssuedOptions[0]

      await act(async () => fireEvent.click(goodsIssuedOption))
      await act(async () => fireEvent.click(screen.getByText('Apply')))

      expect(screen.getByText('Delivery 0801766479')).toBeDefined()
      expect(screen.queryByText('Delivery 0801766484')).not.toBeInTheDocument()
      expect(screen.queryByText('Delivery 0801766483')).not.toBeInTheDocument()
      expect(screen.queryByText('Delivery 0801766464')).not.toBeInTheDocument()
      expect(screen.queryByText('Delivery 0801766462')).not.toBeInTheDocument()
    })
  })

  describe('Desktop List', () => {
    it('Renders the TransferList component with loading state', async () => {
      const { baseElement } = render(<TransferList {...defaultProps} />, { store: setUpStore() })

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('Loading transfers...')).toBeInTheDocument()
    })

    it('Renders the TransferList component with error state', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.error()))

      const { baseElement } = render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait()

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('Failed to load transfers')).toBeInTheDocument()
    })

    it('displays transfers when data is available', async () => {
      render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait(100)

      // debug(baseElement, 1000000)

      // Check table is rendered
      expect(screen.getAllByRole('table')).toBeDefined()

      // Check filters are rendered
      expect(screen.getByTestId('All Filters')).toBeDefined()
      expect(screen.getByTestId('Transfer Type')).toBeDefined()
      expect(screen.getByTestId('Transfer Status')).toBeDefined()

      // Shows Table Headers
      expect(screen.getByRole('columnheader', { name: 'Transfer Type' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'SeedPro Name' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Delivery ID' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Date Created' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Transfer Status' })).toBeInTheDocument()

      // Shows Table Rows
      expect(screen.getAllByRole('cell', { name: 'Ship From' })).toHaveLength(2)
      expect(screen.getByRole('cell', { name: 'GRAIN STORAGE SYSTEMS LLC' })).toBeDefined()
      expect(screen.getByRole('cell', { name: '0801766484' })).toBeDefined()
      expect(screen.getAllByRole('cell', { name: '01/06/2025' })).toHaveLength(5)
      expect(screen.getAllByRole('cell', { name: 'Staged' })).toHaveLength(4)

      expect(screen.getAllByRole('cell', { name: 'Ship To' })).toHaveLength(3)
      expect(screen.getAllByRole('cell', { name: 'JASON KANE' })).toHaveLength(4)
      expect(screen.getByRole('cell', { name: '0801766483' })).toBeDefined()
      expect(screen.getByRole('cell', { name: 'Goods Issued' })).toBeDefined()
    })

    it('filters transfers by status', async () => {
      render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait()

      const transferStatusFilter = screen.getByTestId('Transfer Status')
      expect(transferStatusFilter).toBeDefined()

      await act(async () => fireEvent.click(transferStatusFilter))

      const goodsIssuedOptions = screen.getAllByRole('option', { name: 'Goods Issued' })
      expect(goodsIssuedOptions.length).toBeGreaterThan(0)
      const goodsIssuedOption = goodsIssuedOptions[0]

      const applyButtons = screen.getAllByLabelText('Apply')
      expect(applyButtons.length).toBeGreaterThan(0)
      const applyButton = applyButtons[0]

      await act(async () => fireEvent.click(goodsIssuedOption))
      await act(async () => fireEvent.click(applyButton))

      expect(screen.getByRole('cell', { name: 'Goods Issued' })).toBeDefined()
      expect(screen.queryByRole('cell', { name: 'Staged' })).not.toBeInTheDocument()
    })

    it('sorts transfers by code', async () => {
      render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait()

      const id1 = screen.getByRole('cell', { name: '0801766484' })
      const id2 = screen.getByRole('cell', { name: '0801766483' })

      expect(id1).toBeDefined()
      expect(id2).toBeDefined()
      expect(id1.compareDocumentPosition(id2)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    })

    it('displays appropriate actions when staged and transfer type is SHIP TO', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ consignments: [transfers.consignments[1]] })))

      const { getByRole } = render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait(100)
      fireEvent.click(screen.getAllByRole('button', { name: 'more_horiz' })[0])

      const disabledClass = 'mdc-list-item--disabled'
      expect(getByRole('option', { name: 'deliveries.goods_issue.label' }).className).not.toMatch(disabledClass)
      expect(getByRole('option', { name: 'transfers.goods_receive.label' }).className).toMatch(disabledClass)
    })

    it('displays appropriate actions when goods issued and transfer type is SHIP TO', async () => {
      server.use(
        http.post(/\/consignments/, () =>
          HttpResponse.json({ consignments: [{ ...transfers.consignments[1], status: 'GOODS_ISSUED' }] })
        )
      )

      const { getByRole } = render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait(100)
      fireEvent.click(screen.getAllByRole('button', { name: 'more_horiz' })[0])

      const disabledClass = 'mdc-list-item--disabled'
      expect(getByRole('option', { name: 'deliveries.goods_issue.label' }).className).toMatch(disabledClass)
      expect(getByRole('option', { name: 'transfers.goods_receive.label' }).className).toMatch(disabledClass)
    })

    it('displays appropriate actions status is other than good issued or staged', async () => {
      server.use(
        http.post(/\/consignments/, () =>
          HttpResponse.json({ consignments: [{ ...transfers.consignments[1], status: 'XYZ' }] })
        )
      )

      const { getByRole } = render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait(100)
      fireEvent.click(screen.getAllByRole('button', { name: 'more_horiz' })[0])

      const disabledClass = 'mdc-list-item--disabled'
      expect(getByRole('option', { name: 'deliveries.goods_issue.label' }).className).toMatch(disabledClass)
      expect(getByRole('option', { name: 'transfers.goods_receive.label' }).className).toMatch(disabledClass)
    })

    it('displays appropriate actions when staged and transfer type is SHIP FROM', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ consignments: [transfers.consignments[0]] })))

      const { getByRole } = render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait(100)
      fireEvent.click(screen.getAllByRole('button', { name: 'more_horiz' })[0])

      const disabledClass = 'mdc-list-item--disabled'
      expect(getByRole('option', { name: 'deliveries.goods_issue.label' }).className).toMatch(disabledClass)
      expect(getByRole('option', { name: 'transfers.goods_receive.label' }).className).toMatch(disabledClass)
    })

    it('displays appropriate actions when goods issued and transfer type is SHIP TO', async () => {
      server.use(
        http.post(/\/consignments/, () =>
          HttpResponse.json({ consignments: [{ ...transfers.consignments[0], status: 'GOODS_ISSUED' }] })
        )
      )

      const { getByRole } = render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait()
      await actAwait(100)
      fireEvent.click(screen.getAllByRole('button', { name: 'more_horiz' })[0])

      const disabledClass = 'mdc-list-item--disabled'
      expect(getByRole('option', { name: 'deliveries.goods_issue.label' }).className).toMatch(disabledClass)
      expect(getByRole('option', { name: 'transfers.goods_receive.label' }).className).not.toMatch(disabledClass)
    })

    it('renders buttons array in ConsignmentExpandedRowTemplate', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ consignments: [transfers.consignments[1]] })))

      render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait(100)

      // Find and click the expansion button to expand the row
      const expandButton = screen.getAllByRole('button', { name: /keyboard_arrow_down/ })[0]
      expect(expandButton).toBeTruthy()

      fireEvent.click(expandButton)
      await actAwait(50)

      // Check that the expansion panel contains the button section
      expect(screen.getByTestId('button-section')).toBeInTheDocument()
    })

    it('passes correct buttons array based on transfer status and type', async () => {
      // Test STAGED status
      server.use(
        http.post(/\/consignments/, () =>
          HttpResponse.json({ consignments: [{ ...transfers.consignments[1], status: 'STAGED' }] })
        )
      )

      render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait(100)

      // Find and click the expansion button to expand the row
      const expandButton = screen.getAllByRole('button', { name: /keyboard_arrow_down/ })[0]
      expect(expandButton).toBeTruthy()

      fireEvent.click(expandButton)
      await actAwait(150)

      // Check that the button section contains the correct button
      const buttonSection = screen.getByTestId('button-section')
      expect(buttonSection).toBeInTheDocument()

      // Use within() to scope the search to just the button section
      const { getByText, queryByText } = within(buttonSection)
      expect(getByText('deliveries.goods_issue.label')).toBeInTheDocument()
      expect(queryByText('transfers.goods_receive.label')).not.toBeInTheDocument()
    })

    it('passes empty buttons array when no actions are available', async () => {
      // Test with status that doesn't match any action conditions
      server.use(
        http.post(/\/consignments/, () =>
          HttpResponse.json({ consignments: [{ ...transfers.consignments[1], status: 'PENDING' }] })
        )
      )

      render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait(100)

      // Find and click the expansion button to expand the row
      const expandButton = screen.getAllByRole('button', { name: /keyboard_arrow_down/ })[0]
      expect(expandButton).toBeTruthy()

      fireEvent.click(expandButton)
      await actAwait(50)

      // Should still render the button section but with no buttons
      const buttonSection = screen.getByTestId('button-section')
      expect(buttonSection).toBeInTheDocument()

      // Use within() to scope the search to just the button section
      const { queryByText } = within(buttonSection)
      expect(queryByText('deliveries.goods_issue.label')).not.toBeInTheDocument()
      expect(queryByText('transfers.goods_receive.label')).not.toBeInTheDocument()
    })

    it('renders action buttons in the expansion panel based on transfer status', async () => {
      server.use(
        http.post(/\/consignments/, () =>
          HttpResponse.json({ consignments: [{ ...transfers.consignments[1], status: 'STAGED' }] })
        )
      )

      render(<TransferList {...defaultProps} />, { store: setUpStore() })
      await actAwait(100)

      // Find and click the expansion button to expand the row
      const expandButton = screen.getAllByRole('button', { name: /keyboard_arrow_down/ })[0]
      expect(expandButton).toBeTruthy()

      fireEvent.click(expandButton)
      await actAwait(50)

      // Check that the button section contains the correct action button
      const buttonSection = screen.getByTestId('button-section')
      const goodsIssueButton = within(buttonSection).getByText('deliveries.goods_issue.label')
      expect(goodsIssueButton).toBeInTheDocument()

      // Verify it's actually a clickable button
      expect(goodsIssueButton.closest('button')).toBeTruthy()
    })
  })
})
