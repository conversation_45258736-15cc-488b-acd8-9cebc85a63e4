jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: jest.fn()
}))

jest.mock('@gc/hooks', () => ({
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useScreenRes: () => 5
}))

describe('TransferDetailsMobile', () => {
  it('should render successfully', () => {
    // const { baseElement } = render(<TransferDetailsMobile />)
    // expect(baseElement).toBeTruthy()
  })
})
