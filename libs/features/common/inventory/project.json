{"name": "features-common-inventory", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/features/common/inventory/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/features/common/inventory/jest.config.ts"}}}}