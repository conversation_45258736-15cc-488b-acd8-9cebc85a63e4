import { fn } from '@storybook/test'

import LicenseStatusChart from './LicenseStatusChart'

export default {
  title: 'Farmers/Molecules/LicenseStatusChart',
  component: LicenseStatusChart,
  args: {
    farmersData: {
      licensedPeople: 1,
      unLicensedPeople: 2,
      unauthorizedPeople: 3
    },
    unitsData: {
      licensedUnit: 3,
      unLicensedUnit: 2,
      unauthorizedUnit: 1
    },
    tabs: [
      {
        title: 'Farmers Chart',
        colors: ['red', 'green', 'blue'],
        usage: 'farmersChart'
      },
      {
        title: 'Units Chart',
        colors: ['red', 'green', 'blue'],
        usage: 'unitsChart'
      }
    ],
    refetch: fn()
  }
}

export const Basic = {
  args: {}
}

export const IsError = {
  args: {
    isError: true
  }
}
