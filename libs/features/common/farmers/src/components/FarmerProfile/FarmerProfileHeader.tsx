import { ButtonProps } from '@element/react-components'
import { <PERSON><PERSON>, Header, SubHeader } from '@gc/components'
import { useGcPortalConfig, useLocale, useMemoizedTranslation, useSelectedAccount } from '@gc/hooks'
import { Farmer } from '@gc/types'
import { getCurrencyFormat } from '@gc/utils'
import { useCallback, useMemo } from 'react'
import { useParams } from 'react-router-dom'

import { useGetCreditLimitQuery } from '../../store'
import styles from './FarmerProfileHeader.module.scss'

const FarmerProfileHeader = ({
  farmerInfo,
  listItemsDesktop,
  desktopButton
}: {
  farmerInfo: Farmer
  desktopButton?: ButtonProps[]
  listItemsDesktop: {
    value: string
    label: string
    onClick: () => void
  }[]
}) => {
  const locale = useLocale()
  const { code = '' } = useParams()
  const t = useMemoizedTranslation()

  const gcPortalConfig = useGcPortalConfig()
  const { sapAccountId } = useSelectedAccount()
  const desktopButtonProps = useMemo(() => desktopButton || [], [desktopButton])

  const {
    data: creditLimit,
    error: creditLimitError,
    isLoading: isCreditLimitLoading
  } = useGetCreditLimitQuery(
    {
      headers: { 'sap-instance': 'pbc-customer-number', 'customer-number': sapAccountId },
      payload: { selectedSapId: code, creditControlNumber: gcPortalConfig.creditControlNumber }
    },
    {
      skip: farmerInfo?.partyStatus !== 'ACTIVE'
    }
  )

  const headerProps = useMemo(() => {
    const secText1 = `${
      farmerInfo?.contracts?.length > 0 && farmerInfo.contracts[0].connectId?.length > 0
        ? `${t('farmers.connect_id.label')}: ${farmerInfo.contracts[0].connectId[0].sourceId} • `
        : ''
    }${farmerInfo?.pricingZones ? (farmerInfo.pricingZones[0]?.pricingZoneDescription ?? '') : ''}`
    const overlineBadgeProps = {
      labelText:
        farmerInfo?.contracts?.length > 0 && farmerInfo.contracts[0]
          ? `${farmerInfo.contracts[0].contractStatus} • ${farmerInfo.contracts[0].sourceId}`
          : ''
    }
    return {
      title: farmerInfo ? farmerInfo.name : '',
      secText1: secText1,
      overlineBadgeProps: overlineBadgeProps,
      buttonProps: desktopButtonProps
    }
  }, [desktopButtonProps, farmerInfo, t])

  const getRemainingCreditLimit = useCallback(() => {
    return creditLimit?.percentage ? creditLimit?.percentage : 0
  }, [creditLimit])

  const subHeaderProps = useMemo(() => {
    const { available = 0, creditLimit: creditLimitValue = 0 } = creditLimit ?? {}
    return {
      title: 'Remaining credit limit',
      subtitle: `${getCurrencyFormat('USD', available, locale)} ${t('common.of.label')}  ${getCurrencyFormat('USD', creditLimitValue, locale)}`,
      trailingBlock: <Badge themeColor='orange' labelText={`${getRemainingCreditLimit()}% ${t('common.used.label')}`} />
    }
  }, [creditLimit, getRemainingCreditLimit, locale, t])

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Header
          {...headerProps}
          {...(listItemsDesktop.length > 0 && {
            moreActions: {
              buttonLabel: t('common.create.label'),
              data: farmerInfo,
              listItems: listItemsDesktop,
              leadingIcon: 'add',
              isPrimary: true
            }
          })}
        />
        <div className={styles.credit_limit}>
          <SubHeader
            {...subHeaderProps}
            className={isCreditLimitLoading || !!creditLimitError ? styles.credit_limit_loader : ''}
            loading={isCreditLimitLoading}
            error={!!creditLimitError}
          />
        </div>
      </div>
    </div>
  )
}
export default FarmerProfileHeader
