.farmers_modal {
  :global(.mdc-dialog__title) {
    border-bottom: none !important;
    min-height: 64px;
  }

  &.location_picker_modal {
    :global(.mdc-dialog__title) {
      height: 143px !important;
    }

    :global(.mdc-dialog__content) {
      height: 260px !important;
    }

    :global(.mdc-dialog__actions) {
      border-top: 1px solid var(--lmnt-theme-on-surface-stroke) !important;
      padding: 16px 24px;

      :global(.mdc-form-field) {
        padding: 8px;
      }
    }
  }
}

@media (max-width: 599px) {
  :global(.lmnt.lmnt-modal .lmnt-dialog__actions__directional) {
    width: 100%;
    display: flex;
    flex-direction: column-reverse;
  }
}

:global(.lmnt-modal__actions.mdc-dialog__actions) {
  margin: 0px;
  padding: 12px 16px;
}

:global(.lmnt-modal .lmnt-snackbar) {
  bottom: 100px;
}
