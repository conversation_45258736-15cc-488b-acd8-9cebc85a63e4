import '@testing-library/jest-dom'

import { render, screen } from '@testing-library/react'

import TableMessage from './TableMessage'

describe('TableMessage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('no data message', () => {
    render(<TableMessage loading={false} error={false} refetch={jest.fn()} />)

    const message = screen.queryAllByText(/common.data_unavailable.label/)

    expect(message).toBeTruthy()
  })

  test('error message', () => {
    render(<TableMessage loading={false} error={true} refetch={jest.fn()} />)

    const message = screen.getByText(/common.try_again_msg.label/)

    expect(message).toBeInTheDocument()
  })

  test('loading message', () => {
    render(<TableMessage loading={true} error={false} refetch={jest.fn()} />)

    const messages = screen.queryAllByText(/[a-zA-Z]+/)

    expect(messages).toHaveLength(0)
  })
})
