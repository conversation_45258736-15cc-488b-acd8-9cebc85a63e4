import { FilterBar } from '@gc/components'
import { Filter } from '@gc/types'
import _ from 'lodash'
import { useState } from 'react'

type ManualPaginatedTableFilterBarProps = {
  headers?: object
  onFilterHandler: (value: object) => void
}

type TableConfig = { header: string; accessor: string; filterable?: boolean; filterableOptions?: string[] }

const convertToFilterConfig = (tableConfig: TableConfig[]): Filter[] => {
  return tableConfig
    .filter(
      (column): column is TableConfig & { filterable: true; filterableOptions: string[] } =>
        column.filterable === true && Array.isArray(column.filterableOptions)
    )
    .map((column) => ({
      category: column.accessor,
      title: column.header,
      options: column.filterableOptions.map((option) => ({
        label: option,
        value: option
      })),
      selectedOptions: []
    }))
}

export const ManualPaginatedTableFilterBar = ({ headers, onFilterHandler }: ManualPaginatedTableFilterBarProps) => {
  const parsedFilterConfig = headers ? convertToFilterConfig(headers as TableConfig[]) : []
  const [filterList, setFilterList] = useState<Filter[]>(parsedFilterConfig)

  const renderFilters = () => {
    return (
      <div>
        <FilterBar
          filterList={filterList}
          applyFilters={(selectedFilters) => {
            const updatedFilterList = _.cloneDeep(filterList)
            updatedFilterList.forEach((f) => {
              f.selectedOptions = selectedFilters[f.category] || f.selectedOptions
            })
            setFilterList(updatedFilterList)
            onFilterHandler(selectedFilters)
          }}
        />
      </div>
    )
  }

  return (
    <div style={{ margin: '10px 10px 10px 0px', display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
      {renderFilters()}
    </div>
  )
}

export default ManualPaginatedTableFilterBar
