import { Loading, MessageWithAction } from '@gc/components'
import { useTranslation } from 'react-i18next'

import styles from './TableLoadingErrorWrapper.module.scss'

type MessageInfo = {
  loadingMsg: string
  errorMsgHeader: string
  errorMsg: string
  noDataMsgHeader: string
  noDataMsg: string
}

type TableLoadingErrorProps = {
  tableMessages: MessageInfo
  isLoading: boolean
  isError: boolean
  hasNoData: boolean
  refetchDetails?: () => void
  children?: React.ReactNode
}

export const TableLoadingErrorWrapper = ({
  tableMessages,
  isLoading,
  isError,
  hasNoData,
  refetchDetails,
  children
}: TableLoadingErrorProps) => {
  const { t } = useTranslation()
  if (isLoading) {
    return <Loading className={styles.loadingContainer} label={t(tableMessages.loadingMsg)} />
  }
  if (isError) {
    return (
      <MessageWithAction
        className={styles.messageWrapperContainer}
        messageHeader={t(tableMessages.errorMsgHeader)}
        messageDescription={t(tableMessages.errorMsg)}
        primaryButtonProps={{ label: t('common.try_again.label'), variant: 'text', onClick: refetchDetails }}
        iconProps={{
          icon: 'info',
          variant: 'filled-secondary',
          className: 'gc-icon-info'
        }}
      />
    )
  }

  if (hasNoData) {
    return (
      <MessageWithAction
        className={styles.messageWrapperContainer}
        messageHeader={t(tableMessages.noDataMsgHeader)}
        messageDescription={t(tableMessages.noDataMsg)}
        iconProps={{
          icon: 'info',
          variant: 'filled-secondary',
          className: 'gc-icon-info'
        }}
      />
    )
  }

  return children
}

export default TableLoadingErrorWrapper
