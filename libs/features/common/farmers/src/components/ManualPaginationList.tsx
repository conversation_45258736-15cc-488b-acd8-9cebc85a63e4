import { List, Loading, MessageWithAction } from '@gc/components'
import { FarmerListDetails } from '@gc/types'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import InfiniteScroll from 'react-infinite-scroll-component'

import styles from './ManualPaginationList.module.scss'

type ManualPaginationListProps = {
  data: FarmerListDetails[]
  searchTerm: string
  fasteStoreKey?: string
  isLoading: boolean
  pageSize?: number
  totalPages?: number
  uniqueItemKey: 'growerIrdId'
  onChangeHandler?: (data: { pageSize: number; currentPage: number }, sortKey: string, searchTerm: string) => void
  mobileDataListItem: (data: FarmerListDetails) => {
    code: string
    row: FarmerListDetails
    leadingBlock?: React.ReactNode
    primaryText?: React.ReactNode
    secondaryText?: React.ReactNode
    trailingBlock?: React.ReactNode
  }
  sortProps?: { options: { label: string; columnName: string; sortingType: string }[] }
  onListAction?: <T>(arg1: string, arg2: T | undefined) => void
}

export const ManualPaginationList = ({
  isLoading,
  data,
  fasteStoreKey,
  uniqueItemKey,
  pageSize = 10,
  onChangeHandler,
  totalPages = 0,
  searchTerm = '',
  mobileDataListItem,
  onListAction,
  sortProps
}: ManualPaginationListProps) => {
  const { t } = useTranslation()
  const [page, setPage] = useState({ currentPage: 0, pageSize })
  const [sortBy, setSortBy] = useState('farmName-asc')
  const [sortLoading, setSortLoading] = useState(false)
  const [uniqueData, setUniqueData] = useState<Map<string, FarmerListDetails>>(new Map())

  useEffect(() => {
    const newUniqueData = new Map(searchTerm || page.currentPage === 0 ? [] : uniqueData)
    data.forEach((item) => newUniqueData.set(item[uniqueItemKey], item))
    if (
      newUniqueData.size !== uniqueData.size ||
      [...newUniqueData].some(([key, value]) => !uniqueData.has(key) || uniqueData.get(key) !== value)
    ) {
      setUniqueData(newUniqueData)
    }
    setSortLoading(false)
  }, [data, uniqueData, uniqueItemKey, searchTerm, page.currentPage])

  const persistedData = useMemo(() => {
    return Array.from(uniqueData.values())
  }, [uniqueData])

  const replaceSortValue = (str: string) => {
    return str.replace('farmName', 'name')
  }

  const sortHandler = (event: React.ChangeEvent<HTMLInputElement>) => {
    const sortValue = event.target.value
    const parsedSortBy = replaceSortValue(sortValue)
    const pageInfo = { currentPage: 0, pageSize }
    setSortBy(parsedSortBy)
    setPage(pageInfo)
    setUniqueData(new Map())
    setSortLoading(true)
    onChangeHandler && onChangeHandler(pageInfo, parsedSortBy, searchTerm)
  }

  const fetchMoreData = () => {
    const updatedpage = { ...page, currentPage: page.currentPage + 1 }
    setPage(updatedpage)
    onChangeHandler && onChangeHandler(updatedpage, sortBy, searchTerm)
  }

  const hasMore = useMemo(() => {
    const canLoadMore = isLoading || page.currentPage + 1 < totalPages
    return canLoadMore
  }, [page, totalPages, isLoading])

  const showList = useMemo(() => {
    return !sortLoading || persistedData.length > 0
  }, [persistedData, sortLoading])

  const allLoading = isLoading || sortLoading

  return (
    <div style={{ width: '100%' }}>
      <InfiniteScroll
        dataLength={persistedData.length}
        next={fetchMoreData}
        hasMore={hasMore}
        loader={allLoading && <Loading type='circular' label={t('common.loading_farmers_message.label')} />}
        scrollThreshold={0.5}
        endMessage={
          <p style={{ textAlign: 'center' }}>
            <b>You have reached the end of the list</b>
          </p>
        }
      >
        <div style={{ minHeight: '100px' }}>
          {showList ? (
            <div className={styles.farmer_list_container}>
              <List
                leadingBlockType={'icon'}
                className={styles.farmer_list}
                listItemClassName={styles.farmer_list_item}
                divider={true}
                onAction={onListAction}
                data={persistedData}
                sortProps={sortProps}
                customSortHandler={sortHandler}
                searchTerm={searchTerm}
                fasteStoreKey={fasteStoreKey}
                dataToListItem={mobileDataListItem}
              />
            </div>
          ) : (
            !allLoading && (
              <MessageWithAction
                messageHeader={t('common.no_matching_results_message_header_label')}
                messageDescription={t('common.no_results_message_description')}
                iconProps={{
                  icon: 'info',
                  variant: 'filled-secondary',
                  className: 'gc-icon-info'
                }}
              />
            )
          )}
        </div>
      </InfiniteScroll>
    </div>
  )
}

export default ManualPaginationList
