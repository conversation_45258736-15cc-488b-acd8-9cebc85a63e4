import { fn } from '@storybook/test'

import InputFields from './InputFields'

export default {
  title: 'Farmers/Atoms/InputFields',
  component: InputFields,
  args: {
    field: 'brand',
    label: 'Brand',
    onChangeHandler: fn()
  }
}

export const Basic = {
  args: {}
}

export const Radio = {
  args: {
    type: 'radio',
    options: [
      {
        value: 'channel',
        displayName: 'Channel'
      },
      {
        value: 'dekalb',
        displayName: 'Dekalb'
      }
    ]
  }
}

export const Select = {
  args: {
    type: 'select',
    options: [
      {
        value: 'channel',
        text: 'Channel'
      },
      {
        value: 'dekalb',
        text: 'Dekalb'
      }
    ]
  }
}
