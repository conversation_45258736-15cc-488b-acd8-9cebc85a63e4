import { FloatingButton } from '@gc/components'

type MobileActionButtonProps = {
  config: Array<BaseProps & (RegularButtonProps | MenuButtonProps)>
}

type BaseProps = {
  disabled?: boolean
  hide?: boolean
  showOnMobile?: boolean
  data: object
  type?: 'regular' | 'menu' | 'icon'
  onClick?: (a?: object) => void
  label?: string
}

type RegularButtonProps = {
  variant?: string
  leadingIcon?: string
}

type MenuButtonProps = {
  listItems: { value: string; label: string; onClick: (a?: object) => void }[]
  leadingIcon?: string
  trailingIcon?: string
  variant?: string
}

type ButtonComponentProps = BaseProps & (RegularButtonProps | MenuButtonProps)

export function MobileActionButton({ config }: MobileActionButtonProps) {
  const mobileButtons = config.filter((item: ButtonComponentProps) => item.showOnMobile)
  if (mobileButtons && mobileButtons.length === 1) {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    const { variant, leadingIcon, label = '', onClick = () => {} } = mobileButtons[0]
    return (
      <FloatingButton
        variant={variant}
        themeColor='secondary'
        leadingIcon={leadingIcon}
        label={label}
        buttonSize={'xlarge'}
        onClick={onClick}
      />
    )
  }
  return ''
}

export default MobileActionButton
