import { fn } from '@storybook/test'

import FarmerWidget from './FarmerWidget'

export default {
  title: 'Farmers/Molecules/FarmerWidget',
  component: FarmerWidget,
  args: {
    title: 'Farmer Brands',
    infoText: 'This is some information about Farmer Brands.',
    children: <div>This is a Farmer Brands Widget</div>,
    refetch: fn()
  }
}

export const Basic = {
  args: {}
}

export const ShowFlagLabel = {
  args: {
    showFlagLabel: true
  }
}

export const Loading = {
  args: {
    loading: true
  }
}

export const IsError = {
  args: {
    isError: true
  }
}

export const NoData = {
  args: {
    noData: true
  }
}
