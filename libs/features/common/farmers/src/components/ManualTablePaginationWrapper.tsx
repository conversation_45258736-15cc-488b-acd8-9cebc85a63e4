import { Icon } from '@element/react-icon'
import { Loading, MessageWithAction, Table } from '@gc/components'
import { debounce } from 'lodash'
import { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { ManualPaginatedTableFilterBar } from '../components'
import { onClickTableHeader } from '../utils'

type ManualTablePaginationWrapperProps = {
  isLoading: boolean
  title: string
  csvFileName: string
  onChangeHandler: (arg1: any, arg2?: string, arg3?: string) => void
  data: any
  headers: any
  columns: any
  pageSize?: number
  totalPageCount: number
  availableSortOptions: string[]
  defaultSortBy?: { label: string; value: string }
  fasteStoreKey?: string
}

export const ManualTablePaginationWrapper = ({
  isLoading,
  title,
  csvFileName,
  onChangeHandler,
  data,
  headers,
  columns,
  pageSize = 10,
  totalPageCount = 0,
  availableSortOptions,
  defaultSortBy = { label: '', value: '' },
  fasteStoreKey
}: ManualTablePaginationWrapperProps) => {
  const { t } = useTranslation()
  const [paginationState, setPaginationState] = useState({ pageSize: pageSize, currentPage: 0 })
  const [sortBy, setSortBy] = useState(defaultSortBy.value)
  const [sortInfo, setSortInfo] = useState({ sortBy: defaultSortBy.label, sortOrder: 'asc' })
  const [tableFilter, setTableFilter] = useState({})

  const onChangePagination = useCallback(
    (updatedPageState: { pageSize: number; currentPage: number }) => {
      setPaginationState(updatedPageState)
      const updatedPagniantionState = { ...updatedPageState, ...tableFilter }
      onChangeHandler(updatedPagniantionState)
    },
    [tableFilter, onChangeHandler]
  )

  const onChangeSort = useCallback(
    (value: string) => {
      const updatedSortByFetchQuery = { ...paginationState, ...tableFilter, currentPage: 0 }
      setSortBy(value)
      onChangeHandler(updatedSortByFetchQuery, value)
    },
    [paginationState, tableFilter, onChangeHandler]
  )

  const onChangeFilter = useCallback(
    (value: object) => {
      const updatedSortByFetchQuery = { ...paginationState, ...value, currentPage: 0 }
      setTableFilter(value)
      onChangeHandler(updatedSortByFetchQuery, sortBy)
    },
    [paginationState, sortBy, onChangeHandler]
  )

  const onChangeSearch = useMemo(() => {
    return debounce((searchStr) => {
      const updatedSortByFetchQuery = { ...paginationState, currentPage: 0 }
      onChangeHandler(updatedSortByFetchQuery, sortBy, searchStr)
    }, 900)
  }, [onChangeHandler, paginationState, sortBy])

  const configureHeaderByDisplayType = () => {
    if (!headers?.length) return []

    return headers?.map((c: any) => {
      const relatedColumnFromHeader = columns.find((col: any) => col.accessor === c.accessor)
      const column = { ...relatedColumnFromHeader, ...c }
      column.header = (
        <span style={{ alignItems: 'center', display: 'flex', gap: 4, cursor: column.sortable ? 'pointer' : '' }}>
          {column.header}
          {column.header === sortInfo.sortBy && (
            <Icon icon={sortInfo.sortOrder === 'asc' ? 'arrow_upward' : 'arrow_downward'} />
          )}
        </span>
      ) as unknown as string
      return column
    })
  }

  const MemoizedHeaders = useMemo(() => configureHeaderByDisplayType(), [columns, sortInfo])
  const onClickHeader = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      onClickTableHeader({
        event,
        tableColumns: columns,
        sortInfo,
        setSortInfo,
        availableSortOptions,
        onChangeSort
      })
    },
    [columns, sortInfo, availableSortOptions, onChangeSort] // List dependencies of onClickHeader
  )

  const MemomizedTable = useMemo(() => {
    return (
      <Table
        title={title}
        data={isLoading ? [] : data}
        headers={MemoizedHeaders}
        manualPaginated
        searchable
        fasteStoreKey={fasteStoreKey}
        manualSearchFn={onChangeSearch}
        itemsPerPage={pageSize}
        totalPageCount={totalPageCount}
        onChangePagination={onChangePagination}
        onClickHeader={onClickHeader}
        filterBar={<ManualPaginatedTableFilterBar headers={MemoizedHeaders} onFilterHandler={onChangeFilter} />}
        enableCsvDownload
        csvFileName={csvFileName}
        noContentMessage={
          <div>
            {isLoading && <Loading type='circular' label={t('common.loading_farmers_message.label')} />}
            {!isLoading && (
              <MessageWithAction
                messageHeader={t('common.no_results_message_header_label')}
                messageDescription={t('common.no_results_message_description')}
                iconProps={{
                  icon: 'info',
                  variant: 'filled-secondary',
                  className: 'gc-icon-info'
                }}
              />
            )}
          </div>
        }
      />
    )
  }, [
    title,
    isLoading,
    data,
    MemoizedHeaders,
    onChangeSearch,
    pageSize,
    totalPageCount,
    onChangePagination,
    onClickHeader,
    onChangeFilter,
    csvFileName,
    t
  ])

  return MemomizedTable
}

export default ManualTablePaginationWrapper
