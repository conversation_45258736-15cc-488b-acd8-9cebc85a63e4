import { angellFarmerDetails, myCropPortalConfig, myCropV2PortalConfig, seedsmanPortalConfig } from '@gc/shared/test'
import { PortalKey } from '@gc/types'
import { StoryContext } from '@storybook/react'

import FarmerDetails from './FarmerDetails'

export default {
  title: 'Farmers/Molecules/FarmerDetails',
  component: FarmerDetails
}

export const Basic = {
  render: (_args: object, context: StoryContext) => {
    const portal = context.globals.portal
    let portalConfig
    switch (portal) {
      case PortalKey.MyCrop:
        portalConfig = myCropPortalConfig
        break
      case PortalKey.MyCropV2:
        portalConfig = myCropV2PortalConfig
        break
      case PortalKey.SMS:
        portalConfig = seedsmanPortalConfig
        break
      default:
        throw new Error(`This component does not support ${portal}`)
    }
    return <FarmerDetails data={angellFarmerDetails} fields={portalConfig.farmersModule.farmerDetailFields} />
  }
}
