import { GridCol, GridRow } from '@element/react-grid'
import { List, ListItem } from '@element/react-list'
import { Typography } from '@element/react-typography'
import React from 'react'

import styles from './DashDataBlock.module.scss'

type DashDataBlockProps = {
  data: Array<
    Array<{
      primaryText: string
      secondaryText?: string | number
      trailingBlock?: React.ReactNode | string
      leadingBlock?: React.ReactNode | string
    }>
  >
  blockLabel?: string
  trailingBlockType?: string
  leadingBlockType?: string
  containerStyle?: object
}

export const DashDataBlock = ({
  data,
  blockLabel = '',
  trailingBlockType,
  leadingBlockType,
  containerStyle = {}
}: DashDataBlockProps) => {
  return (
    <div style={containerStyle}>
      {data && data.length > 0 && blockLabel && (
        <GridRow style={{ marginBottom: '-14px' }}>
          <GridCol align='bottom' verticalAlign='bottom' desktopCol={3}>
            <List dense nonInteractive>
              <ListItem>
                <Typography type='display6'>{blockLabel}</Typography>
              </ListItem>
            </List>
          </GridCol>
        </GridRow>
      )}
      <div className={styles.gridContainer}>
        {data.map((item, index) => (
          <div key={index} className={styles.gridItem}>
            <List
              style={{ width: '100%' }}
              showDivider
              nonInteractive
              dense
              dividerVariant='padded'
              className={styles.listContainer}
              trailingBlockType={trailingBlockType}
              leadingBlockType={leadingBlockType}
              items={item}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

export default DashDataBlock
