.container {
  position: relative;
  overflow-x: auto;
  white-space: nowrap;
  padding: 12px;
}

.listContainer {
  display: flex;
  flex-direction: column;
}

.contentContainer {
  display: inline-flex;
  align-items: center;
}

.listItem {
  margin-right: 50px;
  white-space: nowrap;
  flex-shrink: 0;
}

.divider {
  width: 1px;
  height: 100%;
  background-color: #d8d8d8;
  margin: 0 15px;
}

.gridContainer {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.gridItem {
  display: flex;
  flex-direction: column;
  width: 100%;
}
