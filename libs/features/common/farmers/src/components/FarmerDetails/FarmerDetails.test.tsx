import { angellFarmerDetails, myCropPortalConfig } from '@gc/shared/test'
import { render, screen } from '@testing-library/react'
import React from 'react'

import FarmerDetails from './FarmerDetails'

test('renders fields and data', () => {
  render(<FarmerDetails fields={myCropPortalConfig.farmersModule.farmerDetailFields} data={angellFarmerDetails} />)

  const licenseStatusHeader = screen.queryAllByText(/farmers.farmerDetails.list.licenseStatus/)
  const licenseStatusData = screen.queryAllByText(/Not Licensed/)

  expect(licenseStatusHeader).toBeTruthy()
  expect(licenseStatusData).toBeTruthy()
})
