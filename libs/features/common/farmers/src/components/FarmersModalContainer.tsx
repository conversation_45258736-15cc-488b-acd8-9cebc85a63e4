import {
  commonModalNames,
  ContactModal,
  createEditDeliveryModalNames,
  createEditFarmerModalNames,
  getModals,
  LocationPickerModal,
  ModalContainer,
  ModalState,
  ResumeProcessModal
} from '@gc/components'
import { resolutions } from '@gc/constants'
import { AbandonOrderModal, CartAndReviewProductsModal, ProductSelectionModal } from '@gc/features-nb-orders'
import { getModal } from '@gc/redux-store'
import classNames from 'classnames'
import React from 'react'
import isEqual from 'react-fast-compare'
import { useSelector } from 'react-redux'

import styles from './FarmersModalContainer.module.scss'

const modals: Record<string, ModalState> = {
  ...getModals(createEditDeliveryModalNames),
  ...getModals(createEditFarmerModalNames),
  ...getModals(commonModalNames),
  VIEW_CONTACT: { modalBody: ContactModal },
  SELECT_PRODUCTS: { modalBody: ProductSelectionModal },
  ABANDON_ORDER: { modalBody: AbandonOrderModal },
  CART_REVIEW_PRODUCTS_MOBILE: { modalBody: CartAndReviewProductsModal },
  SELECT_LOCATION: { modalBody: LocationPickerModal },
  RESUME_PROCESS: { modalBody: ResumeProcessModal }
}

export function FarmersModalContainer() {
  const modal = useSelector(getModal)
  if (!modal) return null

  const isConfirmationModal = modal.name === 'CONFIRMATION'
  const isLocationPickerModal = modal.name === 'SELECT_LOCATION'

  const getModalSize = (ModalBody: ModalState['modalBody'], screenRes: number) => {
    if (isConfirmationModal) return 'medium'
    const needsLargeModal = ModalBody === ProductSelectionModal
    const isLargeScreen = screenRes >= resolutions.M1023
    if (isLargeScreen && needsLargeModal) return 'max'
    if (screenRes <= resolutions.M719) {
      return 'fullscreen'
    }
    return 'medium'
  }

  return (
    <ModalContainer
      modals={modals}
      className={classNames(styles.farmers_modal, { [styles.location_picker_modal]: isLocationPickerModal })}
      getModalSize={getModalSize}
      open={modal.open}
      modalName={modal.name}
      modalProps={modal.props}
      useDefaultHeight={isConfirmationModal || isLocationPickerModal}
      customInitialFocus={!isLocationPickerModal}
    />
  )
}

export default React.memo(FarmersModalContainer, isEqual)
