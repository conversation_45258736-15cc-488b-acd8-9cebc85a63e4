import { Button } from '@element/react-button'
import { Icon } from '@element/react-icon'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

type BackButtonProps = {
  className?: string
}

export const BackButton = ({ className }: BackButtonProps) => {
  const navigate = useNavigate()
  const { t } = useTranslation()

  const handleClick = () => {
    // if user is coming from linking their climate FV account (a multipage workflow) go back before it
    const numberOfPagesToGoBack = document.referrer?.includes('climate.com') ? 4 : 1
    navigate(-numberOfPagesToGoBack)
  }

  return (
    <Button className={className} variant='text' onClick={handleClick}>
      <Icon icon='arrow_back' iconSize='small' style={{ verticalAlign: 'text-bottom', marginRight: 4 }}></Icon>
      {t('common.back.label')}
    </Button>
  )
}

export default BackButton
