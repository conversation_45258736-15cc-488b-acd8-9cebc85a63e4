import { Button } from '@element/react-button'
import { Icon } from '@element/react-icon'
import { TypoBody, TypoDisplay } from '@element/react-typography'
import { useTranslation } from 'react-i18next'

import styles from './TableMessage.module.scss'

type TableErrorMessageProps = {
  refetch?: () => void
}

const TableErrorMessage = ({ refetch }: TableErrorMessageProps) => {
  const { t } = useTranslation()
  return (
    <div>
      <Icon icon='sync_problem' />
      <TypoDisplay level={6}>{t('common.try_again_msg.label')}</TypoDisplay>
      <TypoBody level={2}>{t('common.try_again_desc.label')}</TypoBody>
      <Button onClick={refetch} themeColor='secondary'>
        {t('common.try_again.label')}
      </Button>
    </div>
  )
}

const TableNoDataMessage = () => {
  const { t } = useTranslation()
  return (
    <div>
      <Icon icon='warning' />
      <TypoDisplay level={6}>{t('common.data_unavailable.label')}</TypoDisplay>
      <TypoBody level={2}>{t('common.no_data.label')}</TypoBody>
    </div>
  )
}

type TableMessageProps = {
  loading: boolean
  error: boolean
  refetch?: () => void
}

export const TableMessage = ({ loading, error, refetch }: TableMessageProps) => {
  if (loading) return false
  if (error) {
    return (
      <div className={styles.tableMessageContainer}>
        <TableErrorMessage refetch={refetch} />
      </div>
    )
  }
  return (
    <div className={styles.tableMessageContainer}>
      <TableNoDataMessage />
    </div>
  )
}

export default TableMessage
