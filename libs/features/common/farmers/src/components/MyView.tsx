import { TypoCaption } from '@element/react-typography'
import { useFarmersModuleConfig, useSelectedAccount } from '@gc/hooks'
import React, { memo, useMemo } from 'react'

import styles from './MyView.module.scss'

const MyView = () => {
  const { accountName, lob } = useSelectedAccount()
  const { myView } = useFarmersModuleConfig()

  // Memoize the lob config computation
  const lobConfig = useMemo(() => myView[lob] ?? {}, [myView, lob])

  // Memoize the background image style
  const backgroundStyle = useMemo(
    () => ({
      backgroundImage: `url(${lobConfig.backgroundImage})`
    }),
    [lobConfig.backgroundImage]
  )

  return (
    <div className={styles.myViewContainer}>
      <div className={styles.imageBackground} style={backgroundStyle}>
        <div className={styles.inner}>
          <svg className={styles.svg}>
            <image className={styles.image} href={lobConfig.logo} />
          </svg>
          <div className={styles.container}>
            <div className={styles.column}>
              <TypoCaption bold>Line of Business</TypoCaption>
              <TypoCaption>{lobConfig.lobDisplayName}</TypoCaption>
            </div>
            <div className={styles.column}>
              <TypoCaption bold>Location</TypoCaption>
              <TypoCaption>{accountName}</TypoCaption>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const MemoizedMyView = memo(MyView)
export { MemoizedMyView as MyView }
export default MemoizedMyView
