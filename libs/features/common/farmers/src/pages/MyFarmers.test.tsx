import {
  angellFarmerDetails,
  channelArrowFasteStoreBuilder,
  renderWithTestWrapper,
  seedsmanFasteStoreBuilder
} from '@gc/shared/test'
import { Farmer } from '@gc/types'
import { screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { setupStore } from '../store'
import MyFarmers from './MyFarmers'

const mockUseFarmerDetails = jest.fn()
jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useUpdateFasteStore: () => [jest.fn()],
  useAppSessionData: () => [jest.fn()],
  useSelectedAccount: () => ({ sapAccountId: '123' }),
  useEntitlement: () => ({ hasFarmerWriteAccess: true })
}))

jest.mock('../hooks', () => ({
  ...jest.requireActual('../hooks'), // Keep actual implementation for other hooks
  useFarmerDetails: () => mockUseFarmerDetails()
}))

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useEntitlement: () => ({ hasFarmerWriteAccess: true }),
  useSelectedAccount: () => ({ sapAccountId: '**********' })
}))

describe('MyFarmers', () => {
  test('renders title for arrow', () => {
    mockUseFarmerDetails.mockImplementation(() => ({
      data: { farmerDetails: [angellFarmerDetails] },
      isLoading: false,
      isError: false,
      refetch: jest.fn()
    }))

    renderWithTestWrapper(<MyFarmers />, {
      fasteStore: channelArrowFasteStoreBuilder.build(),
      reduxProps: {
        store: setupStore()
      }
    })

    const headerLabel = screen.getByText(/common.farmerPage.headerLabel/)
    const tableTitle = screen.getByText(/farmers.tables.farmerList.your.tableTitle/)

    expect(headerLabel).toBeInTheDocument()
    expect(tableTitle).toBeInTheDocument()
  })

  test('renders title for sms', () => {
    mockUseFarmerDetails.mockImplementation(() => ({
      data: { farmerDetails: [angellFarmerDetails] },
      isLoading: false,
      isError: false,
      refetch: jest.fn()
    }))

    renderWithTestWrapper(<MyFarmers />, {
      fasteStore: seedsmanFasteStoreBuilder.build(),
      reduxProps: {
        store: setupStore()
      }
    })

    const headerLabel = screen.getByText(/common.farmerPage.headerLabel/)
    const tableTitle = screen.getByText(/farmers.tables.farmerList.tableTitle/)

    expect(headerLabel).toBeInTheDocument()
    expect(tableTitle).toBeInTheDocument()
  })

  test('renders is table loading', () => {
    mockUseFarmerDetails.mockImplementation(() => ({
      data: { farmerDetails: [] },
      isLoading: true,
      isError: false,
      refetch: jest.fn()
    }))

    renderWithTestWrapper(<MyFarmers />, {
      fasteStore: seedsmanFasteStoreBuilder.build(),
      reduxProps: {
        store: setupStore()
      }
    })

    const table = screen.getByText(/common.loading_farmers_message.label/)

    expect(table).toBeInTheDocument()
  })

  test('renders empty table content', () => {
    mockUseFarmerDetails.mockImplementation(() => ({
      data: { farmerDetails: [] },
      isLoading: false,
      isError: false,
      refetch: jest.fn()
    }))

    renderWithTestWrapper(<MyFarmers />, {
      fasteStore: seedsmanFasteStoreBuilder.build(),
      reduxProps: {
        store: setupStore()
      }
    })

    const tableHeader = screen.getByText(/farmers.no_data_header_msg/)
    const tableDesc = screen.getByText(/farmers.no_data_description_msg/)

    expect(tableHeader).toBeInTheDocument()
    expect(tableDesc).toBeInTheDocument()
  })

  test('renders error showing table', () => {
    mockUseFarmerDetails.mockImplementation(() => ({
      data: { farmerDetails: [] },
      isLoading: false,
      isError: true,
      refetch: jest.fn()
    }))

    renderWithTestWrapper(<MyFarmers />, {
      fasteStore: seedsmanFasteStoreBuilder.build(),
      reduxProps: {
        store: setupStore()
      }
    })

    const tableHeader = screen.getByText(/farmers_api_error_header_msg/)
    const tableDesc = screen.getByText(/farmer.api_error_description_msg/)

    expect(tableHeader).toBeInTheDocument()
    expect(tableDesc).toBeInTheDocument()
  })

  test('displays banner when cart has billToParties and growerInfo', () => {
    mockUseFarmerDetails.mockImplementation(() => ({
      data: { farmerDetails: [angellFarmerDetails] },
      isLoading: false,
      isError: false,
      refetch: jest.fn()
    }))

    renderWithTestWrapper(<MyFarmers />, {
      fasteStore: channelArrowFasteStoreBuilder.build(),
      reduxProps: {
        store: setupStore({
          cart: {
            cartId: 'testCartId',
            addedPayerList: [],
            discretionaryDiscounts: [],
            currentEntryNetUnitPrice: 0,
            netUnitPrices: [],
            saveInProgressEntries: [],
            brandDiscount: undefined,
            appliedAllItems: [],
            isDeleteEntryInProgress: false,
            updateCartRequests: {}
          }
        })
      }
    })

    const bannerMessage = screen.getByText(/common.farmerPage.headerLabel Test Farmer/)
    expect(bannerMessage).toBeInTheDocument()
  })

  test('handles add farmer button click', () => {
    mockUseFarmerDetails.mockImplementation(() => ({
      data: { farmerDetails: [angellFarmerDetails] },
      isLoading: false,
      isError: false,
      refetch: jest.fn()
    }))

    renderWithTestWrapper(<MyFarmers />, {
      fasteStore: channelArrowFasteStoreBuilder.build(),
      reduxProps: {
        store: setupStore()
      }
    })

    const addButton = screen.getByRole('button', { name: /common.farmer.header.label/ })
    userEvent.click(addButton)
    // Add assertions to verify the expected outcome of the button click
  })

  test('filters farmers based on search term', () => {
    mockUseFarmerDetails.mockImplementation(() => ({
      data: { farmerDetails: [angellFarmerDetails] },
      isLoading: false,
      isError: false,
      refetch: jest.fn()
    }))

    renderWithTestWrapper(<MyFarmers />, {
      fasteStore: channelArrowFasteStoreBuilder.build(),
      reduxProps: {
        store: setupStore()
      }
    })

    const searchInput = screen.getByRole('textbox')
    userEvent.type(searchInput, 'Test')
    // Add assertions to verify the filtered list of farmers
  })
})

test('filters actions based on portalKey and partyStatus', () => {
  const portalKey = 'arrow'

  const mockFarmer = {
    partyStatus: 'INACTIVE'
  }

  const actions = ['edit', 'inactivate', 'view']

  const column = {
    actions
  }

  const filteredActions =
    portalKey === 'arrow' || portalKey === 'aurora'
      ? column.actions?.filter(
          (action) => action !== 'inactivate' || (mockFarmer as Farmer).partyStatus?.toUpperCase() === 'ACTIVE'
        )
      : column.actions

  expect(filteredActions).toEqual(['edit', 'view'])
})
