/* eslint-disable @nx/enforce-module-boundaries */
import { Button } from '@element/react-button'
import { Grid, GridCol, GridRow } from '@element/react-grid'
import { Icon } from '@element/react-icon'
import { TextBubble } from '@element/react-text-bubble'
import { TypoSubtitle } from '@element/react-typography'
import { Header, HeaderType, Loading, MessageWithAction, TopAppBar } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { useLocation, useMemoizedTranslation, useSearch } from '@gc/hooks'
import { FarmerListDetails, TableColConfig } from '@gc/types'
import { configSortToSortBy, downloadXlsx, formatFarmerDetailsCSVData, getFasteStoreKey } from '@gc/utils'
import _ from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import MediaQuery from 'react-responsive'
import { NavigateFunction, useNavigate } from 'react-router-dom'

import { ManualPaginationList, ManualTablePaginationWrapper } from '../components'
import { useColumns, usePaginatedMyCropFarmerDetails } from '../hooks'
import { useLazyGetPaginatedFarmerDetailsQuery } from '../store'
import styles from './MyFarmers.module.scss'

const FARMER_FS_KEY = getFasteStoreKey('farmers', 'farmer')
const FARMER_LIST_FS_KEY = getFasteStoreKey('farmers', 'farmerList')
const DEFAULT_PAGE_SIZE = 10
const DEFAULT_SORT = 'farmName-asc'
const SORT_OPTIONS = [
  DEFAULT_SORT,
  'farmName-desc',
  'city-asc',
  'city-desc',
  'state-asc',
  'state-desc',
  'gln-asc',
  'gln-desc',
  'irdId-asc',
  'irdId-desc',
  'licenseStatus-asc',
  'licenseStatus-desc',
  'currentCornZone-asc',
  'currentCornZone-desc'
]
const FARMER_INFO_LINK = '/farmer-info'

const configureHeaderByDisplayType = (farmerListColumns: TableColConfig[], navigate: NavigateFunction) =>
  farmerListColumns.map((c) => {
    const column = { ...c } as HeaderType<FarmerListDetails> & { sortable?: boolean }
    if (column.displayType === 'link') {
      column.onLinkClick = (_farmer: FarmerListDetails) => {
        navigate(FARMER_INFO_LINK, {
          state: { farmer: _farmer }
        })
      }
    }
    return column
  })

export const NbMyFarmers = () => {
  const t = useMemoizedTranslation()
  const navigate = useNavigate()
  const [location] = useLocation()

  const [downloadRequest, setDownloadRequest] = useState(false)
  const [dealerSapIds, setDealerSapIds] = useState<string[]>([])
  const [showTable, setShowTable] = useState(false)

  const {
    data: initialPaginatedFarmers,
    isFetching: isPagniatedFarmersInitialFetching,
    isError: isPaginatedFarmersErrors,
    refetch: refetchFarmerDetails
  } = usePaginatedMyCropFarmerDetails({
    dealerSapIds,
    pageSize: DEFAULT_PAGE_SIZE,
    currentPage: 0,
    sort: DEFAULT_SORT
  })

  const [
    getPaginatedFarmerDetails,
    { data: lazyPaginatedFarmers, isFetching: isPaginatedFarmersLazyFetching, isError: isPaginatedFarmersLazyError }
  ] = useLazyGetPaginatedFarmerDetailsQuery()

  const [getAllFarmerDetails, { data: lazyAllPaginatedFarmers, isFetching: isAllPaginatedFarmersLazyFetching }] =
    useLazyGetPaginatedFarmerDetailsQuery()

  const isPaginatedFarmersFetching = isPagniatedFarmersInitialFetching || isPaginatedFarmersLazyFetching
  const isError = isPaginatedFarmersErrors || isPaginatedFarmersLazyError
  const paginatedFarmers = lazyPaginatedFarmers || initialPaginatedFarmers
  const { farmerDetails, pagination } = paginatedFarmers || {
    farmerDetails: [],
    pagination: { count: 0, totalCount: 0, totalPages: 0 }
  }

  const {
    tableCol: { farmerListColumns = [] },
    csvDownload: { farmerDownloadReportColumns }
  } = useColumns()
  const [columnHeaders, mobileSortKey] = useMemo(
    () => [
      configureHeaderByDisplayType(farmerListColumns, navigate),
      configSortToSortBy(farmerListColumns)?.[0]?.id ?? ''
    ],
    [farmerListColumns, navigate]
  )

  const [searchTerm, openSearch, { handleOpenSearch, handleCancelSearch, handleCloseSearch, handleSearch }] = useSearch(
    FARMER_FS_KEY,
    'searchTerm',
    (searchTerm) => fetchGrowersSearch(searchTerm)
  )

  useEffect(() => {
    setDealerSapIds(location.selectedSapAccountIds ?? [])
  }, [location.selectedSapAccountIds])

  useEffect(() => {
    // don't hide table after it has been shown
    if (!showTable && farmerDetails?.length > 0) {
      setShowTable(true)
    }
  }, [farmerDetails?.length, searchTerm, showTable])

  useEffect(() => {
    if (lazyAllPaginatedFarmers && downloadRequest) {
      setDownloadRequest(false)
      downloadXlsx(
        farmerDownloadReportColumns as { displayName: string; id: string }[],
        formatFarmerDetailsCSVData(lazyAllPaginatedFarmers.farmerDetails as FarmerListDetails[], new Map()),
        'My-Farmers_Full-Farmer-Report_'
      )
    }
  }, [lazyAllPaginatedFarmers, downloadRequest, farmerDownloadReportColumns])

  const fetchGrowersByPage = useCallback(
    (pageState: { pageSize: number; currentPage: number }, sort = DEFAULT_SORT, search = '') => {
      if (dealerSapIds?.length) {
        getPaginatedFarmerDetails({ dealerSapIds, ...pageState, sort, search }, true)
      }
    },
    [dealerSapIds, getPaginatedFarmerDetails]
  )

  const fetchGrowersReset = useCallback(() => {
    fetchGrowersByPage({ pageSize: pagination.count, currentPage: 0 }, DEFAULT_SORT, '')
  }, [fetchGrowersByPage, pagination.count])

  const fetchGrowersSearch = useMemo(() => {
    return _.debounce((searchStr: string) => {
      fetchGrowersByPage({ pageSize: pagination.count, currentPage: 0 }, DEFAULT_SORT, searchStr)
    }, 900)
  }, [fetchGrowersByPage, pagination.count])

  const handleDownload = useCallback(() => {
    getAllFarmerDetails({ dealerSapIds, fetchAll: true })
    setDownloadRequest(true)
  }, [dealerSapIds, getAllFarmerDetails])

  const handleGoToFarmersDetails = useCallback(
    <T,>(_code: string, row: T | undefined) => {
      navigate(FARMER_INFO_LINK, {
        state: { farmer: row ?? {} }
      })
    },
    [navigate]
  )

  const getMessageContent = useCallback(() => {
    let messageHeaderKey
    if (isError) messageHeaderKey = 'farmers_api_error_header_msg'
    else if (farmerDetails?.length === 0) messageHeaderKey = 'farmers.no_data_header_msg'
    else messageHeaderKey = 'common.no_matching_results_message_header_label'

    let messageDescriptionKey
    if (isError) messageDescriptionKey = 'farmer.api_error_description_msg'
    else if (farmerDetails?.length === 0) messageDescriptionKey = 'farmers.no_data_description_msg'
    else messageDescriptionKey = 'common.no_results_message_description'

    return (
      <MessageWithAction
        messageHeader={t(messageHeaderKey)}
        messageDescription={t(messageDescriptionKey)}
        primaryButtonProps={
          isError
            ? {
                label: t('common.try_again.label'),
                variant: 'text',
                themeColor: 'danger',
                onClick: refetchFarmerDetails
              }
            : undefined
        }
        iconProps={{
          icon: 'info',
          variant: 'filled-secondary',
          className: 'gc-icon-info'
        }}
      />
    )
  }, [t, isError, farmerDetails?.length, refetchFarmerDetails])

  const getMobileDataListItem = useCallback(
    (farmer: FarmerListDetails) => ({
      code: FARMER_INFO_LINK,
      row: farmer,
      leadingBlock: (
        <div>
          <TextBubble text={farmer.farmName.charAt(0)} themeColor='blue' />
        </div>
      ),
      primaryText: <TypoSubtitle level={2}>{farmer.farmName}</TypoSubtitle>
    }),
    []
  )

  return (
    <Grid className={styles.myFarmersContainer}>
      <MediaQuery minWidth={IS_DESKTOP}>
        <GridRow className={styles.header_padding_partial}>
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <Header title={t('common.farmerPage.headerLabel')} />
          </GridCol>
        </GridRow>
        <GridRow className={styles.pageActionRowEnd}>
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <Button
              variant='outlined'
              themeColor='primary'
              disabled={!farmerDetails?.length || isAllPaginatedFarmersLazyFetching}
              buttonSize='small'
              leadingIcon={
                isAllPaginatedFarmersLazyFetching && (
                  <span style={{ marginTop: '-9px' }}>
                    <Loading type='circular' size='sm' />
                  </span>
                )
              }
              onClick={handleDownload}
            >
              {!isAllPaginatedFarmersLazyFetching && (
                <span>
                  <Icon icon='file_download' />
                  {t('farmers.tables.farmerList.buttonLabel')}
                </span>
              )}
            </Button>
          </GridCol>
        </GridRow>
      </MediaQuery>
      <GridRow className={styles.content}>
        {showTable ? (
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <MediaQuery minWidth={IS_DESKTOP} onChange={fetchGrowersReset}>
              <ManualTablePaginationWrapper
                isLoading={isPaginatedFarmersFetching}
                headers={columnHeaders}
                title={t('farmers.tables.farmerList.tableTitle')}
                data={farmerDetails}
                pageSize={pagination.count}
                totalPageCount={pagination.totalCount}
                availableSortOptions={SORT_OPTIONS}
                onChangeHandler={fetchGrowersByPage}
                columns={farmerListColumns}
                csvFileName='My-Farmers_Farmers_'
                defaultSortBy={{ label: 'Farm Name', value: DEFAULT_SORT }}
                fasteStoreKey={FARMER_LIST_FS_KEY}
              />
            </MediaQuery>
            <MediaQuery maxWidth={IS_MOBILE} onChange={fetchGrowersReset}>
              <TopAppBar
                leadingContent={openSearch || searchTerm !== '' ? 'search' : 'title'}
                title={t('common.farmer.label_other')[0].toUpperCase() + t('common.farmer.label_other').slice(1)}
                className={styles.farmer_search}
                isModalTopBar={true}
                trailingIconButtonProps={[{ icon: 'search', onClick: handleOpenSearch }]}
                searchProps={{
                  searchTerm,
                  onChange: handleSearch,
                  onClear: handleCancelSearch,
                  closeSearchButtonProps: { onClick: handleCloseSearch }
                }}
              />
              <ManualPaginationList
                isLoading={isPaginatedFarmersFetching}
                totalPages={pagination?.totalPages || 0}
                data={farmerDetails as FarmerListDetails[]}
                searchTerm={searchTerm}
                pageSize={pagination.count}
                onChangeHandler={fetchGrowersByPage}
                mobileDataListItem={getMobileDataListItem}
                onListAction={handleGoToFarmersDetails}
                uniqueItemKey={'growerIrdId'}
                fasteStoreKey={FARMER_LIST_FS_KEY}
                sortProps={{
                  options: [
                    {
                      label: t('common.name_a-z.label'),
                      columnName: mobileSortKey,
                      sortingType: 'asc'
                    },
                    {
                      label: t('common.name_z-a.label'),
                      columnName: mobileSortKey,
                      sortingType: 'desc'
                    }
                  ]
                }}
              />
            </MediaQuery>
          </GridCol>
        ) : (
          <GridCol
            desktopCol={12}
            tabletCol={8}
            phoneCol={4}
            className={styles.container_contingency}
            verticalAlign='middle'
          >
            {isPaginatedFarmersFetching ? (
              <Loading label={t('common.loading_farmers_message.label')} />
            ) : (
              getMessageContent()
            )}
          </GridCol>
        )}
      </GridRow>
    </Grid>
  )
}

export default NbMyFarmers
