/* eslint-disable @nx/enforce-module-boundaries */
import { But<PERSON> } from '@element/react-button'
import { Grid, GridCol, GridRow } from '@element/react-grid'
import { Icon } from '@element/react-icon'
import { TextBubble } from '@element/react-text-bubble'
import { TypoDisplay, TypoSubtitle } from '@element/react-typography'
import { HeaderType, Loading, MessageWithAction, TopAppBar } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE, resolutions } from '@gc/constants'
import { useScreenRes } from '@gc/hooks'
import { AccountHierarchy, FarmerListDetails, TableColConfig } from '@gc/types'
import {
  configSortToSortBy,
  downloadXlsx,
  formatFarmerDetailsCSVData,
  getFasteStoreKey,
  mapTreeAsArray
} from '@gc/utils'
import _ from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import MediaQuery from 'react-responsive'
import { NavigateFunction, useNavigate } from 'react-router-dom'

import { BackButton, ManualPaginationList, ManualTablePaginationWrapper } from '../components'
import { useColumns, usePaginatedMyCropFarmerDetails } from '../hooks'
import { useGetDealerAccountHierarchyQuery, useLazyGetPaginatedFarmerDetailsQuery } from '../store'
import styles from './MyFarmers.module.scss'

export const OldMyFarmers = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [openSearch, setOpenSearch] = useState(false)
  const [downloadRequest, setDownloadRequest] = useState(false)

  const {
    tableCol: { farmerListColumns = [] },
    csvDownload: { farmerDownloadReportColumns }
  } = useColumns()
  const availableSortOptions = [
    'farmName-asc',
    'farmName-desc',
    'city-asc',
    'city-desc',
    'state-asc',
    'state-desc',
    'gln-asc',
    'gln-desc',
    'irdId-asc',
    'irdId-desc',
    'licenseStatus-asc',
    'licenseStatus-desc',
    'currentCornZone-asc',
    'currentCornZone-desc'
  ]

  const configureHeaderByDisplayType = (farmerListColumns: TableColConfig[], navigate: NavigateFunction) => {
    if (!farmerListColumns?.length) return []

    return farmerListColumns?.map((c) => {
      const column = { ...c } as HeaderType<FarmerListDetails> & { sortable?: boolean }
      if (column.displayType === 'link') {
        column.onLinkClick = (_farmer: FarmerListDetails) => {
          navigate('/farmer-info', {
            state: { farmer: _farmer }
          })
        }
      }
      return column
    })
  }

  const res = useScreenRes()
  const farmerListFasteStoreKey = getFasteStoreKey('farmers', 'farmerList')
  const DEFAULT_PAGE_SIZE = 10
  const {
    data: dealerHierarchy,
    isFetching: isDealersFetching,
    isError: isDealersError,
    refetch: refetchDealers
  } = useGetDealerAccountHierarchyQuery()
  const dealerSapIds = _.uniq(mapTreeAsArray<AccountHierarchy, string>((node) => node.sapAccountId, dealerHierarchy))

  const dealerMap = useMemo(() => {
    const map = new Map<string, string>()
    if (dealerHierarchy) {
      const collectAccounts = (node: AccountHierarchy) => {
        if (node.sapAccountId && node.accountName) {
          map.set(node.sapAccountId, node.accountName)
        }
        if (Array.isArray(node.children)) {
          node.children.forEach(collectAccounts)
        }
      }
      collectAccounts(dealerHierarchy)
    }
    return map
  }, [dealerHierarchy])

  const [
    getPaginatedFarmerDetails,
    { data: lazyPaginatedFarmers, isFetching: isPaginatedFarmersLazyFetching, isError: isPaginatedFarmersLazyError }
  ] = useLazyGetPaginatedFarmerDetailsQuery()

  const [getAllFarmerDetails, { data: lazyAllPaginatedFarmers, isFetching: isAllPaginatedFarmersLazyFetching }] =
    useLazyGetPaginatedFarmerDetailsQuery()
  const {
    data: initialPaginatedFarmers,
    isLoading: isPagniatedFarmersInitialLoading,
    isError: isPaginatedFarmersErrors,
    refetch: refetchFarmerDetails
  } = usePaginatedMyCropFarmerDetails({
    dealerSapIds,
    pageSize: DEFAULT_PAGE_SIZE,
    currentPage: 0,
    sort: 'farmName-asc'
  })
  useEffect(() => {
    if (lazyAllPaginatedFarmers && downloadRequest) {
      setDownloadRequest(false)
      downloadXlsx(
        farmerDownloadReportColumns as { displayName: string; id: string }[],
        formatFarmerDetailsCSVData(lazyAllPaginatedFarmers.farmerDetails as FarmerListDetails[], dealerMap),
        'My-Farmers_Full-Farmer-Report_'
      )
    }
  }, [lazyAllPaginatedFarmers, downloadRequest, farmerDownloadReportColumns, dealerMap])

  const paginatedFarmers = lazyPaginatedFarmers || initialPaginatedFarmers
  const { farmerDetails, pagination } = paginatedFarmers || {
    farmerDetails: [],
    pagination: { totalCount: 0, totalPages: 0 }
  }
  const isPaginatedFarmersFetching =
    isDealersFetching || isPagniatedFarmersInitialLoading || isPaginatedFarmersLazyFetching
  const isError = isDealersError || isPaginatedFarmersErrors || isPaginatedFarmersLazyError

  const fetchGrowersByPage = useCallback(
    (pageState: { pageSize: number; currentPage: number }, sort = 'farmName-asc', search = '') => {
      setSearchTerm(search)
      getPaginatedFarmerDetails({ dealerSapIds, ...pageState, sort, search })
    },
    [dealerSapIds, getPaginatedFarmerDetails]
  )

  const allRefetchs = useCallback(() => {
    refetchFarmerDetails()
    refetchDealers()
  }, [refetchFarmerDetails, refetchDealers])

  const getMessageContent = useCallback(() => {
    let messageHeaderKey
    if (isError) messageHeaderKey = 'farmers_api_error_header_msg'
    else if (farmerDetails?.length === 0) messageHeaderKey = 'farmers.no_data_header_msg'
    else messageHeaderKey = 'common.no_matching_results_message_header_label'

    let messageDescriptionKey
    if (isError) messageDescriptionKey = 'farmer.api_error_description_msg'
    else if (farmerDetails?.length === 0) messageDescriptionKey = 'farmers.no_data_description_msg'
    else messageDescriptionKey = 'common.no_results_message_description'

    const messageHeader = t(messageHeaderKey)
    const messageDescription = t(messageDescriptionKey)

    return (
      <MessageWithAction
        messageHeader={messageHeader}
        messageDescription={messageDescription}
        primaryButtonProps={
          isError
            ? {
                label: t('common.try_again.label'),
                variant: 'text',
                themeColor: 'danger',
                onClick: allRefetchs
              }
            : undefined
        }
        iconProps={{
          icon: 'info',
          variant: 'filled-secondary',
          className: 'gc-icon-info'
        }}
      />
    )
  }, [t, isError, farmerDetails?.length, allRefetchs])

  const configSort = configSortToSortBy(farmerListColumns)
  const mobileSortKey = configSort?.[0]?.id ?? ''

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
    onChangeSearch(e.target.value)
  }

  const onChangeSearch = useMemo(() => {
    return _.debounce((searchStr: string) => {
      fetchGrowersByPage({ pageSize: DEFAULT_PAGE_SIZE, currentPage: 0 }, 'farmName-asc', searchStr)
    }, 900)
  }, [fetchGrowersByPage])

  const handleOpenSearch = () => {
    setOpenSearch(true)
  }

  const handleCloseSearch = () => {
    setSearchTerm('')
    setOpenSearch(false)
    onChangeSearch('')
  }
  const handleCancelSearch = () => {
    setSearchTerm('')
    onChangeSearch('')
  }

  const goToFarmersDetails = <T,>(code: string, row: T | undefined) => {
    navigate('/farmer-info', {
      state: { farmer: row ?? {} }
    })
  }

  const getMobileFarmerName = (farmer: FarmerListDetails) => {
    return farmer.farmName
  }

  const getMobileRedirectLink = () => {
    return '/farmer-info'
  }

  const mobileDataListItem = (farmer: FarmerListDetails) => ({
    code: getMobileRedirectLink(),
    row: farmer,
    leadingBlock: (
      <div>
        <TextBubble text={getMobileFarmerName(farmer).charAt(0)} themeColor='blue' />
      </div>
    ),
    primaryText: <TypoSubtitle level={2}>{getMobileFarmerName(farmer)}</TypoSubtitle>
  })

  const showTable = searchTerm || farmerDetails?.length > 0

  const onClickDownload = () => {
    getAllFarmerDetails({ dealerSapIds, fetchAll: true })
    setDownloadRequest(true)
  }
  return (
    <Grid className={styles.myFarmersContainer}>
      <GridRow className={`${styles.headerRow} ${styles.header_padding_partial}`}>
        <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
          <BackButton />
        </GridCol>
        <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
          <TypoDisplay
            level={res > resolutions.M1023 ? 3 : 5}
            className={res <= resolutions.M1023 ? styles.welcome_msg_mobile : ''}
          >
            {`${t('common.farmerPage.headerLabel')}`}
          </TypoDisplay>
        </GridCol>
      </GridRow>
      <GridRow className={res <= resolutions.M1023 ? styles.pageActionRowStart : styles.pageActionRowEnd}>
        <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
          <Button
            variant='outlined'
            themeColor='primary'
            disabled={!farmerDetails?.length || isAllPaginatedFarmersLazyFetching}
            buttonSize='small'
            leadingIcon={
              isAllPaginatedFarmersLazyFetching && (
                <span style={{ marginTop: '-9px' }}>
                  <Loading type='circular' size='sm' />
                </span>
              )
            }
            onClick={onClickDownload}
          >
            {!isAllPaginatedFarmersLazyFetching && (
              <span>
                <Icon icon='file_download' />
                {t('farmers.tables.farmerList.buttonLabel')}
              </span>
            )}
          </Button>
        </GridCol>
      </GridRow>
      <GridRow className={styles.content}>
        {showTable ? (
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <MediaQuery minWidth={IS_DESKTOP}>
              <ManualTablePaginationWrapper
                isLoading={isPaginatedFarmersFetching}
                headers={configureHeaderByDisplayType(farmerListColumns, navigate)}
                title={t('farmers.tables.farmerList.tableTitle')}
                data={farmerDetails}
                totalPageCount={pagination?.totalCount || 0}
                availableSortOptions={availableSortOptions}
                onChangeHandler={fetchGrowersByPage}
                columns={farmerListColumns}
                csvFileName='My-Farmers_Farmers_'
                defaultSortBy={{ label: 'Farm Name', value: 'farmName-asc' }}
              />
            </MediaQuery>
            <MediaQuery maxWidth={IS_MOBILE}>
              <TopAppBar
                leadingContent={openSearch || searchTerm !== '' ? 'search' : 'title'}
                title={t('common.farmer.label_other')[0].toUpperCase() + t('common.farmer.label_other').slice(1)}
                className={styles.farmer_search}
                isModalTopBar={true}
                trailingIconButtonProps={[{ icon: 'search', onClick: handleOpenSearch }]}
                searchProps={{
                  searchTerm,
                  onChange: handleSearch,
                  onClear: handleCancelSearch,
                  closeSearchButtonProps: { onClick: handleCloseSearch }
                }}
              />
              <ManualPaginationList
                isLoading={isPaginatedFarmersFetching}
                totalPages={pagination?.totalPages || 0}
                data={farmerDetails as FarmerListDetails[]}
                searchTerm={searchTerm}
                pageSize={DEFAULT_PAGE_SIZE}
                onChangeHandler={fetchGrowersByPage}
                mobileDataListItem={mobileDataListItem}
                onListAction={goToFarmersDetails}
                uniqueItemKey={'growerIrdId'}
                fasteStoreKey={farmerListFasteStoreKey}
                sortProps={{
                  options: [
                    {
                      label: t('common.name_a-z.label'),
                      columnName: mobileSortKey,
                      sortingType: 'asc'
                    },
                    {
                      label: t('common.name_z-a.label'),
                      columnName: mobileSortKey,
                      sortingType: 'desc'
                    }
                  ]
                }}
              />
            </MediaQuery>
          </GridCol>
        ) : (
          <GridCol
            desktopCol={12}
            tabletCol={8}
            phoneCol={4}
            className={styles.container_contingency}
            verticalAlign='middle'
          >
            {isPaginatedFarmersFetching ? (
              <Loading label={t('common.loading_farmers_message.label')} />
            ) : (
              getMessageContent()
            )}
          </GridCol>
        )}
      </GridRow>
    </Grid>
  )
}

export default OldMyFarmers
