.container {
  position: relative;
  @media (max-width: 1023px) {
    padding: 24px;
    position: relative;
  }

  @media (min-width: 1024px) {
    padding: 0px 80px;
  }
  @media (max-width: 719px) {
    padding: 0px;
    margin-bottom: 24px;
  }
}
.header {
  @media (max-width: 719px) {
    padding: 24px 16px;
  }
}

.credit_limit {
  max-width: 592px;
  margin-top: 48px;

  @media (max-width: 1023px) {
    margin-top: 24px;
  }
}
.farmer_tabs {
  margin: 48px 0;
  @media (max-width: 1023px) {
    margin: 24px 0;
  }
}
.mobile_create_btn {
  position: fixed !important;
  bottom: 110px !important;
  right: 16px !important;
  border-radius: 16px !important;
  z-index: 100;
  height: 48px !important;
  @media (min-width: 600px) {
    bottom: 20px;
  }
}

.desktop_top_bar {
  position: fixed;
  height: 63px;
  width: 100%;
  top: 0px;
  background: #fff;
  z-index: 10;
  right: 0;
  left: 81px;
  line-height: 64px;

  .trailing_icon {
    margin-left: 16px;
    margin-top: 12px;
  }

  .title {
    margin-left: 22px;
    line-height: 64px;
  }
}
:global(.mdc-tab-scroller) {
  display: grid;
}
.credit_limit_loader {
  padding: 25px;
}
.tab_content {
  @media (min-width: 1024px) {
    margin-top: 48px;
  }

  @media (max-width: 719px) {
    margin-top: 8px;
  }

  @media (min-width: 720px) and (max-width: 1023px) {
    margin: 8px -16px;
  }
}
