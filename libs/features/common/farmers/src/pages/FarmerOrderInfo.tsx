import { List } from '@element/react-list'
import { TypoDisplay } from '@element/react-typography'
import { TopAppBar } from '@gc/components'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

import styles from './FarmerOrderInfo.module.scss'

export const FarmerOrderInfo = () => {
  const navigate = useNavigate()
  const { t } = useTranslation()
  const {
    state: { product, usr, year }
  } = window.history

  const createItem = (year: number, label: string, value: string | number) => ({
    primaryText: `${year} ${label}`,
    trailingBlock: <div className={styles.centered}>{value || 0}</div>
  })

  const getItems = () => {
    if (!year) return []

    return [
      createItem(year, 'Orders', product?.currentYearGrowerOrder),
      createItem(year - 1, 'Sales', product?.priorYearGrowerOrder),
      createItem(year - 2, 'Sales', product?.priorYearGrowerOrderMinus1)
    ]
  }

  const handleBackClick = () => {
    navigate('/farmer-info', { state: usr })
  }

  return (
    <div>
      <TopAppBar
        title={t('farmers.farmerDetails.orderDetails.appBar.title')}
        leadingIconButtonProps={{ icon: 'arrow_back', onClick: handleBackClick }}
      />
      <div className={styles.detailsContainer}>
        <TypoDisplay className={styles.header} level={5}>
          {product?.product || ''}
        </TypoDisplay>
        <List nonInteractive trailingBlockType='meta' items={getItems()} />
      </div>
    </div>
  )
}

export default FarmerOrderInfo
