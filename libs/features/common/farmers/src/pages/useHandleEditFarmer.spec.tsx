/* eslint-disable prettier/prettier */
import { useDispatch } from 'react-redux'

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn()
}))

describe('Edit Farmer', () => {
  describe('extract10DigitPhone', () => {
    const extract10DigitPhone = (input: string) => input.replace(/\D/g, '').slice(-10)

    it('should extract last 10 digits from mixed string', () => {
      expect(extract10DigitPhone('+****************')).toBe('8001234567')
    })

    it('should return input if already 10 digits', () => {
      expect(extract10DigitPhone('1234567890')).toBe('1234567890')
    })

    it('should return last 10 digits from longer input', () => {
      expect(extract10DigitPhone('00011234567890')).toBe('1234567890')
    })

    it('should return empty string for non-digit input', () => {
      expect(extract10DigitPhone('abc-def-ghij')).toBe('')
    })
  })

  describe('handleEditFarmer', () => {
    const dispatchMock = jest.fn()
    const openModalMock = jest.fn()
    const extract10DigitPhone = (input: string) => input.replace(/\D/g, '').slice(-10)

    beforeEach(() => {
      (useDispatch as unknown as jest.Mock).mockReturnValue(dispatchMock)
      dispatchMock.mockClear()
      openModalMock.mockClear()
    })

    it('should dispatch correct actions with provided farmerInfo', () => {
      const farmerInfo = {
        sourceId: '123',
        name: 'John Doe',
        pricingZones: [{ pricingZoneCode: 'Z1' }],
        sapSalesAreas: [{ salesOrg: 'S1' }],
        contactInfo: [{ mobileNumber: '+****************', phoneNumber: '************', email: '<EMAIL>' }],
        address: [{ address1Text: '123 Main St', cityTown: 'Denver', stateProvinceCode: 'CO', postalCode: '80202' }]
      }

      const handleEditFarmer = () => {
        const { contactInfo = [] } = farmerInfo

        const { mobileNumber = '', phoneNumber = '' } = contactInfo[0] ?? {}
        // const { cityTown = '', stateProvinceCode = '' } = address[0] ?? {}

        dispatchMock({ type: 'clearAddFarmer' })
        dispatchMock({ type: 'setFarmerId', payload: '123' })
        dispatchMock({ type: 'setSalesAreaCode', payload: 'Z1' })
        dispatchMock({ type: 'setSalesOrgCode', payload: 'S1' })
        dispatchMock({
          type: 'setFarmerContactAddress',
          payload: {
            businessName: 'John Doe',
            firstName: '',
            lastName: '',
            mobile: extract10DigitPhone(mobileNumber),
            secondaryPhone: extract10DigitPhone(phoneNumber),
            email: '<EMAIL>',
            farmerId: '123',
            salesAreaCode: 'Z1',
            salesOrgCode: 'S1'
          }
        })
        dispatchMock({
          type: 'setFarmerLocationAddress',
          payload: {
            physicalAddress: {
              addressLine1: '123 Main St',
              addressLine2: '',
              city: 'DENVER',
              state: { text: 'CO', value: 'CO' },
              zipCode: '80202'
            },
            POBoxAddress: {
              postBoxNumber: '',
              postBoxCity: '',
              postBoxCounty: '',
              postBoxState: ''
            }
          }
        })

        openModalMock({ name: 'ADD_FARMER', props: { action: 'edit' } })
      }

      handleEditFarmer()

      expect(dispatchMock).toHaveBeenCalled()
      expect(openModalMock).toHaveBeenCalledWith({ name: 'ADD_FARMER', props: { action: 'edit' } })
    })
  })
})
