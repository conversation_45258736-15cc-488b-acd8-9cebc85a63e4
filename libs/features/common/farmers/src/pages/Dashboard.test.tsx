import '@testing-library/jest-dom'

import { <PERSON><PERSON>ey as mockPortalKey } from '@gc/types'
import { render, screen } from '@testing-library/react'

import Dashboard from './Dashboard'

jest.mock('@gc/hooks/useFasteStore', () => ({
  __esModule: true,
  ...jest.requireActual('@gc/hooks/useFasteStore'),
  useSelectedAccount: jest.fn(() => ({
    lob: {}
  })),
  usePortalConfig: jest.fn(() => ({
    portalKey: mockPortalKey.MyCrop
  })),
  useFarmersModuleConfig: jest.fn(() => ({
    farmerDashboardConfig: []
  }))
}))

jest.mock('../hooks/useFarmerDetails', () => ({
  __esModule: true,
  useFarmerDetails: jest.fn(() => ({
    data: { licensedGrowerTotals: undefined },
    isLoading: false,
    isError: false
  }))
}))

jest.mock('../store', () => ({
  __esModule: true,
  ...jest.requireActual('../store'),
  useGetUnitsDetailsQuery: jest.fn(() => ({
    data: {},
    isLoading: false,
    isError: false
  }))
}))

test('renders title', () => {
  render(<Dashboard />)
  const element = screen.getByText('FARMERS', { selector: 'span.mdc-typography--headline3' })
  expect(element).toBeInTheDocument()
})
