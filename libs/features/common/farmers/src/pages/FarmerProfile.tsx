/* eslint-disable @nx/enforce-module-boundaries */
import { TabBar } from '@element/react-tabs'
import { ActionMenuButton, DeliveryList, FarmersReturnList, OrdersList, QuotesList, TopAppBar } from '@gc/components'
import { IS_MOBILE } from '@gc/constants'
import {
  useAppSessionData,
  useFarmerActions,
  useFarmerViewContact,
  useIsMobile,
  useMemoizedTranslation,
  useModal,
  usePortalConfig,
  useTabs
} from '@gc/hooks'
import {
  clearAddFarmer,
  getModal,
  setFarmerContactAddress,
  setFarmerId,
  setFarmerLocationAddress,
  setSalesAreaCode,
  setSalesOrgCode
} from '@gc/redux-store'
import { Address, Farmer, PendingFarmer } from '@gc/types'
import { fasteRoute, getFasteStoreKey } from '@gc/utils'
import _ from 'lodash'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import MediaQuery from 'react-responsive'
import { useNavigate, useParams } from 'react-router-dom'

import FarmerProfileHeader from '../components/FarmerProfile/FarmerProfileHeader'
import { useFarmerDetails as useGetFarmersQuery } from '../hooks/useFarmerDetails'
import { useAppDispatch } from '../store'
import styles from './FarmerProfile.module.scss'

const FarmerTabBar = memo(
  ({
    currentTab,
    handleTabActivated,
    ComponentTabs: FarmerTabs
  }: {
    currentTab: number
    handleTabActivated: (index: number) => void
    ComponentTabs: () => JSX.Element[] | undefined
  }) => {
    return (
      <TabBar
        clusterAlign='start'
        clustered={true}
        elevated={false}
        stacked={false}
        variant='surface'
        className={styles['tabs']}
        activeTabIndex={currentTab}
        onTabActivated={handleTabActivated}
      >
        <FarmerTabs />
      </TabBar>
    )
  },
  (prevProps, nextProps) => {
    return prevProps.currentTab === nextProps.currentTab
  }
)

export function FarmerProfile() {
  const navigate = useNavigate()
  const isMobile = useIsMobile()
  const { code } = useParams()
  const t = useMemoizedTranslation()

  const dispatch = useAppDispatch()
  const appSessionData = useAppSessionData()
  const { openModal } = useModal()

  const portalConfig = usePortalConfig()
  const fasteStoreKey = getFasteStoreKey('farmers', 'quotes')
  const farmerTabs = portalConfig?.gcPortalConfig?.farmerTabs
  const actions = portalConfig?.farmersModule?.farmersProfile?.actions ?? []

  const historyState = useMemo(() => window.history.state ?? {}, [])
  const { ComponentTabs: FarmerTabs } = useTabs(farmerTabs)

  const [openSearch, setOpenSearch] = useState(false)
  const [currentTab, setCurrentTab] = useState(historyState.tab ? Number(historyState.tab) : 0)
  const [searchTerm, setSearchTerm] = useState(_.get(appSessionData, `${fasteStoreKey}.searchTerm`, '') as string)

  const listItemsDesktop = useFarmerActions(actions)
  const { data: { farmerDetails } = { farmerDetails: [] } } = useGetFarmersQuery()
  const farmerInfo = farmerDetails.find((row): row is Farmer => (row as Farmer).sourceId === code) as Farmer

  const { contactBlocks, handleCallAction, handleDirectionsAction } = useFarmerViewContact()

  const modal = useSelector(getModal)
  const createDeliveryStarted = useMemo(() => modal?.name === 'CREATE_DELIVERY', [modal?.name])

  const handleQuoteCreate = useCallback(() => {
    fasteRoute(`/quotes`, { farmer: farmerInfo })
  }, [farmerInfo])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const handleOpenSearch = () => {
    setOpenSearch(true)
  }

  const handleCloseSearch = () => {
    setSearchTerm('')
    setOpenSearch(false)
  }
  const handleCancelSearch = () => {
    setSearchTerm('')
  }

  const handleTabActivated = (index: number) => {
    window.history.replaceState({ tab: index }, '')
    setCurrentTab(index)
  }

  const FarmerTabContent = useMemo(() => {
    switch (currentTab) {
      case 0:
        return (
          <QuotesList
            tableTitle={`${farmerInfo ? farmerInfo.name : ''} ${t('quotes.label')}`}
            soldToAccounts={[`${code}`]}
            searchTerm={searchTerm}
            fasteStoreKey={getFasteStoreKey('farmers', 'quotes')}
            handleCreateQuote={handleQuoteCreate}
          />
        )
      case 1:
        return (
          <OrdersList
            tableTitle={`${farmerInfo ? farmerInfo.name : ''} ${t('orders.orders.label')}`}
            soldToAccounts={[`${code}`]}
            searchTerm={searchTerm}
            fasteStoreKey={getFasteStoreKey('farmers', 'orders')}
            dispatch={dispatch}
          />
        )
      case 2:
        return (
          <DeliveryList
            usage='farmer'
            tableTitle={`${farmerInfo ? farmerInfo.name : ''} ${t('deliveries.deliveries.label')}`}
            farmerSapId={`${code}`}
            searchTerm={searchTerm}
            fasteStoreKey={getFasteStoreKey('farmers', 'deliveries')}
          />
        )
      case 3:
        return (
          <FarmersReturnList
            tableTitle={`${farmerInfo ? farmerInfo.name : ''} ${t('returns.returns.label')}`}
            soldToAccounts={[`${code}`]}
            searchTerm={searchTerm}
            fasteStoreKey={getFasteStoreKey('farmers', 'returns')}
            dispatch={dispatch}
          />
        )
    }
  }, [code, currentTab, dispatch, farmerInfo, handleQuoteCreate, searchTerm, t])

  useEffect(() => {
    if (isMobile && historyState.tab) {
      setCurrentTab(historyState.tab)
    }
  }, [historyState, isMobile])

  useEffect(() => {
    if (createDeliveryStarted) {
      setCurrentTab(2)
    }
  }, [createDeliveryStarted])

  // Contact modal start here

  const formatAddress = useCallback((parts: (string | undefined)[]) => {
    return parts.filter(Boolean).join(', ')
  }, [])

  const extract10DigitPhone = useCallback((input: string) => input.replace(/\D/g, '').slice(-10), [])

  const getAddress = useCallback(
    (addressType: string) => {
      return farmerInfo?.address.find((address: Address) => address.addressType === addressType)
    },
    [farmerInfo?.address]
  )

  const getFarmerAddress = useCallback(() => {
    const address = getAddress('SHIPPING')
    if (!address) return { address1: '', address2: '', address3: '' }
    const address1 = `${address?.address1Text ?? ''}`
    const address2 = `${address?.address2Text ?? ''}`
    const address3 = formatAddress([
      address?.cityTown?.toUpperCase(),
      address?.countyDivision?.name?.toUpperCase(),
      address?.stateProvinceCode?.toUpperCase(),
      address?.postalCode
    ])
    return { address1, address2, address3 }
  }, [formatAddress, getAddress])

  const getFarmerPoBoxAddress = useCallback(() => {
    const address = getAddress('MAILING')
    if (!address) return { poBoxAddress1: '', poBoxAddress2: '' }
    const poBoxAddress1 = `${address?.poBox ?? ''}`
    const poBoxAddress2 = formatAddress([
      address?.cityTown?.toUpperCase(),
      address?.countyDivision?.name?.toUpperCase(),
      address?.stateProvinceCode?.toUpperCase(),
      address?.postalCode
    ])
    return { poBoxAddress1, poBoxAddress2 }
  }, [formatAddress, getAddress])

  const handleEditFarmer = useCallback(() => {
    const {
      sourceId = '',
      pricingZones = [],
      sapSalesAreas = [],
      contactInfo = [],
      name = ''
    } = farmerInfo || ({} as PendingFarmer)

    const { mobileNumber = '', phoneNumber = '', email = '' } = contactInfo?.[0] ?? {}
    const {
      address1Text = '',
      address2Text = '',
      cityTown = '',
      stateProvinceCode = '',
      postalCode = ''
    } = getAddress('SHIPPING') ?? {}
    const city = cityTown.toUpperCase()
    const state = stateProvinceCode.toUpperCase()

    const {
      poBox = '',
      cityTown: poBoxCity = '',
      stateProvinceCode: poBoxStateCode = '',
      postalCode: poBoxPostalCode = ''
    } = getAddress('MAILING') ?? {}

    dispatch(clearAddFarmer())
    dispatch(setFarmerId(sourceId))
    dispatch(setSalesAreaCode(pricingZones[0]?.pricingZoneCode ?? ''))
    dispatch(setSalesOrgCode(sapSalesAreas[0]?.salesOrg ?? ''))

    dispatch(
      setFarmerContactAddress({
        businessName: name,
        firstName: '',
        lastName: '',
        mobile: extract10DigitPhone(mobileNumber),
        secondaryPhone: extract10DigitPhone(phoneNumber),
        email,
        farmerId: sourceId,
        salesAreaCode: pricingZones[0]?.pricingZoneCode ?? '',
        salesOrgCode: sapSalesAreas[0]?.salesOrg ?? ''
      })
    )

    dispatch(
      setFarmerLocationAddress({
        physicalAddress: {
          addressLine1: address1Text,
          addressLine2: address2Text,
          city,
          state: { text: state, value: state },
          zipCode: postalCode
        },
        POBoxAddress: {
          postBoxNumber: poBox,
          postBoxCity: poBoxCity,
          postBoxState: { text: poBoxStateCode?.toUpperCase(), value: poBoxStateCode?.toUpperCase() },
          postBoxZipCode: poBoxPostalCode
        }
      })
    )

    openModal({ name: 'ADD_FARMER', props: { action: 'edit' } })
  }, [dispatch, extract10DigitPhone, farmerInfo, getAddress, openModal])

  const getFarmerContactInfo = useCallback(() => {
    if (!farmerInfo) return []
    const { firstName = '', lastName = '', name = '' } = farmerInfo as unknown as PendingFarmer
    const contactInfo = farmerInfo.contactInfo?.[0] ?? {}
    return [
      (firstName?.trim() || lastName?.trim()) && {
        label: t('common.farmer_name.label'),
        value: `${firstName ?? ''} ${lastName ?? ''}`.trim()
      },
      name?.trim() && {
        label: t('farmers.farmerDetails.list.business_name.label'),
        value: name
      },
      contactInfo.mobileNumber?.trim() && {
        label: t('common.mobile_phone.label'),
        value: contactInfo.mobileNumber
      },
      contactInfo.phoneNumber?.trim() && {
        label: t('common.secondary_phone.label'),
        value: contactInfo.phoneNumber
      },
      contactInfo.email?.trim() && {
        label: t('farmers.farmerDetails.list.email'),
        value: contactInfo.email
      }
    ].filter(Boolean)
  }, [farmerInfo, t])

  const openViewContactModal = useCallback(() => {
    const { address1, address2, address3 } = getFarmerAddress()
    const { poBoxAddress1, poBoxAddress2 } = getFarmerPoBoxAddress()
    openModal({
      name: 'VIEW_CONTACT',
      props: {
        blocks: contactBlocks(
          getFarmerContactInfo() as { label: string; value: string }[],
          [address1, address2, address3],
          [poBoxAddress1, poBoxAddress2],
          true // Show labels in contact info
        ),
        mobilePrimaryActHandler: () => handleCallAction(farmerInfo?.contactInfo?.[0]?.phoneNumber ?? ''),
        mobileSecondaryActHandler: () => handleDirectionsAction(`${address1} ${address2}`),
        handleEditFarmer: () => handleEditFarmer()
      }
    })
  }, [
    getFarmerAddress,
    getFarmerPoBoxAddress,
    openModal,
    contactBlocks,
    getFarmerContactInfo,
    handleCallAction,
    farmerInfo?.contactInfo,
    handleDirectionsAction,
    handleEditFarmer
  ])

  const contactButton = {
    label: t('farmers.farmerDetails.actionButton.contact'),
    onClick: () => openViewContactModal(),
    variant: 'outlined',
    hide: IS_MOBILE
  }
  // Contact modal end here

  return (
    <>
      <MediaQuery maxWidth={IS_MOBILE}>
        <TopAppBar
          leadingContent={openSearch || searchTerm !== '' ? 'search' : 'title'}
          title={t('common.farmer.header.label')}
          leadingIconButtonProps={{ icon: 'arrow_back', onClick: () => navigate('/') }}
          trailingIconButtonProps={[{ icon: 'search', onClick: handleOpenSearch }]}
          searchProps={{
            searchTerm,
            onChange: handleSearch,
            onClear: handleCancelSearch,
            closeSearchButtonProps: { onClick: handleCloseSearch }
          }}
        />
      </MediaQuery>

      <FarmerProfileHeader
        farmerInfo={farmerInfo}
        listItemsDesktop={farmerInfo ? listItemsDesktop : []}
        desktopButton={farmerInfo ? [contactButton] : []}
      />

      {/* Mobile */}

      {!modal?.open && farmerInfo && (
        <MediaQuery maxWidth={IS_MOBILE}>
          <ActionMenuButton
            leadingIcon='add'
            buttonLabel={t('common.actions.label')}
            actionItems={listItemsDesktop}
            data={farmerInfo}
          />
        </MediaQuery>
      )}

      <div className={styles.container}>
        <FarmerTabBar currentTab={currentTab} handleTabActivated={handleTabActivated} ComponentTabs={FarmerTabs} />
        <div className={styles.tab_content}>{FarmerTabContent}</div>
      </div>
    </>
  )
}

export default FarmerProfile
