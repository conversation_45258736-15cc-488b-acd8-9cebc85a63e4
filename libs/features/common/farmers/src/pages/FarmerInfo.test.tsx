import {
  angellFarmerDetails,
  myCropFasteStoreBuilder,
  myCropPortalConfig as mockMyCropPortalConfig,
  renderWithTestWrapper
} from '@gc/shared/test'
import { screen } from '@testing-library/react'

import { setupStore } from '../store'
import FarmerInfo from './FarmerInfo'

jest.mock('@gc/hooks', () => ({
  __esModule: true,
  ...jest.requireActual('@gc/hooks'),
  useFarmersModuleConfig: jest.fn(() => mockMyCropPortalConfig.farmersModule)
}))

jest.mock('../hooks/useColumns', () => ({
  __esModule: true,
  useColumns: jest.fn(() => ({
    tableCol: {
      farmerCropTotalsColumns: [],
      farmerProductsOrderedColumns: [],
      zoneDetailsColumns: []
    },
    csvDownload: {}
  }))
}))

jest.mock('../hooks/useFarmerOrderDetails', () => ({
  __esModule: true,
  useFarmerOrderDetails: jest.fn(() => ({
    data: [],
    isLoading: false,
    isError: false
  }))
}))

jest.mock('../store', () => ({
  __esModule: true,
  ...jest.requireActual('../store'),
  useGetSingleFarmerDetailsQuery: jest.fn(() => [jest.fn(), { data: {} }]),
  useLazyGetSingleFarmerDetailsQuery: jest.fn(() => [jest.fn(), { data: {} }])
}))

test('renders title', () => {
  renderWithTestWrapper(<FarmerInfo />, {
    fasteStore: myCropFasteStoreBuilder.build(),
    reduxProps: {
      store: setupStore()
    },
    memoryRouterProps: {
      initialEntries: [
        { pathname: '/my-farmers' },
        { pathname: '/farmer-info', state: { farmer: angellFarmerDetails } }
      ]
    }
  })

  const farmName = screen.queryAllByText(/John/)
  const licenseStatusHeader = screen.queryAllByText(/licenseStatus/i)
  const licenseStatusData = screen.queryAllByText(/Not Licensed/)

  expect(farmName.length).toBeGreaterThan(0)
  expect(licenseStatusHeader.length).toBeGreaterThan(0)
  expect(licenseStatusData.length).toBeGreaterThan(0)
})
