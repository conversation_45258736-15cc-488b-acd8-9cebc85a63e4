import { myCropFasteStoreBuilder, renderWithTestWrapper } from '@gc/shared/test'
import { screen } from '@testing-library/react'

import { setupStore } from '../store'
import LicenseSearch from './LicenseSearch'

jest.mock('@gc/hooks', () => ({
  __esModule: true,
  ...jest.requireActual('@gc/hooks'),
  useFarmersModuleConfig: jest.fn(() => ({
    licenseFormConfig: [],
    licenseFarmersTableColumns: []
  }))
}))

test('renders title', () => {
  renderWithTestWrapper(<LicenseSearch />, {
    fasteStore: myCropFasteStoreBuilder.build(),
    reduxProps: {
      store: setupStore()
    }
  })

  const title = screen.getByText(/License Agreement/)

  expect(title).toBeInTheDocument()
})
