import { Grid, GridCol, GridRow } from '@element/react-grid'
import { TypoDisplay } from '@element/react-typography'
import { HeaderType, Table } from '@gc/components'
import { useFarmersModuleConfig, useSelectedAccount } from '@gc/hooks'
import { CropZone, FarmerDetails, GrowerAccount, Order } from '@gc/types'
import { checkFarmerDetails, fromUId, getAccountName, getContactName } from '@gc/utils'
import { skipToken } from '@reduxjs/toolkit/query/react'
import { isEmpty, mapKeys, omitBy } from 'lodash'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate } from 'react-router-dom'

import { BackButton, FarmerDetails as FarmerDetailsDisplay, TableLoadingErrorWrapper } from '../components'
import {
  useColumns,
  useCropTotals,
  useFarmerOrderDetails,
  useLazySingleFarmerDetails,
  useSingleFarmerDetails
} from '../hooks'
import styles from './FarmerInfo.module.scss'

type Farmer = FarmerDetails | GrowerAccount

type ExternalFarmer = {
  farmerirdId: string
  farmerId: string
  dealerId: string
}

const TABLE_MSG_KEYS = {
  cropTotal: {
    errorMsgHeader: 'farmer.cropTotal_api_error_header_msg',
    errorMsg: 'farmer.cropTotal_api_error_description_msg',
    noDataMsgHeader: 'farmer.cropTotal_no_data_header_msg',
    noDataMsg: 'farmer.cropTotal_no_data_description_msg',
    loadingMsg: 'farmers.tables.cropTotals.tableLoading'
  },
  productsOrdered: {
    errorMsgHeader: 'farmer.productsOrdered_api_error_header_msg',
    errorMsg: 'farmer.productsOrdered_api_error_description_msg',
    noDataMsgHeader: 'farmer.productsOrdered_no_data_header_msg',
    noDataMsg: 'farmer.productsOrdered_no_data_description_msg',
    loadingMsg: 'farmers.tables.productsOrdered.tableLoading'
  },
  zoneHistory: {
    errorMsgHeader: 'farmer.zoneHistory_api_error_header_msg',
    errorMsg: 'farmer.zoneHistory_api_error_description_msg',
    noDataMsgHeader: 'farmer.zoneHistory_no_data_header_msg',
    noDataMsg: 'farmer.zoneHistory_no_data_description_msg',
    loadingMsg: 'farmers.tables.zoneTable.tableLoading'
  }
}

const isAccount = (farmer: Farmer | ExternalFarmer): farmer is GrowerAccount => {
  return (farmer as GrowerAccount).accountName !== undefined
}

const isExternalFarmer = (farmer: ExternalFarmer | Farmer) => {
  return Object.keys(farmer).includes('farmerirdId') || Object.keys(farmer).includes('farmerId')
}

const previousPageStateTransformer = (farmer: Farmer | ExternalFarmer) => {
  if (!farmer) return {} as FarmerDetails

  let propsToRename: Record<string, string> = {}
  if (isExternalFarmer(farmer)) {
    propsToRename = {
      farmerirdId: 'growerIrdId',
      farmerId: 'growerSapId',
      dealerId: 'dealerSapId'
    }
  } else if (isAccount(farmer)) {
    propsToRename = {
      accountName: 'farmName',
      irdId: 'growerIrdId',
      sapAccountId: 'growerSapId',
      uId: 'growerUId'
    }
  } // else is already FarmerDetails

  // clone, rename props
  const farmerDetails = (isEmpty(propsToRename)
    ? farmer
    : mapKeys(farmer, (value, key) => {
        return propsToRename[key] ?? key
      })) as unknown as FarmerDetails

  const { irdId, sapId } = fromUId(farmerDetails.growerUId)
  farmerDetails.growerIrdId = irdId || farmerDetails.growerIrdId
  farmerDetails.growerSapId = sapId || farmerDetails.growerSapId

  const [firstName, ...lastName] = getContactName(farmerDetails)?.split(' ') ?? ['', '']
  farmerDetails.firstName = farmerDetails.firstName || firstName
  farmerDetails.lastName = farmerDetails.lastName || lastName?.join(' ')

  return farmerDetails
}

export const FarmerInfo = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { t } = useTranslation()
  const { farmerDetailFields } = useFarmersModuleConfig()
  const previousPageFarmerInfo = location.state?.farmer || JSON.parse(localStorage.getItem('FarmerData') || '{}')
  const [farmerDetails, setFarmerDetails] = useState<FarmerDetails>(() =>
    previousPageStateTransformer(previousPageFarmerInfo)
  )
  const {
    tableCol: { farmerCropTotalsColumns, farmerProductsOrderedColumns, zoneDetailsColumns }
  } = useColumns()
  const { sapAccountId } = useSelectedAccount()
  const [getFarmerDetails] = useLazySingleFarmerDetails()

  const showZoneHistory = Boolean(zoneDetailsColumns?.length)

  useEffect(() => {
    if (isEmpty(farmerDetails)) {
      navigate('/my-farmers', { replace: true })
    } else if (!checkFarmerDetails(farmerDetails)) {
      const fetchData = async () => {
        const { data: singleFarmerData } = await getFarmerDetails({
          growerIrdId: farmerDetails.growerIrdId,
          growerSapId: farmerDetails.growerSapId
        })
        const parseSingleFarmerData = omitBy(singleFarmerData, isEmpty)
        const details = { ...farmerDetails, ...parseSingleFarmerData }
        setFarmerDetails(previousPageStateTransformer(details))
      }
      fetchData()
    }
    return () => {
      localStorage.removeItem('FarmerData')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const {
    data: farmerOrderDetails = [],
    isLoading: isFarmerOrderLoading,
    isError: isFarmerOrderError,
    refetch: reftechFarmerOrderDetails
  } = useFarmerOrderDetails({
    dealerSapId: farmerDetails.dealerSapId || sapAccountId,
    growerSapId: farmerDetails.growerSapId || farmerDetails.growerIrdId
  })
  const {
    data: { cropZones: farmerZoneHistory = [] } = {},
    isLoading: isFarmerZoneHistoryLoading,
    isError: isFarmerZoneError,
    refetch: refetchZoneHistory
  } = useSingleFarmerDetails(
    showZoneHistory
      ? {
          growerIrdId: farmerDetails.growerIrdId,
          growerSapId: farmerDetails.growerSapId
        }
      : skipToken
  )

  const cropTotalsData = useCropTotals(farmerOrderDetails)

  return (
    <>
      <Grid className={styles.farmerInfoContainer}>
        <GridRow className={styles.headerRow}>
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <BackButton />
          </GridCol>
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <TypoDisplay level={4}>{getAccountName(farmerDetails)}</TypoDisplay>
          </GridCol>
        </GridRow>
      </Grid>
      <FarmerDetailsDisplay data={farmerDetails} fields={farmerDetailFields} />
      <Grid className={styles.farmerInfoContainer}>
        <GridRow>
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <TableLoadingErrorWrapper
              tableMessages={TABLE_MSG_KEYS.cropTotal}
              isLoading={isFarmerOrderLoading}
              isError={isFarmerOrderError}
              hasNoData={!cropTotalsData.length}
              refetchDetails={reftechFarmerOrderDetails}
            >
              <Table<Order>
                title={t('farmers.farmerDetails.totalTable.title')}
                data={cropTotalsData}
                headers={farmerCropTotalsColumns as HeaderType<Order>[]}
                paginated
              />
            </TableLoadingErrorWrapper>
          </GridCol>

          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <TableLoadingErrorWrapper
              tableMessages={TABLE_MSG_KEYS.productsOrdered}
              isLoading={isFarmerOrderLoading}
              isError={isFarmerOrderError}
              hasNoData={!farmerOrderDetails.length}
              refetchDetails={reftechFarmerOrderDetails}
            >
              <Table<Order>
                title={t('farmers.farmerDetails.table.title')}
                data={farmerOrderDetails}
                headers={farmerProductsOrderedColumns as HeaderType<Order>[]}
                searchable
                paginated
                enableCsvDownload
                csvFileName={'My-Farmers_Products_Ordered_'}
                expandable={farmerProductsOrderedColumns?.[0]?.displayType === 'expandable'}
              />
            </TableLoadingErrorWrapper>
          </GridCol>

          {showZoneHistory && (
            <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
              <TableLoadingErrorWrapper
                tableMessages={TABLE_MSG_KEYS.zoneHistory}
                isLoading={isFarmerZoneHistoryLoading}
                isError={isFarmerZoneError}
                hasNoData={!farmerZoneHistory.length}
                refetchDetails={refetchZoneHistory}
              >
                <Table<CropZone>
                  title={t('farmers.farmerDetails.zoneTable.title')}
                  data={farmerZoneHistory}
                  headers={zoneDetailsColumns as HeaderType<CropZone>[]}
                  paginated
                />
              </TableLoadingErrorWrapper>
            </GridCol>
          )}
        </GridRow>
      </Grid>
    </>
  )
}

export default FarmerInfo
