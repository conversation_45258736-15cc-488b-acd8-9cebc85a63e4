/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @nx/enforce-module-boundaries */
import { Banner } from '@element/react-components'
import { Grid, GridCol, GridRow } from '@element/react-grid'
import { Icon } from '@element/react-icon'
import { TextBubble } from '@element/react-text-bubble'
import { TypoDisplay, TypoSubtitle } from '@element/react-typography'
import {
  FloatingButton,
  Header,
  HeaderType,
  List,
  Loading,
  MessageWithAction,
  Table,
  TableMenu,
  TableMenuListItem,
  TopAppBar
} from '@gc/components'
import { IS_DESKTOP, IS_MOBILE, resolutions } from '@gc/constants'
import {
  useCurrentCart,
  useEntitlement,
  useFarmerInactivation,
  useIsTablet,
  useMemoizedTranslation,
  useModal,
  usePortalConfig,
  useScreenRes,
  useSearch,
  useSetCurrentCart,
  useUpdateSortBy,
  useUser
} from '@gc/hooks'
import { clearAddFarmer, getModal, useAdminSelector, useDeleteCartMutation, useGlobalDispatch } from '@gc/redux-store'
import { Farmer, FarmerDetails, PortalKey, SortByConfig, TableColConfig } from '@gc/types'
import { configSortToSortBy, fasteRoute, getFasteStoreKey } from '@gc/utils'
import { TFunction } from 'i18next'
import _ from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import MediaQuery from 'react-responsive'
import { NavigateFunction, useNavigate } from 'react-router-dom'

import { BackButton } from '../components'
import { useColumns, useFarmerDetails } from '../hooks'
import styles from './MyFarmers.module.scss'

const configureHeaderByDisplayType = (
  farmerListColumns: TableColConfig[],
  portalKey: string,
  t: TFunction<'translation', undefined>,
  navigate: NavigateFunction,
  handleFarmerInactivation: (farmer: Farmer) => void
) => {
  if (!farmerListColumns?.length) return []
  return farmerListColumns?.map((c) => {
    const column = { ...c } as HeaderType<Farmer | FarmerDetails>
    if (column.displayType === 'actionMenu' && c.actions?.length) {
      column.excludeFromDownload = true
      column.accessor = (_data) => {
        const allActions: {
          [key: string]: {
            value: string
            label: string
            onClick: () => void
          }
        } = {
          edit: {
            value: 'edit',
            label: t('common.edit.label'),
            // eslint-disable-next-line no-console
            onClick: () => console.log('Edit Clicked!')
          },
          viewDetails: {
            value: 'viewDetails',
            label: t('common.view_details.label'),
            onClick: () =>
              navigate(
                portalKey === PortalKey.Arrow || portalKey === PortalKey.Aurora
                  ? (_data as Farmer).sourceId
                  : '/farmer-info',
                {
                  state: { _data }
                }
              )
          },
          inactivate: {
            value: 'inactivate',
            label: t('common.inactivate.label'),
            onClick: () => handleFarmerInactivation(_data as Farmer)
          }
        }
        const filteredActions =
          portalKey === PortalKey.Arrow || portalKey === PortalKey.Aurora
            ? c.actions?.filter(
                (action) => action !== 'inactivate' || (_data as Farmer).partyStatus?.toUpperCase() === 'ACTIVE'
              )
            : c.actions
        return (
          <TableMenu<Farmer>
            listItems={filteredActions?.map((action) => allActions[action]) as TableMenuListItem[]}
            currentRow={_data as Farmer}
          />
        )
      }
    } else if (column.displayType === 'link') {
      column.onLinkClick = (_farmer: Farmer | FarmerDetails) => {
        navigate(
          portalKey === PortalKey.Arrow || portalKey === PortalKey.Aurora
            ? (_farmer as Farmer).sourceId
            : '/farmer-info',
          {
            state: { farmer: _farmer }
          }
        )
      }
    }
    if (
      column.displayType === 'link' &&
      (c.accessor === 'name' || (typeof c.accessor === 'function' && c.id === 'name'))
    ) {
      // Add a type-safe, null-safe sortType for farmer name
      column.sortType = (x: { original: Farmer }, y: { original: Farmer }) => {
        const a: string = x.original?.name || ''
        const b: string = y.original?.name || ''
        return a.localeCompare(b)
      }
    }
    return column
  })
}

const getMobileFarmerName = (farmer: Farmer | FarmerDetails) => {
  let name = ''
  if ('name' in farmer) name = farmer.name
  else name = farmer.farmName
  return name
}

const getMobileRedirectLink = (farmer: Farmer | FarmerDetails) => {
  let code = ''
  if ('sourceId' in farmer) code = farmer.sourceId
  else code = '/farmer-info'
  return code
}

export const MyFarmers = () => {
  const t = useMemoizedTranslation()
  const setCurrentCart = useSetCurrentCart()
  const { data: cart } = useCurrentCart()
  const [deleteCart] = useDeleteCartMutation()
  const [showBanner, setShowBanner] = useState(false)
  const navigate = useNavigate()
  const isTablet = useIsTablet()
  const user = useUser()
  const res = useScreenRes()
  const { openModal } = useModal()

  const { hasFarmerWriteAccess } = useEntitlement()

  const modal = useSelector(getModal)
  const { portalKey } = usePortalConfig()

  const {
    data: { farmerDetails } = { farmerDetails: [] },
    isLoading,
    isError,
    refetch: refetchFarmerDetails
  } = useFarmerDetails()

  const admin = useAdminSelector()
  const isUserSelected = !admin || sessionStorage.getItem('username')
  const dispatch = useGlobalDispatch()
  const inactivateFarmer = useFarmerInactivation()

  const {
    tableCol: { farmerListColumns = [] }
  } = useColumns()

  const fasteStoreKey = getFasteStoreKey('farmers', 'farmers')
  const farmerListFasteStoreKey = getFasteStoreKey('farmers', 'farmerList')

  const configSort = configSortToSortBy(farmerListColumns)
  const isArrowOrAuroraPortal = portalKey === PortalKey.Arrow || portalKey === PortalKey.Aurora
  const mobileSortKey = isArrowOrAuroraPortal ? 'name' : (configSort?.[0]?.id ?? '')

  const matchingFarmer = useMemo(
    () => farmerDetails?.find((f) => (f as Farmer).sourceId === cart?.growerInfo?.uid),
    [cart?.growerInfo?.uid, farmerDetails]
  )
  const [searchTerm, openSearch, { handleOpenSearch, handleCancelSearch, handleCloseSearch, handleSearch }] =
    useSearch(fasteStoreKey)

  useEffect(() => {
    if (!isUserSelected) {
      return
    }
    setCurrentCart(true)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []) //to be executed on mount

  useEffect(() => {
    if (!isUserSelected) {
      return
    }
    if (cart?.billToParties?.length && cart?.growerInfo) {
      setShowBanner(true)
    }
  }, [cart, isUserSelected])

  const searchFun = (farmer: Farmer | FarmerDetails, searchStr: string) => {
    const hits = (value: string) => value?.toLowerCase().includes(searchStr)
    const name: string = getMobileFarmerName(farmer)
    const matchingQuote = (name: string) => hits(name)
    return matchingQuote(name)
  }

  const searchTableFun = (farmer: Farmer | FarmerDetails, searchStr: string) => {
    const hits = (value: string) => value?.toLowerCase().includes(searchStr)
    const matchingQuote = (farmer: Farmer) =>
      hits(farmer.name) ||
      hits(farmer.partyStatus) ||
      hits(farmer.sourceId) ||
      hits(farmer.relationshipStatus) ||
      hits(farmer.contracts && farmer.contracts.length > 0 ? farmer.contracts[0].contractStatus : '')
    return matchingQuote(farmer as Farmer)
  }

  const handleAddFarmer = useCallback(() => {
    dispatch(clearAddFarmer())
    openModal({ name: 'ADD_FARMER', props: { action: 'add' } })
  }, [dispatch, openModal])

  const handleDiscardCart = useCallback(async () => {
    deleteCart({ cartId: cart?.code ?? '', skipCartRefetch: false }).unwrap()
  }, [cart?.code, deleteCart])

  const handleCreateOrder = useCallback(() => {
    fasteRoute(`/orders`, { continueOrder: true, farmer: matchingFarmer })
  }, [matchingFarmer])

  const handleCreateQuote = useCallback(() => {
    fasteRoute(`/quotes`, { continueQuote: true, farmer: matchingFarmer })
  }, [matchingFarmer])

  const goToFarmersDetails = <T,>(code: string, row: T | undefined) => {
    navigate(isArrowOrAuroraPortal ? `/${code}` : '/farmer-info', {
      state: { farmer: row ?? {} }
    })
  }

  const getMessageContent = useCallback(() => {
    const messageHeader = t(
      isError
        ? 'farmers_api_error_header_msg'
        : farmerDetails?.length === 0
          ? 'farmers.no_data_header_msg'
          : 'common.no_matching_results_message_header_label'
    )

    const messageDescription = t(
      isError
        ? 'farmer.api_error_description_msg'
        : farmerDetails?.length === 0
          ? 'farmers.no_data_description_msg'
          : 'common.no_results_message_description'
    )

    return (
      <MessageWithAction
        messageHeader={messageHeader}
        messageDescription={messageDescription}
        primaryButtonProps={
          isError
            ? {
                label: t('common.try_again.label'),
                variant: 'text',
                themeColor: 'danger',
                onClick: refetchFarmerDetails
              }
            : undefined
        }
        iconProps={{
          icon: 'info',
          variant: 'filled-secondary',
          className: 'gc-icon-info'
        }}
      />
    )
  }, [t, isError, farmerDetails?.length, refetchFarmerDetails])

  useUpdateSortBy({
    fasteStoreKey: farmerListFasteStoreKey,
    sortBy: isArrowOrAuroraPortal ? [{ id: 'name', desc: false }] : (configSort as SortByConfig[])
  })

  return (
    <Grid className={styles.myFarmersContainer}>
      {isArrowOrAuroraPortal && showBanner && (
        <Banner
          primaryButtonLabel={t('common.continue.label')}
          onPrimaryClicked={cart?.cartType?.toUpperCase() === 'QUOTE' ? handleCreateQuote : handleCreateOrder}
          secondaryButtonLabel={t('common.discard.label')}
          onSecondaryClicked={handleDiscardCart}
          media={<Icon icon='error' variant='blue' className='gc-icon-info-secondary' />}
        >
          <TypoSubtitle level={1}>
            {t('farmers.abandoned_cart_alert.message', {
              cartType: cart?.cartType,
              farmerName: cart?.growerInfo?.name
            })}
          </TypoSubtitle>
        </Banner>
      )}
      {isArrowOrAuroraPortal && (
        <div className={styles.container}>
          <MediaQuery minWidth={IS_DESKTOP}>
            <Header
              title={portalKey === PortalKey.Arrow ? `${t('common.farmerPage.headerLabel')} ${user.name}` : ''}
              buttonProps={
                hasFarmerWriteAccess
                  ? [
                      {
                        label: t('common.farmer.header.label'),
                        onClick: handleAddFarmer,
                        variant: 'filled',
                        leadingIcon: 'add',
                        disabled: !isUserSelected
                      }
                    ]
                  : []
              }
            />
          </MediaQuery>
        </div>
      )}
      {!isArrowOrAuroraPortal && (
        <GridRow className={`${styles.headerRow} ${styles.header_padding_full}`}>
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <BackButton />
          </GridCol>

          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <TypoDisplay
              level={res > resolutions.M1023 ? 3 : 5}
              className={res <= resolutions.M1023 ? styles.welcome_msg_mobile : ''}
            >
              {t('common.farmerPage.headerLabel')}
            </TypoDisplay>
          </GridCol>
        </GridRow>
      )}
      <GridRow className={styles.content}>
        {farmerDetails?.length > 0 ? (
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <MediaQuery minWidth={IS_DESKTOP}>
              <Table<Farmer | FarmerDetails>
                title={t(
                  isArrowOrAuroraPortal
                    ? 'farmers.tables.farmerList.your.tableTitle'
                    : 'farmers.tables.farmerList.tableTitle'
                )}
                data={farmerDetails as (Farmer | FarmerDetails)[]}
                headers={configureHeaderByDisplayType(farmerListColumns, portalKey, t, navigate, inactivateFarmer)}
                searchable
                paginated
                enableCsvDownload
                csvFileName={'My-Farmers_Farmers_'}
                noContentMessage={
                  <MessageWithAction
                    messageHeader={t('common.no_results_message_header_label')}
                    messageDescription={t('common.no_results_message_description')}
                    iconProps={{
                      icon: 'info',
                      variant: 'filled-secondary',
                      className: 'gc-icon-info'
                    }}
                  />
                }
                fasteStoreKey={farmerListFasteStoreKey}
                {...(isArrowOrAuroraPortal ? { customSearchFn: searchTableFun } : {})}
              />
            </MediaQuery>

            <MediaQuery maxWidth={IS_MOBILE}>
              <TopAppBar
                className={admin ? styles.aurora_top_bar : styles.farmer_search}
                leadingContent={openSearch || searchTerm !== '' ? 'search' : 'title'}
                title={t('common.farmer.label_other')[0].toUpperCase() + t('common.farmer.label_other').slice(1)}
                isModalTopBar={true}
                trailingIconButtonProps={[{ icon: 'search', onClick: handleOpenSearch }]}
                searchProps={{
                  searchTerm,
                  onChange: handleSearch,
                  onClear: handleCancelSearch,
                  closeSearchButtonProps: { onClick: handleCloseSearch }
                }}
              />
              {!modal?.open && hasFarmerWriteAccess && (
                <FloatingButton
                  leadingIcon={<Icon icon='add' />}
                  variant='filled'
                  label={t('common.farmer.header.label')}
                  themeColor='secondary'
                  buttonSize='xlarge'
                  onClick={handleAddFarmer}
                />
              )}
              {farmerDetails.length > 0 ? (
                <div className={styles.farmer_list_container}>
                  <List<Farmer | FarmerDetails>
                    noPadding={isTablet}
                    leadingBlockType={'icon'}
                    className={styles.farmer_list}
                    listItemClassName={styles.farmer_list_item}
                    divider={true}
                    onAction={goToFarmersDetails}
                    data={_.orderBy(farmerDetails, [mobileSortKey], ['asc']) as Farmer[] | FarmerDetails[]}
                    sortProps={{
                      options: [
                        {
                          label: t('common.name_a-z.label'),
                          columnName: mobileSortKey,
                          sortingType: 'asc'
                        },
                        {
                          label: t('common.name_z-a.label'),
                          columnName: mobileSortKey,
                          sortingType: 'desc'
                        }
                      ]
                    }}
                    searchTerm={searchTerm}
                    searchFn={searchFun}
                    fasteStoreKey={farmerListFasteStoreKey}
                    dataToListItem={(farmer: Farmer | FarmerDetails) => ({
                      code: getMobileRedirectLink(farmer),
                      row: farmer,
                      leadingBlock: (
                        <div>
                          <TextBubble text={getMobileFarmerName(farmer).charAt(0)} themeColor='blue' />
                        </div>
                      ),
                      primaryText: <TypoSubtitle level={2}>{getMobileFarmerName(farmer)}</TypoSubtitle>
                    })}
                  />
                </div>
              ) : (
                <MessageWithAction
                  messageHeader={t('common.no_results_message_header_label')}
                  messageDescription={t('common.no_results_message_description')}
                  iconProps={{
                    icon: 'info',
                    variant: 'filled-secondary',
                    className: 'gc-icon-info'
                  }}
                />
              )}
            </MediaQuery>
          </GridCol>
        ) : (
          <GridCol
            desktopCol={12}
            tabletCol={8}
            phoneCol={4}
            className={styles.container_contingency}
            verticalAlign='middle'
          >
            {isLoading ? <Loading label={t('common.loading_farmers_message.label')} /> : getMessageContent()}
          </GridCol>
        )}
      </GridRow>
    </Grid>
  )
}

export default MyFarmers
