import { useSelectedAccount } from '@gc/hooks'
import { skipToken } from '@reduxjs/toolkit/query/react'

import { useGetLicSingleFarmerDetailsQuery, useGetSingleFarmerDetailsQuery } from '../store'

type QueryArgs = {
  growerSapId: string
  growerIrdId: string
}

type SkipToken = typeof skipToken

export const useSingleFarmerDetails = (props: QueryArgs | SkipToken) => {
  const { lob } = useSelectedAccount()

  const queryArgs = (props as QueryArgs).growerIrdId || (props as QueryArgs).growerSapId ? props : skipToken

  if (lob === 'lic') {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    return useGetLicSingleFarmerDetailsQuery(queryArgs)
  }

  // eslint-disable-next-line react-hooks/rules-of-hooks
  return useGetSingleFarmerDetailsQuery(queryArgs)
}

export default useSingleFarmerDetails
