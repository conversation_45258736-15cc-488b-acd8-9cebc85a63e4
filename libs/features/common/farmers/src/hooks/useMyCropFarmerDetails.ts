import { AccountHierarchy } from '@gc/types'
import { mapTreeAsArray } from '@gc/utils'
import { skipToken } from '@reduxjs/toolkit/query/react'
import uniq from 'lodash/uniq'

import { useGetDealerAccountHierarchyQuery, useGetPaginatedFarmerDetailsQuery } from '../store'

export const useMyCropFarmerDetails = () => {
  const {
    data: dealerHierarchy,
    isLoading: isDealersLoading,
    isFetching: isDealersFetching,
    isError: isDealersError,
    refetch: refetchDealers
  } = useGetDealerAccountHierarchyQuery()
  const dealerSapIds = uniq(mapTreeAsArray<AccountHierarchy, string>((node) => node.sapAccountId, dealerHierarchy))

  const {
    data: farmerDetails,
    isLoading: isFarmerDetailsLoading,
    isFetching: isFarmerDetailsFetching,
    isError: isFarmerDetailsError,
    refetch: refetchFarmerDetails
  } = useGetPaginatedFarmerDetailsQuery(dealerSapIds ? { dealerSapIds, fetchAll: true } : skipToken)

  const refetch = () => {
    refetchDealers()
    refetchFarmerDetails()
  }

  const isFetching = isDealersFetching || isFarmerDetailsFetching
  const isLoading = isDealersLoading || isFarmerDetailsLoading || isFetching
  const isError = !isLoading && (isDealersError || isFarmerDetailsError)

  return {
    data: farmerDetails,
    isLoading,
    isError,
    refetch
  }
}

export default useMyCropFarmerDetails
