import { usePortalConfig, useSelectedAccount } from '@gc/hooks'
import { PortalKey } from '@gc/types'
import { skipToken } from '@reduxjs/toolkit/query/react'

import {
  useGetFarmerOrderDetailsChannelQuery,
  useGetFarmerOrderDetailsNationalQuery,
  useGetFiscalYearQuery,
  useGetLicFarmerSummaryQuery
} from '../store'

export const useFarmerOrderDetails = (props: { dealerSapId: string; growerSapId: string }) => {
  const { portalKey } = usePortalConfig()
  const { lob } = useSelectedAccount()
  const fiscalYear = useGetFiscalYearQuery()?.data

  switch (portalKey) {
    case PortalKey.MyCrop:
    case PortalKey.MyCropV2:
      // eslint-disable-next-line react-hooks/rules-of-hooks
      if (lob === 'lic') return useGetLicFarmerSummaryQuery({ ...props })

      // eslint-disable-next-line react-hooks/rules-of-hooks
      return useGetFarmerOrderDetailsNationalQuery(fiscalYear ? { ...props, fiscalYear } : skipToken)
    case PortalKey.SMS:
      // eslint-disable-next-line react-hooks/rules-of-hooks
      return useGetFarmerOrderDetailsChannelQuery(props)
    default:
      throw new Error(`The portal parameter portalKey is invalid: ${portalKey}.`)
  }
}

export default useFarmerOrderDetails
