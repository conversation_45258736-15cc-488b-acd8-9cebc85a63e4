/* eslint-disable react-hooks/rules-of-hooks */
import { useFarmerListData, usePortalConfig } from '@gc/hooks'
import { PortalKey } from '@gc/types'

import useMyCropFarmerDetails from './useMyCropFarmerDetails'
import useSeedsmanFarmerDetails from './useSeedsmanFarmerDetails'

export const useFarmerDetails = () => {
  const { portalKey } = usePortalConfig()

  switch (portalKey) {
    case PortalKey.MyCrop:
    case PortalKey.MyCropV2:
      return useMyCropFarmerDetails()
    case PortalKey.SMS:
      return useSeedsmanFarmerDetails()
    case PortalKey.Arrow:
    case PortalKey.Aurora:
      return useFarmerListData()
    default:
      throw new Error(`The portal parameter portalKey is invalid: ${portalKey}.`)
  }
}

export default useFarmerDetails
