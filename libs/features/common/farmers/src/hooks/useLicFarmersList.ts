import { useGetAllLicFarmersQuery } from '../store'

export const useLicFarmersList = () => {
  const { data, isLoading, isError, refetch: refetchLicFarmers } = useGetAllLicFarmersQuery()

  const refetch = () => {
    refetchLicFarmers()
  }

  return {
    data: data ? { farmerDetails: data, licensedGrowerTotals: undefined } : undefined,
    isLoading: isLoading,
    isError: isError,
    refetch
  }
}

export default useLicFarmersList
