import { useFarmersModuleConfig, useSelectedAccount } from '@gc/hooks'
import { CsvDownloadConfig, DomainDefFarmersModuleCols, TableColConfig } from '@gc/types'
import { uniqueId } from 'lodash'
import { useMemo } from 'react'

import { useGetFiscalYearQuery } from '../store'

const isTableColConfig = (config: TableColConfig[] | CsvDownloadConfig[]): config is TableColConfig[] => {
  return (config as TableColConfig[])?.[0].header !== undefined
}

const replaceYearTemplate = (str?: string, currentFiscalYear?: number) => {
  if (!currentFiscalYear) {
    return str?.replace(/{(current|prior)Year.*?}/, '')
  }
  return str
    ?.replace('{currentYear}', `${currentFiscalYear}`)
    .replace('{priorYear}', `${currentFiscalYear - 1}`)
    .replace('{priorYear-1}', `${currentFiscalYear - 2}`)
}

const configureTableColHeader = (column: TableColConfig, currentFiscalYear?: number) => {
  return {
    id: uniqueId(),
    ...column,
    header: replaceYearTemplate(column.header, currentFiscalYear)
  } as TableColConfig
}

const configureCsvDownloadHeader = (column: CsvDownloadConfig, currentFiscalYear?: number) => {
  return {
    ...column,
    displayName: replaceYearTemplate(column.displayName, currentFiscalYear)
  } as CsvDownloadConfig
}

export const useColumns = () => {
  const farmerModuleConfig = useFarmersModuleConfig()
  const { lob } = useSelectedAccount()
  const fiscalYear = useGetFiscalYearQuery()?.data

  return useMemo(() => {
    const fiscalYearAsNum = fiscalYear ? Number(fiscalYear) : undefined
    const formattedColConfig = Object.keys(farmerModuleConfig)
      .filter((key) => key.endsWith('Columns'))
      .reduce(
        (result, key) => {
          const colConfigs = farmerModuleConfig[key as keyof DomainDefFarmersModuleCols]
          const lobColConfig = colConfigs.find((colConfig) => colConfig.lobs?.includes(lob))?.config
          if (!lobColConfig) {
            return result
          }

          if (isTableColConfig(lobColConfig)) {
            result.tableCol = {
              ...result.tableCol,
              [key]: lobColConfig.map((colConfig: TableColConfig) =>
                configureTableColHeader(colConfig, fiscalYearAsNum)
              )
            }
          } else {
            result.csvDownload = {
              ...result.csvDownload,
              [key]: lobColConfig.map((colConfig: CsvDownloadConfig) =>
                configureCsvDownloadHeader(colConfig, fiscalYearAsNum)
              )
            }
          }

          return result
        },
        { tableCol: {}, csvDownload: {} } as {
          tableCol: { [key: string]: TableColConfig[] }
          csvDownload: { [key: string]: CsvDownloadConfig[] }
        }
      )
    return formattedColConfig
  }, [fiscalYear, lob, farmerModuleConfig])
}

export default useColumns
