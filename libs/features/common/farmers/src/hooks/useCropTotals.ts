import { Order } from '@gc/types'

export const useCropTotals = (orders: Order[]) => {
  const uniqueAggregation: Record<string, Order & { currentYearGrowerOrder: number }> = {}

  orders.forEach((order) => {
    const {
      cropName,
      currentYearGrowerOrder = 0,
      currentYearNetGPOS,
      priorYearGrowerOrder,
      priorYearGrowerOrderMinus1
    } = order

    uniqueAggregation[cropName] ??= {
      cropName,
      currentYearGrowerOrder: 0,
      currentYearNetGPOS: 0,
      priorYearGrowerOrder: 0,
      priorYearGrowerOrderMinus1: 0
    }

    uniqueAggregation[cropName].currentYearGrowerOrder += currentYearGrowerOrder
    uniqueAggregation[cropName].currentYearNetGPOS += currentYearNetGPOS
    uniqueAggregation[cropName].priorYearGrowerOrder += priorYearGrowerOrder
    uniqueAggregation[cropName].priorYearGrowerOrderMinus1 += priorYearGrowerOrderMinus1
  })

  return Object.values(uniqueAggregation)
}

export default useCropTotals
