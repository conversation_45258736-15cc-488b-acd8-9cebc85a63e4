import { skipToken } from '@reduxjs/toolkit/query/react'

import { useGetPaginatedFarmerDetailsQuery } from '../store'

interface UsePaginatedMyCropFarmerDetailsParams {
  dealerSapIds: string[]
  pageSize: number
  currentPage: number
  sort: string
}

export const usePaginatedMyCropFarmerDetails = ({
  dealerSapIds,
  pageSize,
  currentPage,
  sort
}: UsePaginatedMyCropFarmerDetailsParams) => {
  const {
    data: farmerDetails,
    isLoading: isFarmerDetailsLoading,
    isFetching: isFarmerDetailsFetching,
    isError: isFarmerDetailsError,
    isUninitialized,
    refetch: refetchFarmerDetails
  } = useGetPaginatedFarmerDetailsQuery(
    dealerSapIds.length > 0 ? { dealerSapIds, pageSize, currentPage, sort } : skipToken
  )

  const refetch = () => {
    refetchFarmerDetails()
  }

  // add isUninitialized to show that the query is waiting on dealerSapIds
  const isFetching = isFarmerDetailsFetching || isUninitialized
  const isLoading = isFarmerDetailsLoading || isUninitialized
  const isError = !isFetching && isFarmerDetailsError

  return {
    data: farmerDetails,
    isLoading,
    isFetching,
    isError,
    refetch
  }
}

export default usePaginatedMyCropFarmerDetails
