import { useGetFarmerDetailsByScorQuery } from '../store'

export const useSeedsmanFarmerDetails = () => {
  const {
    data: farmerDetails,
    isLoading: isFarmerDetailsLoading,
    isFetching: isFarmerDetailsFetching,
    isError: isFarmerDetailsError,
    refetch: refetchMyCropFarmerDetails
  } = useGetFarmerDetailsByScorQuery()

  const refetch = () => {
    refetchMyCropFarmerDetails()
  }

  return {
    data: farmerDetails,
    isLoading: isFarmerDetailsLoading || isFarmerDetailsFetching,
    isError: isFarmerDetailsError,
    refetch
  }
}

export default useSeedsmanFarmerDetails
