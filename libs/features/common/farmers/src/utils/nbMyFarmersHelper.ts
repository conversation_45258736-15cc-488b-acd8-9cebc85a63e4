import { TableColConfig } from '@gc/types'
import React from 'react'

type SortOption = { text: string; value: string }

const getSortString = (tableConfig: TableColConfig[], sortConfig: string[], selectedOption: SortOption) => {
  const column = tableConfig.find((col) => col.header === selectedOption.text)
  if (!column) {
    // eslint-disable-next-line no-console
    console.log(`No matching column found for ${selectedOption}`)
    return ''
  }

  const isAscending = selectedOption.value === 'asc'
  // Find related sort configuration
  const sortField = sortConfig.find((sort) => {
    const sortName = sort.split('-')[0]
    return (
      sortName === column.accessor ||
      (sortName === 'name' && column.accessor === 'accountName') ||
      (sortName === 'name' && column.accessor === 'farmName') ||
      (sortName === 'irdAccountNumber' && column.accessor === 'irdId')
    )
  })

  if (!sortField) {
    // eslint-disable-next-line no-console
    console.log(`No matching sort configuration found for ${selectedOption.value}`)
    return ''
  }

  // Construct the sort string
  const sortDirection = isAscending ? 'asc' : 'desc'
  return `${sortField.split('-')[0]}-${sortDirection}`
}

export const onClickTableHeader = ({
  event,
  tableColumns,
  sortInfo,
  setSortInfo,
  availableSortOptions,
  onChangeSort
}: {
  event: React.MouseEvent<HTMLDivElement>
  tableColumns: TableColConfig[]
  sortInfo: { sortBy: string; sortOrder: string }
  setSortInfo: React.Dispatch<React.SetStateAction<{ sortBy: string; sortOrder: string }>>
  availableSortOptions: string[]
  onChangeSort: (sortValue: string) => void
}) => {
  const parsedHtmlText = (event.target as HTMLDivElement).innerText

  const clickedText = parsedHtmlText.includes('arrow')
    ? parsedHtmlText.split(/\s+/).slice(0, -1).join(' ')
    : parsedHtmlText

  const isItAHeader = tableColumns.find((column) => clickedText === column.header && column.sortable)
  if (!isItAHeader) return
  let sortOrder = 'asc'
  if (sortInfo.sortBy === clickedText) {
    sortOrder = sortInfo.sortOrder === 'asc' ? 'desc' : 'asc'
    setSortInfo((prev) => ({
      ...prev,
      sortOrder
    }))
  } else {
    setSortInfo({ sortBy: clickedText, sortOrder })
  }
  if (availableSortOptions) {
    const convertedSortValue = getSortString(
      tableColumns,
      availableSortOptions as string[],
      { text: clickedText, value: sortOrder } as SortOption
    )
    onChangeSort(convertedSortValue)
  }
}
