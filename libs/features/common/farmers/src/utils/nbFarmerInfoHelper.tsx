import { LabelBadge } from '@element/react-badge'
import { interpunct } from '@gc/constants'
import { FarmerDetails, GrowerAccount } from '@gc/types'
import { fromUId, getContactName } from '@gc/utils'
import { isEmpty, mapKeys } from 'lodash'

type Farmer = FarmerDetails | GrowerAccount

type ExternalFarmer = {
  farmerirdId: string
  farmerId: string
  dealerId: string
}

const isAccount = (farmer: Farmer | ExternalFarmer): farmer is GrowerAccount => {
  return (farmer as GrowerAccount).accountName !== undefined
}

const isExternalFarmer = (farmer: ExternalFarmer | Farmer) => {
  return Object.keys(farmer).includes('farmerirdId') || Object.keys(farmer).includes('farmerId')
}

export const previousPageStateTransformer = (farmer: Farmer | ExternalFarmer) => {
  if (!farmer) return {} as FarmerDetails

  let propsToRename: Record<string, string> = {}
  if (isExternalFarmer(farmer)) {
    propsToRename = {
      farmerirdId: 'growerIrdId',
      farmerId: 'growerSapId',
      dealerId: 'dealerSapId'
    }
  } else if (isAccount(farmer)) {
    propsToRename = {
      accountName: 'farmName',
      irdId: 'growerIrdId',
      sapAccountId: 'growerSapId',
      uId: 'growerUId'
    }
  } // else is already FarmerDetails

  // clone, rename props
  const farmerDetails = (isEmpty(propsToRename)
    ? farmer
    : mapKeys(farmer, (value, key) => {
        return propsToRename[key] ?? key
      })) as unknown as FarmerDetails

  const { irdId, sapId } = fromUId(farmerDetails.growerUId)
  farmerDetails.growerIrdId = irdId || farmerDetails.growerIrdId
  farmerDetails.growerSapId = sapId || farmerDetails.growerSapId

  const [firstName, ...lastName] = getContactName(farmerDetails)?.split(' ') ?? ['', '']
  farmerDetails.firstName = farmerDetails.firstName || firstName
  farmerDetails.lastName = farmerDetails.lastName || lastName?.join(' ')

  return farmerDetails
}

export const infoBlockParser = (farmerDetails: FarmerDetails, zoneHistory: { cyZone: string; crop: string }[]) => {
  const secondaryText3 = zoneHistory
    .map((zone: { cyZone: string; crop: string }) => {
      if (zone.cyZone) return `${zone.crop} ${zone.cyZone}`
      return null
    })
    .filter(Boolean)
    .join(` ${interpunct} `)

  const secondaryText2 = [
    farmerDetails?.gln && `GLN ${farmerDetails.gln}`,
    farmerDetails?.crtva && `Enlist Id ${farmerDetails.crtva}`
  ]
    .filter(Boolean)
    .join(` ${interpunct} `)

  const labelText = [
    farmerDetails?.licenseStatus?.toUpperCase(),
    farmerDetails?.growerIrdId && `${farmerDetails.growerIrdId}`
  ]
    .filter(Boolean)
    .join(` ${interpunct} `)

  return {
    mainText: farmerDetails?.farmName || '',
    secondaryText1: farmerDetails?.contactName || '',
    secondaryText2,
    secondaryText3: secondaryText3,
    badgeProps: {
      labelText: labelText,
      themeColor: farmerDetails?.licenseStatus?.toUpperCase() === 'LICENSED' ? 'green' : 'orange'
    }
  }
}

const calculateSalesChange = (
  currentYearSales: number,
  previousYearSales: number
): { changePercentage: number; status: string } => {
  const result = {
    changePercentage: 0,
    status: 'same'
  }

  if (previousYearSales === 0) {
    if (currentYearSales === 0) {
      result.changePercentage = 0
      result.status = 'same'
    } else {
      result.changePercentage = 100
      result.status = currentYearSales > 0 ? 'increase' : 'decrease'
    }
  } else {
    result.changePercentage = ((currentYearSales - previousYearSales) / previousYearSales) * 100

    if (result.changePercentage > 0) result.status = 'increase'
    else if (result.changePercentage < 0) result.status = 'decrease'
    else result.status = 'same'
    result.changePercentage = Math.abs(result.changePercentage)
  }

  result.changePercentage = Number.parseFloat(result.changePercentage.toFixed(2))

  return result
}

const ICONS = {
  increase: 'add',
  decrease: 'remove',
  same: ''
}

const COLORS = {
  increase: 'green',
  decrease: 'red',
  same: ''
}

export const dashBlockParser = (
  data: { cropName: string; currentYearNetGPOS: number; priorYearGrowerOrder: number }[],
  year: number | undefined
) => {
  if (!data || data.length === 0) return []
  return data.map((item) => {
    const result = []
    const calculatedChange = calculateSalesChange(item?.currentYearNetGPOS || 0, item?.priorYearGrowerOrder || 0)
    result.push({
      primaryText: `${year ?? ''} ${item.cropName} Sales`,
      secondaryText: `${item?.currentYearNetGPOS || 0}`,
      trailingBlock: calculatedChange.status !== 'same' && (
        <LabelBadge
          leadingIcon={ICONS[calculatedChange.status as keyof typeof ICONS]}
          themeColor={COLORS[calculatedChange.status as keyof typeof COLORS]}
          label={`${calculatedChange.changePercentage}${calculatedChange.changePercentage === 0 ? '' : '%'}`}
        />
      )
    })
    result.push({
      primaryText: `${year ? year - 1 : ''} ${item.cropName} Sales`,
      secondaryText: `${item?.priorYearGrowerOrder || 0}`
    })
    return result
  })
}

export const productOrderedDetailsParser = (products: { subRows: Record<string, unknown>[] }[]) => {
  return products.flatMap((product) => product?.subRows || [])
}

export const contactParser = (farmerDetails: FarmerDetails) => {
  if (!farmerDetails) return []

  const {
    contactName = '',
    farmName = '',
    phoneNumber = '',
    email = '',
    streetAddress = '',
    city = '',
    county = '',
    state = '',
    zipCode = ''
  } = farmerDetails
  const restAddress = [city, county, state, zipCode].filter(Boolean).join(', ')

  return [
    {
      title: 'Contact Info',
      subTexts: [contactName, farmName, phoneNumber, email]
    },
    {
      title: 'Physical Address',
      subTexts: [streetAddress, restAddress]
    }
  ]
}
