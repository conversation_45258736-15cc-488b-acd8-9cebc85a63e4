import { axiosBaseQuery, TokenType } from '@gc/api/client'
import { getFarmerDetailsByYear, getFarmerOrderDetailsNational } from '@gc/rtk-queries'
import { fetchStore } from '@gc/utils'
import { BaseQueryFn, createApi } from '@reduxjs/toolkit/query/react'

const baseQuery: BaseQueryFn = (...baseQueryArgs) => {
  const baseUrl = fetchStore('domainDef').gcPortalConfig.services.seedServiceUrl
  return axiosBaseQuery({ baseURL: baseUrl, tokenType: TokenType.Gigya })(...baseQueryArgs)
}
const acsCommonApi = createApi({
  reducerPath: 'seedServiceApi',
  baseQuery,
  endpoints: (builder) => ({
    getFarmerDetailsByYear: getFarmerDetailsByYear(builder),
    getFarmerOrderDetailsNational: getFarmerOrderDetailsNational(builder)
  })
})

export const { useGetFarmerDetailsByYearQuery, useGetFarmerOrderDetailsNationalQuery } = acsCommonApi

export default acsCommonApi
