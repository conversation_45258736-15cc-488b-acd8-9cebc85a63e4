import { buildStore, ccApi, mergeWithGlobalReducers, middlewareApi } from '@gc/redux-store'
import { isDev } from '@gc/utils'
import { useDispatch } from 'react-redux'
import logger from 'redux-logger'

import acsCommonApi from './acsCommonApi'
import acsMyAccountApi from './acsMyAccountApi'
import financeApi from './financeApi'
import licApi from './licApi'
import seedServiceApi from './seedServiceApi'

const rootReducer = mergeWithGlobalReducers({
  [acsCommonApi.reducerPath]: acsCommonApi.reducer,
  [acsMyAccountApi.reducerPath]: acsMyAccountApi.reducer,
  [seedServiceApi.reducerPath]: seedServiceApi.reducer,
  [licApi.reducerPath]: licApi.reducer,
  [financeApi.reducerPath]: financeApi.reducer
})

export const setupStore = (preloadedState?: Partial<RootState>) => {
  return buildStore(
    {
      preloadedState,
      reducer: rootReducer,
      devTools: isDev,
      middleware: (getDefaultMiddleware) => {
        const middleware = getDefaultMiddleware({ serializableCheck: false })
          .concat(acsCommonApi.middleware)
          .concat(acsMyAccountApi.middleware)
          .concat(ccApi.middleware)
          .concat(seedServiceApi.middleware)
          .concat(licApi.middleware)
          .concat(financeApi.middleware)
          .concat(middlewareApi.middleware)
        if (isDev) {
          middleware.concat(logger)
        }
        return middleware
      }
    },
    {
      injectQuotesApi: true,
      injectConfigDataApi: true,
      injectOrdersApi: true,
      injectConsignmentsApi: true,
      injectInventoryApi: true,
      injectFavoritesApi: true,
      injectAcsCommonApi: true,
      useGlobalMiddleware: false,
      middlewareOpts: { serializableCheck: false }
    }
  )
}

export type RootState = ReturnType<typeof rootReducer>
export type AppStore = ReturnType<typeof setupStore>
export type AppDispatch = AppStore['dispatch']
export const useAppDispatch: () => AppDispatch = useDispatch

export * from './acsCommonApi'
export * from './acsMyAccountApi'
export * from './financeApi'
export * from './licApi'
export * from './seedServiceApi'
