import { axiosBaseQuery, TokenType } from '@gc/api/client'
import {
  getDealerAccountHierarchy,
  getFarmerDetailsByScor,
  getFarmerIds,
  getFiscalYear,
  getPaginatedFarmerDetails,
  getSingleFarmerDetails,
  paginatedSearchFarmers
} from '@gc/rtk-queries'
import { fetchStore } from '@gc/utils'
import { BaseQueryFn, createApi } from '@reduxjs/toolkit/query/react'

const baseQuery: BaseQueryFn = (...baseQueryArgs) => {
  const baseUrl = fetchStore('domainDef').gcPortalConfig.services.acsCommonUrl
  return axiosBaseQuery({ baseURL: baseUrl, tokenType: TokenType.Gigya })(...baseQueryArgs)
}

const acsCommonApi = createApi({
  reducerPath: 'acsCommonApi',
  baseQuery,
  endpoints: (builder) => ({
    getDealerAccountHierarchy: getDealerAccountHierarchy(builder),
    getFarmerDetailsByScor: getFarmerDetailsByScor(builder),
    getFiscalYear: getFiscal<PERSON>ear(builder),
    getSingleFarmerDetails: getSingleFarmerDetails(builder),
    paginatedSearchFarmers: paginatedSearchFarmers(builder),
    getFarmerIds: getFarmerIds(builder),
    getPaginatedFarmerDetails: getPaginatedFarmerDetails(builder)
  })
})

export const {
  useGetDealerAccountHierarchyQuery,
  useGetFarmerDetailsByScorQuery,
  useGetFiscalYearQuery,
  useGetSingleFarmerDetailsQuery,
  useLazyGetSingleFarmerDetailsQuery,
  useLazyPaginatedSearchFarmersQuery,
  useGetFarmerIdsQuery,
  useGetPaginatedFarmerDetailsQuery,
  useLazyGetPaginatedFarmerDetailsQuery
} = acsCommonApi

export default acsCommonApi
