import { axiosBaseQuery, TokenType } from '@gc/api/client'
import { fetchLicFarmers, getLicFarmerSummary, getLicSingleFarmerDetails } from '@gc/rtk-queries'
import { fetchStore } from '@gc/utils'
import { BaseQueryFn, createApi } from '@reduxjs/toolkit/query/react'

const baseQuery: BaseQueryFn = (...baseQueryArgs) => {
  const baseUrl = fetchStore('domainDef').gcPortalConfig.services.licServiceUrl
  return axiosBaseQuery({ baseURL: baseUrl, tokenType: TokenType.Gigya })(...baseQueryArgs)
}

const licApi = createApi({
  reducerPath: 'licApi',
  baseQuery,
  endpoints: (builder) => ({
    getAllLicFarmers: fetchLicFarmers(builder),
    getLicFarmerSummary: getLicFarmerSummary(builder),
    getLicSingleFarmerDetails: getLicSingleFarmerDetails(builder)
  })
})

export const {
  useGetAllLicFarmersQuery,
  useGetLicFarmerSummaryQuery,
  useGetLicSingleFarmerDetailsQuery,
  useLazyGetLicSingleFarmerDetailsQuery
} = licApi

export default licApi
