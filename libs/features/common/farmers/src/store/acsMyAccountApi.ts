import { axiosBaseQuery, TokenType } from '@gc/api/client'
import { getFarmerOrderDetailsChannel, getUnitsDetails } from '@gc/rtk-queries'
import { fetchStore } from '@gc/utils'
import { BaseQueryFn, createApi } from '@reduxjs/toolkit/query/react'

const baseQuery: BaseQueryFn = (...baseQueryArgs) => {
  const baseUrl = fetchStore('domainDef').gcPortalConfig.services.acsMyAccountUrl
  return axiosBaseQuery({ baseURL: baseUrl, tokenType: TokenType.Gigya })(...baseQueryArgs)
}

const acsMyAccountApi = createApi({
  reducerPath: 'acsMyAccountApi',
  baseQuery,
  endpoints: (builder) => ({
    getFarmerOrderDetailsChannel: getFarmerOrderDetailsChannel(builder),
    getUnitsDetails: getUnitsDetails(builder)
  })
})

export const { useGetFarmerOrderDetailsChannelQuery, useGetUnitsDetailsQuery } = acsMyAccountApi

export default acsMyAccountApi
