import { setUpStore } from '@gc/redux-store'
import { AccountHierarchy } from '@gc/types'
import { actAwait, render } from '@gc/utils'

import LocationPicker from './LocationPicker'

type mockResponseType = {
  data: {
    baseAccount: AccountHierarchy
    selectedAccount: AccountHierarchy
    includeSubAccounts: boolean
  }
  isError: boolean
}

const mockResponse: mockResponseType = {
  data: {
    baseAccount: {
      accountName: 'PHIL LEHMAN',
      children: [
        {
          accountName: 'LAWES AG SERVICE',
          uId: '',
          irdId: '31',
          sapAccountId: '2',
          level: 2
        }
      ],
      irdId: '89607',
      sapAccountId: '1',
      level: 1
    },
    selectedAccount: {
      accountName: 'PHIL LEHMAN',
      irdId: '89607',
      sapAccountId: '1',
      level: 1,
      uId: ''
    },
    includeSubAccounts: false
  },
  isError: false
}

jest.mock('@gc/redux-store', () => ({
  ...jest.requireActual('@gc/redux-store'),
  useAcsCommonQueries: () => ({ useGetDealerAccountHierarchyQuery: () => mockResponse })
}))

const translations: { [key: string]: string } = {
  'common.try_again.label': 'Try again',
  'common.try_again_msg.label': 'Loading Error',
  'location_picker.all_locations.label': 'All Locations'
}

jest.mock('react-i18next', () => ({
  // this mock makes sure any components using the translate hook can use it without a warning being shown
  useTranslation: () => {
    return {
      t: (str: string) => translations[str] ?? str
    }
  }
}))

jest.mock('@gc/hooks', () => {
  const originalModule = jest.requireActual('@gc/hooks')
  const mockAppSessionData = {}
  const mockUpsertAppSessionData = jest.fn()

  return {
    ...originalModule,
    useIsMobile: jest.fn(() => true),
    useScreenRes: jest.fn(() => 375),
    useIsSmallMobile: jest.fn(),
    useAppSession: () => [mockAppSessionData, mockUpsertAppSessionData],
    useMemoizedTranslation: () => (key: string) => translations[key] ?? key
  }
})

describe('LocationPicker', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render component without error', async () => {
    const { getAllByRole } = render(
      <LocationPicker dealerHierarchy={mockResponse.data.baseAccount} isError={false} refetch={jest.fn()} />,
      {
        store: setUpStore()
      }
    )
    await actAwait(100)
    expect(getAllByRole('button')[0]).toBeTruthy()
    expect(getAllByRole('button')[0]).toBeInTheDocument()
  })

  it('should display error message when isError is true', async () => {
    const { getByText } = render(
      <LocationPicker dealerHierarchy={mockResponse.data.baseAccount} isError={true} refetch={jest.fn()} />,
      {
        store: setUpStore()
      }
    )
    await actAwait(100)
    expect(getByText('Try again')).toBeInTheDocument()
    expect(getByText('Loading Error')).toBeInTheDocument()
  })

  it('should display custom error message when isError is true', async () => {
    const { getByText } = render(
      <LocationPicker
        dealerHierarchy={mockResponse.data.baseAccount}
        isError={true}
        refetch={jest.fn()}
        errorMessage='No data found'
      />,
      {
        store: setUpStore()
      }
    )
    await actAwait(100)
    expect(getByText('Try again')).toBeInTheDocument()
    expect(getByText('No data found')).toBeInTheDocument()
  })

  it('should call refetch function on error button click', async () => {
    const refetchMock = jest.fn()
    const { getByText } = render(
      <LocationPicker dealerHierarchy={mockResponse.data.baseAccount} isError={true} refetch={refetchMock} />,
      {
        store: setUpStore()
      }
    )
    await actAwait(100)
    const button = getByText('Try again')
    button.click()
    expect(refetchMock).toHaveBeenCalled()
  })
})
