import { LocationPickerModal, ModalContainer, ModalState } from '@gc/components'
import { getModal } from '@gc/redux-store'
import React from 'react'
import isEqual from 'react-fast-compare'
import { useSelector } from 'react-redux'

import styles from './LocationPickerModalContainer.module.scss'

const modals: Record<string, ModalState> = {
  SELECT_LOCATION: { modalBody: LocationPickerModal }
}

export const LocationPickerModalContainer = () => {
  const modal = useSelector(getModal)

  if (!modal) return null

  return (
    <ModalContainer
      modals={modals}
      className={styles.container}
      open={modal.open}
      modalName={modal.name}
      modalProps={modal.props}
      customInitialFocus={false}
    />
  )
}

export default React.memo(LocationPickerModalContainer, isEqual)
