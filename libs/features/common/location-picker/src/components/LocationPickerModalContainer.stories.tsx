import { useModal } from '@gc/hooks'
import { accounts } from '@gc/shared/test'
import { fn } from '@storybook/test'
import { useEffect } from 'react'
import { Provider } from 'react-redux'

import { setUpStore } from '../store'
import LocationPickerModalContainer from './LocationPickerModalContainer'

export default {
  title: 'Location Picker/Molecules/LocationPickerModalContainer',
  component: LocationPickerModalContainer
}

const Template = () => (
  <Provider store={setUpStore()}>
    <TemplateContent />
  </Provider>
)

const TemplateContent = () => {
  const { openModal } = useModal()

  useEffect(() => {
    openModal({
      name: 'SELECT_LOCATION',
      props: {
        baseAccount: accounts.accounts[0].value,
        mobilePrimaryActHandler: fn()
      }
    })
  }, [openModal])

  return <LocationPickerModalContainer />
}

export const Basic = Template.bind({})
