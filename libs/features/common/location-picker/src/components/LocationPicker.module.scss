.container {
  height: 48px;
  background: var(--lmnt-theme-surface-variant);
  display: flex;
  padding: 0px 31px;
  flex-direction: row;
  align-items: center;
  gap: 4px;

  .action_button {
    padding: 8px;
    gap: 4px;
    border-radius: 10px;

    :global(.lmnt-icon) {
      margin: 0 !important;
    }

    &:global(.mdc-button) {
      text-transform: none;
    }

    &_error {
      @extend .action_button;
      color: var(--lmnt-theme-danger);
    }
  }
}
