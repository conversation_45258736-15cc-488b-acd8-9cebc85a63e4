import { Button, TypoBody } from '@element/react-components'
import { useLocation, useMemoizedTranslation, useModal } from '@gc/hooks'
import { AccountHierarchy, LocationPickerState } from '@gc/types'
import { mapTreeAsArray, pruneTree } from '@gc/utils'
import _ from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'

import styles from './LocationPicker.module.scss'

export type LocationPickerProps = {
  dealerHierarchy: AccountHierarchy | undefined
  isError: boolean
  errorMessage?: string
  isLoading?: boolean
  hideSubAccounts?: boolean
  leafSelectable?: boolean
  isAurora?: boolean
  refetch: () => void
}
export const LocationPicker = (props: LocationPickerProps) => {
  const { dealerHierarchy, isError, isLoading, hideSubAccounts, refetch, isAurora, leafSelectable, errorMessage } =
    props
  const [location, updateLocationState] = useLocation()
  const { openModal, closeModal } = useModal()

  const t = useMemoizedTranslation()
  const allLocationsLabel = t('location_picker.all_locations.label')
  const loadingLabel = t('common.please_wait.loading_msg')
  const [accountLabel, setAccountLabel] = useState(allLocationsLabel)

  const prunedTree = useMemo(() => {
    // Remove duplicate nodes in tree
    return pruneTree<AccountHierarchy>(
      (node, parentNode) => node.sapAccountId === parentNode?.sapAccountId,
      dealerHierarchy
    )
  }, [dealerHierarchy])

  const updateLabel = useCallback(
    (selectedAccount?: AccountHierarchy) => {
      if (selectedAccount) {
        setAccountLabel(
          selectedAccount.level === 1
            ? `${allLocationsLabel} - ${selectedAccount.accountName}`
            : selectedAccount.accountName
        )
      } else {
        setAccountLabel(allLocationsLabel)
      }
    },
    [allLocationsLabel]
  )

  const updateLocation = useCallback(
    (baseAccount: AccountHierarchy, selectedAccount?: AccountHierarchy, includeSubAccounts = true) => {
      const selectedSapAccountIds = mapTreeAsArray((account: AccountHierarchy) => account.sapAccountId, selectedAccount)
      updateLocationState({
        baseAccount,
        selectedAccount,
        includeSubAccounts,
        selectedSapAccountIds
      })
    },
    [updateLocationState]
  )

  const handleSave = useCallback(
    (formState: LocationPickerState) => {
      updateLabel(formState.selectedAccount)
      updateLocation(formState.baseAccount, formState.selectedAccount, formState.includeSubAccounts)
      if (isAurora) {
        setTimeout(() => {
          closeModal()
        }, 1000)
      } else {
        closeModal()
      }
    },
    [closeModal, isAurora, updateLocation, updateLabel]
  )

  const openLocationPickerModal = useCallback(() => {
    openModal({
      name: 'SELECT_LOCATION',
      props: {
        baseAccount: location.baseAccount,
        selectedAccount: location.selectedAccount,
        includeSubAccounts: location.includeSubAccounts,
        hideSubAccounts: hideSubAccounts,
        leafSelectable: leafSelectable,
        mobilePrimaryActHandler: handleSave
      }
    })
  }, [location, handleSave, openModal, hideSubAccounts, leafSelectable])

  useEffect(() => {
    if (isLoading) {
      setAccountLabel(_.startCase(loadingLabel))
      return
    }
    if (prunedTree && !_.isEqual(prunedTree, location.baseAccount)) {
      updateLabel(prunedTree)
      updateLocation(prunedTree, prunedTree)
    } else if (location.selectedAccount) {
      updateLabel(location.selectedAccount)
    }
  }, [prunedTree, location.baseAccount, location.selectedAccount, updateLabel, updateLocation, isLoading, loadingLabel])

  if (isError) {
    return (
      <div className={styles.container}>
        <Button className={styles.action_button_error} variant='text' leadingIcon='refresh' onClick={refetch}>
          {t('common.try_again.label')}
        </Button>
        <TypoBody themeColor='error' level={2}>
          {errorMessage || t('common.try_again_msg.label')}
        </TypoBody>
      </div>
    )
  }

  return (
    (isLoading || dealerHierarchy) && (
      <div className={styles.container}>
        <Button
          className={styles.action_button}
          variant='text'
          trailingIcon='expand_more'
          onClick={openLocationPickerModal}
          disabled={!location.baseAccount}
        >
          {accountLabel}
        </Button>
      </div>
    )
  )
}

export default LocationPicker
