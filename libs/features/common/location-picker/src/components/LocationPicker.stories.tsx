import { StoryFn } from '@storybook/react'
import { Provider } from 'react-redux'

import { setUpStore } from '../store'
import LocationPicker from './LocationPicker'
import LocationPickerModalContainer from './LocationPickerModalContainer'

export default {
  title: 'Location Picker/Molecules/LocationPicker',
  component: LocationPicker,
  decorators: [
    (Story: StoryFn) => (
      <Provider store={setUpStore()}>
        <Story />
        <LocationPickerModalContainer />
      </Provider>
    )
  ]
}

export const Basic = {
  args: {}
}
