import { useFetchFasteStore, useGcPortalConfig, useLocation, useMemoizedTranslation } from '@gc/hooks'
import { useConfigDataQueries, useInternalPortalQueries } from '@gc/redux-store'
import { DomainDef, SelectedAccount, User } from '@gc/types'
import { fetchStore, getNetworkId } from '@gc/utils'
import { skipToken } from '@reduxjs/toolkit/query'
import { isEmpty } from 'lodash'
import { useEffect, useMemo, useState } from 'react'

import LocationPicker from '../LocationPicker'

export function AuroraLocationPicker({ isAdmin = false }: { isAdmin: boolean }) {
  const { useGetUserIdentityQuery, useGetPositionHierarchyQuery, useGetSeedProHierarchyQuery } =
    useInternalPortalQueries()
  const gcPortalConfig = useGcPortalConfig()
  const { devUsers, defaultTestUser } = gcPortalConfig.auroraPortalConfig ?? {}
  const t = useMemoizedTranslation()
  const cwid = isAdmin ? getNetworkId() : undefined
  let testUserId = sessionStorage.getItem('testUserId')
  if (isEmpty(testUserId) && cwid && devUsers?.includes(cwid) && defaultTestUser) {
    sessionStorage.setItem('testUserId', defaultTestUser)
    testUserId = defaultTestUser
  }
  const [location] = useLocation()
  const [dealerHierarchy, setDealerHierarchy] = useState<any | undefined>(undefined)
  const userId = testUserId ?? cwid ?? skipToken
  const { data: userInfo } = useGetUserIdentityQuery(userId === 'US' ? skipToken : userId)
  const seedYear = gcPortalConfig?.seedYear
  const dataHierarchyQuery = isAdmin ? useGetPositionHierarchyQuery : useGetSeedProHierarchyQuery
  const sapAccountId = fetchStore('selectedAccount')?.sapAccountId //**********
  const dataHierarchyQueryParams = isAdmin
    ? { id: userId, seedYear: seedYear }
    : { id: sapAccountId, seedYear: seedYear }
  const {
    data: positionHierarchy,
    isLoading: isPositionHierarchyLoading,
    error: positionHierarchyError,
    refetch: refetchPositionHierarchy
  } = dataHierarchyQuery(dataHierarchyQueryParams)

  const internalPortalLob = useFetchFasteStore<DomainDef>('domainDef').internalPortalLob
  const entitlementsObj = sessionStorage.getItem('entitlements')
  const internalPortalEntitlements = useMemo(
    () => (entitlementsObj ? JSON.parse(entitlementsObj) : {}),
    [entitlementsObj]
  )
  const [agent, setAgent] = useState<any>({})
  const { useGetUserForAccountQuery } = useConfigDataQueries()
  const { data: usernameData } = useGetUserForAccountQuery(agent.sapAccountId ? agent.sapAccountId : skipToken)
  const hasData = useMemo(() => {
    return usernameData !== undefined
  }, [usernameData])
  useEffect(() => {
    if (!isEmpty(positionHierarchy) && !isPositionHierarchyLoading) {
      setDealerHierarchy(positionHierarchy)
    }

    if (location.selectedAccount) {
      setAgent(location.selectedAccount)
    }
  }, [isPositionHierarchyLoading, location.selectedAccount, positionHierarchy])

  useEffect(() => {
    if (
      hasData &&
      agent.sapAccountId &&
      (testUserId === 'US' || agent.sapAccountId !== userInfo?.federationIdentifier)
    ) {
      const user: User = {
        username: '',
        name: agent.accountName,
        userType: '',
        addressLine1: '',
        addressLine2: '',
        brand: '',
        city: '',
        contactSfdcId: '',
        federationId: '',
        roleID: [],
        primaryPhone: '',
        primaryPhoneType: '',
        secondaryPhone: '',
        secondaryPhoneType: '',
        state: '',
        zipCode: '',
        testUser: '',
        entitlements: internalPortalEntitlements,
        picture: {
          type: ''
        }
      }
      user.username = usernameData ?? ''
      const selectedAccount: SelectedAccount = {
        sapAccountId: agent.sapAccountId,
        accountName: agent.accountName,
        city: '',
        state: '',
        entitlements: {},
        lob: internalPortalLob ?? 'none',
        sourceSystem: '',
        uid: agent.irdId ?? ''
      }
      sessionStorage.setItem('selectedAccount', JSON.stringify(selectedAccount))
      sessionStorage.setItem('user', JSON.stringify(user))
      sessionStorage.setItem('sapAccountId', agent.sapAccountId)
      sessionStorage.setItem('username', user.username)
    }
  }, [
    agent,
    hasData,
    internalPortalEntitlements,
    internalPortalLob,
    userInfo?.federationIdentifier,
    usernameData,
    location.selectedAccount
  ])
  return (
    <LocationPicker
      dealerHierarchy={dealerHierarchy}
      isLoading={isPositionHierarchyLoading}
      isError={positionHierarchyError ? true : false}
      errorMessage={t('location_picker.no_data_found.msg')}
      hideSubAccounts={true}
      refetch={refetchPositionHierarchy}
      isAurora={true}
      leafSelectable={isAdmin}
    />
  )
}

export default AuroraLocationPicker
