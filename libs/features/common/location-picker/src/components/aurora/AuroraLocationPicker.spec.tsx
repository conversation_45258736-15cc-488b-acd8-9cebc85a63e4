import { setUpStore } from '@gc/redux-store'
import { render, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'

import AuroraLocationPicker from './AuroraLocationPicker'
let mockStore: ReturnType<typeof setUpStore>
const devUsers = ['devUser1', 'devUser2']
const defaultTestUser = 'testUser123'
const cwid = 'devUser1'
const mockUseGetPositionHierarchyQuery = jest.fn(() => ({
  data: mockResponse,
  isLoading: false,
  error: null
}))
const mockUseGetSeedProHierarchyQuery = jest.fn(() => ({
  data: mockResponse,
  isLoading: false,
  error: null
}))

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useFetchFasteStore: jest.fn(() => ({
    internalPortalLob: {
      internalPortalLob: 'US'
    }
  })),
  fetchStore: { subscribe: jest.fn(), get: jest.fn() },
  useGcPortalConfig: jest.fn(() => ({
    auroraPortalConfig: { devUsers, defaultTestUser }
  }))
}))

jest.mock('@gc/utils', () => ({
  ...jest.requireActual('@gc/utils'),
  subscribeStore: jest.fn(),
  getNetworkId: jest.fn(() => cwid)
}))

jest.mock('@gc/redux-store', () => {
  const originalModule = jest.requireActual('@gc/redux-store')
  return {
    ...originalModule,
    useInternalPortalQueries: jest.fn(() => ({
      useGetUserIdentityQuery: jest.fn(() => ({
        data: { fullName: 'John Doe', federationIdentifier: '12345' }
      })),
      useGetPositionHierarchyQuery: mockUseGetPositionHierarchyQuery,
      useGetSeedProHierarchyQuery: mockUseGetSeedProHierarchyQuery
    }))
  }
})

const sessionStorageMock = (function () {
  let store: { [key: string]: string } = {
    username: 'John Doe',
    selectedAccount: JSON.stringify({ accountName: 'Test Account' })
  }
  return {
    getItem: jest.fn((key) => store[key]),
    setItem: jest.fn((key, value) => {
      store[key] = value
    }),
    removeItem: jest.fn((key) => {
      delete store[key]
    }),
    clear: jest.fn(() => {
      store = {}
    })
  }
})()

Object.defineProperty(window, 'sessionStorage', { value: sessionStorageMock })

const renderWithStore = (isAdmin: boolean) =>
  render(
    <Provider store={mockStore}>
      <AuroraLocationPicker isAdmin={isAdmin} />
    </Provider>
  )

describe('AuroraLocationPicker', () => {
  beforeEach(() => {
    Object.defineProperty(window, 'sessionStorage', { value: sessionStorageMock })
    mockStore = setUpStore(
      {
        app: {
          selectedAccount: {
            name: 'Test Account',
            sourceId: '12345',
            sapAccountId: '',
            source: ''
          }
        }
      },
      {
        injectDiscountsApi: true,
        injectOrdersApi: true,
        injectConfigDataApi: true,
        injectProductsApi: true,
        injectConsignmentsApi: true,
        injectInventoryApi: true,
        useGlobalMiddleware: true,
        middlewareOpts: { serializableCheck: false }
      }
    )
  })
  it('should render successfully', () => {
    const { baseElement } = renderWithStore(true)
    expect(baseElement).toBeTruthy()
  })

  it('should handle data correctly', () => {
    mockStore.dispatch({
      type: 'internalPortalQueries/useGetPositionHierarchyQuery',
      payload: { data: { someData: 'test' }, isLoading: false, error: null }
    })
    renderWithStore(true)
    // Add assertions to verify data handling
  })

  it('sets testUserId in sessionStorage when conditions are met', async () => {
    renderWithStore(true)
    await waitFor(() => {
      expect(sessionStorage.getItem('testUserId')).toBe('testUser123')
    })
  })
  it('should render the location picker for non-admin users', () => {
    renderWithStore(false)
    expect(mockUseGetSeedProHierarchyQuery).toHaveBeenCalled()
  })
  it('should render the location picker for admin users', () => {
    renderWithStore(true)
    expect(mockUseGetPositionHierarchyQuery).toHaveBeenCalled()
  })
})

const mockResponse = {
  accountName: 'US',
  sapAccountId: '',
  positionCode: 'US',
  parentPositionCode: '',
  irdId: '',
  level: '1',
  children: [
    {
      accountName: 'Adriano Santos - CENT',
      sapAccountId: 'AASAN10',
      positionCode: 'CENT',
      parentPositionCode: 'US',
      irdId: 'AASAN10',
      level: '2',
      children: [
        {
          accountName: 'Kevin Moore - US0201',
          sapAccountId: 'KJMOOR',
          positionCode: 'US0201',
          parentPositionCode: 'CENT',
          irdId: 'KJMOOR',
          level: '3',
          children: [
            {
              accountName: 'Kevin Moore - CS03',
              sapAccountId: 'KJMOOR',
              positionCode: 'CS03',
              parentPositionCode: 'US0201',
              irdId: 'KJMOOR',
              level: '4',
              children: [
                {
                  accountName: 'MATT MC ELROY - N2A',
                  sapAccountId: 'MDMCEL',
                  positionCode: 'N2A',
                  parentPositionCode: 'CS03',
                  irdId: 'MDMCEL',
                  level: '5',
                  children: [
                    {
                      accountName: 'JARED MCLAUGHLIN - **********',
                      sapAccountId: '**********',
                      irdId: '**********',
                      level: '6',
                      children: []
                    },
                    {
                      accountName: 'MILLER AGRI SEED LLC - **********',
                      sapAccountId: '**********',
                      irdId: '**********',
                      level: '6',
                      children: []
                    },
                    {
                      accountName: 'STOUTSVILLE SEED SHED LLC - **********',
                      sapAccountId: '**********',
                      irdId: '**********',
                      level: '6',
                      children: []
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
