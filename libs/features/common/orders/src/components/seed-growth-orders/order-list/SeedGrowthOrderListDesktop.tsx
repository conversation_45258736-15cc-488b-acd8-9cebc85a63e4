import { Band, Contingency, HeaderType, Loading, NestedTable } from '@gc/components'
import { useGcPortalConfig, useLocale, useMemoizedTranslation } from '@gc/hooks'
import { SeedGrowthOrderProduct, SeedGrowthOrderWithProducts, TableRow } from '@gc/types'
import { createCacheKey, formatNumber, getFasteStoreKey, hitsOnData, LRUCache } from '@gc/utils'
import { ReactNode, useCallback, useMemo } from 'react'

import SeedGrowthProductDeliveries from '../SeedGrowthProductDeliveries'
import styles from './SeedGrowthOrderListDesktop.module.scss'

type SeedGrowthOrderSearchData = {
  ordersColumns: string[]
  productsColumns: string[]
  deliveriesColumns: (string | number | undefined)[][]
}

const defaultSearchColumns = {
  ordersColumns: ['name'],
  productsColumns: ['name'],
  deliveriesColumns: ['carrier', 'deliveryId', 'trackingNumber', 'scheduledDeliveryDate', 'shippedQuantity', 'status']
}

const useGetSeedGrowthHeaders = (ssuOrPackage: 'ssu' | 'package', isTrackingDetailsLoading: boolean) => {
  const t = useMemoizedTranslation()
  const locale = useLocale()

  const LoadingHeaderTemplate = useCallback((elementToRender: ReactNode, isTrackingDetailsLoading: boolean) => {
    return isTrackingDetailsLoading ? <Loading /> : elementToRender
  }, [])

  const getHeaders = useCallback(
    (): HeaderType<SeedGrowthOrderProduct>[] => [
      {
        header: t('common.product_name.label'),
        accessor: 'name',
        id: 'name',
        defaultSort: 'asc',
        widthPercentage: 75,
        displayTemplate: (_value: string, product: SeedGrowthOrderProduct) => <>{product.name}&nbsp;</>,
        sortType: (x: TableRow<SeedGrowthOrderProduct>, y: TableRow<SeedGrowthOrderProduct>) =>
          (x.original.name ?? '').localeCompare(y.original.name ?? '')
      },
      {
        accessor: 'unit',
        align: 'center',
        header: ssuOrPackage === 'ssu' ? t('common.ssu.label') : t('common.uom.label'),
        width: 53
      },
      {
        align: 'right',
        accessor: 'orderedQuantity',
        header: `${t('common.total.label')} ${t('orders.ordered_quantity.label')}`,
        width: 105,
        displayTemplate: (_value: string, product: SeedGrowthOrderProduct) =>
          LoadingHeaderTemplate(
            <>
              {product.orderedQuantity !== undefined ? formatNumber(product.orderedQuantity, locale, true) : '--'}&nbsp;
            </>,
            isTrackingDetailsLoading
          )
      },
      {
        align: 'right',
        accessor: 'shippedQuantity',
        header: t('common.shipped.label'),
        width: 73,
        displayTemplate: (_value: string, product: SeedGrowthOrderProduct) =>
          LoadingHeaderTemplate(
            <>
              {product.shippedQuantity !== undefined ? formatNumber(product.shippedQuantity, locale, true) : '--'}&nbsp;
            </>,
            isTrackingDetailsLoading
          )
      },
      {
        align: 'right',
        accessor: 'pendingQuantity',
        header: t('common.pending.label'),
        width: 73,
        displayTemplate: (_value: string, product: SeedGrowthOrderProduct) =>
          LoadingHeaderTemplate(
            <>
              {product.pendingQuantity !== undefined ? formatNumber(product.pendingQuantity, locale, true) : '--'}&nbsp;
            </>,
            isTrackingDetailsLoading
          )
      }
    ],
    [t, ssuOrPackage, LoadingHeaderTemplate, locale, isTrackingDetailsLoading]
  )

  return getHeaders
}

// Create a singleton cache instance
const searchCache = new LRUCache<string, SeedGrowthOrderSearchData>()

export interface SeedGrowthOrderListDesktopProps {
  isTrackingDetailsLoading: boolean
  seedGrowthOrders: SeedGrowthOrderWithProducts[]
  packageSwitchAction?: ReactNode
  ssuOrPackage: 'ssu' | 'package'
}

export function SeedGrowthOrderListDesktop({
  seedGrowthOrders,
  ssuOrPackage,
  packageSwitchAction,
  isTrackingDetailsLoading
}: Readonly<SeedGrowthOrderListDesktopProps>) {
  const t = useMemoizedTranslation()
  const getHeaders = useGetSeedGrowthHeaders(ssuOrPackage, isTrackingDetailsLoading)
  const tableData = useMemo(
    () => seedGrowthOrders.map((order) => ({ rows: order.products, tableData: order })),
    [seedGrowthOrders]
  )
  const { seedGrowthOrderConfig } = useGcPortalConfig()

  const fasteStoreKey = getFasteStoreKey('orders', 'seedGrowthOrderDesktop')

  const {
    ordersColumns: searchableOrderColumns,
    productsColumns: searchableProductColumns,
    deliveriesColumns: searchableDeliveryColumns
  } = seedGrowthOrderConfig.searchColumns ?? defaultSearchColumns

  const searchFun = useCallback(
    (data: SeedGrowthOrderProduct, searchStr: string, tableData: SeedGrowthOrderWithProducts) => {
      function searchData({
        ordersColumns,
        productsColumns,
        deliveriesColumns
      }: {
        ordersColumns: string[]
        productsColumns: string[]
        deliveriesColumns: (string | number | undefined)[][]
      }) {
        const ordersSearch = hitsOnData(searchStr, ordersColumns)
        const productsSearch = hitsOnData(searchStr, productsColumns)
        const deliveriesSearch = deliveriesColumns.some((delivery) => hitsOnData(searchStr, delivery))
        return ordersSearch || productsSearch || deliveriesSearch
      }

      const cacheKey = createCacheKey(tableData, ['orderId'])
        .concat(`-${data.name}`)
        .concat(`-${data.deliveries.map((delivery) => delivery.deliveryId).join(',')}`)

      const cachedResult = searchCache.get(cacheKey)
      if (cachedResult) {
        return searchData(cachedResult)
      }

      const ordersColumns = searchableOrderColumns.map(
        (column) => tableData[column as keyof typeof tableData] as string
      )
      const productsColumns = searchableProductColumns.map((column) => data[column as keyof typeof data] as string)
      const deliveriesColumns = data.deliveries.map((delivery) =>
        searchableDeliveryColumns.map((c) => delivery[c as keyof typeof delivery] as string)
      )

      searchCache.set(cacheKey, { ordersColumns, productsColumns, deliveriesColumns })
      return searchData({ ordersColumns, productsColumns, deliveriesColumns })
    },
    [searchableDeliveryColumns, searchableOrderColumns, searchableProductColumns]
  )

  return (
    <NestedTable<SeedGrowthOrderProduct, SeedGrowthOrderWithProducts>
      searchable
      data={tableData}
      enableCsvDownload
      getHeaders={getHeaders}
      customSearchFn={searchFun}
      fasteStoreKey={fasteStoreKey}
      title={`${t('common.seedgrowth.label')} ${t('orders.orders.label')}`}
      additionalFilters={[{ accessor: 'salesYear', title: t('common.sales_year.label') }]}
      noMatchingDataContingency={<NoMatchingDataContingency />}
      filterBarTrailingContent={packageSwitchAction}
      tableTopBar={(data) => (
        <Band
          placement='table'
          primaryText={{
            level: 1,
            bold: true,
            children: `Order ${data.tableData.orderId}`
          }}
          secondaryText1={`${data.tableData.shipTo.city}, ${data.tableData.shipTo.state}`}
          trailingTextProps={{
            children: `${t('common.sales_year.label')} ${data.tableData.salesYear}`
          }}
        />
      )}
      tableProps={{
        noHover: true,
        layout: 'block',
        style: { border: 'none' },
        className: styles.shipments_table,
        expandable: true,
        expandedRowTemplate: ({ row }: { row: { original: SeedGrowthOrderProduct } }) => (
          <div id='shipments_expansion_panel' className={styles.shipments_expansion_panel}>
            <SeedGrowthProductDeliveries
              deliveries={row.original.deliveries}
              isTrackingDetailsLoading={isTrackingDetailsLoading}
            />
          </div>
        )
      }}
    />
  )
}

function NoMatchingDataContingency() {
  const t = useMemoizedTranslation()

  return (
    <Contingency
      codes={['DEFAULT']}
      types={['messageWithAction']}
      className={styles.no_matching_data_contingency}
      contingency={{
        code: 'DEFAULT',
        displayType: 'messageWithAction',
        messageWithActionProps: {
          iconProps: {
            icon: 'info',
            variant: 'filled-secondary',
            className: 'gc-icon-info'
          },
          messageDescription: t('common.no_results_message_description'),
          messageHeader: t('common.no_matching_results_message_header_label')
        }
      }}
    />
  )
}

export default SeedGrowthOrderListDesktop
