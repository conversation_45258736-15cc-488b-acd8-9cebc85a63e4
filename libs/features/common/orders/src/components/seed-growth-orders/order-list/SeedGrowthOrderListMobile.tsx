import { TypoCaption } from '@element/react-typography'
import { Band, Loading, NestedList } from '@gc/components'
import { useDeepMemo, useLocale, useMemoizedTranslation } from '@gc/hooks'
import { SeedGrowthOrderProduct, SeedGrowthOrdersWithProducts, SeedGrowthOrderWithProducts } from '@gc/types'
import { fasteRoute, formatNumber, getProductName, hitsOnData } from '@gc/utils'
import { useCallback } from 'react'

import styles from './SeedGrowthOrderListMobile.module.scss'

export interface SeedGrowthOrderListMobileProps {
  searchTerm?: string
  fasteStoreKey: string
  ssuOrPackage: 'ssu' | 'package'
  packageSwitchAction?: React.ReactNode
  noMatchingDataContingency: React.ReactNode
  seedGrowthOrders: SeedGrowthOrdersWithProducts
  isTrackingDetailsLoading: boolean
}

const goToSeedGrowthOrderDetails = (code: string, orderId?: string) => {
  if (!orderId) return
  fasteRoute(`/orders/${orderId}/${code}`)
}

export function SeedGrowthOrderListMobile({
  searchTerm,
  ssuOrPackage: _ssuOrPackage,
  packageSwitchAction,
  noMatchingDataContingency,
  seedGrowthOrders,
  fasteStoreKey,
  isTrackingDetailsLoading
}: Readonly<SeedGrowthOrderListMobileProps>) {
  const t = useMemoizedTranslation()
  const locale = useLocale()

  const products = useDeepMemo(() => seedGrowthOrders.flatMap((order) => order.products), seedGrowthOrders)

  // Create a dependency that changes when product quantities change
  // This forces the NestedList to rerender when sub-data changes
  const productDataHash = useDeepMemo(() => {
    return products
      .map(
        (product) =>
          `${product.orderId}-${product.code}-${product.orderedQuantity}-${product.shippedQuantity}-${product.pendingQuantity}`
      )
      .join('|')
  }, products)

  // Memoize quantity formatting logic
  const formatQuantity = useCallback(
    (quantity: number | undefined, label: string) => {
      const formattedQty = quantity === undefined ? '--' : formatNumber(quantity, locale, true)
      return `${formattedQty} ${t('common.unit.label_other').toLowerCase()} ${label.toLowerCase()}`
    },
    [t, locale]
  )

  const shippedQuantityHeader = useCallback(
    (quantity: number | undefined) => formatQuantity(quantity, t('common.shipped.label')),
    [formatQuantity, t]
  )

  const orderedQuantityHeader = useCallback(
    (quantity: number | undefined) => formatQuantity(quantity, t('orders.ordered_quantity.label')),
    [formatQuantity, t]
  )

  const pendingQuantityHeader = useCallback(
    (quantity: number | undefined) => formatQuantity(quantity, t('common.pending.label')),
    [formatQuantity, t]
  )

  const getProductHeader = useCallback(
    (item: SeedGrowthOrderWithProducts) => (
      <Band
        placement='list'
        primaryText={{
          level: 1,
          bold: true,
          children: `Order ${item.orderId}`
        }}
        secondaryText1={{
          children: `${t('common.sales_year.label')}: ${item.salesYear}`,
          themeColor: 'text-secondary-on-background'
        }}
        secondaryText2={{
          children: `${t('common.ship_to.label')}: ${item.shipTo.city}, ${item.shipTo.state}`,
          themeColor: 'text-secondary-on-background'
        }}
      />
    ),
    [t]
  )

  const getStyledListItems = useCallback(
    (seedGrowthOrder: SeedGrowthOrderWithProducts) => {
      return seedGrowthOrder.products.map((seedGrowthOrderProduct: SeedGrowthOrderProduct, index: number) => ({
        row: seedGrowthOrderProduct,
        code: `${seedGrowthOrderProduct.code}-${seedGrowthOrderProduct.name}-${index}`,
        primaryText: getProductName(seedGrowthOrderProduct.name),
        secondaryText: isTrackingDetailsLoading ? (
          <Loading />
        ) : (
          <>
            <span className={styles.secondary_text_wrapper}>
              <TypoCaption>{orderedQuantityHeader(seedGrowthOrderProduct.orderedQuantity)}</TypoCaption>
              <TypoCaption>{pendingQuantityHeader(seedGrowthOrderProduct.pendingQuantity)}</TypoCaption>
            </span>
            <br />
            <TypoCaption>{shippedQuantityHeader(seedGrowthOrderProduct.shippedQuantity)}</TypoCaption>
          </>
        )
      }))
    },
    [isTrackingDetailsLoading, orderedQuantityHeader, pendingQuantityHeader, shippedQuantityHeader]
  )

  const searchFun = useCallback(
    (seedGrowthOrder: SeedGrowthOrderWithProducts, searchStr: string) => {
      if (!searchStr) return true

      return seedGrowthOrder.products?.some((product) =>
        hitsOnData(searchStr, [
          product.name,
          product.unit,
          orderedQuantityHeader(product.orderedQuantity),
          shippedQuantityHeader(product.shippedQuantity),
          pendingQuantityHeader(product.pendingQuantity)
        ])
      )
    },
    [orderedQuantityHeader, shippedQuantityHeader, pendingQuantityHeader]
  )

  return (
    <div className={styles.seed_growth_list_mobile_container}>
      <NestedList<SeedGrowthOrderWithProducts, SeedGrowthOrderProduct>
        listKey='seedGrowthOrders'
        data={seedGrowthOrders}
        hashKey={productDataHash}
        fasteStoreKey={fasteStoreKey}
        noMatchingDataContingency={noMatchingDataContingency}
        filterProps={{
          filters: [{ accessor: 'salesYear', title: t('common.sales_year.label') }]
        }}
        filterBarProps={{
          allFilterLabel: t('common.filters.label'),
          displayAllFilters: true,
          displayIndividualFilters: false,
          expansionForAllFilters: false,
          trailingContent: packageSwitchAction
        }}
        listProps={{
          divider: true,
          className: styles.seed_growth_list_mobile,
          listItemClassName: styles.seed_growth_list_item,
          getStyledListItems: getStyledListItems,
          getHeader: getProductHeader,
          onAction: (code, item) => goToSeedGrowthOrderDetails(code, item?.orderId)
        }}
        searchProps={{
          searchTerm,
          customSearchFn: searchFun,
          searchKeys: ['orderId', 'salesYear', 'shipTo.city', 'shipTo.state'],
          subItemsProps: {
            uniqAccessorKey: 'orderId',
            subItemsKeys: ['products'],
            searchSubItemsKeys: ['name', 'unit']
          }
        }}
      />
    </div>
  )
}

export default SeedGrowthOrderListMobile
