import { Grid, GridCol, GridRow } from '@element/react-grid'
import { Alert, Contingency, Loading, MessageWithAction, SegmentButton } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { useAppLoading, useMemoizedTranslation } from '@gc/hooks'
import { useMemo, useState } from 'react'
import MediaQuery from 'react-responsive'

import { useSeedGrowthData } from '../../hooks'
import SeedGrowthOrderListDesktop from './order-list/SeedGrowthOrderListDesktop'
import SeedGrowthOrderListMobile from './order-list/SeedGrowthOrderListMobile'
import styles from './SeedGrowthOrders.module.scss'

function SeedGrowthNoMatchingContingency() {
  const t = useMemoizedTranslation()

  return (
    <Contingency
      codes={['DEFAULT']}
      types={['messageWithAction']}
      className={styles.no_matching_data_contingency}
      contingency={{
        code: 'DEFAULT',
        displayType: 'messageWithAction',
        messageWithActionProps: {
          iconProps: {
            icon: 'info',
            variant: 'filled-secondary',
            className: 'gc-icon-info'
          },
          messageDescription: t('common.no_results_message_description'),
          messageHeader: t('common.no_matching_results_message_header_label')
        }
      }}
    />
  )
}

export interface SeedGrowthOrdersProps {
  searchTerm?: string
  fasteStoreKey: string
}

export function SeedGrowthOrders({ searchTerm, fasteStoreKey }: Readonly<SeedGrowthOrdersProps>) {
  const t = useMemoizedTranslation()

  const [ssuOrPackage, setSsuOrPackage] = useState<'ssu' | 'package'>('package')
  const packageSwitchAction = useMemo(() => {
    return (
      <SegmentButton
        selectedValue={ssuOrPackage}
        buttonProps={{ buttonSize: 'xsmall' }}
        onClick={(selectedValue: string) => setSsuOrPackage(selectedValue as 'ssu' | 'package')}
        data={[
          { name: `${t('common.ssu.label')}`, value: 'ssu' },
          { name: `${t('common.package.label')}`, value: 'package' }
        ]}
      />
    )
  }, [t, ssuOrPackage])

  const {
    data = [],
    error,
    isDataLoading,
    refetch,
    isTrackingDetailsLoading,
    seedGrowthOrders
  } = useSeedGrowthData(ssuOrPackage)

  useAppLoading(isDataLoading)

  if (isDataLoading || error) {
    return (
      <Grid>
        <GridRow className={styles.container_contingency}>
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4} verticalAlign='middle'>
            {isDataLoading ? (
              <Loading data-testid='loader' label={t('orders.loading_order_message.label')} />
            ) : (
              <MessageWithAction
                messageHeader={t('orders.could_not_load_order.label')}
                messageDescription={t('orders.could_not_load_order.description')}
                iconProps={{
                  icon: 'info',
                  variant: 'filled-secondary',
                  className: 'gc-icon-info'
                }}
                primaryButtonProps={{
                  label: t('common.try_again.label'),
                  variant: 'text',
                  onClick: refetch
                }}
              />
            )}
          </GridCol>
        </GridRow>
      </Grid>
    )
  }

  if (data.length === 0) {
    return (
      <Contingency
        codes={['DEFAULT']}
        types={['messageWithAction']}
        className={styles.no_matching_data_contingency}
        contingency={{
          code: 'DEFAULT',
          displayType: 'messageWithAction',
          messageWithActionProps: {
            iconProps: {
              icon: 'info',
              variant: 'filled-secondary',
              className: 'gc-icon-info'
            },
            messageDescription: t('orders.no_seed_growth_order.description'),
            messageHeader: t('orders.no_seed_growth_order.label')
          }
        }}
      />
    )
  }

  return (
    <div>
      <Alert
        type='info'
        variant='tonal'
        className={styles.alert_message}
        title={t('orders.questions_about_acceleron.label')}
        description={t('orders.questions_about_acceleron.description')}
      />

      <MediaQuery maxWidth={IS_MOBILE}>
        <SeedGrowthOrderListMobile
          searchTerm={searchTerm}
          ssuOrPackage={ssuOrPackage}
          fasteStoreKey={fasteStoreKey}
          seedGrowthOrders={seedGrowthOrders}
          packageSwitchAction={packageSwitchAction}
          isTrackingDetailsLoading={isTrackingDetailsLoading}
          noMatchingDataContingency={<SeedGrowthNoMatchingContingency />}
        />
      </MediaQuery>

      <MediaQuery minWidth={IS_DESKTOP}>
        <SeedGrowthOrderListDesktop
          ssuOrPackage={ssuOrPackage}
          seedGrowthOrders={seedGrowthOrders}
          packageSwitchAction={packageSwitchAction}
          isTrackingDetailsLoading={isTrackingDetailsLoading}
        />
      </MediaQuery>
    </div>
  )
}

export default SeedGrowthOrders
