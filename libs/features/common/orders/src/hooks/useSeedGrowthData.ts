/* eslint-disable @nx/enforce-module-boundaries */
import { SALES_UOM } from '@gc/constants'
import { useFarmerAddress, useGcPortalConfig, useLocale, useSelectedAccount } from '@gc/hooks'
import { useConsignmentsQueries, useOrdersQueries } from '@gc/redux-store'
import {
  AddressType,
  DeliveryItem,
  Locale,
  SeedGrowthOrder,
  SeedGrowthOrderEntry,
  SeedGrowthOrderProduct,
  SeedGrowthProductDelivery
} from '@gc/types'
import { createCacheKey, getDateFromUTC, LRUCache } from '@gc/utils'
import { isUndefined } from 'es-toolkit'
import { useMemo } from 'react'

// Create a singleton cache instance
const productsCache = new LRUCache<string, SeedGrowthOrderProduct[]>()

const createProductsCacheKey = (
  order: SeedGrowthOrder,
  trackingDetailsItems?: DeliveryItem[],
  ssuOrPackage?: 'ssu' | 'package'
) => {
  const trackingDetailsKey = createCacheKey(trackingDetailsItems, [{ key: 'deliveryNumber', fallback: 'no-tracking' }])

  // For object
  const cacheKey = createCacheKey({ order, trackingDetailsKey, ssuOrPackage }, [
    { key: 'order.code', fallback: 'no-code' },
    'ssuOrPackage',
    { key: 'trackingDetailsKey' }
  ])

  return cacheKey
}

export function useSeedGrowthData(ssuOrPackage?: 'ssu' | 'package') {
  const locale = useLocale()
  const gcPortalConfig = useGcPortalConfig()
  const { sapAccountId } = useSelectedAccount()
  const seedGrowthOrderConfig = gcPortalConfig?.seedGrowthOrderConfig

  const { useGetSeedGrowthOrdersQuery } = useOrdersQueries()
  const { useGetConsignmentsTrackingDetailsQuery } = useConsignmentsQueries()

  const {
    isLoading: isShippingAddressLoading,
    isFetching: isShippingAddressFetching,
    data: shipTo = []
  } = useFarmerAddress(sapAccountId, AddressType.SHIPPING)

  const {
    data = [],
    error,
    isLoading,
    isFetching,
    refetch
  } = useGetSeedGrowthOrdersQuery({
    params: {
      fields: {
        MOBILE: 'ONEDCE_CBUS',
        DESKTOP: 'ONEDCE_CBUS'
      }
    },
    reqBody: {
      pageSize: 100,
      startDate: '2021-09-01T00:00:00-06:00',
      // endDate: '2024-08-31T23:59:59-06:00',
      soldToAccounts: [`${sapAccountId}`],
      documentTypes: seedGrowthOrderConfig?.documentTypes,
      salesYears: gcPortalConfig?.salesYear
    }
  })

  const hasConsignments = useMemo(() => data.some((order) => order.consignments?.length), [data])
  const deliveryNumbers = data.flatMap((order) => {
    if (!hasConsignments) return []
    const consignments = order.consignments ?? []
    return consignments.map((consignment) => consignment.code)
  })

  const {
    data: consignmentsTrackingDetails,
    isLoading: isConsignmentsTrackingDetailsLoading,
    isFetching: isConsignmentsTrackingDetailsFetching
  } = useGetConsignmentsTrackingDetailsQuery({ deliveryNumbers }, { skip: !hasConsignments })

  const isDataLoading = useMemo(() => {
    return data.length === 0 && (isFetching || isLoading)
  }, [isFetching, isLoading, data])

  const isAddressLoading = useMemo(() => {
    return isShippingAddressLoading || isShippingAddressFetching
  }, [isShippingAddressLoading, isShippingAddressFetching])

  const isTrackingDetailsLoading = useMemo(() => {
    return (
      isUndefined(consignmentsTrackingDetails) &&
      (isConsignmentsTrackingDetailsFetching || isConsignmentsTrackingDetailsLoading)
    )
  }, [consignmentsTrackingDetails, isConsignmentsTrackingDetailsFetching, isConsignmentsTrackingDetailsLoading])

  const seedGrowthOrders = useMemo(() => {
    if (data.length === 0) return []

    return data.map((order) => {
      const cacheKey = createProductsCacheKey(order, consignmentsTrackingDetails?.items, ssuOrPackage)
      let products = productsCache.get(cacheKey)

      if (!products) {
        products = createProducts({
          order,
          locale,
          ssuOrPackage,
          deliveryItems: consignmentsTrackingDetails?.items
        })
        productsCache.set(cacheKey, products)
      }

      return {
        products,
        orderId: order.orderNumber,
        salesYear: order.salesYear ?? '',
        shipTo: {
          city: shipTo?.[0]?.addresses?.[0]?.cityTown?.toUpperCase() ?? '',
          state: shipTo?.[0]?.addresses?.[0]?.stateProvinceCode?.toUpperCase() ?? ''
        }
      }
    })
  }, [consignmentsTrackingDetails?.items, data, locale, shipTo, ssuOrPackage])

  return {
    data,
    error,

    isAddressLoading,
    isDataLoading,
    isTrackingDetailsLoading,

    seedGrowthOrders,
    refetch
  }
}

const createProducts = ({
  order,
  locale,
  ssuOrPackage,
  deliveryItems
}: {
  locale: Locale
  order: SeedGrowthOrder
  deliveryItems?: DeliveryItem[]
  ssuOrPackage?: 'ssu' | 'package'
}): SeedGrowthOrderProduct[] => {
  // Pre-calculate consignments length check to avoid repeated access
  const hasValidConsignments = order.consignments && order.consignments.length > 0
  const hasValidDeliveryItems = deliveryItems && deliveryItems.length > 0

  return order.entries.map((entry) => {
    let multiplier = 1
    let hasValidConversion = true

    if (ssuOrPackage === 'ssu') {
      const salesUOM = entry.product.unitOfMeasures?.find((uom) => uom.sapCode === SALES_UOM)
      if (!salesUOM || salesUOM.conversion === undefined) {
        console.warn(
          `SeedGrowthOrder: ${order.orderNumber} - ${entry.product.code} - ${entry.product.name} - No sales UOM found`
        )
        hasValidConversion = false
      } else {
        multiplier = salesUOM.conversion
      }
    }

    // Skip delivery calculations if there are no consignments
    const deliveries =
      hasValidConsignments && hasValidDeliveryItems
        ? createDeliveries({ entry, order, deliveryItems, locale, multiplier, hasValidConversion })
        : []

    // Use a regular for loop instead of reduce for better performance
    let shippedQuantity = 0
    if (hasValidConversion) {
      for (const element of deliveries) {
        shippedQuantity += element.shippedQuantity ?? 0
      }
    }

    // Calculate shipped quantity in sales UOM
    const orderedQuantityInSalesUOM = hasValidConversion ? entry.quantity * multiplier : undefined
    const pendingQuantityInSalesUOM =
      orderedQuantityInSalesUOM !== undefined ? orderedQuantityInSalesUOM - shippedQuantity : undefined

    return {
      deliveries,
      code: entry.product.code,
      name: entry.product.name,
      orderId: order.orderNumber,
      orderedQuantity: orderedQuantityInSalesUOM,
      pendingQuantity: pendingQuantityInSalesUOM,
      shippedQuantity: hasValidConversion ? shippedQuantity : undefined,
      unit: ssuOrPackage === 'ssu' ? 'SSU' : entry.salesUOM
    }
  })
}

const createDeliveries = ({
  entry,
  order,
  deliveryItems,
  locale,
  multiplier,
  hasValidConversion
}: {
  entry: SeedGrowthOrderEntry
  order: SeedGrowthOrder
  deliveryItems: DeliveryItem[]
  locale: Locale
  multiplier: number
  hasValidConversion: boolean
}) => {
  if (!entry?.consignmentIndexes?.length || !order.consignments) {
    return []
  }

  // Pre-convert entry number to string once
  const entryNumberStr = entry.entryNumber.toString()

  return entry.consignmentIndexes.reduce((arr, consignmentIndex) => {
    const matchingConsignment = order.consignments?.[consignmentIndex]
    if (!matchingConsignment?.entries) return arr

    const matchingConsignmentEntry = matchingConsignment.entries.find(
      (consignmentEntry) => consignmentEntry.salesOrderEntryNumber === entryNumberStr
    )
    if (!matchingConsignmentEntry) return arr

    const deliveryItemIndex = deliveryItems.findIndex((item) => item.deliveryNumber === matchingConsignment.code)
    const deliveryItem = deliveryItems[deliveryItemIndex] ?? {}

    // Calculate the shipped quantity in the sales UOM
    const shippedQuantity = hasValidConversion ? (matchingConsignmentEntry.quantity ?? 0) * multiplier : undefined

    arr.push({
      shippedQuantity,
      carrier: deliveryItem.forwardingAgentVendorName,
      carrierId: deliveryItem.carrierId,
      deliveryId: matchingConsignment.code ?? '',
      plannedShipDate: getDateFromUTC(matchingConsignment.plannedShipDate, locale),
      scheduledDeliveryDate: getDateFromUTC(deliveryItem.plannedDeliveryTimestamp, locale),
      status: matchingConsignment.statusText ?? '',
      trackingNumber: deliveryItem.carrierTrackingNumber,
      trackingUrl: deliveryItem.trackingUrl
    })

    return arr
  }, [] as SeedGrowthProductDelivery[])
}
