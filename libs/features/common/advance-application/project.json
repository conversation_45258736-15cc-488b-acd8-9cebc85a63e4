{"name": "features-common-advance-application", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/features/common/advance-application/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/features/common/advance-application/jest.config.ts"}}}}