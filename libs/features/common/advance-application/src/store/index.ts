import { buildStore, mergeWithGlobalReducers } from '@gc/redux-store'
import { useDispatch } from 'react-redux'

const rootReducer = mergeWithGlobalReducers({})
export const setUpStore = (preloadedState?: Partial<RootState>) =>
  buildStore(
    { preloadedState, reducer: rootReducer },
    {
      injectReportsApi: true,
      injectAemApi: true,
      useGlobalMiddleware: true,
      injectConfigDataApi: true,
      middlewareOpts: { serializableCheck: false }
    }
  )

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof rootReducer>
export type AppStore = ReturnType<typeof setUpStore>
export type AppDispatch = AppStore['dispatch']

export const store = setUpStore()
export const useAppDispatch: () => AppDispatch = useDispatch // Export a hook that can be reused to resolve types
