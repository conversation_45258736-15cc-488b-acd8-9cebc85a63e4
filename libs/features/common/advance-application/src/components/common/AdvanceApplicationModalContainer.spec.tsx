import '@testing-library/jest-dom'

import { ModalContainerProps } from '@gc/components'
import { render, screen } from '@testing-library/react'
import { useSelector } from 'react-redux'

import AdvanceApplicationModalContainer from './AdvanceApplicationModalContainer'

// Mock redux and dependencies
jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}))

jest.mock('@gc/components', () => ({
  getModals: jest.fn(() => ({
    AdvanceApplicationModal: {
      modalBody: () => <div>Start Application</div>,
      modalFooter: () => <div>Footer</div>
    }
  })),
  createEditAdvanceApplicationModalNames: ['AdvanceApplicationModal'],
  ModalContainer: (props: ModalContainerProps) => (
    <div data-testid='modal-container' data-open={props.open}>
      ModalContainer: {props.modalName}
    </div>
  )
}))

jest.mock('@gc/redux-store', () => ({
  getModal: () => ({})
}))

describe('AdvanceApplicationModalContainer', () => {
  it('renders null if modal is not defined', () => {
    const { container } = render(<AdvanceApplicationModalContainer />)
    expect(container.firstChild).toBeNull()
  })

  it('renders ModalContainer when modal exists', () => {
    const selector = useSelector as unknown as jest.Mock
    selector.mockReturnValue({
      open: true,
      name: 'AdvanceApplicationModal',
      props: {}
    })

    render(<AdvanceApplicationModalContainer />)

    expect(screen.getByTestId('modal-container')).toBeInTheDocument()
    expect(screen.getByTestId('modal-container')).toHaveAttribute('data-open', 'true')
  })
})
