.container {
  box-sizing: content-box !important;
  @media (min-width: 1024px) {
    padding: 48px 80px;
    position: relative;
  }
  @media (max-width: 1023px) {
    margin-bottom: 24px;
  }
}

.alert_container {
  padding: 24px;
}

.header {
  @media (min-width: 1024px) {
    margin-bottom: 48px;
  }
}

@media (min-width: 720px) {
  :global(.lmnt-table__cell-content) {
    min-height: 48px;
    height: auto !important;
  }
}
