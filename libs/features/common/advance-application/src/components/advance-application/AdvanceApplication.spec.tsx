import '@testing-library/jest-dom'

import { ButtonProps } from '@element/react-button'
import { AlertProps } from '@gc/types'
import { render, screen } from '@testing-library/react'

import { AdvanceApplication } from './AdvanceApplication'
// Mocks
jest.mock('@gc/components', () => ({
  Alert: (props: AlertProps) => (
    <div data-testid='alert' onClick={(event) => props.actionButtonProps?.onClick?.(event)}>
      {props.title} - {props.description}
      {props.actionButtonProps?.label && <button>{props.actionButtonProps.label}</button>}
    </div>
  ),
  Header: (props: { title: string }) => <div data-testid='header'>{props.title}</div>,
  TopAppBar: (props: { title: string; trailingIconButtonProps: ButtonProps[]; onClick?: () => void }) => (
    <div data-testid='top-app-bar'>
      {props.title}
      <button onClick={props.trailingIconButtonProps[0].onClick as React.MouseEventHandler<HTMLButtonElement>}>
        Search
      </button>
    </div>
  ),
  AdvanceApplicationList: (props: { tableTitle: string }) => (
    <div data-testid='application-list'>{props.tableTitle}</div>
  )
}))

jest.mock('@gc/hooks', () => ({
  useMemoizedTranslation: () => (key: string) => key,
  useModal: () => ({ openModal: jest.fn() }),
  useSearch: () => [
    '',
    false,
    {
      handleOpenSearch: jest.fn(),
      handleCloseSearch: jest.fn(),
      handleCancelSearch: jest.fn(),
      handleSearch: jest.fn()
    }
  ],
  useGcPortalConfig: () => ({
    enrollmentDateRange: [{ start: '01/01/2020', end: '12/31/2099' }],
    openEnrollAlertTitle: 'Applications Due October 14',
    openEnrollAlertDesc: 'Apply now to join 1st quarter and auto-enroll in 2nd & 3rd.',
    closedEnrollAlertTitle: 'Closed Title',
    closedEnrollAlertDesc: 'Closed Description'
  })
}))

jest.mock('react-responsive', () => ({
  __esModule: true,
  default: ({ children }: never) => children
}))

jest.mock('@gc/utils', () => ({
  getFasteStoreKey: () => 'mocked_key'
}))

describe('AdvanceApplication', () => {
  it('shows open enrollment alert with action button', () => {
    render(<AdvanceApplication />)
    const alert = screen.getByTestId('alert')
    expect(alert).toHaveTextContent('advance_application.start_application.label')
    expect(alert).toHaveTextContent('Applications Due October 14')
    expect(alert).toHaveTextContent('Apply now to join 1st quarter and auto-enroll in 2nd & 3rd.')
  })

  it('renders header', () => {
    render(<AdvanceApplication />)
    expect(screen.getByTestId('header')).toBeInTheDocument()
    expect(screen.getByTestId('header')).toHaveTextContent('advance_application.label')
  })

  it('renders top app bar', () => {
    render(<AdvanceApplication />)
    expect(screen.getByTestId('top-app-bar')).toBeInTheDocument()
  })

  it('renders advance application list component with title', () => {
    render(<AdvanceApplication />)
    expect(screen.getByTestId('application-list')).toHaveTextContent('advance_application.submitted_applications.label')
  })
})
