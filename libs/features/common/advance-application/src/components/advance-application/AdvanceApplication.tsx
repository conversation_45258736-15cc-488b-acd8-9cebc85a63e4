/* eslint-disable @nx/enforce-module-boundaries */
import { AdvanceApplication<PERSON>ist, <PERSON><PERSON>, <PERSON><PERSON>, TopAppBar } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { useGcPortalConfig, useMemoizedTranslation, useModal, useSearch } from '@gc/hooks'
import { getFasteStoreKey } from '@gc/utils'
import { parse } from 'date-fns'
import { useCallback, useMemo } from 'react'
import MediaQuery from 'react-responsive'

import styles from './AdvanceApplication.module.scss'

export function AdvanceApplication() {
  const t = useMemoizedTranslation()
  const { openModal } = useModal()

  const fasteStoreKey = getFasteStoreKey('advance_application', 'advance_application')
  const {
    enrollmentDateRange,
    openEnrollAlertTitle,
    closedEnrollAlertDesc,
    openEnrollAlertDesc,
    closedEnrollAlertTitle
  } = useGcPortalConfig('advanceApplicationConfig')

  const handleCreateReport = useCallback(() => {
    openModal({
      name: 'ADVANCE_APPLICATION',
      props: {}
    })
  }, [openModal])

  const [searchTerm, openSearch, { handleOpenSearch, handleCloseSearch, handleCancelSearch, handleSearch }] =
    useSearch(fasteStoreKey)

  const isTodayInRange = useMemo(() => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    return enrollmentDateRange?.some(({ start, end }) => {
      const startDate = parse(start, 'MM/dd/yyyy', new Date())
      const endDate = parse(end, 'MM/dd/yyyy', new Date())

      startDate.setHours(0, 0, 0, 0)
      endDate.setHours(0, 0, 0, 0)

      return today >= startDate && today <= endDate
    })
  }, [enrollmentDateRange])

  return (
    <>
      <div className={styles.alert_container}>
        {isTodayInRange ? (
          <Alert
            className={styles.alert_message}
            type='info'
            variant='tonal'
            title={openEnrollAlertTitle}
            description={openEnrollAlertDesc}
            actionButtonProps={{
              label: t('advance_application.start_application.label'),
              onClick: handleCreateReport
            }}
          />
        ) : (
          <Alert
            className={styles.alert_message}
            type='info'
            variant='tonal'
            title={closedEnrollAlertTitle}
            description={closedEnrollAlertDesc}
          />
        )}
      </div>

      <div className={styles.container}>
        <MediaQuery minWidth={IS_DESKTOP}>
          <div className={styles.header}>
            <Header title={t('advance_application.label')} />
          </div>
        </MediaQuery>

        <MediaQuery maxWidth={IS_MOBILE}>
          <TopAppBar
            title={t('advance_application.label')}
            leadingContent={openSearch || searchTerm !== '' ? 'search' : 'title'}
            trailingIconButtonProps={[
              {
                icon: 'search',
                onClick: handleOpenSearch
              }
            ]}
            searchProps={{
              searchTerm,
              onChange: handleSearch,
              onClear: handleCancelSearch,
              closeSearchButtonProps: {
                onClick: handleCloseSearch
              }
            }}
          />
        </MediaQuery>

        <AdvanceApplicationList
          fasteStoreKey={fasteStoreKey}
          tableTitle={t('advance_application.submitted_applications.label')}
        />
      </div>
    </>
  )
}
