import { ReportTypeConfiguration } from '../reports'

export const reportTypeConfigMock: ReportTypeConfiguration = {
  version: 'current',
  reportCategory: 'orderByProduct',
  description: 'Order By Product',
  name: 'Order By Product',
  availableFields: [
    {
      dataType: 'string',
      description: 'Without Prefix',
      displayName: 'Order ID',
      fieldId: 'code',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: '',
      displayName: 'Farmer Name',
      fieldId: 'growerInfo.name',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: '',
      displayName: 'SeedPro Name',
      fieldId: 'agent',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      accessor: 'statusText',
      dataType: 'string',
      displayName: 'Status',
      fieldId: 'status',
      isFilterable: true,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: 'Order Name',
      displayName: 'Order Name',
      fieldId: 'name',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: '',
      displayName: 'Order Type',
      fieldId: 'documentType',
      isFilterable: true,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'boolean',
      description: '',
      displayName: 'Split Bill',
      fieldId: 'hasSplitBill',
      isFilterable: true,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: 'This field is going to be repurposed to store corteva id. It is in Monsanto82Bunit',
      displayName: 'Connect ID',
      fieldId: 'taxId',
      isFilterable: true,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: '',
      displayName: 'Seed Year',
      fieldId: 'salesYear',
      isFilterable: true,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'number',
      description: 'Gross Price',
      displayName: 'Gross Price',
      fieldId: 'grossPrice',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'number',
      description: 'Net Price',
      displayName: 'Net Price',
      fieldId: 'netPrice',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'number',
      description: 'Discount',
      displayName: 'Discount',
      fieldId: 'totalDiscounts',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'boolean',
      description: '',
      displayName: 'Hub/Spoke',
      fieldId: 'hasHubLocation',
      isFilterable: true,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: 'Entry Number',
      displayName: 'Entry Number',
      fieldId: 'entry.entryNumber',
      groupBy: 'entry',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: 'Entry Product Name',
      displayName: 'Product Name',
      fieldId: 'entry.product.name',
      groupBy: 'entry',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: 'Entry Package Size',
      displayName: 'Package Size',
      fieldId: 'entry.packageSizeCode',
      groupBy: 'entry',
      isFilterable: true,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: 'Entry Package Type',
      displayName: 'Package Type',
      fieldId: 'entry.packageType',
      groupBy: 'entry',
      isFilterable: true,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: 'Entry Treatment',
      displayName: 'Treatment',
      fieldId: 'entry.product.specialTreatmentCode',
      groupBy: 'entry',
      isFilterable: true,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: 'Entry Product Acronym',
      displayName: 'Acronym',
      fieldId: 'entry.product.acronymID',
      isFilterable: false,
      isHidden: false,
      isRequired: false,
      groupBy: 'entry'
    },
    {
      dataType: 'string',
      description: 'Entry Crop',
      displayName: 'Crop',
      fieldId: 'entry.cropCode',
      accessor: 'entry.cropName',
      isFilterable: true,
      isHidden: false,
      isRequired: false,
      groupBy: 'entry'
    },
    {
      dataType: 'string',
      description: 'Entry Rejection Reason',
      displayName: 'Rejection Reason',
      fieldId: 'entry.rejectionReason',
      isFilterable: true,
      isHidden: false,
      isRequired: false,
      groupBy: 'entry'
    },
    {
      dataType: 'number',
      description: 'Entry Net Quantity',
      displayName: 'Net Quantity',
      fieldId: 'entry.netQuantity',
      isFilterable: false,
      isHidden: false,
      isRequired: false,
      groupBy: 'entry'
    },
    {
      dataType: 'number',
      description: 'Entry Order Quantity',
      displayName: 'Order Quantity',
      fieldId: 'entry.quantityDouble',
      isFilterable: false,
      isHidden: false,
      isRequired: false,
      groupBy: 'entry'
    },
    {
      dataType: 'number',
      description: 'Entry Confirmed Quantity',
      displayName: 'Confirmed Quantity',
      fieldId: 'entry.confirmedQuantity',
      isFilterable: false,
      isHidden: false,
      isRequired: false,
      groupBy: 'entry'
    },
    {
      dataType: 'number',
      description: 'Entry Unconfirmed Quantity',
      displayName: 'Unconfirmed Quantity',
      fieldId: 'entry.unconfirmedQuantity',
      isFilterable: true,
      isHidden: false,
      isRequired: false,
      groupBy: 'entry'
    },
    {
      dataType: 'number',
      description: 'Entry Returned Quantity',
      displayName: 'Returned Quantity',
      fieldId: 'entry.returnQuantity',
      isFilterable: false,
      isHidden: false,
      isRequired: false,
      groupBy: 'entry'
    },
    {
      dataType: 'number',
      description: 'Entry Remaining To Deliver Quantity',
      displayName: 'Remaining To Deliver',
      fieldId: 'entry.remainingToDeliverQuantity',
      isFilterable: true,
      isHidden: false,
      isRequired: false,
      groupBy: 'entry'
    },
    {
      dataType: 'number',
      description: 'Entry Delivered Quantity',
      displayName: 'Delivered',
      fieldId: 'entry.deliveredQuantity',
      isFilterable: false,
      isHidden: false,
      isRequired: false,
      groupBy: 'entry'
    },
    {
      dataType: 'number',
      description: 'Entry Quantity on Delivery',
      displayName: 'Quantity on Delivery',
      fieldId: 'entry.consignedQuantity',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'number',
      description: 'Entry Net Price Per Unit',
      displayName: 'Net Price Per Unit',
      fieldId: 'entry.netPricePerUnit',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: 'Entry Location',
      displayName: 'Location',
      fieldId: 'entry.storageLocation',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'number',
      description: 'Entry Discount',
      displayName: 'Discount',
      fieldId: 'entry.totalDiscountPrice',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'number',
      description: 'Calculated Value',
      displayName: 'Price Per Unit',
      fieldId: 'entry.totalPricePerUnit',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      description: 'Entry Warehouse',
      displayName: 'Warehouse',
      fieldId: 'entry.warehouse',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'boolean',
      description: 'Entry Replant',
      displayName: 'Replant',
      fieldId: 'entry.isReplant',
      isFilterable: true,
      isHidden: false,
      isRequired: false
    }
  ],
  availableFilters: [
    {
      description: 'Filter by status',
      displayName: 'Order Status',
      filterId: 'statusCodes',
      fieldId: 'status',
      filterType: 'enum',
      filterOptions: [
        {
          displayValue: 'Cancelled',
          internalValue: 'cancelled',
          optionId: 'CANCELLED'
        },
        {
          displayValue: 'Credit Block',
          internalValue: 'creditBlock',
          optionId: 'CREDIT_BLOCK'
        },
        {
          displayValue: 'Delivered',
          internalValue: 'delivered',
          optionId: 'DELIVERED'
        },
        {
          displayValue: 'Discounts in Progress',
          internalValue: 'discountsInProgress',
          optionId: 'DISCOUNTS_IN_PROGRESS'
        },
        {
          displayValue: 'Open',
          internalValue: 'open',
          optionId: 'OPEN'
        },
        {
          displayValue: 'Partially Delivered',
          internalValue: 'partiallyDelivered',
          optionId: 'PARTIALLY_DELIVERED'
        },
        {
          displayValue: 'Pricing Error',
          internalValue: 'pricingError',
          optionId: 'PRICING_ERROR'
        },
        {
          displayValue: 'Processing',
          internalValue: 'processing',
          optionId: 'PROCESSING'
        },
        {
          displayValue: 'Submitted with Errors',
          internalValue: 'submittedWithError',
          optionId: 'SUBMITTED_WITH_ERRORS'
        }
      ]
    },
    {
      description: 'Filter by order type',
      displayName: 'Order Type',
      filterId: 'orderTypes',
      fieldId: 'documentType',
      filterOptions: [
        {
          displayValue: 'Farmer Order',
          internalValue: 'farmerOrder',
          optionId: 'ZU3O'
        },
        {
          displayValue: 'Stock Order',
          internalValue: 'stockOrder',
          optionId: 'ZU3L'
        }
      ],
      filterType: 'enum'
    },
    {
      filterType: 'switch',
      description: 'Filter by split bill',
      displayName: 'Split Bill',
      filterId: 'hasSplitBill',
      filterOptions: [
        { displayValue: 'Yes', internalValue: true, optionId: 'true' },
        { displayValue: 'No', internalValue: false, optionId: 'false' }
      ]
    },
    {
      filterType: 'enum',
      description: 'Filter by seed year',
      displayName: 'Seed Year',
      filterId: 'salesYear',
      filterOptions: [
        {
          displayValue: '2025',
          internalValue: '2025',
          optionId: '2025'
        },
        {
          displayValue: '2026',
          internalValue: '2026',
          optionId: '2026'
        }
      ]
    },
    {
      filterType: 'switch',
      description: 'Filter by connect ID',
      displayName: 'Connect ID',
      filterId: 'taxId',
      filterOptions: [
        {
          displayValue: 'Yes',
          internalValue: true,
          optionId: 'true'
        },
        {
          displayValue: 'No',
          internalValue: false,
          optionId: 'false'
        }
      ]
    },
    {
      filterType: 'switch',
      description: 'Filter by hub/spoke',
      displayName: 'Hub/Spoke',
      filterId: 'hasHubLocation',
      filterOptions: [
        { displayValue: 'Yes', internalValue: true, optionId: 'true' },
        { displayValue: 'No', internalValue: false, optionId: 'false' }
      ]
    },
    {
      filterType: 'enum',
      description: 'Filter by entry package size',
      displayName: 'Package Size',
      filterId: 'packageSizeCode',
      filterOptions: [
        {
          displayValue: '140M',
          internalValue: '140M',
          optionId: '140M'
        },
        {
          displayValue: '35SCU',
          internalValue: '35SCU',
          optionId: '35SCU'
        },
        {
          displayValue: '40SCU',
          internalValue: '40SCU',
          optionId: '40SCU'
        },
        {
          displayValue: '70M',
          internalValue: '70M',
          optionId: '70M'
        },
        {
          displayValue: '20M',
          internalValue: '20M',
          optionId: '20M'
        },
        {
          displayValue: '40U',
          internalValue: '40U',
          optionId: '40U'
        },
        {
          displayValue: '50U',
          internalValue: '50U',
          optionId: '50U'
        },
        {
          displayValue: '80M',
          internalValue: '80M',
          optionId: '80M'
        }
      ]
    },
    {
      filterType: 'enum',
      description: 'Filter by entry package type',
      displayName: 'Package Type',
      filterId: 'packageType',
      filterOptions: [
        { displayValue: 'Bag', internalValue: 'Bag', optionId: 'Bag' },
        { displayValue: 'SP', internalValue: 'SP', optionId: 'SP' },
        { displayValue: 'MB', internalValue: 'MB', optionId: 'MB' },
        { displayValue: 'SC-BULK-FG', internalValue: 'SC-BULK-FG', optionId: 'SC-BULK-FG' }
      ]
    },
    {
      description: 'Filter by crop',
      displayName: 'Crop',
      filterId: 'crop',
      fieldId: 'entry.cropCode',
      filterType: 'enum',
      filterOptions: [
        { displayValue: 'Corn', internalValue: 'Corn', optionId: 'Corn' },
        { displayValue: 'Soybean', internalValue: 'Soybean', optionId: 'Soybean' },
        { displayValue: 'Sorghum', internalValue: 'Sorghum', optionId: 'Sorghum' }
      ]
    },
    {
      description: 'Filter by Rejection Reason',
      displayName: 'Rejection Reason',
      filterId: 'rejectionReason',
      fieldId: 'entry.rejectionReason',
      filterType: 'switch',
      filterOptions: [
        { displayValue: 'Yes', internalValue: true, optionId: 'true' },
        { displayValue: 'No', internalValue: false, optionId: 'false' }
      ]
    },
    {
      filterType: 'switch',
      description: 'Filter by unconfirmed quantity > 0',
      displayName: 'Unconfirmed Quantity',
      filterId: 'hasUnconfirmedQuantity',
      fieldId: 'entry.unconfirmedQuantity',
      filterOptions: [
        { displayValue: 'Yes', internalValue: true, optionId: 'true' },
        { displayValue: 'No', internalValue: false, optionId: 'false' }
      ]
    },
    {
      filterType: 'switch',
      description: 'Filter by remaining to deliver quantity > 0',
      displayName: 'Remaining To Deliver',
      filterId: 'remainingToDeliverQuantity',
      fieldId: 'entry.remainingToDeliverQuantity',
      filterOptions: [
        { displayValue: 'Yes', internalValue: 'positive', optionId: 'positive' },
        { displayValue: 'No', internalValue: 'zero', optionId: 'zero' }
      ]
    },
    {
      filterType: 'switch',
      description: 'Filter by replant',
      displayName: 'Replant',
      filterId: 'isReplant',
      fieldId: 'entry.isReplant',
      filterOptions: [
        { displayValue: 'Yes', internalValue: true, optionId: 'true' },
        { displayValue: 'No', internalValue: false, optionId: 'false' }
      ]
    }
  ]
}
