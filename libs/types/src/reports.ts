import * as z from 'zod/v4'

import { Pagination } from './response'

export const ReportTypeOptions = {
  Live: 'live',
  Snapshot: 'snapshot'
} as const

export const ReportCategoryOptions = {
  OrderByProduct: 'orderByProduct'
} as const

export type ReportCategoryIds = (typeof ReportCategoryOptions)[keyof typeof ReportCategoryOptions]
export type ReportTypeIds = (typeof ReportTypeOptions)[keyof typeof ReportTypeOptions]
export type ReportVersionIds = 'current' | string

// Data types for report fields
export const ReportFieldDataOptions = {
  STRING: 'string',
  NUMBER: 'number',
  DATE: 'date',
  BOOLEAN: 'boolean'
} as const

export type ReportFieldDataType = (typeof ReportFieldDataOptions)[keyof typeof ReportFieldDataOptions]

// Filter types for report filters
export const ReportFilterOptions = {
  SWITCH: 'switch',
  ENUM: 'enum'
} as const

export type ReportFilterType = (typeof ReportFilterOptions)[keyof typeof ReportFilterOptions]

// Zod schema for ReportFilterOption
export const ReportFilterOptionSchema = z.object({
  optionId: z.string(),
  displayValue: z.string(),
  internalValue: z.string().or(z.boolean()).or(z.number())
})

// Zod schema for ReportFieldDefinition
export const ReportFieldDefinitionSchema = z.object({
  /** Description of the field */
  description: z.string().optional(),
  /** Group the field by this field */
  groupBy: z.string().optional(),
  /** Order of the field in the group */
  groupOrder: z.number().optional(),
  /** Order of the field in the report */
  displayOrder: z.number().optional(),
  /** Unique identifier for the field */
  fieldId: z.string(),
  /** Human-readable name for the field */
  displayName: z.string(),
  /** Data type of the field (string, number, date, boolean) */
  dataType: z.enum(Object.values(ReportFieldDataOptions)),
  /** Whether this field can be used for filtering */
  isFilterable: z.boolean(),
  /** Whether this field must be included in all reports of this type */
  isRequired: z.boolean(),
  /** Whether this field should be hidden from the user interface */
  isHidden: z.boolean(),
  /** Accessor for the field to display in the report */
  accessor: z.string().optional()
})

// Zod schema for ReportFilterDefinition
export const ReportFilterDefinitionSchema = z.object({
  filterId: z.string(),
  fieldId: z.string().optional(),
  displayName: z.string(),
  description: z.string().optional(),
  filterType: z.enum(Object.values(ReportFilterOptions)),
  filterOptions: z.array(ReportFilterOptionSchema).optional()
})

// Schema for selected filter values (what users actually select)
export const SelectedFilterSchema = z.object({
  filterId: z.string(),
  fieldId: z.string(),
  displayName: z.string().optional(),
  filterType: z.enum(Object.values(ReportFilterOptions)),
  selectedOptions: z.array(z.string()).or(z.array(z.boolean())).optional()
})

// Type definitions inferred from schemas
export type ReportFilterOption = z.infer<typeof ReportFilterOptionSchema>
export type ReportFieldDefinition = z.infer<typeof ReportFieldDefinitionSchema>
export type ReportFilterDefinition = z.infer<typeof ReportFilterDefinitionSchema>
export type SelectedReportFilter = z.infer<typeof SelectedFilterSchema>

// Report Type Configuration DTO (DynamoDB Model)
export interface ReportTypeConfiguration {
  name: string
  version: ReportVersionIds
  description?: string
  reportCategory: string
  availableFields: ReportFieldDefinition[]
  availableFilters: ReportFilterDefinition[]
}

// Selected Field DTO
export interface SelectedReportField extends Omit<ReportFieldDefinition, 'groupOrder'> {
  displayOrder?: number
}

// User Reports DTO (DynamoDB Model)
export interface UserReport {
  [key: string]: string | number | boolean | SelectedReportField[] | SelectedReportFilter[]

  reportId: string
  userId: string
  version: string
  createdAt: string
  lastModifiedAt: string
  reportName: string
  reportType: ReportTypeIds
  selectedFields: SelectedReportField[]
  selectedFilters: SelectedReportFilter[]
  reportCategory: ReportCategoryIds
  reportCategoryDisplayName: string
  reportConfigurationVersion: string
}

// API RESPONSE TYPES

export type UserReportsListResponse = {
  reports: UserReport[]
  pagination?: Pagination
}

export type UserReportResponse = UserReport

// MUTATION TYPES

// Schema for user report submission
export const UserReportSubmissionSchema = z.object({
  userId: z.string(),
  createdBy: z.string().optional(),
  reportType: z.enum(Object.values(ReportTypeOptions)),
  reportCategory: z.enum(Object.values(ReportCategoryOptions)),
  reportCategoryDisplayName: z.string(),
  reportName: z.string().min(1, { message: 'Report name is required' }),
  selectedFields: z.array(ReportFieldDefinitionSchema).nonempty({ message: 'At least one field must be selected' }),
  selectedFilters: z.array(SelectedFilterSchema),
  reportConfigurationVersion: z.string().min(1, { message: 'Report configuration version is required' }),
  version: z.string().min(1, { message: 'Version is required' })
})

// Update schema for existing user reports
export const UserReportUpdateSchema = UserReportSubmissionSchema.extend({
  reportId: z.string()
})

export type UserReportUpdateData = z.infer<typeof UserReportUpdateSchema>
export type UserReportSubmissionData = z.infer<typeof UserReportSubmissionSchema>
