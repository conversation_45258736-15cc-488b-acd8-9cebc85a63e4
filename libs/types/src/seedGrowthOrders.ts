import { Country, PriceValueAndCurrency } from './shared'
import { User } from './user'

export type SeedGrowthOrdersResponse = {
  orders: SeedGrowthOrder[]
  pagination: Pagination
  sorts: Sort[]
}

export type SeedGrowthOrder = {
  billToParty?: string
  calculated: boolean
  code: string
  deliveryItemsQuantity: number
  distributionChannel?: string
  division?: string
  entries: SeedGrowthOrderEntry[]
  net: boolean
  orderType: OrderType
  requestedDeliveryDate?: Date
  roundUp: boolean
  sameDeliveryDate: boolean
  shipToParty?: string
  site: string
  store: string
  subTotal: PriceValueAndCurrency
  totalDiscounts: PriceValueAndCurrency
  totalItems: number
  totalPrice: PriceValueAndCurrency
  totalPriceWithTax: PriceValueAndCurrency
  totalTax: PriceValueAndCurrency
  unit: Unit
  user: User
  consignments?: SeedGrowthConsignment[]
  created: Date
  deliveryStatus?: string
  documentType: string
  lob?: string
  orderNumber: string
  paymentType: PaymentType
  purchaseOrderNumber?: string
  status: string
  statusText: string
  orderReason?: OrderReason
  growerInfo?: GrowerInfo
  shipFromParty?: string
  shipToWarehouse?: ShipToWarehouseClass
  warehouse?: ShipToWarehouseClass
  cropLevelDetails?: CropLevelDetail[]
  customerTaxId?: string
  totalBrandDiscount?: PriceValueAndCurrency
  totalDiscountsPrice?: PriceValueAndCurrency
  fromLocation?: Location
  toLocation?: Location
  salesYear?: string
}

export type SeedGrowthConsignment = {
  code: string
  createdOnDateTime: Date
  entries: SeedGrowthConsignmentEntry[]
  plannedShipDate: string
  shipmentId?: string
  status: string
  statusText: string
}

type SeedGrowthConsignmentEntry = {
  batchName?: string
  deliveryItemNumber: string
  quantity: number
  salesOrderEntryNumber: string
  salesUnitOfMeasureCode: string
}

type CropLevelDetail = {
  crop: string
  details: Details
}

type Details = {
  averagePricePerUnit: PriceValueAndCurrency
  discounts: PriceValueAndCurrency
  grossPrice: PriceValueAndCurrency
  netPrice: PriceValueAndCurrency
  productsCount: number
}

export type SeedGrowthOrderEntry = {
  consignmentIndexes?: number[]
  cropCode?: string
  cropName?: string
  deliveredQuantity: number
  entryNumber: number
  isReplant?: boolean
  lineItemSubTotal?: PriceValueAndCurrency
  netPricePerUnit?: PriceValueAndCurrency
  netQuantity: number
  product: Product
  quantity: number
  rejected?: boolean
  remainingToDeliverQuantity: number
  returnQuantity: number
  salesUOM: string
  toStorageLocation?: ToStorageLocation
  toWarehouse?: ToWarehouseClass
  totalDiscountPrice?: PriceValueAndCurrency
  totalPricePerUnit?: PriceValueAndCurrency
  warehouse?: ToWarehouseClass
}

type Product = {
  available: number
  brandName?: string
  code: string
  name: string
  salesUnitOfMeasure: string
  unitOfMeasures: UnitOfMeasure[]
  packageType?: string
  acronymID?: string
  isPackagingMaterial?: boolean
  packageDescription?: string
  packageSizeCode?: string
  shortPackageType?: string
  specialTreatmentCode?: string
  specialTreatmentDescription?: string
  species?: string
}

type UnitOfMeasure = {
  conversion: number
  name: string
  sapCode: string
}

export type ToStorageLocation = {
  address: Address
  code: string
  locationCode: string
  locationName: string
  plant: string
}

type Address = {
  country: Country
  defaultAddress: boolean
  formattedAddress: string
  id: string
  line1: string
  phone: string
  postalCode: string
  region: Region
  shippingAddress: boolean
  town: string
  visibleInAddressBook: boolean
  email?: string
}

type Region = {
  countryIso: string
  isocode: string
  isocodeShort: string
  name: string
}

type ToWarehouseClass = {
  address: Address
  b2bUnit: GrowerInfo
  code: string
  name: string
  plantCode: string
}

type GrowerInfo = {
  name: string
  uid: string
  sapAccountId: string
  taxId: string
}

type OrderReason = {
  code: string
  description: string
  visible: boolean
}

type OrderType = {
  code: string
  visible: boolean
}

type PaymentType = {
  code: string
}

type ShipToWarehouseClass = {
  code: string
  name: string
  plantCode: string
}

type Unit = {
  sapAccountId: string
}

type Pagination = {
  currentPage: number
  pageSize: number
  sort: string
  totalPages: number
  totalResults: number
}

type Sort = {
  code: string
  name: string
  selected: boolean
}

export type SeedGrowthProductDelivery = {
  deliveryId: string
  carrier?: string
  carrierId?: string
  trackingNumber?: string
  plannedShipDate: string
  trackingUrl?: string
  scheduledDeliveryDate: string
  shippedQuantity?: number
  status: string
}

export type SeedGrowthOrderProduct = {
  code: string
  name: string
  unit: string
  orderId: string
  shippedQuantity?: number
  orderedQuantity?: number
  pendingQuantity?: number
  deliveries: SeedGrowthProductDelivery[]
}

export type SeedGrowthOrderWithProducts = {
  orderId: string
  salesYear: string
  shipTo: { city: string; state: string }
  products: SeedGrowthOrderProduct[]
}

export type SeedGrowthOrdersWithProducts = SeedGrowthOrderWithProducts[]
