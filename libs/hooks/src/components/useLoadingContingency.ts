import { BaseQueryFn, QueryActionCreatorResult, QueryDefinition } from '@reduxjs/toolkit/query'
import { useMemo } from 'react'

import { useMemoizedTranslation } from '../useMemoizedTranslations'

export type GridListRefetch<T> = () => void | QueryActionCreatorResult<
  QueryDefinition<unknown, BaseQueryFn, string, T[], string>
>

export interface UseLoadingContingencyProps<T> {
  data: T[]
  isError: boolean
  isLoading: boolean
  isFetching: boolean
  errorHeader: string
  errorDescription: string
  noDataHeader: string
  noDataDescription: string
  loadingMessage?: string
  refetch: GridListRefetch<T>
}

export function useLoadingContingency<T>({
  data,
  isLoading,
  isFetching,
  isError,
  refetch,
  errorHeader,
  errorDescription,
  noDataHeader,
  noDataDescription,
  loadingMessage
}: UseLoadingContingencyProps<T>) {
  const t = useMemoizedTranslation()

  const hasError = !!isError
  const dataLength = useMemo(() => data.length, [data])
  const isDataLoading = useMemo(() => data.length === 0 && (isFetching || isLoading), [data, isFetching, isLoading])
  const hasNoData = useMemo(() => data.length === 0, [data])

  const loadingMessageProps = useMemo(() => {
    return loadingMessage ?? t('common.loading.label')
  }, [loadingMessage, t])

  return {
    hasError,
    dataLength,
    hasNoData,
    isDataLoading,
    refetchData: refetch,
    loadingMessage: loadingMessageProps,
    errorProps: { errorHeader, errorDescription },
    noDataProps: { noDataHeader, noDataDescription }
  }
}
