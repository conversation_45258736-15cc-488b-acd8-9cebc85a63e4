import { BaseQueryFn, QueryActionCreatorResult, QueryDefinition } from '@reduxjs/toolkit/query'
import { useMemo } from 'react'

import { useMemoizedTranslation } from '../useMemoizedTranslations'

export type GridListRefetch<T> = () => void | QueryActionCreatorResult<
  QueryDefinition<unknown, BaseQueryFn, string, T[], string>
>

// New config types matching the component
export type ErrorConfig = {
  header?: string
  description?: string
}

export type LoadingConfig = {
  message?: string
}

export type NoDataConfig = {
  header?: string
  description?: string
}

export interface UseLoadingContingencyProps<T> {
  data: T[]
  isError: boolean
  isLoading: boolean
  isFetching: boolean
  isDataLoading?: boolean
  refetch: GridListRefetch<T>

  // New config objects (preferred)
  errorConfig?: ErrorConfig
  loadingConfig?: LoadingConfig
  noDataConfig?: NoDataConfig

  // Legacy individual props (deprecated but backward compatible)
  /** @deprecated Use errorConfig.header instead */
  errorHeader?: string
  /** @deprecated Use errorConfig.description instead */
  errorDescription?: string
  /** @deprecated Use noDataConfig.header instead */
  noDataHeader?: string
  /** @deprecated Use noDataConfig.description instead */
  noDataDescription?: string
  /** @deprecated Use loadingConfig.message instead */
  loadingMessage?: string
}

export function useLoadingContingency<T>({
  data,
  isLoading,
  isFetching,
  isError,
  refetch,

  // New config objects
  errorConfig,
  loadingConfig,
  noDataConfig,

  // Legacy props (backward compatibility)
  errorHeader,
  errorDescription,
  noDataHeader,
  noDataDescription,
  loadingMessage,

  // More options
  isDataLoading: _isDataLoading
}: UseLoadingContingencyProps<T>) {
  const t = useMemoizedTranslation()

  const hasError = !!isError
  const dataLength = useMemo(() => data.length, [data])
  const isDataLoading = useMemo(
    () => _isDataLoading ?? (data.length === 0 && (isFetching || isLoading)),
    [_isDataLoading, data, isFetching, isLoading]
  )
  const hasNoData = useMemo(() => data.length === 0, [data])

  // Merge new config with legacy props for backward compatibility
  const resolvedErrorConfig = useMemo(
    (): ErrorConfig => ({
      header: errorConfig?.header ?? errorHeader,
      description: errorConfig?.description ?? errorDescription
    }),
    [errorConfig, errorHeader, errorDescription]
  )

  const resolvedLoadingConfig = useMemo(
    (): LoadingConfig => ({
      message: loadingConfig?.message ?? loadingMessage
    }),
    [loadingConfig, loadingMessage]
  )

  const resolvedNoDataConfig = useMemo(
    (): NoDataConfig => ({
      header: noDataConfig?.header ?? noDataHeader,
      description: noDataConfig?.description ?? noDataDescription
    }),
    [noDataConfig, noDataHeader, noDataDescription]
  )

  const loadingMessageProps = useMemo(() => {
    return resolvedLoadingConfig.message ?? t('common.loading.label')
  }, [resolvedLoadingConfig.message, t])

  return {
    hasError,
    dataLength,
    hasNoData,
    isDataLoading,
    refetchData: refetch,
    loadingMessage: loadingMessageProps,

    // New config objects (preferred)
    errorConfig: resolvedErrorConfig,
    loadingConfig: resolvedLoadingConfig,
    noDataConfig: resolvedNoDataConfig
  }
}
