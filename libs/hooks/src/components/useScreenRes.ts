import { resolutions } from '@gc/constants'
import { useMediaQuery } from 'react-responsive'

// https://www.browserstack.com/guide/responsive-design-breakpoints

// Mobile Devices
// Extra Small Mobile (Portrait): 320px – 480px
// Small Mobile (Landscape): 481px – 600px
// Mobile (Portrait): 601px – 768px

// Tablets
// Small Tablets (Portrait): 601px – 768px
// Large Tablets (Landscape): 769px – 1024px

// Laptops and Small Desktops
// Small Desktops and Laptops: 1025px – 1280px

// Large Desktops
// Large Desktops and High-Resolution Screens: 1281px – 1440px

// Extra-Large Screens
// Extra-Large Desktops: 1441px and up

export const useScreenRes = (): number => {
  return [
    useMediaQuery({ maxWidth: 599 }), // 0 - small-mobile
    useMediaQuery({ minWidth: 600, maxWidth: 719 }), // 1 - mobile
    useMediaQuery({ minWidth: 720, maxWidth: 839 }), // 2 - tablet
    useMediaQuery({ minWidth: 840, maxWidth: 1023 }), // 3 - desktop
    useMediaQuery({ minWidth: 1024, maxWidth: 1439 }), // 4 - large-desktop
    useMediaQuery({ minWidth: 1440 }) // 5 - extra-large-desktop
  ].findIndex((item) => item)
}

export const useIsMobile = (): boolean => {
  return useScreenRes() <= resolutions.M1023
}

export const useIsSmallMobile = (): boolean => {
  return useScreenRes() <= resolutions.M719
}

export const useIsDesktop = (): boolean => {
  return !useIsMobile()
}

export const useIsTablet = (): boolean => {
  const res = useScreenRes()
  return res > resolutions.M719 && res <= resolutions.M1023
}
