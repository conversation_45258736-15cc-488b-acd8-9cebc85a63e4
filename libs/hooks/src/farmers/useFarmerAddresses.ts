/* eslint-disable @nx/enforce-module-boundaries */
import { useConfigDataQueries } from '@gc/redux-store'
import { FarmerAddressWithContact } from '@gc/types'

import { useGcPortalConfig } from '../useFasteStore'

/**
 * Retrieves a multiple farmer's addresses.
 * @param {string[]} [sourceIds] - The source IDs of the farmers.
 * @returns {Object} An object containing the data, error, loading states, and a refetch function.
 */

export function useFarmerAddresses(sourceIds: string[] = []) {
  const { useGetFarmerShippingAddressesQuery } = useConfigDataQueries()

  const shippingAddressConfig = useGcPortalConfig('shippingAddressConfig')
  const source = shippingAddressConfig?.source

  const {
    data = [],
    error,
    isLoading,
    isFetching,
    refetch
  } = useGetFarmerShippingAddressesQuery({ payload: { source, sourceIds } }, { skip: !sourceIds.length })

  return {
    data,
    error,
    isLoading,
    isFetching,
    refetch
  }
}

function filterFarmerAddressesByType(
  farmers: FarmerAddressWithContact[],
  addressType?: string
): FarmerAddressWithContact[] {
  if (!addressType) return farmers

  return farmers
    .map((farmer) => ({
      ...farmer,
      addresses: farmer.addresses.filter((addr) => addr.addressType === addressType)
    }))
    .filter((farmer) => farmer.addresses.length > 0)
}

/**
 * Retrieves a single farmer's address by type.
 * @param {string} sourceId - The source ID of the farmer.
 * @param {string} addressType - The type of address to retrieve (e.g., 'SHIPPING', 'MAILING').
 * @returns {Object} An object containing the filtered data, error, loading states, and a refetch function.
 */
export function useFarmerAddress(sourceId = '', addressType?: string) {
  const { data: addresses = [], error, isLoading, isFetching, refetch } = useFarmerAddresses([sourceId])

  const filteredData = filterFarmerAddressesByType(addresses, addressType)

  return {
    data: filteredData,
    error,
    isLoading,
    isFetching,
    refetch
  }
}
