/* eslint-disable @nx/enforce-module-boundaries */
import { useSelectedAccount } from '@gc/hooks'
import { useConfigDataQueries } from '@gc/redux-store'
import { skipToken } from '@reduxjs/toolkit/query'
import { isEmpty } from 'lodash'
import { useCallback, useMemo } from 'react'

export const useFarmerListData = () => {
  const { sapAccountId } = useSelectedAccount()
  const { useGetFarmersQuery, useGetPendingFarmersQuery } = useConfigDataQueries()
  const pendingFarmers = useGetPendingFarmersQuery(!isEmpty(sapAccountId) ? { sapId: sapAccountId } : skipToken)
  const farmers = useGetFarmersQuery(!isEmpty(sapAccountId) ? { sapId: sapAccountId } : skipToken)

  const { data, isError, isFetching, isLoading, isSuccess } = useMemo(() => {
    const isError = !!(pendingFarmers.error || farmers.error)
    const isFetching = pendingFarmers.isFetching || farmers.isFetching
    const isLoading = pendingFarmers.isLoading || farmers.isLoading

    return {
      data: {
        farmerDetails:
          isError || isLoading || isFetching
            ? []
            : [...(farmers.data?.farmerDetails || []), ...(pendingFarmers.data?.farmerDetails || [])],
        licensedGrowerTotals: undefined
      },
      isError,
      isFetching,
      isLoading,
      isSuccess: pendingFarmers.isSuccess && farmers.isSuccess
    }
  }, [pendingFarmers, farmers])

  const refetch = useCallback(() => {
    if (isFetching || isLoading) return
    if (pendingFarmers.error) pendingFarmers.refetch()
    if (farmers.error) farmers.refetch()
  }, [isFetching, isLoading, pendingFarmers, farmers])

  return { data, isError, isLoading: isFetching || isLoading, isSuccess, refetch }
}

export default useFarmerListData
