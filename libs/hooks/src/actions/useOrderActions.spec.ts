// Mock dependencies BEFORE importing the hook
import type { Consignment, OrderDetailsCBUS } from '@gc/types'
import { renderHook } from '@testing-library/react'

import { useDisableOrderAction } from './useOrderActions'

jest.mock('../useFasteStore', () => ({
  usePortalConfig: () => ({
    ordersModule: {
      disableEditStatuses: [
        'BEING_PROCESSED',
        'MODIFICATION_PENDING',
        'ORDER_MODIFIED_BY_ANOTHER_PROCESS',
        'BLOCKED',
        'CANCELLED'
      ],
      disableCancelStatuses: ['MODIFICATION_PENDING', 'BLOCKED', 'CANCELLED', 'CANCELLING', 'PARTIALLY_DELIVERED'],
      disableDuplicateStatuses: [
        'BEING_PROCESSED',
        'PROCESSING',
        'SUBMITTED_W_ERRORS',
        'CANCELLED',
        'BLOCKED',
        'CANCELLING',
        'MODIFICATION_PENDING'
      ]
    }
  }),
  useGcPortalConfig: () => ({
    orderConfig: {
      salesYear: 2025
    }
  })
}))
jest.mock('../common/useOrderStatus', () => ({
  useOrderStatus: () => ({
    checkForCancelOrderStatus: jest.fn()
  })
}))

describe('useDisableOrderAction', () => {
  it('should disable cancel if order status is in disableCancelStatuses', () => {
    const { result } = renderHook(() => useDisableOrderAction())
    const order: Partial<OrderDetailsCBUS> = { status: 'CANCELLED', billToParties: [], consignments: [] }
    expect(result.current.getIsCancelOrderDisabled(order as OrderDetailsCBUS)).toBe(true)
  })

  it('should disable cancel if any consignment is not cancelled', () => {
    const { result } = renderHook(() => useDisableOrderAction())
    const order: Partial<OrderDetailsCBUS> = {
      status: 'OPEN',
      billToParties: [],
      consignments: [{ status: 'CANCELLED' } as Consignment, { status: 'SHIPPED' } as Consignment]
    }
    expect(result.current.getIsCancelOrderDisabled(order as OrderDetailsCBUS)).toBe(true)
  })

  it('should enable cancel if all consignments are cancelled', () => {
    const { result } = renderHook(() => useDisableOrderAction())
    const order: Partial<OrderDetailsCBUS> = {
      status: 'OPEN',
      billToParties: [],
      consignments: [{ status: 'CANCELLED' } as Consignment, { status: 'CANCELLED' } as Consignment]
    }
    expect(result.current.getIsCancelOrderDisabled(order as OrderDetailsCBUS)).toBe(false)
  })

  it('should enable cancel if no consignments and status is not disabled', () => {
    const { result } = renderHook(() => useDisableOrderAction())
    const order: Partial<OrderDetailsCBUS> = {
      status: 'OPEN',
      billToParties: [],
      consignments: []
    }
    expect(result.current.getIsCancelOrderDisabled(order as OrderDetailsCBUS)).toBe(false)
  })

  it('should return true for non-OrderDetailsCBUS/ChannelOrder', () => {
    const { result } = renderHook(() => useDisableOrderAction())
    const order = { foo: 'bar' } as unknown
    expect(result.current.getIsCancelOrderDisabled(order as OrderDetailsCBUS)).toBe(true)
  })
})
