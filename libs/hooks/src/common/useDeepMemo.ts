// useDeepMemo.ts
import { useRef } from 'react'
import { default as ReactFastCompareIsEqual } from 'react-fast-compare'

/**
 * Deep‑comparison memoisation hook.
 *
 * @template T   The type of the value returned by `factory`.
 * @param factory   Function that creates the value to be memoised.
 * @param deps      Dependency list – typically an array of objects.
 * @returns        The memoised value of type `T`.
 *
 * The hook recomputes `factory` only when a **deep equality**
 * check (`react-fast-compare`) reports a change in `deps`.
 */
export function useDeepMemo<T>(
  factory: () => T,
  deps: readonly unknown[],
  comparator?: (a: readonly unknown[], b: readonly unknown[]) => boolean
): T {
  // `cache` lives for the whole component lifetime.
  const cache = useRef<{ deps: readonly unknown[]; value: T }>()
  const eq = comparator ?? ReactFastCompareIsEqual

  // If we already have a cached value and the deps are deep‑equal,
  // return the cached value.
  if (cache.current && eq(cache.current.deps, deps)) {
    return cache.current.value
  }

  // Otherwise compute a fresh value and store it together with the
  // current deps for the next render.
  const value = factory()
  cache.current = { deps, value }
  return value
}

/* -----------------------------------------------------------------
   If you ever need a custom comparator you can overload the hook:

   export function useDeepMemo<T>(
     factory: () => T,
     deps: readonly unknown[],
     comparator?: (a: readonly unknown[], b: readonly unknown[]) => boolean
   ): T;
   ----------------------------------------------------------------- */
