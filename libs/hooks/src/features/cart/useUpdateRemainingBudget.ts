import {
  getAppliedDiscount,
  getIsConvertQuoteToOrder,
  getRemainingDiscretionaryBudget,
  setRemainingBudget,
  useGlobalDispatch
} from '@gc/redux-store'
import { Cart, DiscretionaryBudget, RemainingBudget } from '@gc/types'
import { useCallback, useEffect } from 'react'
import { useSelector } from 'react-redux'

export const useUpdateRemainingBudget = (cart: Cart | undefined, budgets: DiscretionaryBudget[] | undefined) => {
  const dispatch = useGlobalDispatch()

  const existingBudgets = useSelector(getRemainingDiscretionaryBudget(cart?.code ?? ''))
  const isConvertQuoteToOrder = useSelector(getIsConvertQuoteToOrder)

  const setBudgets = useCallback(() => {
    if (!cart || !budgets) return

    const isQuote = cart?.cartType === 'QUOTE' || isConvertQuoteToOrder
    const updatedBudgets: RemainingBudget = { cartId: cart.code, budgets: {} }

    budgets.forEach((budget) => {
      const appliedDiscounts = getAppliedDiscount(budget, cart?.entries ?? [])

      const budgetKey = `${budget.crop}-${budget.programName}`
      const remainingBudget = Number(budget.remainingBudget ?? 0)

      updatedBudgets.budgets[budgetKey] = !isQuote ? remainingBudget + appliedDiscounts : remainingBudget
    })

    dispatch(setRemainingBudget(updatedBudgets))
  }, [budgets, cart, dispatch, isConvertQuoteToOrder])

  useEffect(() => {
    if (!existingBudgets && cart && budgets) {
      setBudgets()
    }
  }, [budgets, cart, existingBudgets, setBudgets])
}
