/* eslint-disable @nx/enforce-module-boundaries */
import { TRANSFER_OPERATION } from '@gc/constants'
import { useCartQueries } from '@gc/redux-store'
import { useCallback } from 'react'

export const useFetchPreShipCart = () => {
  const { useLazyGetPreShipCartQuery } = useCartQueries()
  const [getPreShipCart] = useLazyGetPreShipCartQuery()

  const fetchCart = useCallback(
    async ({
      fromOrderCode,
      isReconfirm = false,
      toOrderCode
    }: {
      fromOrderCode: string
      isReconfirm?: boolean
      toOrderCode?: string
    }) => {
      const getOperationType = () => {
        if (isReconfirm) return TRANSFER_OPERATION.reconfirm

        return toOrderCode ? TRANSFER_OPERATION.transfer : TRANSFER_OPERATION.transferToNew
      }

      return await getPreShipCart({
        operationType: getOperationType(),
        sourceOrderNumber: fromOrderCode,
        targetOrderNumber: toOrderCode
      })
    },
    [getPreShipCart]
  )

  return fetchCart
}
