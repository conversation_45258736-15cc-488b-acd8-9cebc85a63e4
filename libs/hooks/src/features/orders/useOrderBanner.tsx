import { Banner, Icon, TypoSubtitle } from '@element/react-components'
import { setContingency, useGlobalDispatch, useOrdersQueries } from '@gc/redux-store'
import { OrderDetailsCBUS, StockOrder } from '@gc/types'
import { useCallback, useEffect, useMemo, useState } from 'react'

import { useMemoizedTranslation, useOrderStatus } from '../../.'

type OrderData = {
  orderDetails?: OrderDetailsCBUS | StockOrder
  isLoading: boolean
  inEditMode: boolean
  refetch: () => void
  isStockOrder?: boolean
}

export const useOrderBanner = ({ inEditMode, isLoading, orderDetails, refetch, isStockOrder = false }: OrderData) => {
  const t = useMemoizedTranslation()
  const dispatch = useGlobalDispatch()

  const [showRefreshOrderBanner, setShowRefreshOrderBanner] = useState(false)
  const [showErrorBanner, setShowErrorBanner] = useState(false)
  const [showBanner, setShowBanner] = useState(true)
  const [retryingOrder, setRetryingOrder] = useState(false)
  const [orderStatus, setOrderStatus] = useState<string>()

  const ordersApi = useOrdersQueries()
  const { useRetryOrderSubmitMutation } = ordersApi
  const [retryOrderSubmit] = useRetryOrderSubmitMutation()
  const { getIsOrderProcessing, getIsOrderSubmittedWithError } = useOrderStatus()

  useEffect(() => {
    if (orderStatus !== orderDetails?.status && !retryingOrder && !isLoading) {
      setOrderStatus(orderDetails?.status ?? '')
      setShowBanner(true)
    }
  }, [isLoading, orderDetails?.status, orderStatus, retryingOrder])

  useEffect(() => {
    if (orderStatus && orderStatus !== 'RETRYING' && (!showErrorBanner || !showRefreshOrderBanner)) {
      setShowRefreshOrderBanner(getIsOrderProcessing(orderStatus) || orderStatus === 'CANCELLING')
      setShowErrorBanner(getIsOrderSubmittedWithError(orderStatus) && showBanner)
    }
  }, [
    getIsOrderProcessing,
    getIsOrderSubmittedWithError,
    orderStatus,
    showBanner,
    showErrorBanner,
    showRefreshOrderBanner
  ])

  const handleRetryOrderSubmit = useCallback(async () => {
    setRetryingOrder(true)
    setOrderStatus('RETRYING') // A FAKE UPDATE TO RE DISPLAY BANNER IN CASE ORDER STAYS IN SUBMITTED_W_ERROR!!
    setShowBanner(false)

    const res = await retryOrderSubmit({
      reqBody: {
        codes: [orderDetails?.code ?? '']
      },
      invalidateTags: !isStockOrder ? ['Orders'] : []
    })
    if (res?.error || res.data?.status?.toUpperCase() !== 'SUCCESS') {
      setShowBanner(true)
      dispatch(
        setContingency({
          code: 'ORDER_RE-SUBMIT_ERROR',
          displayType: 'dialog',
          dialogProps: {
            title: t('orders.order_submit_failed.label'),
            message: t('common.refresh_page_to_fix.description'),
            open: true,
            dismissButtonLabel: t('common.dismiss.label'),
            actionButtonProps: {
              label: t('common.try_again.label'),
              onAction: () => {
                handleRetryOrderSubmit()
                dispatch(setContingency())
              }
            }
          }
        })
      )
    } else {
      refetch()
    }
    setRetryingOrder(false)
  }, [retryOrderSubmit, orderDetails?.code, isStockOrder, dispatch, t, refetch])

  const bannerMessage = useMemo(() => {
    if (showRefreshOrderBanner) return t('orders.refresh_order_message.label')
    if (showErrorBanner) {
      return orderDetails?.orderErrorMessage ?? t('orders.submit_order_failed_message.description')
    }
  }, [showRefreshOrderBanner, t, showErrorBanner, orderDetails?.orderErrorMessage])

  const banner = useMemo(
    () => (
      <Banner
        primaryButtonLabel={t(showRefreshOrderBanner ? 'common.refresh.label' : 'common.try_again.label')}
        onPrimaryClicked={showRefreshOrderBanner ? refetch : handleRetryOrderSubmit}
        media={
          <Icon
            icon={showRefreshOrderBanner ? 'check_circle' : 'error'}
            variant={showRefreshOrderBanner ? 'green' : 'filled-danger'}
            className={showRefreshOrderBanner ? 'gc-icon-success' : 'gc-icon-danger-variant'}
          />
        }
      >
        <TypoSubtitle level={1}>{bannerMessage}</TypoSubtitle>
      </Banner>
    ),
    [bannerMessage, handleRetryOrderSubmit, refetch, showRefreshOrderBanner, t]
  )

  const bannerState = useMemo(
    () => ({
      orderBanner: (showRefreshOrderBanner || showErrorBanner) && !isLoading && !inEditMode ? banner : null,
      retryingOrder
    }),
    [banner, inEditMode, isLoading, showErrorBanner, showRefreshOrderBanner, retryingOrder]
  )

  return bannerState
}
