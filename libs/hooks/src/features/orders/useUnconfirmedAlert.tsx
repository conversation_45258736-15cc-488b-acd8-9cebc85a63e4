import { Alert } from '@gc/components'
import { getInEditMode } from '@gc/redux-store'
import { OrderDetailsCBUS, StockOrder } from '@gc/types'
import { getUnconfirmedProductCount, removeRejectedItems } from '@gc/utils'
import { useCallback, useMemo } from 'react'
import { useSelector } from 'react-redux'

import { useMemoizedTranslation } from '../../useMemoizedTranslations'
import { useModal } from '../../useModal'

export const useUnconfirmedAlert = (orderDetails: StockOrder | OrderDetailsCBUS | undefined, alertClass?: string) => {
  const t = useMemoizedTranslation()
  const inEditMode = useSelector(getInEditMode)
  const { openModal } = useModal()

  const { unconfirmedCount, nonExcludedUnconfirmedCount } = useMemo(
    () => getUnconfirmedProductCount(orderDetails?.entries || []),
    [orderDetails]
  )

  const reconfirmOrder = useCallback(() => {
    openModal({
      name: 'RECONFIRM_ORDER',
      props: {
        entries:
          orderDetails?.entries?.filter(
            (item) => (item.unconfirmedQuantity ?? 0) > 0 && item.product.canOrder !== false
          ) ?? [],
        orderCode: orderDetails?.code ?? '',
        usage: 'stockOrder'
      }
    })
  }, [openModal, orderDetails?.code, orderDetails?.entries])

  const resultAlert = useMemo(
    () => (
      <Alert
        className={alertClass ?? ''}
        type='warning'
        variant='tonal'
        title={t('common.unconfirmed_products.label')}
        description={`${unconfirmedCount} of ${
          removeRejectedItems(orderDetails?.entries ?? []).length
        } products has unconfirmed quantities`}
        actionButtonProps={
          !inEditMode && nonExcludedUnconfirmedCount > 0
            ? {
                label: t('order.reconfirm.label'),
                variant: 'text',
                onClick: reconfirmOrder
              }
            : {}
        }
      />
    ),
    [alertClass, inEditMode, nonExcludedUnconfirmedCount, orderDetails?.entries, reconfirmOrder, t, unconfirmedCount]
  )

  if (unconfirmedCount === 0) return null

  return resultAlert
}
