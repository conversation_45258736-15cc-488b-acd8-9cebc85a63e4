// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  extendedCart<PERSON>piSlice,
  setOrdersCurrentTab,
  useGlobalDispatch,
  useUpdateCartAttributesMutation
} from '@gc/redux-store'
import { useCallback } from 'react'

import { usePortal } from '../../useCheckPortalKey'
import { useGcPortalConfig, useSelectedAccount } from '../../useFasteStore'
import { useModal } from '../../useModal'
import { useSeedProSalesHierarchy } from '../../useSalesHierarchy'
import { useSetCurrentCart } from '../useCurrentCart'

const tabMapping: { [key: string]: number } = {
  ZAKE: 2,
  ZAKB1: 0
}

export const useCreateStockOrder = () => {
  const dispatch = useGlobalDispatch()
  const { isMyCropCentre } = usePortal()
  const [updateCartAttributes] = useUpdateCartAttributesMutation()
  const { stockOrderConfig } = useGcPortalConfig()
  const sapAccountId = useSelectedAccount().sapAccountId
  const setCurrentCart = useSetCurrentCart()
  const seedProSalesHierarchy = useSeedProSalesHierarchy()
  const { openModal } = useModal()

  const createFn = useCallback(
    async (docType = '') => {
      const tab = isMyCropCentre && tabMapping[docType] !== undefined ? tabMapping[docType] : 1
      dispatch(setOrdersCurrentTab(tab))
      setCurrentCart(true)
      dispatch(extendedCartApiSlice.endpoints.getCurrentCart.initiate('current', { forceRefetch: true })).then(
        (res) => {
          const cart = res.data
          if (!cart) return
          updateCartAttributes({
            cartId: cart?.code,
            attributes: {
              cartType: 'ORDER',
              distributionChannel: stockOrderConfig.distributionChannel,
              division: stockOrderConfig.division,
              documentType: isMyCropCentre ? docType : stockOrderConfig.documentType,
              grower: sapAccountId,
              agentSapId: sapAccountId,
              shipToParty: sapAccountId,
              salesOrg: stockOrderConfig.salesOrg,
              salesYear: stockOrderConfig.salesYear,
              salesOffice: seedProSalesHierarchy?.salesOffice,
              salesGroup: seedProSalesHierarchy?.salesGroup,
              salesDistrict: seedProSalesHierarchy?.salesDistrict,
              erpSystem: isMyCropCentre ? 'BC' : undefined,
              billToParties: [
                {
                  isPrimaryBillTo: true,
                  paymentTerm: stockOrderConfig.paymentTerm,
                  actualPaymentTerm: seedProSalesHierarchy?.paymentTermsCode ?? '',
                  percentage: 100,
                  sapAccountId: sapAccountId,
                  name: 'Bill To Party',
                  paymentTermDescription: stockOrderConfig.paymentTermDescription
                }
              ],
              requestedDeliveryDate: stockOrderConfig.requestedDeliveryDate
            }
          })
            .unwrap()
            .then(() => {
              openModal(
                {
                  name: 'SELECT_PRODUCTS',
                  props: { usage: isMyCropCentre && docType === 'ZAKE' ? 'issueOrder' : 'stockOrder' }
                },
                { resetPrevious: true }
              )
            })
        }
      )
    },
    [
      dispatch,
      isMyCropCentre,
      openModal,
      sapAccountId,
      seedProSalesHierarchy?.paymentTermsCode,
      seedProSalesHierarchy?.salesDistrict,
      seedProSalesHierarchy?.salesGroup,
      seedProSalesHierarchy?.salesOffice,
      setCurrentCart,
      stockOrderConfig.distributionChannel,
      stockOrderConfig.division,
      stockOrderConfig.documentType,
      stockOrderConfig.paymentTerm,
      stockOrderConfig.paymentTermDescription,
      stockOrderConfig.requestedDeliveryDate,
      stockOrderConfig.salesOrg,
      stockOrderConfig.salesYear,
      updateCartAttributes
    ]
  )

  return createFn
}
