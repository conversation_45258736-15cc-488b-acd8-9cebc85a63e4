// eslint-disable-next-line @nx/enforce-module-boundaries
import { extendedCartApiSlice, useGlobalDispatch, useUpdateCartAttributesMutation } from '@gc/redux-store'
import type { Cart } from '@gc/types'
import { getPortal } from '@gc/utils'
import { useCallback, useEffect, useState } from 'react'

import { useGcPortalConfig, useSelectedAccount } from '../../useFasteStore'
import { useSeedProSalesHierarchy } from '../../useSalesHierarchy'
import { useSetCurrentCart } from '../useCurrentCart'

interface StockTakeOrderProps {
  isCreateCart: boolean
  setIsCreateCart: (value: boolean) => void
}

export const useCreateStockTakeOrder = ({ isCreateCart = false, setIsCreateCart }: StockTakeOrderProps) => {
  const dispatch = useGlobalDispatch()
  const { isMyCropCentre } = getPortal()
  const [updateCartAttributes] = useUpdateCartAttributesMutation()
  const { stockOrderConfig } = useGcPortalConfig()
  const sapAccountId = useSelectedAccount().sapAccountId
  const setCurrentCart = useSetCurrentCart()
  const seedProSalesHierarchy = useSeedProSalesHierarchy()
  const [cartData, setCartData] = useState<Cart>({} as Cart)

  const createCart = useCallback(async () => {
    setCurrentCart(true)
    dispatch(extendedCartApiSlice.endpoints.getCurrentCart.initiate('current', { forceRefetch: true })).then((res) => {
      const cart = res.data
      if (!cart) return
      updateCartAttributes({
        cartId: cart?.code,
        attributes: {
          cartType: 'ORDER',
          distributionChannel: stockOrderConfig.distributionChannel,
          division: stockOrderConfig.division,
          documentType: '',
          grower: sapAccountId,
          agentSapId: sapAccountId,
          shipToParty: sapAccountId,
          salesOrg: stockOrderConfig.salesOrg,
          salesYear: stockOrderConfig.salesYear,
          salesOffice: seedProSalesHierarchy?.salesOffice,
          salesGroup: seedProSalesHierarchy?.salesGroup,
          salesDistrict: seedProSalesHierarchy?.salesDistrict,
          erpSystem: isMyCropCentre ? 'BC' : undefined,
          billToParties: [
            {
              isPrimaryBillTo: true,
              paymentTerm: stockOrderConfig.paymentTerm,
              actualPaymentTerm: seedProSalesHierarchy?.paymentTermsCode ?? '',
              percentage: 100,
              sapAccountId: sapAccountId,
              name: 'Bill To Party',
              paymentTermDescription: stockOrderConfig.paymentTermDescription
            }
          ],
          requestedDeliveryDate: stockOrderConfig.requestedDeliveryDate
        }
      })
      setCartData(cart)
    })
  }, [
    dispatch,
    isMyCropCentre,
    sapAccountId,
    seedProSalesHierarchy,
    setCurrentCart,
    stockOrderConfig,
    updateCartAttributes
  ])

  useEffect(() => {
    if (isCreateCart) {
      createCart()
      setIsCreateCart(false)
    }
  }, [createCart, isCreateCart, setIsCreateCart])

  return { createCartFn: createCart, data: cartData }
}
