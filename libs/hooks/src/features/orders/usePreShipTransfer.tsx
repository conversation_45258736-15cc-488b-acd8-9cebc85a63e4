/* eslint-disable @nx/enforce-module-boundaries */
import { TypoLink } from '@element/react-components'
import { useIsDesktop, useMemoizedTranslation, useModal } from '@gc/hooks'
import {
  getPreShipTransfer,
  resetPreShipTransfer,
  setCartId,
  setContingency,
  useCartQueries,
  useGlobalDispatch
} from '@gc/redux-store'
import { AlertProps, Usage } from '@gc/types'
import { fasteRoute, getOrderNoFromCode } from '@gc/utils'
import _ from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'

export const usePreShipTransferSuccessAlert: (usage: Usage, productCount?: number) => AlertProps | undefined = (
  usage: Usage,
  productCount = -1
) => {
  const dispatch = useGlobalDispatch()
  const params = useParams()
  const t = useMemoizedTranslation()
  const preShipTransfer = useSelector(getPreShipTransfer)
  const [resultAlert, setResultAlert] = useState<{ alert: AlertProps | undefined; code: string }>()
  const showAlert = useMemo(
    () => !params?.code?.startsWith('BC') || usage === 'stockOrder' || params?.code === resultAlert?.code,
    [params?.code, resultAlert?.code, usage]
  )

  useEffect(() => {
    if (showAlert && !!resultAlert) {
      dispatch(resetPreShipTransfer())
    }
  }, [dispatch, resultAlert, showAlert])

  useEffect(() => {
    if (!_.isEmpty(preShipTransfer) && !preShipTransfer.isSuccess) {
      setResultAlert(undefined)
    }
  }, [preShipTransfer])

  useEffect(() => {
    if (
      !preShipTransfer?.isSuccess ||
      (!!resultAlert && (resultAlert.code === preShipTransfer.toOrderCode || !preShipTransfer?.toOrderCode))
    )
      return

    const { fromOrderCode, transferEntries, toOrderCode } = preShipTransfer
    let alertTitle = '',
      alertDescription
    const transferEntriesCount = !toOrderCode && productCount !== -1 ? productCount : (transferEntries?.length ?? 0)
    const productCountText = `${transferEntriesCount} ${t('common.product.label', { count: transferEntriesCount })}`
    const getDescKey = (key: string) => {
      if (transferEntriesCount === 1) return key
      return key.replace('product', 'products')
    }
    const orderCodeLink = (
      <TypoLink
        onClick={() => {
          setContingency()
          fasteRoute(`/orders/${fromOrderCode}`)
        }}
      >
        {`${t('orders.order.label')} ${getOrderNoFromCode(fromOrderCode)}`}
      </TypoLink>
    )

    if (usage === 'stockOrder') {
      alertTitle = t('orders.product_transfer_success_stock_order.label')
      alertDescription = (
        <span>
          {`${productCountText} ${t(getDescKey('orders.product_transfer_success_stock_order.description'))}`}{' '}
          {orderCodeLink}.
        </span>
      )
    } else {
      alertDescription = (
        <span>
          {`${productCountText} ${t(getDescKey('orders.product_transfer_to_order.description'))}`} {orderCodeLink}.
        </span>
      )
      if (!preShipTransfer.toOrderCode) {
        alertTitle = t('orders.product_transfer_to_new_order.label')
      } else {
        alertTitle = `${t('orders.order.label')} ${getOrderNoFromCode(toOrderCode)} ${t('orders.product_transfer_to_existing_order.label')}`
      }
    }

    setResultAlert({
      alert: {
        type: 'success',
        title: alertTitle,
        description: alertDescription,
        variant: 'tonal'
      },
      code: toOrderCode ?? 'newOrder'
    })
  }, [preShipTransfer, productCount, resultAlert, t, usage])

  return useMemo(() => (showAlert ? resultAlert?.alert : undefined), [resultAlert, showAlert])
}

const useDiscardTransfer = () => {
  const dispatch = useGlobalDispatch()

  const { closeModal } = useModal()
  const { useDeleteCartMutation } = useCartQueries()

  const [deleteCart] = useDeleteCartMutation()
  const discardFn = useCallback(
    (cartId?: string) => {
      if (cartId) {
        deleteCart({ cartId, skipCartRefetch: true })
        dispatch(setCartId(''))
      }
      dispatch(resetPreShipTransfer())
      closeModal()
    },
    [closeModal, deleteCart, dispatch]
  )

  return discardFn
}

export const useOpenAbandonModal = () => {
  const t = useMemoizedTranslation()
  const isDesktop = useIsDesktop()
  const { openModal } = useModal()
  const discardTransfer = useDiscardTransfer()

  const triggerFn = useCallback(
    (cartId?: string) => {
      openModal({
        name: 'ABANDON_DELIVERY',
        props: {
          modalSize: isDesktop ? 'xlarge' : undefined,
          messageProps: {
            header: t('orders.discard_transfer_products.label'),
            description: t('common.action_cannot_undone.label')
          },
          discardButtonProps: {
            label: t('common.discard.label'),
            variant: 'text',
            themeColor: 'danger',
            onClick: () => discardTransfer(cartId)
          }
        }
      })
    },
    [discardTransfer, isDesktop, openModal, t]
  )
  return triggerFn
}

export const useSetTransferFailContingency = () => {
  const t = useMemoizedTranslation()
  const dispatch = useGlobalDispatch()
  const { closeModal } = useModal()
  const discardTransfer = useDiscardTransfer()

  const triggerFn = useCallback(
    (retryFn: () => void, cartId?: string) => {
      closeModal()
      dispatch(
        setContingency({
          code: 'CANCELLED_DELIVERY',
          displayType: 'dialog',
          onDismissAction: () => discardTransfer(cartId),
          dialogProps: {
            title: t('orders.transfer_products_failed.label'),
            message: t('common.try_again_to_fix.description'),
            open: true,
            dismissButtonLabel: t('common.cancel_transfer.label'),
            actionButtonProps: {
              label: t('common.try_again.label'),
              onAction: retryFn
            }
          }
        })
      )
    },
    [closeModal, discardTransfer, dispatch, t]
  )

  return triggerFn
}
