import { LocationState } from '@gc/types'
import { isEqual } from 'lodash'
import { useEffect, useMemo, useRef, useState } from 'react'

const SESSION_KEY = 'location'

const DEFAULT_LOCATION = {
  baseAccount: undefined,
  selectedAccount: undefined,
  includeSubAccounts: false,
  selectedSapAccountIds: []
} as Partial<LocationState>

const useMemoizedLocation = (initialLocation: LocationState): LocationState => {
  const [location, setLocation] = useState(initialLocation)
  const previousLocationRef = useRef(initialLocation)

  useEffect(() => {
    if (!isEqual(location, previousLocationRef.current)) {
      previousLocationRef.current = location
    }
  }, [location])

  const memoizedLocation = useMemo(() => location, [JSON.stringify(location)])

  useEffect(() => {
    const handleLocationChange = () => {
      const item = window.sessionStorage.getItem(SESSION_KEY)
      if (item) {
        const newLocation = JSON.parse(item)
        if (JSON.stringify(newLocation) !== JSON.stringify(location)) {
          setLocation(newLocation)
        }
      }
    }

    window.addEventListener(SESSION_KEY, handleLocationChange)
    window.addEventListener('storage', handleLocationChange)

    return () => {
      window.removeEventListener(SESSION_KEY, handleLocationChange)
      window.removeEventListener('storage', handleLocationChange)
    }
  }, [location])

  return memoizedLocation
}

export const useLocation = (): [LocationState, (location: LocationState) => void] => {
  const initialLocation = (() => {
    const item = window.sessionStorage.getItem(SESSION_KEY)
    return item ? JSON.parse(item) : DEFAULT_LOCATION
  })()

  const location = useMemoizedLocation(initialLocation)

  const updateLocation = (newLocation: LocationState) => {
    const newValue = JSON.stringify(newLocation)
    window.sessionStorage.setItem(SESSION_KEY, newValue)
    window.dispatchEvent(new Event(SESSION_KEY))
  }

  return [location, updateLocation]
}
