import { useLocation } from '@gc/hooks'
import { useAdminSelector } from '@gc/redux-store'
import { getAppConfig } from '@gc/shared/config'
import {
  AppSessionData,
  DomainDef,
  DomainDefGcPortalConfig,
  FasteRoute,
  Locale,
  SelectedAccount,
  User
} from '@gc/types'
import { useFasteStore as useFasteStoreCore } from '@monsantoit/faste-lite-react'
import { isEmpty } from 'lodash'
import { useCallback } from 'react'

import { usePortal } from './useCheckPortalKey'

export function useFetchFasteStore<T>(key: string) {
  const store = useFasteStoreCore()
  return store.fetch(key) as T
}

export function useRouteSettings() {
  return useFetchFasteStore<FasteRoute>('fasteRoute')?.settings
}

export function usePortalConfig() {
  return useFetchFasteStore<DomainDef>('domainDef')
}

export function useGcPortalConfig(): DomainDefGcPortalConfig
export function useGcPortalConfig<T extends keyof DomainDefGcPortalConfig>(key: T): DomainDefGcPortalConfig[T]
export function useGcPortalConfig<T extends keyof DomainDefGcPortalConfig>(key?: T) {
  const config = usePortalConfig()?.gcPortalConfig ?? {}
  return key ? config[key] : config
}

type FarmersModuleType = ReturnType<typeof usePortalConfig>['farmersModule']

export function useFarmersModuleConfig(): FarmersModuleType {
  const appConfig = getAppConfig() as Partial<{ farmersModule: FarmersModuleType }>
  const portalConfig = usePortalConfig().farmersModule
  if (appConfig?.farmersModule && Object.keys(appConfig.farmersModule).length > 0) {
    return appConfig.farmersModule
  } else {
    return portalConfig
  }
}

export function useNbOrdersModuleConfig() {
  return usePortalConfig().ordersModule
}

export function useDashboardModuleConfig() {
  return usePortalConfig().dashboardModule
}

export function useInventoryModuleConfig() {
  return usePortalConfig().inventoryModule
}

export function useLocale(): Locale {
  const admin = useAdminSelector()
  const internalPortalLanguage = useFetchFasteStore<DomainDef>('domainDef').internalPortalLanguage
  const locale = useFetchFasteStore<Locale>('locale')
  if (admin && internalPortalLanguage !== undefined) {
    const [language, country] = internalPortalLanguage.split('-')
    return { code: internalPortalLanguage, country, language }
  } else {
    return locale
  }
}

export function useSelectedAccount(): SelectedAccount {
  const selectedAccountFromFasteStore = useFetchFasteStore<SelectedAccount>('selectedAccount')
  const { isMyCrop, isMyCropV2 } = usePortal()
  if (isMyCrop || isMyCropV2) {
    return selectedAccountFromFasteStore
  }

  let selectedAccountObj = sessionStorage.getItem('selectedAccount')
  const location = sessionStorage.getItem('location')
  if (!selectedAccountFromFasteStore && !location && !selectedAccountObj) {
    return { sapAccountId: '' } as SelectedAccount
  }
  if (selectedAccountFromFasteStore && !location && !selectedAccountObj) {
    return selectedAccountFromFasteStore
  }
  if (!selectedAccountObj) {
    const locationObj = location ? JSON.parse(location) : {}
    selectedAccountObj = locationObj.selectedAccounts
  }
  return (selectedAccountObj ? JSON.parse(selectedAccountObj) : {}) as SelectedAccount
}

export function useUser() {
  const admin = useAdminSelector()
  const userFromFasteStore = useFetchFasteStore<User>('user')
  if (admin) {
    const userObj = sessionStorage.getItem('user')
    return userObj ? JSON.parse(userObj) : {}
  }
  return userFromFasteStore
}

export function useUserEntitlements(): string[] | undefined {
  const admin = useAdminSelector()
  const { entitlements } = useUser() || {}
  if (admin) {
    const entitlementsObj = sessionStorage.getItem('entitlements')
    const internalPortalEntitlements = entitlementsObj ? JSON.parse(entitlementsObj) : {}
    return internalPortalEntitlements ? Object.values(internalPortalEntitlements) : undefined
  }
  const sapAccountId = Object.keys(entitlements || {})[0]
  return (entitlements as Record<string, string[]>)[sapAccountId]
}

export function useAppSessionData() {
  return useFetchFasteStore<AppSessionData>('appSessionData')
}

export function useUpdateFasteStore() {
  const fasteStore = useFasteStoreCore()
  return [fasteStore.update]
}

export function useUpsertAppSessionData() {
  const [updateFaste] = useUpdateFasteStore()
  const appSessionData = useAppSessionData()

  const upsert = useCallback(
    (key: string, value: object) => {
      updateFaste('appSessionData', {
        ...(appSessionData || {}),
        [`${key}`]: {
          ...(appSessionData?.[key] || {}),
          ...value
        }
      })
    },
    [appSessionData, updateFaste]
  )
  return [upsert]
}

export function useAppSession() {
  const appSessionData = useAppSessionData()
  const [upsertAppSessionData] = useUpsertAppSessionData()
  return [appSessionData, upsertAppSessionData] as const
}

type Keys =
  | 'seedProducts'
  | 'orders'
  | 'quotes'
  | 'products'
  | 'seedProductDeliveries'
  | 'seedProductFarmers'
  | 'seedProductInventories'

export function usePageSize<T extends Keys>(key: T, defaultValue = 50): DomainDefGcPortalConfig['pageSize'][T] {
  const portalConfig = useGcPortalConfig()
  return portalConfig?.pageSize?.[key] ?? defaultValue
}

export function useCheckForSelectedAccount(refetch: () => void) {
  const { sapAccountId } = useSelectedAccount()
  const [location] = useLocation()

  if (
    location.selectedAccount &&
    !isEmpty(location.selectedAccount.sapAccountId) &&
    location.selectedAccount.sapAccountId !== sapAccountId
  ) {
    refetch()
  }
}
