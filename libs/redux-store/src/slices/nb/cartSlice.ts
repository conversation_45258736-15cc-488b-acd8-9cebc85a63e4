/* eslint-disable @nx/enforce-module-boundaries */
import {
  cartFromOrderMutation,
  cartToOrderMutation,
  deleteCartEntryMutation,
  deleteCartMutation,
  updateCartEntriesMutation,
  updateCartEntryMutation
} from '@gc/rtk-queries'
import { InferQueryArg, InferResultType } from '@gc/types'
import { createSlice } from '@reduxjs/toolkit'

import { ccApi } from '../../rtk-apis'

export type NbCartState = {
  cartId: string
}

const initialState: NbCartState = {
  cartId: ''
}

export const nbCartSlice = createSlice({
  name: 'nbCart',
  initialState,
  reducers: {
    setNbCartId: (state, action) => {
      state.cartId = action.payload
    }
  }
})

export const extendedNbCartApiSlice = ccApi.injectEndpoints({
  overrideExisting: true,
  endpoints: (builder) => ({
    createNbCart: cartFromOrderMutation(builder),
    updateNbCartEntry: updateCartEntryMutation(builder),
    updateNbCartEntries: updateCartEntriesMutation(builder),
    deleteNbCart: deleteCartMutation(builder),
    deleteNbCartEntry: deleteCartEntryMutation(builder),
    saveNbCart: cartToOrderMutation(builder)
  })
})

type Endpoints = (typeof extendedNbCartApiSlice)['endpoints']
type Q<T extends keyof Endpoints> = InferQueryArg<Endpoints, T>
type R<T extends keyof Endpoints> = InferResultType<Endpoints, T>

export const useNbCartQueries = () => ({
  ...extendedNbCartApiSlice
})
