/* eslint-disable @nx/enforce-module-boundaries */
import {
  BaseQueryOptions,
  cancelOrderMutation,
  cartToOrderMutation,
  getAllOrdersForDeliveryQuery,
  getAllOrdersQuery,
  getAllOrdersQueryV2,
  getOrderDetailsQuery,
  getOrderForModifyDeliveryQuery,
  getSeedGrowthOrdersQuery,
  OrdersRequestBody,
  retryOrderSubmitMutation
} from '@gc/rtk-queries'
import { Cart, CCApiSlice, ChannelOrder, ExtendedCCApiSlice, OrderDetailsCBUS, StockOrder } from '@gc/types'
import { QueryOptions } from '@gc/utils'
import { createSlice } from '@reduxjs/toolkit'

import { GlobalAppDispatch } from '../types'
import { extendedCartApiSlice } from './cartSlice'

export type OrdersState = {
  ordersCurrentTab?: number
  preShipTransfer: {
    transferType?: 'order' | 'stockOrder'
    fromOrderCode?: string
    toOrderCode?: string
    transferEntries?: OrderDetailsCBUS['entries']
    isSuccess?: boolean
  }
}

const initialState: OrdersState = {
  preShipTransfer: {},
  ordersCurrentTab: window.history?.state?.tab ?? 0
}

export const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    setOrdersCurrentTab: (state, action: { payload: number }) => {
      state.ordersCurrentTab = action.payload
    },
    setPreShipTransferType: (state, action: { payload: 'order' | 'stockOrder' }) => {
      state.preShipTransfer.transferType = action.payload
    },
    setPreShipTransferFromOrderCode: (state, action: { payload: string | undefined }) => {
      state.preShipTransfer.fromOrderCode = action.payload
    },
    setPreShipTransferToOrderCode: (state, action: { payload: string | undefined }) => {
      state.preShipTransfer.toOrderCode = action.payload
    },
    setPreShipTransferEntries: (state, action: { payload: OrderDetailsCBUS['entries'] }) => {
      state.preShipTransfer.transferEntries = action.payload
    },
    setPreShipTransferIsSuccess: (state, action: { payload: boolean }) => {
      if (state.preShipTransfer) {
        state.preShipTransfer.isSuccess = action.payload
      }
    },
    resetPreShipTransfer: (state) => {
      state.preShipTransfer = {}
    }
  }
})

export const resetPreShipTransfer = () => (dispatch: GlobalAppDispatch) => {
  dispatch(ordersSlice.actions.resetPreShipTransfer())

  // This is workaround to reset cart without fetching a new one!!
  dispatch(extendedCartApiSlice.util.updateQueryData('getCurrentCart', 'current', () => null as unknown as Cart))
}

export const {
  setOrdersCurrentTab,
  setPreShipTransferType,
  setPreShipTransferFromOrderCode,
  setPreShipTransferToOrderCode,
  setPreShipTransferEntries,
  setPreShipTransferIsSuccess
} = ordersSlice.actions

export const getIsProductTransferOrder = (state: { orders: OrdersState }) =>
  !!state.orders.preShipTransfer?.transferType

type ExtendedApiSlice<T extends ChannelOrder | StockOrder> = ExtendedCCApiSlice<{
  getAllOrders: ReturnType<typeof getAllOrdersQuery<T>>
  getAllOrdersV2: ReturnType<typeof getAllOrdersQueryV2<ChannelOrder>>
  getSeedGrowthOrders: ReturnType<typeof getSeedGrowthOrdersQuery>
  getOrderDetails: ReturnType<typeof getOrderDetailsQuery>
  cartToOrder: ReturnType<typeof cartToOrderMutation>
  getAllOrdersForDelivery: ReturnType<typeof getAllOrdersForDeliveryQuery>
  getOrderForModifyDelivery: ReturnType<typeof getOrderForModifyDeliveryQuery>
  cancelOrder: ReturnType<typeof cancelOrderMutation>
  retryOrderSubmit: ReturnType<typeof retryOrderSubmitMutation>
}>

let extendedApiSlice: ExtendedApiSlice<ChannelOrder | StockOrder>

export const injectOrdersEndpoints = (apiSlice: CCApiSlice) => {
  extendedApiSlice = apiSlice.injectEndpoints({
    overrideExisting: true,
    endpoints: (builder) => ({
      getAllOrders: getAllOrdersQuery(builder),
      getAllOrdersV2: getAllOrdersQueryV2(builder),
      getSeedGrowthOrders: getSeedGrowthOrdersQuery(builder),
      getOrderDetails: getOrderDetailsQuery(builder),
      cartToOrder: cartToOrderMutation(builder),
      getAllOrdersForDelivery: getAllOrdersForDeliveryQuery(builder),
      cancelOrder: cancelOrderMutation(builder),
      getOrderForModifyDelivery: getOrderForModifyDeliveryQuery(builder),
      retryOrderSubmit: retryOrderSubmitMutation(builder)
    })
  })
}

export const useOrdersQueries = <T extends ChannelOrder | StockOrder>() => {
  if (extendedApiSlice) {
    return extendedApiSlice as ExtendedApiSlice<T>
  } else {
    throw new Error('Make sure injectOrdersEndpoints was called in store.')
  }
}

export const useGetAllOrdersV2 = (
  payload: BaseQueryOptions<ChannelOrder, OrdersRequestBody>,
  options?: QueryOptions
) => {
  const { useGetAllOrdersV2Query } = useOrdersQueries<ChannelOrder>()
  return useGetAllOrdersV2Query(payload, options)
}
