import { ThirdPartyFinancingFormData } from '@gc/types'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { useSelector } from 'react-redux'

import { GlobalRootState } from '../types'

export type ThirdPartyFinanceState = {
  // State is organized by orderId to support multiple orders
  [orderId: string]: {
    pendingAdditions: ThirdPartyFinancingFormData[]
    pendingRemovals: string[]
  }
}

const initialState: ThirdPartyFinanceState = {}

const checkState = (state: ThirdPartyFinanceState, orderId: string) => {
  if (!state[orderId]) {
    state[orderId] = {
      pendingAdditions: [],
      pendingRemovals: []
    }
  }
}

export const thirdPartyFinanceSlice = createSlice({
  name: 'thirdPartyFinance',
  initialState,
  reducers: {
    // Initialize state for a specific order
    initializeOrderState: (state, action: PayloadAction<{ orderId: string }>) => {
      const { orderId } = action.payload
      if (!state[orderId]) {
        state[orderId] = {
          pendingAdditions: [],
          pendingRemovals: []
        }
      }
    },

    // Add a pending form to the additions array
    addPendingForm: (state, action: PayloadAction<{ orderId: string; data: ThirdPartyFinancingFormData }>) => {
      const { orderId, data } = action.payload
      checkState(state, orderId)

      state[orderId].pendingAdditions.push(data)
    },

    // Add a term ID to the pending removals array
    addPendingRemoval: (state, action: PayloadAction<{ orderId: string; termId: string }>) => {
      const { orderId, termId } = action.payload
      checkState(state, orderId)

      // Avoid duplicate removals
      if (!state[orderId].pendingRemovals.includes(termId)) {
        state[orderId].pendingRemovals.push(termId)
      }
    },

    // Remove a specific pending form by index
    removePendingForm: (state, action: PayloadAction<{ orderId: string; index: number }>) => {
      const { orderId, index } = action.payload
      checkState(state, orderId)

      state[orderId].pendingAdditions.splice(index, 1)
    },

    // Remove a specific pending removal by termId
    removePendingRemoval: (state, action: PayloadAction<{ orderId: string; termId: string }>) => {
      const { orderId, termId } = action.payload
      if (state[orderId]) {
        state[orderId].pendingRemovals = state[orderId].pendingRemovals.filter((id) => id !== termId)
      }
    },

    // Clear all pending changes for a specific order
    clearPendingChanges: (state, action: PayloadAction<{ orderId: string }>) => {
      const { orderId } = action.payload
      if (state[orderId]) {
        state[orderId].pendingAdditions = []
        state[orderId].pendingRemovals = []
      }
    },

    // Remove all state for a specific order
    removeOrderState: (state, action: PayloadAction<{ orderId: string }>) => {
      const { orderId } = action.payload
      delete state[orderId]
    },

    // Clear all state (useful for logout/reset scenarios)
    clearAllState: () => {
      return initialState
    }
  }
})

// Export actions
export const {
  initializeOrderState,
  addPendingForm,
  addPendingRemoval,
  removePendingForm,
  removePendingRemoval,
  clearPendingChanges,
  removeOrderState,
  clearAllState
} = thirdPartyFinanceSlice.actions

// Export reducer
export const thirdPartyFinanceReducer = thirdPartyFinanceSlice.reducer

// Selectors
export const selectThirdPartyFinanceState = (state: GlobalRootState) => state.thirdPartyFinance

export const selectOrderPendingState = (state: GlobalRootState, orderId: string) =>
  state.thirdPartyFinance[orderId] || { pendingAdditions: [], pendingRemovals: [] }

export const selectPendingAdditions = (state: GlobalRootState, orderId: string) =>
  state.thirdPartyFinance[orderId]?.pendingAdditions || []

export const selectPendingRemovals = (state: GlobalRootState, orderId: string) =>
  state.thirdPartyFinance[orderId]?.pendingRemovals || []

export const selectHasPendingChanges = (state: GlobalRootState, orderId: string) => {
  const orderState = state.thirdPartyFinance[orderId]
  return orderState ? orderState.pendingAdditions.length > 0 || orderState.pendingRemovals.length > 0 : false
}

// Typed selector hooks
export const useThirdPartyFinanceSelectors = (orderId: string) => {
  const pendingAdditions = useSelector((state: GlobalRootState) => selectPendingAdditions(state, orderId))
  const pendingRemovals = useSelector((state: GlobalRootState) => selectPendingRemovals(state, orderId))
  const hasPendingChanges = useSelector((state: GlobalRootState) => selectHasPendingChanges(state, orderId))
  const orderState = useSelector((state: GlobalRootState) => selectOrderPendingState(state, orderId))

  return {
    pendingAdditions,
    pendingRemovals,
    hasPendingChanges,
    orderState
  }
}

// Note: thirdPartyFinanceSlice is already exported above via the createSlice call
