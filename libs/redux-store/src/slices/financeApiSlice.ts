/* eslint-disable @nx/enforce-module-boundaries */
import {
  getThirdPartyFinancingQuery,
  removeThirdPartyFinancingMutation,
  submitBatchThirdPartyFinancingMutation,
  submitThirdPartyFinancingMutation
} from '@gc/rtk-queries'
import { ExtendedMiddlewareApiSlice, MiddlewareApiSlice } from '@gc/types'

type ExtendedApiSlice = ExtendedMiddlewareApiSlice<{
  submitThirdPartyFinancing: ReturnType<typeof submitThirdPartyFinancingMutation>
  getThirdPartyFinancing: ReturnType<typeof getThirdPartyFinancingQuery>
  removeThirdPartyFinancing: ReturnType<typeof removeThirdPartyFinancingMutation>
  submitBatchThirdPartyFinancing: ReturnType<typeof submitBatchThirdPartyFinancingMutation>
}>

let extendedApiSlice: ExtendedApiSlice
export const injectMiddlewareFinanceEndpoints = (apiSlice: MiddlewareApiSlice) => {
  extendedApiSlice = apiSlice.injectEndpoints({
    overrideExisting: true,
    endpoints: (builder) => ({
      getThirdPartyFinancing: getThirdPartyFinancingQuery(builder),
      removeThirdPartyFinancing: removeThirdPartyFinancingMutation(builder),
      submitThirdPartyFinancing: submitThirdPartyFinancingMutation(builder),
      submitBatchThirdPartyFinancing: submitBatchThirdPartyFinancingMutation(builder)
    })
  })
}

export const useFinanceQueries = () => {
  if (extendedApiSlice) {
    return extendedApiSlice
  } else {
    throw new Error('Make sure injectMiddlewareFinanceEndpoints was called in store.')
  }
}

export const useThirdPartyQueries = () => {
  const {
    useGetThirdPartyFinancingQuery,
    useRemoveThirdPartyFinancingMutation,
    useSubmitThirdPartyFinancingMutation,
    useSubmitBatchThirdPartyFinancingMutation
  } = useFinanceQueries()
  return {
    useGetThirdPartyFinancingQuery,
    useRemoveThirdPartyFinancingMutation,
    useSubmitThirdPartyFinancingMutation,
    useSubmitBatchThirdPartyFinancingMutation
  }
}

export const useThirdPartyFinancingMutations = () => {
  const {
    useSubmitThirdPartyFinancingMutation,
    useRemoveThirdPartyFinancingMutation,
    useSubmitBatchThirdPartyFinancingMutation
  } = useFinanceQueries()
  return {
    useSubmitThirdPartyFinancingMutation,
    useRemoveThirdPartyFinancingMutation,
    useSubmitBatchThirdPartyFinancingMutation
  }
}

export const useGetThirdPartyFinancingQuery = (orderId: string, { skip }: { skip?: boolean } = {}) => {
  const { useGetThirdPartyFinancingQuery } = useFinanceQueries()
  return useGetThirdPartyFinancingQuery({ orderId }, { skip })
}

export const useSubmitThirdPartyFinancingMutation = () => {
  const { useSubmitThirdPartyFinancingMutation } = useFinanceQueries()
  return useSubmitThirdPartyFinancingMutation()
}

export const useSubmitBatchThirdPartyFinancingMutation = () => {
  const { useSubmitBatchThirdPartyFinancingMutation } = useFinanceQueries()
  return useSubmitBatchThirdPartyFinancingMutation()
}

export const useRemoveThirdPartyFinancingMutation = () => {
  const { useRemoveThirdPartyFinancingMutation } = useFinanceQueries()
  return useRemoveThirdPartyFinancingMutation()
}
