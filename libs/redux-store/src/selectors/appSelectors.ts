import { useSelector } from 'react-redux'

import { GlobalRootState } from '../types'

export const useAdminSelector = () => useSelector((state: GlobalRootState) => state.app.admin)

export const getIsRefreshOrders = (state: GlobalRootState) => state.app.isRefreshOrders
export const getContingency = (state: GlobalRootState) => state.app.contingency

export const usePreviousModals = () => useSelector((state: GlobalRootState) => state.app.previousModals)
