{"name": "api-client", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/api/client/src", "projectType": "library", "tags": ["scope:shared", "type:utils"], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/api/client/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/api/client/jest.config.ts", "passWithNoTests": true}}}}