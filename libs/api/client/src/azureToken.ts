import { fetchStore } from '@gc/utils'
import axios from 'axios'

const CACHE_DURATION_MS = 55 * 60 * 1000 // 55 minutes

type CachedToken = {
  token: string
  expiresAt: number
}

type AccessTokenResponse = {
  access_token: string
}

let tokenCache: CachedToken | null = null
let tokenPromise: Promise<string> | null = null

export const fetchAzureToken = async (gigyaToken: string) => {
  if (tokenCache && tokenCache.expiresAt > Date.now()) {
    return tokenCache.token
  }

  // Return existing promise if there's already a request in flight
  if (tokenPromise) {
    return tokenPromise
  }

  tokenPromise = axios
    .get<AccessTokenResponse>(fetchStore('domainDef')?.azureTokenUrl, {
      headers: {
        Authorization: `Bearer ${gigyaToken}`
      }
    })
    .then((response) => {
      tokenCache = {
        token: response.data.access_token,
        expiresAt: Date.now() + CACHE_DURATION_MS
      }
      tokenPromise = null
      return tokenCache.token
    })

  return tokenPromise
}

export const clearAzureCache = () => {
  tokenCache = null
  tokenPromise = null
}
