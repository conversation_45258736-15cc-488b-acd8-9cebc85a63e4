import { PHOENIX_PERMISSION_URL } from '@gc/shared/env'

import { getAxiosClient } from './axiosClient'

export const fetchPermissions = async () => {
  try {
    const axiosClient = getAxiosClient()
    const response = await axiosClient.get(PHOENIX_PERMISSION_URL)

    if (!response.data || typeof response.data.channelarrow !== 'object') {
      throw new Error('Invalid response format from permissions API')
    }

    return response.data.channelarrow
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    if (error.message === 'Invalid response format from permissions API') {
      throw error
    }
    console.error('Error fetching permissions:', error)
    throw new Error('Failed to fetch permissions. Please try again later.')
  }
}
