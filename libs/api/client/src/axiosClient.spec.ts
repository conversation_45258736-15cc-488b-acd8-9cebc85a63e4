import Axios from 'axios'
import axiosRetry from 'axios-retry'

import { getAxiosClient, initAxiosClient } from './axiosClient'
import * as tokenProvider from './tokenProvider'

jest.mock('axios')
jest.mock('axios-retry')
jest.mock('./tokenProvider')
jest.mock('@gc/utils', () => ({
  ...jest.requireActual('@gc/utils'),
  fetchStore: (key: string) => ({ domainDef: {}, locale: 'en-US' })[key]
}))

describe('initAxiosClient', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(Axios.create as jest.Mock).mockReturnValue({
      defaults: {},
      interceptors: { request: { use: jest.fn() } }
    })
  })

  it('should initialize axios instance and set locale', () => {
    initAxiosClient()
    expect(Axios.create).toHaveBeenCalled()
    const instance = getAxiosClient()
    expect(instance.defaults.data.locale).toBe('en-US')
  })

  it('should call primeCache and set up retry', () => {
    const primeCacheSpy = jest.spyOn(tokenProvider, 'primeCache')
    initAxiosClient()
    expect(primeCacheSpy).toHaveBeenCalled()
    expect(axiosRetry).toHaveBeenCalled()
  })

  it('should set up request interceptor', () => {
    const useMock = jest.fn()
    ;(Axios.create as jest.Mock).mockReturnValue({
      defaults: {},
      interceptors: { request: { use: useMock } }
    })
    initAxiosClient()
    expect(useMock).toHaveBeenCalled()
  })
})
