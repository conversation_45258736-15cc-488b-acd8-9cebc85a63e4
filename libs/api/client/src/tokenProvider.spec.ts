import { clearCache, getToken, primeCache, setTokenProvider, TokenType } from './tokenProvider'

describe('tokenProvider', () => {
  let mockGetToken: jest.Mock
  let mockPrimeCache: jest.Mock
  let mockClearCache: jest.Mock

  beforeEach(() => {
    mockGetToken = jest.fn()
    mockPrimeCache = jest.fn()
    mockClearCache = jest.fn()
    setTokenProvider({
      getToken: mockGetToken,
      primeCache: mockPrimeCache,
      clearCache: mockClearCache
    })
  })

  afterEach(() => {
    jest.resetAllMocks()
  })

  it('calls getToken with default (Azure) when no argument is passed', async () => {
    mockGetToken.mockResolvedValue('azure-token')
    const token = await getToken()
    expect(mockGetToken).toHaveBeenCalledWith(undefined)
    expect(token).toBe('azure-token')
  })

  it('calls getToken with TokenType.Gigya', async () => {
    mockGetToken.mockResolvedValue('gigya-token')
    const token = await getToken(TokenType.Gigya)
    expect(mockGetToken).toHaveBeenCalledWith(TokenType.Gigya)
    expect(token).toBe('gigya-token')
  })

  it('calls primeCache', () => {
    primeCache()
    expect(mockPrimeCache).toHaveBeenCalled()
  })

  it('calls clearCache', () => {
    clearCache()
    expect(mockClearCache).toHaveBeenCalled()
  })
})
