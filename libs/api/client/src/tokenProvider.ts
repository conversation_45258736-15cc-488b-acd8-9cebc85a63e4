import { fetchStore } from '@gc/utils'

import { clearAzureCache, fetchAzureToken } from './azureToken'

export enum TokenType {
  Azure = 'azure',
  Gigya = 'gigya'
}

export type TokenProvider = {
  primeCache?: () => void
  clearCache?: () => void
  getToken: (tokenType?: TokenType) => Promise<string>
}

const defaultTokenProvider: TokenProvider = {
  primeCache: () => {
    getToken()
  },
  clearCache: () => {
    clearAzureCache()
  },
  getToken: async (tokenType: TokenType = TokenType.Azure) => {
    try {
      switch (tokenType) {
        case TokenType.Azure:
          return fetchAzureToken(fetchStore('gigyaToken'))
        case TokenType.Gigya:
          return fetchStore('gigyaToken')
        default:
          throw new Error(`Unknown token type: ${tokenType}`)
      }
    } catch (err) {
      console.error(`Error fetching ${tokenType} token:`, err)
      return Promise.reject(new Error(`Cannot fetch ${tokenType} token`))
    }
  }
}

let tokenProvider = defaultTokenProvider

export const setTokenProvider = (_tokenProvider: TokenProvider) => {
  tokenProvider = _tokenProvider
}

export const primeCache = () => {
  tokenProvider.primeCache?.()
}

export const clearCache = () => {
  tokenProvider.clearCache?.()
}

export const getToken = (tokenType?: TokenType) => {
  return tokenProvider.getToken(tokenType)
}
