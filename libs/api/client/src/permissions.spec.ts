import { PHOENIX_PERMISSION_URL } from '@gc/shared/env'

import { getAxiosClient } from './axiosClient'
import { fetchPermissions } from './permissions'

jest.mock('./axiosClient')

describe('permissions', () => {
  const mockAxiosGet = jest.fn()
  const mockGetAxiosClient = getAxiosClient as jest.Mock

  beforeEach(() => {
    jest.clearAllMocks()
    mockGetAxiosClient.mockReturnValue({
      get: mockAxiosGet
    })
  })

  it('should fetch and return permissions successfully', async () => {
    const mockPermissions = {
      somePermission: true,
      anotherPermission: false
    }
    mockAxiosGet.mockResolvedValueOnce({
      data: {
        channelarrow: mockPermissions
      }
    })

    const result = await fetchPermissions()

    expect(mockGetAxiosClient).toHaveBeenCalled()
    expect(mockAxiosGet).toHaveBeenCalledWith(PHOENIX_PERMISSION_URL)
    expect(result).toEqual(mockPermissions)
  })

  it('should throw error when response format is invalid', async () => {
    mockAxiosGet.mockResolvedValueOnce({
      data: {
        // Missing channelarrow property
      }
    })

    await expect(fetchPermissions()).rejects.toThrow('Invalid response format from permissions API')
    expect(mockGetAxiosClient).toHaveBeenCalled()
    expect(mockAxiosGet).toHaveBeenCalledWith(PHOENIX_PERMISSION_URL)
  })

  it('should throw error when response data is null', async () => {
    mockAxiosGet.mockResolvedValueOnce({
      data: null
    })

    await expect(fetchPermissions()).rejects.toThrow('Invalid response format from permissions API')
    expect(mockGetAxiosClient).toHaveBeenCalled()
    expect(mockAxiosGet).toHaveBeenCalledWith(PHOENIX_PERMISSION_URL)
  })

  it('should handle network errors gracefully', async () => {
    mockAxiosGet.mockRejectedValueOnce(new Error('Network error'))

    await expect(fetchPermissions()).rejects.toThrow('Failed to fetch permissions. Please try again later.')
    expect(mockGetAxiosClient).toHaveBeenCalled()
    expect(mockAxiosGet).toHaveBeenCalledWith(PHOENIX_PERMISSION_URL)
  })
})
