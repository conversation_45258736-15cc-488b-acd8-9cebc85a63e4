import { Pagination } from '@gc/types'

// Generic Pagination Types
export interface BasePaginationParams {
  currentPage?: number
  pageSize?: number
  pageLimit?: number
  totalCount?: number
}

// Generic Request Body Base
export interface BaseRequestBody {
  currentPage?: number
  pageSize?: number
}

// Generic Query Options
export interface BaseQueryOptions<TData, TRequestBody extends BaseRequestBody = BaseRequestBody> {
  isMobile?: boolean
  fetchAllPages?: boolean
  reqBody?: TRequestBody
  params?: BasePaginationParams
  updatePartialData?: (data: TData[]) => void
  transformResponse?: (data: TData[], ...args: unknown[]) => TData[]
}

// Account/Agent Related Types
export interface AccountFilterParams {
  agents?: string[]
  soldToAccounts?: string[]
  salesYears?: string[]
}

// Common Document Types
export interface DocumentFilterParams {
  documentTypes?: string[]
  statusCodes?: string[]
}

// Generic List Response
export interface ListResponse<T> {
  items: T[]
  pagination: Pagination
  filters?: Record<string, unknown>
  sorts?: Array<{ field: string; direction: 'asc' | 'desc' }>
}

// Standard API Response Structures
export interface OrdersResponse<T> {
  orders: T[]
  pagination: Pagination
}

export interface ConsignmentsResponse<T> {
  consignments: T[]
  pagination: Pagination
}

export interface InventoryResponse<T> {
  items: T[]
  pagination: Pagination
}

export interface ProductsResponse<T> {
  products: T[]
  pagination: Pagination
}

// Union type for all possible paginated response structures
export type PaginatedApiResponse<T> =
  | OrdersResponse<T>
  | ConsignmentsResponse<T>
  | InventoryResponse<T>
  | ProductsResponse<T>
  | ListResponse<T>

// Cache Configuration
export interface CacheConfig {
  keepUnusedDataFor?: number
  serializeQueryArgs?: (args: { queryArgs: unknown }) => string
}

// Specific Request Body Types (extend BaseRequestBody)
export interface OrdersRequestBody extends BaseRequestBody, AccountFilterParams, DocumentFilterParams {}

export interface ConsignmentsRequestBody extends BaseRequestBody, AccountFilterParams, DocumentFilterParams {
  consignmentType?: string
  growers?: string[]
}

export interface InventoryRequestBody extends BaseRequestBody, AccountFilterParams {
  lob?: string
  seedYear?: string
  fromLocation?: string
}

// Mobile/Desktop Field Configuration
export interface FieldConfiguration {
  MOBILE: string
  DESKTOP: string
}

// =============================================================================
// FUTURE USE TYPES - Reserved for upcoming features
// =============================================================================

// Generic API Response Wrapper - For standardized API responses
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  errors?: string[]
}

// Generic Error Response - For standardized error handling
export interface ErrorResponse {
  status: number
  message: string
  details?: Record<string, unknown>
  timestamp: string
}

// Generic Transform Function Types - For data transformation pipelines
export type DataTransformer<TInput, TOutput> = (input: TInput) => TOutput
export type ArrayTransformer<TInput, TOutput> = (input: TInput[]) => TOutput[]

// Generic Query Hook Options - For React Query integration
export interface UseQueryOptions<TData> {
  skip?: boolean
  pollingInterval?: number
  refetchOnMountOrArgChange?: boolean | number
  refetchOnFocus?: boolean
  refetchOnReconnect?: boolean
  selectFromResult?: (result: { data?: TData; error?: unknown; isLoading: boolean }) => unknown
}

// Generic Sort Configuration - For table sorting features
export interface SortOption {
  field: string
  direction: 'asc' | 'desc'
  label: string
}

// =============================================================================
// MUTATION-SPECIFIC TYPES
// =============================================================================

// =============================================================================
// TYPE GUARDS AND UTILITIES
// =============================================================================

// Type guard functions for response type checking
export function isOrdersResponse<T>(response: PaginatedApiResponse<T>): response is OrdersResponse<T> {
  return 'orders' in response
}

export function isConsignmentsResponse<T>(response: PaginatedApiResponse<T>): response is ConsignmentsResponse<T> {
  return 'consignments' in response
}

export function isInventoryResponse<T>(response: PaginatedApiResponse<T>): response is InventoryResponse<T> {
  return 'items' in response && !('products' in response)
}

export function isProductsResponse<T>(response: PaginatedApiResponse<T>): response is ProductsResponse<T> {
  return 'products' in response
}

export function isListResponse<T>(response: PaginatedApiResponse<T>): response is ListResponse<T> {
  return 'items' in response && !('products' in response) && !('orders' in response) && !('consignments' in response)
}

// Helper function to extract data from any paginated response
export function extractDataFromResponse<T>(response: PaginatedApiResponse<T>): T[] {
  if (isOrdersResponse(response)) {
    return response.orders
  }
  if (isConsignmentsResponse(response)) {
    return response.consignments
  }
  if (isProductsResponse(response)) {
    return response.products
  }
  if (isInventoryResponse(response) || isListResponse(response)) {
    return response.items
  }
  return []
}
