import { TokenType } from '@gc/api/client'
import {
  PaginationParams,
  ReportTypeConfiguration,
  UserReport,
  UserReportSubmissionData,
  UserReportUpdateData
} from '@gc/types'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

import { CACHE_CONFIGS } from './query-utils'

export const getReportTypeConfigurationsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<ReportTypeConfiguration[], void>({
    query: () => ({
      url: `/reporting/reportTypeConfigurations`,
      method: 'GET',
      headers: {
        Accept: 'application/json'
      }
    }),
    serializeQueryArgs: () => 'report-type-configs',
    providesTags: () => [{ type: 'ReportTypeConfiguration', id: 'LIST' }],
    ...CACHE_CONFIGS.ONE_HOUR
  })
}

// Get Report Type Configuration by ID
export const getReportTypeConfigurationQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<ReportTypeConfiguration, string>({
    query: (reportTypeId: string) => {
      return {
        url: `/reporting/reportTypeConfigurations/${reportTypeId}`,
        method: 'GET',
        headers: {
          Accept: 'application/json'
        }
      }
    },
    serializeQueryArgs: ({ queryArgs }) => `report-type-config-${queryArgs}`,
    providesTags: (_result, _error, reportTypeId) => [{ type: 'ReportTypeConfiguration', id: reportTypeId }],
    ...CACHE_CONFIGS.ONE_HOUR
  })
}

// Get User Reports List
export const getUserReportsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<UserReport[], { userId: string; params?: PaginationParams }>({
    query: ({ userId }) => {
      return {
        url: `/reporting/reports/${userId}`,
        method: 'GET',
        headers: {
          Accept: 'application/json'
        }
      }
    },
    serializeQueryArgs: ({ queryArgs }) => {
      if (!queryArgs) return 'user-reports'
      const { userId, params } = queryArgs
      return `user-reports-${JSON.stringify({ userId, params })}`
    },
    providesTags: (result) =>
      result
        ? [
            ...result.map(({ reportId }) => ({ type: 'UserReport' as const, id: reportId })),
            { type: 'UserReport', id: 'LIST' }
          ]
        : [{ type: 'UserReport', id: 'LIST' }],
    ...CACHE_CONFIGS.FIVE_MINUTES
  })
}

// Get Single User Report by ID
export const getUserReportQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<UserReport, { userId: string; reportId: string }>({
    query: ({ userId, reportId }) => {
      return {
        url: `/reporting/reports/${userId}/${reportId}`,
        method: 'GET',
        headers: {
          Accept: 'application/json'
        }
      }
    },
    serializeQueryArgs: ({ queryArgs }) => `user-report-${JSON.stringify(queryArgs)}`,
    providesTags: (result, error, { reportId }) => [{ type: 'UserReport', id: reportId }],
    ...CACHE_CONFIGS.FIVE_MINUTES
  })
}

// Create User Report
export const createUserReportMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.mutation<UserReport, UserReportSubmissionData>({
    query: (reportData) => ({
      url: `/reporting/reports`,
      method: 'POST',
      data: reportData
    }),
    extraOptions: { tokenType: TokenType.Gigya },
    invalidatesTags: [{ type: 'UserReport', id: 'LIST' }],
    ...CACHE_CONFIGS.FIVE_MINUTES
  })
}

// Update User Report
export const updateUserReportMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.mutation<UserReport, UserReportUpdateData>({
    query: ({ reportId, ...reportData }) => ({
      url: `/reporting/reports/${reportId}`,
      method: 'PUT',
      data: reportData
    }),
    extraOptions: { tokenType: TokenType.Gigya },
    invalidatesTags: (result, error, { reportId }) => [
      { type: 'UserReport', id: reportId },
      { type: 'UserReport', id: 'LIST' }
    ],
    ...CACHE_CONFIGS.FIVE_MINUTES
  })
}

// Delete User Report
export const deleteUserReportMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.mutation<{ success: boolean }, string>({
    query: (reportId) => ({
      url: `/reporting/reports/${reportId}`,
      method: 'DELETE'
    }),
    extraOptions: { tokenType: TokenType.Gigya },
    invalidatesTags: (result, error, reportId) => [
      { type: 'UserReport', id: reportId },
      { type: 'UserReport', id: 'LIST' }
    ],
    ...CACHE_CONFIGS.FIVE_MINUTES
  })
}
