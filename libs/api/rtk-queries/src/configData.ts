import { TokenType } from '@gc/api/client'
import { GC_MIDDLEWARE_API } from '@gc/shared/env'
import {
  AccountSalesHierarchy,
  Farmer,
  FarmerAddressWithContact,
  FarmerSearch,
  ModifyFarmerRequest,
  NewFarmer,
  PaymentTerm,
  PendingFarmer,
  ToStorageLocation
} from '@gc/types'
import { getOneDCECountry, getParams } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

export const getStorageLocationsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<
    ToStorageLocation[],
    {
      warehouse?: string
      b2bUnit?: string
      locationType?: string
    }
  >({
    query: ({ warehouse, b2bUnit, locationType = 'SELLABLE,HUB' }) => ({
      url: `/warehouses/locations${getParams({ b2bUnit, locationType, warehouse }, { isMobile: false, fields: { MOBILE: getOneDCECountry(), DESKTOP: getOneDCECountry() } })}`
    }),
    keepUnusedDataFor: 3600,
    transformResponse: (response: { storageLocations: ToStorageLocation[] }) => response?.storageLocations
  })
}

export const getPaymentTermsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<PaymentTerm[], void>({
    query: () => ({
      url: '/codes?entity=PaymentTerm&erpSystem=BC&fields=FULL&visible=true'
    }),
    transformResponse: (response: { codes: PaymentTerm[] }) => response?.codes
  })
}

export const getFarmersQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<{ farmerDetails: Farmer[] }, { sapId: string }>({
    query: ({ sapId }) => ({
      url: `${GC_MIDDLEWARE_API}/hierarchy/growers/us/${sapId}`
    }),
    extraOptions: { tokenType: TokenType.Gigya },
    transformResponse: (response: Farmer[]) => {
      return {
        farmerDetails: response.filter((grower: Farmer) => grower.sourceId)
      }
    }
  })
}

export const getPendingFarmersQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<{ farmerDetails: Farmer[] }, { sapId: string }>({
    query: ({ sapId }) => ({
      url: `/datafeeds/get-pending-growers`,
      method: 'POST',
      data: { agentId: sapId }
    }),
    transformResponse: ({ pendingGrowerList: response }: { pendingGrowerList: PendingFarmer[] }) => {
      const transformAddress = (address: PendingFarmer['address']) => {
        const addr = Array.isArray(address) ? address[0] : (address ?? {})

        return {
          addressType: 'SHIPPING',
          address1Text: addr.line1 ?? '',
          address2Text: addr.line2 ?? '',
          cityTown: addr.city?.toUpperCase() ?? '',
          countyDivision: { code: addr.county ?? '', name: addr.county ?? '' },
          stateProvinceCode: addr.region ?? '',
          postalCode: addr.postalcode ?? '',
          poBox: addr?.poBox ?? '',
          primary: true,
          countryCode: addr.country ?? ''
        }
      }

      const transformPoBoxAddress = (address: PendingFarmer['poBox']) => ({
        addressType: 'MAILING',
        primary: true,
        address1Text: '',
        address2Text: '',
        poBox: address?.poBox ?? '',
        cityTown: address?.city?.toUpperCase() ?? '',
        countyDivision: { code: address?.county ?? '', name: address?.county ?? '' },
        stateProvinceCode: address?.region ?? '',
        postalCode: address?.postalcode ?? '',
        countryCode: address?.country ?? ''
      })

      const farmerDetails =
        response?.map((farmer) => ({
          firstName: farmer.firstName ?? '',
          lastName: farmer.lastName ?? '',
          name: farmer.businessName ?? '',
          sourceId: farmer.sapAccountId ?? '',
          partyStatus: farmer.status ?? '',
          address: [transformAddress(farmer.address), ...(farmer.poBox ? [transformPoBoxAddress(farmer.poBox)] : [])],
          contactInfo: [
            { email: farmer.email ?? '', mobileNumber: farmer.phone ?? '', phoneNumber: farmer?.secondaryPhone ?? '' }
          ],
          relationshipStatus: '',
          sapSalesAreas: [],
          cyOrder: false,
          licenseStatus: '',
          pricingZones: [],
          contracts: [],
          mdgAction: ''
        })) || []

      return { farmerDetails }
    },
    providesTags: ['PendingFarmers']
  })
}

export const getSalesHierarchyQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<
    AccountSalesHierarchy,
    { sapId: string; source: string; division: string; salesOrg: string; distributionChannel: string }
  >({
    query: ({ sapId, source, division, salesOrg, distributionChannel }) => ({
      url: `${GC_MIDDLEWARE_API}/ship-to-sales-hierarchy?source=${source}&sourceId=${sapId}&division=${division}&salesOrg=${salesOrg}&distributionChannel=${distributionChannel}`
    }),
    extraOptions: { tokenType: TokenType.Gigya },
    transformResponse: (response: AccountSalesHierarchy) => response as AccountSalesHierarchy,
    keepUnusedDataFor: 3600
  })
}

export const getFarmerShippingAddressesQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<FarmerAddressWithContact[], { payload: { source: string; sourceIds: string[] } }>({
    query: ({ payload }) => ({
      url: `${GC_MIDDLEWARE_API}/agency/addresses`,
      method: 'POST',
      data: payload
    }),
    extraOptions: { tokenType: TokenType.Gigya },
    keepUnusedDataFor: Number.POSITIVE_INFINITY
  })
}

export const getFarmerSearchQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<FarmerSearch[], { farmerSearchData: { name: string; city: string; state: string } }>({
    query: ({ farmerSearchData }) => ({
      url: `${GC_MIDDLEWARE_API}/agency/searchFarmers`,
      method: 'POST',
      data: farmerSearchData
    }),
    extraOptions: { tokenType: TokenType.Gigya },
    transformResponse: (response: FarmerSearch[]) => {
      if (!response || !response.length) return []
      return response.map(({ source, sourceId, name, partyStatus, addresses, sapSalesAreas, pricingZones }) => ({
        source,
        sourceId,
        name,
        partyStatus,
        addresses,
        sapSalesAreas,
        pricingZones
      }))
    }
  })
}

export const getPendingFarmerSearchQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<FarmerSearch[], { farmerSearchData: { businessName: string; city: string; state: string } }>({
    query: ({ farmerSearchData }) => ({
      url: `/datafeeds/get-all-pending-growers`,
      method: 'POST',
      data: farmerSearchData
    }),
    transformResponse: ({ pendingGrowerList }: { pendingGrowerList: PendingFarmer[] }) => {
      if (!pendingGrowerList) return []
      return pendingGrowerList.map(({ sapAccountId, businessName, status, address }) => ({
        sourceId: sapAccountId,
        name: businessName,
        partyStatus: status,
        addresses: [
          {
            address1Text: address?.line1 ?? '',
            cityTown: address?.city ?? '',
            stateProvinceCode: address?.region ?? '',
            postalCode: address?.postalcode ?? '',
            primary: true,
            countryCode: address?.country ?? '',
            countyDivision: { code: '', name: '' }
          }
        ],
        sapSalesAreas: [],
        pricingZones: []
      }))
    }
  })
}

export const addFarmerMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<{ status: string }, { farmerData: NewFarmer }>({
    query: ({ farmerData }) => ({
      url: `datafeeds/createb2bunitforpendinggrower`,
      method: 'POST',
      data: farmerData
    }),
    transformResponse: (response: { status: string }) => response,
    invalidatesTags: ['PendingFarmers']
  })
}

export const createFarmerLeadMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<{ status: string }, ModifyFarmerRequest>({
    query: (farmer) => ({
      url: `${GC_MIDDLEWARE_API}/agency/leads`,
      method: 'POST',
      data: farmer as ModifyFarmerRequest
    }),
    transformResponse: (response: { status: string }) => response
  })
}
