import { getAxiosClient } from '@gc/api/client'
import { ccFieldsChannelInventory } from '@gc/constants'
import { COMMERCE_CLOUD_API } from '@gc/shared/env'
import {
  ApiFields,
  InventoryProduct,
  Pagination,
  PaginationParams,
  SeedProduct,
  SeedProductDelivery,
  SeedProductFarmerOrder,
  SeedProductInventory
} from '@gc/types'
import { getParams, getUserPrefix, range } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'
import { noop } from 'es-toolkit'

type InventoryType = 'consignments' | 'orders' | 'overview' | 'inventories' | ''

const getParamsStr = (
  paramsObj: object = {},
  isMobile = false,
  fields: ApiFields = ccFieldsChannelInventory
): string => {
  return getParams(paramsObj, { isMobile, fields })
}
// Types for the request parameters
export interface InventoryOverviewReqBody {
  lob: string
  agents: string[]
  seedYear: string
  pageSize?: number
  currentPage?: number
}

export interface InventoryOverviewResponse<T> {
  inventoryProducts: T[]
  pagination: Pagination
}

export type GetInventoryOverviewOptions<T> = {
  isMobile?: boolean
  fetchAllPages?: boolean
  reqBody?: InventoryOverviewReqBody
  params?: PaginationParams
  updatePartialInventory?: (data: T[]) => void
}

const getInventoryUrl = () => `${COMMERCE_CLOUD_API}/cbus${getUserPrefix()}/inventory`

function fetchInventoryOverview<T>({
  currentPage = 0,
  payloadReqBody
}: {
  currentPage?: number
  params?: object
  isMobile?: boolean
  payloadReqBody?: object
}) {
  const axiosClient = getAxiosClient()
  const url = `${getInventoryUrl()}/overview?fields=ONEDCE_CBUS`
  const reqBody = { ...payloadReqBody, currentPage }
  return axiosClient.post<T>(url, reqBody)
}

export const getInventoryOverviewQuery = <T extends InventoryProduct>(
  builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>
) => {
  return builder.query<T[], GetInventoryOverviewOptions<T> | void>({
    queryFn: async (payload) => {
      const { params, isMobile, updatePartialInventory = noop, reqBody: payloadReqBody = {} } = payload || {}

      const fetchInventoryFn = (currentPage = 0) =>
        fetchInventoryOverview<InventoryOverviewResponse<T>>({ currentPage, params, isMobile, payloadReqBody })

      try {
        const { data } = await fetchInventoryFn()
        const inventoryProducts = data.inventoryProducts ?? []
        updatePartialInventory(inventoryProducts)

        if (data.pagination) {
          const { totalPages, page } = data.pagination
          if (totalPages - 1 > page) {
            const pageRange = range(page + 1, totalPages)

            // Generate an array of promises based on the remaining pages and run the api calls in parallel
            const totalResults = await Promise.allSettled(pageRange.map((page) => fetchInventoryFn(page)))

            // Loop over results, combining inventory data and filtering undefined values
            const batchInventoryProducts: T[] = []
            for (const inventoryResults of totalResults) {
              if (inventoryResults.status === 'fulfilled') {
                batchInventoryProducts.push(...inventoryResults.value.data.inventoryProducts)
              } else {
                throw new Error(inventoryResults.reason)
              }
            }
            inventoryProducts.push(...batchInventoryProducts)
            if (updatePartialInventory) {
              updatePartialInventory(batchInventoryProducts)
            }
          }
        }
        return { data: inventoryProducts }
      } catch (error) {
        console.error('error', error)
        return { error }
      }
    },
    keepUnusedDataFor: 3600,
    serializeQueryArgs: ({ queryArgs }) => {
      if (!queryArgs) return 'inventory'

      const { reqBody, params, isMobile } = queryArgs
      return `inventory-${JSON.stringify({
        isMobile,
        reqBody,
        pagination: params
      })}`
    },
    providesTags: ['InventoryOverview']
  })
}

export interface SeedProductDetailsReqBody {
  productCode: string
  lob: string
  agents: string[]
  seedYear: string
  pageSize?: number
  currentPage?: number
}

export type GetSeedProductDetailsDataOptions<T> = {
  isMobile?: boolean
  fetchAllPages?: boolean
  reqBody?: SeedProductDetailsReqBody
  params?: PaginationParams
  updatePartialData?: (data: T[]) => void
  transformResponse?: (consignments: T[], ...args: unknown[]) => T[] // Transform data before returning
}

function fetchSeedProductDetails<T>({
  currentPage = 0,
  payloadReqBody,
  type,
  params,
  isMobile
}: {
  currentPage?: number
  payloadReqBody?: SeedProductDetailsReqBody
  params?: PaginationParams
  isMobile?: boolean
  type: InventoryType
}) {
  const axiosClient = getAxiosClient()
  const suffix = type === '' ? '' : `/${type}`
  const url = `${getInventoryUrl()}/${payloadReqBody?.productCode}${suffix}${getParamsStr({ ...params }, isMobile)}`
  const reqBody = { ...payloadReqBody, currentPage }
  return axiosClient.post<T>(url, reqBody)
}

export type SeedProductDetailsDataResponse<T> = {
  consignments: T[]
  orders: T[]
  inventories: T[]
  pagination: Pagination
}

export const getSeedProductConsignmentsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return getSeedProductDetails<SeedProductDelivery>(builder, { type: 'consignments' })
}

export const getSeedProductFarmerOrdersQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return getSeedProductDetails<SeedProductFarmerOrder>(builder, { type: 'orders' })
}

export const getSeedProductInventoriesQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return getSeedProductDetails<SeedProductInventory>(builder, { type: 'overview' })
}
const getData = <T>(data: SeedProductDetailsDataResponse<T>, options: { type: InventoryType }) => {
  switch (options.type) {
    case 'consignments':
      return data.consignments
    case 'orders':
      return data.orders
    default:
      return data.inventories
  }
}

const getSeedProductDetails = <T>(
  builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>,
  options: { type: InventoryType }
) => {
  return builder.query<T[], GetSeedProductDetailsDataOptions<T> | void>({
    queryFn: async (payload) => {
      const { updatePartialData = noop, transformResponse, reqBody, params, isMobile } = payload || {}
      const transformResponseFn = transformResponse ?? ((items: T[]) => items)
      const fetchFn = (currentPage = 0) =>
        fetchSeedProductDetails<SeedProductDetailsDataResponse<T>>({
          currentPage,
          payloadReqBody: reqBody,
          params,
          isMobile,
          type: options.type
        })

      try {
        const { data } = await fetchFn()
        const items: T[] = getData(data, options) ?? []
        if (updatePartialData && items.length > 0) {
          updatePartialData(transformResponseFn(items))
        }

        if (data.pagination) {
          const { totalPages, page } = data.pagination
          if (totalPages - 1 > page) {
            const pageRange = range(page + 1, totalPages)

            // Generate an array of promises based on the remaining pages and run the api calls in parallel
            const totalResults = await Promise.allSettled(pageRange.map((page) => fetchFn(page)))

            // Loop over results, combining consignments data and filtering undefined values
            for (const result of totalResults) {
              if (result.status === 'fulfilled') {
                items.push(...getData(result.value.data, options))
              } else {
                throw new Error(result.reason)
              }
            }
          }
        }
        return { data: transformResponseFn(items) }
      } catch (error) {
        console.error('error', error)
        return { error }
      }
    }
  })
}

export type GetSeedProductDetailsOptions<T> = {
  isMobile?: boolean
  fetchAllPages?: boolean
  reqBody?: SeedProductDetailsReqBody
  params?: PaginationParams
  updatePartialData?: (data: T) => void
  transformResponse?: (data: T, ...args: unknown[]) => T // Transform data before returning
}
export const getSeedProductDetailsQuery = <T extends SeedProduct>(
  builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>
) => {
  return builder.query<T, GetSeedProductDetailsOptions<T> | void>({
    queryFn: async (payload) => {
      const { updatePartialData = noop, transformResponse, reqBody, params, isMobile } = payload || {}
      const transformResponseFn = transformResponse ?? ((data: T) => data)
      const fetchFn = (currentPage = 0) =>
        fetchSeedProductDetails<T>({
          currentPage,
          payloadReqBody: reqBody,
          params,
          isMobile,
          type: ''
        })

      try {
        const { data } = await fetchFn()
        if (updatePartialData && data) {
          updatePartialData(transformResponseFn(data))
        }
        return { data }
      } catch (error) {
        console.error('error', error)
        return { error }
      }
    }
  })
}
