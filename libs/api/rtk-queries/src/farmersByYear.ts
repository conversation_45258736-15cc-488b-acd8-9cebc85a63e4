import { AccountHierarchy, CropZone, FarmerDetails, FarmerId, LicensedGrowerTotals } from '@gc/types'
import { fetchStore, filterCropZone, mapTreeAsArray, toUId } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'
import chunk from 'lodash/chunk'

import { transformLicensedGrower } from './farmers'

type CustomerDetailsResponse = {
  dealers: {
    dealerName: string
    growers: {
      farmName: string
      growerName: string
      contactName: string
      irdId: string
      licenseNumber: string
      licenseStatus: string
      growerSapAccountId: string
      gln: string[] | string
      city: string
      state: string
      zipCode: string
      streetAddress: string
      licensedByAug31: string
      county: string
      phoneNumber: string
      email: string
      cornZone: string
      cropZones: CropZone[]
      cyOrder: boolean
      dealerSapId: string
      crtva: string
    }[]
  }[]
  licensedGrowers: {
    people: LicensedGrowerTotals[]
  }[]
}

type FarmerIdsResponse = {
  customerResponseEntityList?: FarmerId[]
}

function transformCustomerDetails(farmerDetails: CustomerDetailsResponse) {
  return (
    farmerDetails?.dealers?.flatMap((dealer) => {
      return (
        dealer.growers?.map((grower) => ({
          farmName: grower.farmName,
          growerName: grower.growerName,
          contactName: grower.contactName,
          growerSapId: grower.growerSapAccountId,
          growerIrdId: grower.irdId,
          growerUId: toUId(grower.growerSapAccountId, grower.irdId),
          licenseStatus: grower.licenseStatus,
          gln: Array.isArray(grower.gln) ? grower.gln.join(', ') : grower.gln,
          city: grower.city,
          state: grower.state,
          zipCode: grower.zipCode,
          streetAddress: grower.streetAddress,
          county: grower.county,
          phoneNumber: grower.phoneNumber,
          email: grower.email,
          licensedByAug31: grower.licensedByAug31,
          cyOrder: grower.cyOrder,
          dealerName: dealer.dealerName,
          dealerSapId: grower.dealerSapId,
          crtva: grower.crtva,
          cornZone: grower.cornZone,
          cropZones: filterCropZone(grower.cropZones)
        })) || []
      )
    }) || []
  )
}

export function getFiscalYear(builder: EndpointBuilder<BaseQueryFn, string, 'acsCommonApi'>) {
  return builder.query<string, void>({
    query: () => {
      const { lob } = fetchStore('selectedAccount')
      const { salesOrgs } = fetchStore('domainDef').gcPortalConfig
      return {
        url: '/fiscalYear',
        params: {
          country: 'US',
          salesOrg: salesOrgs[lob]
        }
      }
    },
    transformResponse: (response: { fiscalYear: string }) => {
      return response?.fiscalYear
    }
  })
}

export function getFarmerIds(builder: EndpointBuilder<BaseQueryFn, string, 'acsCommonApi'>) {
  return builder.query<FarmerId[], { dealerSapIds: string[] }>({
    query({ dealerSapIds }) {
      const body = dealerSapIds
      return {
        url: `/getCustomerIdListBySAPIds`,
        method: 'POST',
        responseHandler: 'content-type',
        body
      }
    },
    transformResponse: (response: FarmerIdsResponse) => {
      return response?.customerResponseEntityList || ([] as FarmerId[])
    }
  })
}

export function getFarmerDetailsByYear(builder: EndpointBuilder<BaseQueryFn, string, 'seedServiceApi'>) {
  return builder.query<
    { farmerDetails: FarmerDetails[]; licensedGrowerTotals: LicensedGrowerTotals },
    { farmerIds: FarmerId[]; dealerHierarchy: AccountHierarchy; dealerSapIds: string[] }
  >({
    async queryFn({ farmerIds, dealerHierarchy, dealerSapIds }, _queryApi, _extraOptions, fetchWithBQ) {
      try {
        const bodyBase = {
          sapAccountIdList: [] as string[],
          growerIdList: [] as FarmerId[],
          dealers: mapTreeAsArray<AccountHierarchy, { dealerName: string; dealerSapId: string }>(
            (node) => ({
              dealerName: node.accountName,
              dealerSapId: node.sapAccountId
            }),
            dealerHierarchy
          )
        }

        const dealerSapIdsChunk = chunk(dealerSapIds, 30)
        const detailPromises: Promise<{ data: CustomerDetailsResponse }>[] = []

        dealerSapIdsChunk.forEach((ch) => {
          const farmerIdsChunk: FarmerId[] = farmerIds.filter((e) => ch.includes(e.dealerSAPId))
          const updatedBody = { ...bodyBase, sapAccountIdList: ch, growerIdList: farmerIdsChunk }
          const result = fetchWithBQ({
            url: `/farmers/customerDetailsList`,
            method: 'POST',
            data: updatedBody
          }) as Promise<{ data: CustomerDetailsResponse }>
          detailPromises.push(result)
        })

        const resolvedDetailsPromises = await Promise.all(detailPromises)
        const finalData = resolvedDetailsPromises.map((e) => e.data)

        const transformedData = {
          farmerDetails: finalData.flatMap((e) => transformCustomerDetails(e)),
          licensedGrowerTotals: transformLicensedGrower(finalData.flatMap((e) => e?.licensedGrowers || []))
        }

        return {
          data: transformedData
        }
      } catch (error) {
        return { error }
      }
    }
  })
}
