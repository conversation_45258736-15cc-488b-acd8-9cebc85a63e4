import { creditLimit, ThirdPartyFinancingRequestedTerm, ThirdPartyFinancingResponse } from '@gc/types'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

export function getCreditLimitQuery(builder: EndpointBuilder<BaseQueryFn, string, 'financeApi'>) {
  return builder.query<
    creditLimit,
    {
      payload: { selectedSapId: string; creditControlNumber: string }
      headers: { 'sap-instance': string; 'customer-number': string }
    }
  >({
    query: ({ payload, headers }) => {
      return {
        url: '/individual-utilization',
        method: 'POST',
        headers,
        data: payload
      }
    }
  })
}

export function getThirdPartyFinancingQuery(builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) {
  return builder.query<ThirdPartyFinancingRequestedTerm[], { orderId: string }>({
    query: ({ orderId }) => {
      return {
        url: `/financial/requestedFinancingTerms/${orderId}`,
        method: 'GET',
        headers: {
          Accept: 'application/json'
        }
      }
    },
    providesTags: ['ThirdPartyFinancing']
  })
}

export function submitThirdPartyFinancingMutation(builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) {
  return builder.mutation<ThirdPartyFinancingResponse, FormData>({
    query: (payload) => {
      return {
        url: `/financial/submitRequestedFinancingTerm`,
        method: 'POST',
        data: payload,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    },
    invalidatesTags: ['ThirdPartyFinancing']
  })
}

export function removeThirdPartyFinancingMutation(builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) {
  return builder.mutation<ThirdPartyFinancingResponse, { orderId: string; termId: string }>({
    query: ({ orderId, termId }) => {
      return {
        url: `/financial/removeRequestedFinancingTerms/${orderId}/${termId}`,
        method: 'DELETE'
      }
    },
    invalidatesTags: ['ThirdPartyFinancing']
  })
}

export function submitBatchThirdPartyFinancingMutation(builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) {
  return builder.mutation<ThirdPartyFinancingResponse, FormData>({
    query: (payload) => {
      return {
        url: `/financial/submitRequestedFinancingTermBatch`,
        method: 'POST',
        data: payload,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    },
    invalidatesTags: ['ThirdPartyFinancing']
  })
}
