/* eslint-disable @typescript-eslint/no-explicit-any */
import { TokenType } from '@gc/api/client'
import { PREPAY } from '@gc/constants'
import { COMMERCE_CLOUD_API } from '@gc/shared/env'
import {
  BayerProgram,
  BayerPrograms,
  BayerTier,
  Discount,
  DiscretionaryBudget,
  DiscretionaryBudgetRequest,
  DomainDefGcPortalConfig,
  RecommendedRange,
  RecommendedRangeRequest,
  Strategy
} from '@gc/types'
import { fetchStore } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

export const getDiscretionaryBudgetsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<DiscretionaryBudget[], DiscretionaryBudgetRequest>({
    query: (request: DiscretionaryBudgetRequest) => ({
      url: `/agency/discretionary-budgets`,
      method: 'POST',
      data: request
    }),
    keepUnusedDataFor: 0,
    transformResponse: (response: DiscretionaryBudget[]) => {
      return transformDiscretionaryBudget(response)
    },
    onQueryStarted: async (_arg, { dispatch }) => {
      // This will reset any locally computed RemainingBudget.
      dispatch({ type: 'cart/resetRemainingBudget' })
    }
  })
}

export const getBrandDiscountsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<Discount, void>({
    query: () => ({
      url: `${COMMERCE_CLOUD_API}/cbus/bayer-program?type=BRAND_DISCOUNT&fields=DEFAULT&year=2025`
    }),
    extraOptions: { tokenType: TokenType.Azure },
    transformResponse: (response: BayerPrograms) => {
      return transformBrandDiscounts(response)
    },
    keepUnusedDataFor: 3600
  })
}

const transformDiscretionaryBudget = (results: any) => {
  return results.map((item: any) => {
    return {
      remainingBudget: item.RemainingdBudget,
      programName: item.ProgramName,
      programId: item.ProgramID,
      programExtId: item.Prg_ExtId,
      crop: item.Crop,
      allocatedBudget: item.TotalBudget,
      discountBuckets: item.lstST
        ? item.lstST.map((bucket: any) => {
            return {
              strategy: bucket.Strategy,
              tactics: bucket.Tactics
            }
          })
        : []
    }
  })
}

const transformBrandDiscounts = (results: BayerPrograms) => {
  const brandDiscount: Discount = {
    programName: 'Brand Discount',
    strategies: []
  }

  const discounts = (fetchStore('domainDef') || {}).gcPortalConfig?.discounts as DomainDefGcPortalConfig['discounts']
  const inValidBrandDiscountProgramIds = discounts?.inValidBrandDiscountProgramIds || []
  const programs =
    results.programs?.filter((program: BayerProgram) => !inValidBrandDiscountProgramIds.includes(program.programId)) ||
    []
  if (programs.length) {
    const strategies: Strategy[] = []
    programs
      .filter((program: BayerProgram) => program.programName !== PREPAY)
      .map((nonPrepayPrograms: BayerProgram) => {
        if (nonPrepayPrograms.bayerTiers && nonPrepayPrograms.bayerTiers.length) {
          const bayerTier = nonPrepayPrograms.bayerTiers[0]
          if (
            !strategies.find((existingStrategy: Strategy) => existingStrategy.name === nonPrepayPrograms.programName)
          ) {
            strategies.push({
              name: nonPrepayPrograms.programName,
              displayDiscount: bayerTier.discount,
              discountValue: bayerTier.discount,
              discountPercentage: bayerTier.discount,
              discountUnit: bayerTier.type,
              discountDescription: `${bayerTier.discount}% off retail price`,
              strategyId: nonPrepayPrograms.programId,
              programName: nonPrepayPrograms.programName,
              bayerTierId: bayerTier.bayerTierId
            })
          }
        }
        return true
      })

    programs
      .filter((program: BayerProgram) => program.programName === PREPAY)
      .map((prepayProgram: BayerProgram) => {
        if (prepayProgram.bayerTiers && prepayProgram.bayerTiers.length) {
          prepayProgram.bayerTiers.sort((prev: BayerTier, next: BayerTier) => prev.sortOrder - next.sortOrder)
          prepayProgram.bayerTiers.map((bayerTier: BayerTier) =>
            strategies.push({
              name: bayerTier.paymentTypeValue ?? '',
              deadline: bayerTier.deadline,
              displayDiscount: bayerTier.discount,
              discountValue: bayerTier.discount,
              discountPercentage: bayerTier.discount,
              discountUnit: bayerTier.type,
              strategyId: prepayProgram.programId,
              programName: prepayProgram.programName,
              isPrepayDiscount: true,
              bayerTierId: bayerTier.bayerTierId
            })
          )
        }
        return true
      })
    brandDiscount.strategies = strategies
  }
  return brandDiscount
}

export const getRecommendedRangeQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<RecommendedRange[], RecommendedRangeRequest>({
    query: (payload: RecommendedRangeRequest) => ({
      url: `/agency/recommended-range`,
      method: 'POST',
      data: payload
    }),
    serializeQueryArgs: () => 'recommendedRange',
    providesTags: ['RecommendedRange']
  })
}
