import { CropZone, FarmerDetails, Order } from '@gc/types'
import { fetchStore, filterCropZone, toUId } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

type PurchaserListResponse = {
  purchasers: {
    addressLine: string
    city: string
    country: string
    email: string
    firstName: string
    gln: string[] | string
    lastName: string
    licenseStatus: string
    licensedByAug31: string
    name: string
    phone: string
    sapAccountId: string
    state: string
    technologyId: string
    zipCode: string
    zones: CropZone[]
  }[]
  sapAccountId: string
  totalCount: number
  updateTimestamp: string
}

export type GrowerSummaryResponse = {
  cropYear: string
  growerSummaryDetails: {
    product: string
    cropDescription: string
    currentYearReportedGpos: number
    priorYearMinus1ReportedGpos: number
    priorYearReportedGpos: number
  }[]
  cropTotals: [
    {
      crop: string
      currentYearReportedGpos: number
      priorYearMinus1ReportedGpos: number
      priorYearReportedGpos: number
    }
  ]
}

type GrowerDetailsResponse = {
  accountName: string
  irdId: string
  licenseId: string
  licenseStatus: string
  sapAccountId: string
  gln: string[] | string
  city: string
  state: string
  zipCode: string
  streetAddress: string
  licensedByAug31: string
  county: string
  phoneNumber: string
  email: string
  cornZone: string
  cropZones: CropZone[]
}

function transformPurchaserList(farmerDetails?: PurchaserListResponse) {
  if (!farmerDetails) return []
  const { sapAccountId: dealerSapId = '', accountName: dealerName } = fetchStore('selectedAccount')
  return (
    farmerDetails?.purchasers?.map((grower) => ({
      farmName: grower.name,
      growerName: grower.name,
      firstName: grower.firstName,
      lastName: grower.lastName,
      growerSapId: grower.sapAccountId,
      growerIrdId: grower.technologyId,
      growerUId: toUId(grower.sapAccountId, grower.technologyId),
      licenseStatus: grower.licenseStatus,
      gln: Array.isArray(grower.gln) ? grower.gln.join(', ') : grower.gln,
      city: grower.city,
      state: grower.state,
      zipCode: grower.zipCode,
      streetAddress: grower.addressLine,
      phoneNumber: grower.phone,
      email: grower.email,
      licensedByAug31: grower.licensedByAug31,
      dealerName,
      dealerSapId,
      cropZones: filterCropZone(grower.zones)
    })) || []
  )
}

function transformGrowerSummary(response: GrowerSummaryResponse) {
  return response?.growerSummaryDetails?.map((e) => ({
    product: e.product,
    cropName: e.cropDescription,
    currentYearNetGPOS: e.currentYearReportedGpos,
    priorYearGrowerOrder: e.priorYearReportedGpos,
    priorYearGrowerOrderMinus1: e.priorYearMinus1ReportedGpos
  }))
}

function transformGrowerDetails(response: GrowerDetailsResponse) {
  if (!response) return undefined
  return {
    farmName: response.accountName,
    growerSapId: response.sapAccountId,
    growerIrdId: response.irdId,
    growerUId: toUId(response.sapAccountId, response.irdId),
    licenseStatus: response.licenseStatus,
    gln: Array.isArray(response.gln) ? response.gln.join(', ') : response.gln,
    city: response.city,
    state: response.state,
    zipCode: response.zipCode,
    streetAddress: response.streetAddress,
    licensedByAug31: response.licensedByAug31,
    county: response.county,
    phoneNumber: response.phoneNumber,
    email: response.email,
    cornZone: response.cornZone,
    cropZones: filterCropZone(response.cropZones)
  }
}

export function fetchLicFarmers(builder: EndpointBuilder<BaseQueryFn, string, 'licApi'>) {
  return builder.query<FarmerDetails[], void>({
    query: () => {
      const { sapAccountId = '' } = fetchStore('selectedAccount')
      return {
        url: '/purchaser-list',
        params: { sapId: sapAccountId }
      }
    },
    transformResponse: transformPurchaserList
  })
}

export function getLicFarmerSummary(builder: EndpointBuilder<BaseQueryFn, string, 'licApi'>) {
  return builder.query<Order[], { dealerSapId: string; growerSapId: string }>({
    query: ({ dealerSapId, growerSapId }) => {
      return {
        url: '/grower-summary',
        params: { sapId: dealerSapId, growerAccountIrdId: growerSapId }
      }
    },
    transformResponse: transformGrowerSummary
  })
}

export function getLicSingleFarmerDetails(builder: EndpointBuilder<BaseQueryFn, string, 'licApi'>) {
  return builder.query<FarmerDetails | undefined, { growerSapId: string; growerIrdId: string }>({
    query: ({ growerSapId, growerIrdId }) => {
      return {
        url: '/grower-details',
        params: { sapId: growerSapId, growerIrdId }
      }
    },
    transformResponse: transformGrowerDetails
  })
}
