/* eslint-disable @nx/enforce-module-boundaries */
import { getAxiosClient } from '@gc/api/client'
import { getAppConfig } from '@gc/shared/config'
import { GC_MIDDLEWARE_API } from '@gc/shared/env'
import {
  NbConsignments,
  NbOrderDetails,
  NbOrderList,
  OrderSummary,
  ProductPalAvailabilityResponse,
  ShipToAddress
} from '@gc/types'
import { fetchStore } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'
import _ from 'lodash'

type NbOrderListResponse = {
  sourceId: string
  soldToName: string
  shipToAddress: ShipToAddress
  orderSummary: Array<OrderSummary>
  consignments: Array<NbConsignments>
  orderNumber: string
  created: string
  statusDisplay: string
  billToParty: string
  code: string
  deliveryItemsQuantity: string
  purchaseOrderNumber: string
  purchaseOrderDate: string
  shipToName: string
  shipToParty: string
  deliveryStatus: string
  deliveryStatusDisplay: string
  documentType: string
  status: string
  season?: string
  payerName: string
}

type NbOrderDetailsResponse = {
  orderNumber: string
  orderId?: string
  soldToName: string
  shipToName: string
  shipToAddress: string
  purchaseOrderNumber: string
  purchaseOrderDate: string
  code: string
  statusDisplay: string
  consignments: Array<NbConsignments>
  orderSummary: {
    entryNumber: number
    quantity: number
    salesUOM: string
    palUom: string
    product: {
      baseUnitofMeasure: string
      name: string
      code: string
    }
    orderMultiple: number
    quantityRange: number
    availableQty: number
  }[]
}

type CpOrderDetailsResponse = {
  code: string
  status: string
  statusDisplay: string
  orderNumber: string
  purchaseOrderNumber: string
  purchaseOrderDate: string
  soldToName: string
  shipToName: string
  shipToAddress?: ShipToAddress
  consignments: Array<NbConsignments>
  orderSummary: Array<OrderSummary>
}

export const getNbOrdersQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  const body = {
    currentPage: 0,
    pageSize: 200,
    startDate: '08/31/2024',
    endDate: '09/01/2025',
    portalKey: 'mycrop',
    transactionType: 'order',
    countryCode: 'nbus'
  }
  return builder.query<NbOrderList[], { selectedAccounts: string[] }>({
    query: ({ selectedAccounts }) => {
      const { lob = '', sapAccountId = '' } = fetchStore('selectedAccount')
      const { username = '' } = fetchStore('user')
      const { orderDocTypes = {} } = getAppConfig()

      const docTypes = (orderDocTypes as Record<string, string[]>)[lob as string] || []
      const accounts = selectedAccounts.length > 0 ? selectedAccounts : [sapAccountId]
      return {
        url: `/orders/list`,
        method: 'POST',
        data: {
          ...body,
          sapId: sapAccountId,
          userId: username,
          documentTypes: docTypes,
          soldToAccounts: accounts
        }
      }
    },
    transformResponse: (response: NbOrderListResponse[]) => {
      return transformNbOrders(response)
    }
  })
}

export const getCPOrdersQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  const body = {
    currentPage: 0,
    pageSize: 200,
    startDate: '08/31/2024',
    endDate: '09/01/2025',
    portalKey: 'mycrop',
    transactionType: 'order',
    countryCode: 'nbus',
    distributionChannels: ['01', '02'],
    salesOrg: 'CJ09'
  }
  return builder.query<NbOrderList[], { selectedAccounts: string[] }>({
    query: ({ selectedAccounts }) => {
      const { lob = '', sapAccountId = '' } = fetchStore('selectedAccount')
      const { username = '' } = fetchStore('user')
      const { orderDocTypes = {} } = getAppConfig()

      const docTypes = (orderDocTypes as Record<string, string[]>)[lob as string] || []
      const accounts = selectedAccounts.length > 0 ? selectedAccounts : [sapAccountId]
      return {
        url: `/orders/list/cp`,
        method: 'POST',
        data: {
          ...body,
          sapId: sapAccountId,
          userId: username,
          documentTypes: docTypes,
          soldToAccounts: accounts
        }
      }
    },
    transformResponse: (response: NbOrderListResponse[]) => {
      return transformNbOrders(response)
    }
  })
}

export const getNbOrderDetailsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<NbOrderDetails, { orderId: string }>({
    query: ({ orderId }) => {
      const COUNTRY_CODE = 'nbus'
      return {
        url: `/orders/${COUNTRY_CODE}/nb/details/${orderId}`,
        method: 'GET'
      }
    },
    transformResponse: (response: NbOrderDetailsResponse) => {
      return transformNbOrderDetails(response)
    }
  })
}

export const getCPOrderDetailsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<NbOrderDetails, { orderId: string }>({
    query: ({ orderId }) => {
      const COUNTRY_CODE = 'nbus'
      return {
        url: `/orders/${COUNTRY_CODE}/details/${orderId}`,
        method: 'GET'
      }
    },
    transformResponse: (response: NbOrderListResponse) => {
      return transformCPOrderDetails(response)
    }
  })
}

export const getPalDataByProducts = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<ProductPalAvailabilityResponse[], { codes: string[] }>({
    queryFn: async (payload) => {
      const { codes = [] } = payload
      const { palRequestConfig = {} } = getAppConfig()
      const { sapAccountId = '', lob = '' } = fetchStore('selectedAccount')

      const middlewareUrl = `${GC_MIDDLEWARE_API}/products/availability`

      function fetchPalData(chunkCodes: Array<string>) {
        const axiosClient = getAxiosClient()
        const config = (palRequestConfig as Record<string, Record<string, string>>)[lob as string]
        const body = {
          saleOrg: config?.saleOrg,
          division: config?.division,
          distributionChannel: config?.distributionChannel,
          sapId: sapAccountId,
          requestType: config?.requestType,
          mmskus: chunkCodes
        }
        return axiosClient.post<ProductPalAvailabilityResponse[]>(middlewareUrl, body)
      }

      async function fetchAllPalData() {
        const codeChunks = _.chunk(codes, 300)
        const palResults = await Promise.allSettled(codeChunks.map((chunk) => fetchPalData(chunk)))

        let allPalData: ProductPalAvailabilityResponse[] = []
        palResults.forEach((result) => {
          if (result.status === 'fulfilled') {
            allPalData = _.concat(allPalData, result?.value?.data || [])
          }
        })
        return allPalData
      }

      try {
        const allPalData = await fetchAllPalData()
        return { data: allPalData }
      } catch (error) {
        return { error: (error as Error).message }
      }
    }
  })
}

const formatAddress = (shipToAddress: ShipToAddress) => {
  return [
    shipToAddress?.address1Text,
    shipToAddress?.cityTown,
    shipToAddress?.stateProvinceCode,
    shipToAddress?.postalCode
  ]
    .filter(Boolean)
    .join(', ')
}

const transformNbOrders = (res: NbOrderListResponse[]): NbOrderList[] => {
  const result =
    res?.map((item) => {
      return {
        soldToName: item.soldToName,
        shipToName: item?.shipToName,
        shipToAddress: formatAddress(item.shipToAddress),
        purchaseOrderNumber: item.purchaseOrderNumber,
        purchaseOrderDate: item.purchaseOrderDate,
        orderNumber: item.orderNumber,
        code: item.code,
        statusDisplay: item.statusDisplay,
        orderSummary: item.orderSummary || [],
        payerName: item.payerName
      }
    }) || []
  return result
}

const transformNbOrderDetails = (res: NbOrderDetailsResponse): NbOrderDetails => {
  const orderDetails: NbOrderDetails = {
    orderNumber: res.orderId ?? '',
    purchaseOrderNumber: res.purchaseOrderNumber ?? '',
    purchaseOrderDate: res.purchaseOrderDate ?? '',
    soldToName: res.soldToName ?? '',
    shipToName: res.shipToName ?? '',
    shipToAddress: res.shipToAddress ?? '',
    code: res.code ?? '',
    statusDisplay: res.statusDisplay ?? '',
    orderSummary: (res.orderSummary?.map((item) => ({
      entryNumber: item.entryNumber,
      quantity: item.quantity,
      salesUom: item.palUom ?? item.salesUOM ?? item?.product?.baseUnitofMeasure,
      quantityRange: item.quantityRange,
      availableQty: item.availableQty,
      productDesc: item.product?.name || '',
      productCode: item.product?.code || ''
    })) || []) as OrderSummary[]
  }
  return orderDetails
}

/**
 * Map Shipments model
 * @param orderData
 */
const transformCPOrderDetails = (res: CpOrderDetailsResponse): NbOrderDetails => {
  const orderSummary = (res.orderSummary || []).map((item) => {
    // Find all consignments/entries matching this product
    const deliveries =
      res.consignments?.flatMap((consignment) =>
        (consignment.entries || [])
          .filter(
            (entry: any) =>
              item.entryNumber === entry?.orderEntry?.entryNumber &&
              item.productCode === entry?.orderEntry?.product?.code
          )
          .map((entry: any) => ({
            deliveryId: consignment.code,
            actualDeliveryDate: consignment.actualDeliveryDate,
            estimatedDeliveryDate: consignment.estimatedDeliveryDate,
            plannedShipDate: consignment.plannedShipDate,
            shippingDate: consignment.shippingDate,
            status: consignment.status,
            quantity: entry.orderEntry.quantity,
            statusDisplay: consignment.statusDisplay,
            carrierName: consignment.forwardingAgentVendorName,
            trackingId: consignment.containerId,
            trackingUrl: consignment.trackingUrl
          }))
      ) || []

    return {
      entryNumber: item.entryNumber,
      quantity: item.quantity,
      salesUom: item.palUom ?? item.salesUom,
      quantityRange: item.quantityRange,
      availableQty: item.availableQty,
      productDesc: item.productDesc || '',
      productCode: item.productCode || '',
      delivery: deliveries
    }
  })

  const orderDetails: NbOrderDetails = {
    orderNumber: res.orderNumber ?? '',
    purchaseOrderNumber: res.purchaseOrderNumber ?? '',
    purchaseOrderDate: res.purchaseOrderDate ?? '',
    soldToName: res.soldToName ?? '',
    shipToName: res.shipToName ?? '',
    shipToAddress: res.shipToAddress ? formatAddress(res.shipToAddress as ShipToAddress) : '',
    code: res.code ?? '',
    statusDisplay: res.statusDisplay ?? '',
    orderSummary: orderSummary as OrderSummary[]
  }

  return orderDetails
}
