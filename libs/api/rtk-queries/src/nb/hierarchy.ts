import { AccountHierarchy } from '@gc/types'
import { appendLeadingZeros, fetchStore, mapTree, toUId } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

type AccountHierarchyResponse = [AccountHierarchy]

function transformAccountHierarchy(response: AccountHierarchyResponse) {
  const baseAccount = response?.[0]
  if (!baseAccount) return undefined

  return mapTree<AccountHierarchy, AccountHierarchy>((account) => {
    return {
      accountName: account.accountName,
      irdId: account.irdId,
      sapAccountId: appendLeadingZeros(account.sapAccountId),
      uId: toUId(account.sapAccountId, account.irdId),
      level: account.level
    }
  }, baseAccount)
}

export function getDealerAccLocationHierarchy(builder: EndpointBuilder<BaseQueryFn, string, 'nbxApi'>) {
  return builder.query<AccountHierarchy | undefined, void>({
    query: () => {
      const { uid, lob } = fetchStore('selectedAccount')
      return {
        url: '/hierarchy/accounts',
        params: {
          customerNumber: uid,
          lob: lob.toUpperCase()
        }
      }
    },
    transformResponse: transformAccountHierarchy
  })
}
