/* eslint-disable @nx/enforce-module-boundaries */
import { getAxiosClient } from '@gc/api/client'
import { ccFieldsChannel, ccFieldsChannelAU, orderDocumentTypes } from '@gc/constants'
import { COMMERCE_CLOUD_API } from '@gc/shared/env'
import {
  ChannelOrder,
  Order,
  OrderDetailsCBUS,
  OrderDetailsCBUSReq,
  OrderForDelivery,
  OrderForModifyDelivery,
  Orders,
  OrdersForDeliveryResponse,
  PaginationParams,
  ProductInventory,
  SeedGrowthConsignment,
  SeedGrowthOrdersResponse,
  StockOrder
} from '@gc/types'
import {
  adjustDataForDisplay,
  adjustEntriesForDisplay,
  areArraysEqual,
  fetchStore,
  getCountryCode,
  getOneDCECountry,
  getParams,
  getPortal,
  getUserName,
  getUserPrefix,
  range
} from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'
import { noop } from 'es-toolkit'
import _ from 'lodash'

type OrderResponse = {
  currentYear: string
  orderList: {
    hybrid: string
    cropName: string
    currentYearGrowerOrder: number
    currentYearNetGPOS: number
    priorYearGrowerOrder: number
    priorYearGrowerOrderMinus1: number
    productDetails: {
      sku: string
      cropName: string
      currentYearGrowerOrder: number
      currentYearNetGPOS: number
      priorYearGrowerOrder: number
      priorYearGrowerOrderMinus1: number
    }[]
  }[]
}

function transformOrderDetails(response: OrderResponse) {
  return response?.orderList?.map((e) => ({
    ...e,
    product: e.hybrid,
    subRows: e.productDetails?.map((p) => ({
      ...p,
      product: p.sku
    }))
  }))
}
const urlPrefix = () => `${getUserPrefix()}/orders`
const urlPrefixAU = () => `/orders`
const getParamsStr = (paramsObj: object = {}, isMobile = false): string => {
  const { isMyCropCentre } = getPortal()
  return getParams(paramsObj, { isMobile, fields: isMyCropCentre ? ccFieldsChannelAU : ccFieldsChannel })
}

export function getFarmerOrderDetailsNational(builder: EndpointBuilder<BaseQueryFn, string, 'seedServiceApi'>) {
  return builder.query<Order[], { dealerSapId: string; growerSapId: string; fiscalYear: string }>({
    query: ({ dealerSapId, growerSapId, fiscalYear }) => {
      const body = { sapAccountId: dealerSapId, growerSAPId: growerSapId, fiscalYear, priorYears: 2 }
      return {
        url: '/farmers/farmerOrderDetails',
        method: 'POST',
        responseHandler: 'content-type',
        body
      }
    },
    transformResponse: transformOrderDetails
  })
}

export function getFarmerOrderDetailsChannel(builder: EndpointBuilder<BaseQueryFn, string, 'acsMyAccountApi'>) {
  return builder.query<Order[], { growerSapId: string }>({
    query({ growerSapId }) {
      const { sapAccountId } = fetchStore('selectedAccount')
      return {
        url: '/farmers/growerOrderDetails',
        params: {
          sapAccountId,
          growerSapAccountId: growerSapId
        }
      }
    },
    transformResponse: transformOrderDetails
  })
}

function fetchOrders<T>({
  currentPage = 0,
  params,
  isMobile,
  payloadReqBody
}: {
  currentPage?: number
  params?: object
  isMobile?: boolean
  payloadReqBody?: GetAllOrdersReqBody
}) {
  const countryRef = getCountryCode()
  const isLight = countryRef === 'cbus' && !areArraysEqual(payloadReqBody?.documentTypes, orderDocumentTypes.RETURN)
  const axiosClient = getAxiosClient()
  const url = `${COMMERCE_CLOUD_API}/${countryRef}${getUserPrefix()}/allorders${isLight ? '-light' : ''}${getParamsStr({ ...params }, isMobile)}`
  const reqBody = { ...payloadReqBody, currentPage }
  return axiosClient.post<T>(url, reqBody)
}

export type GetAllOrdersReqBody = {
  pageSize?: number
  soldToAccounts?: string[]
  documentTypes?: string[]
  agents?: string[]
  salesYears?: string[]
  currentPage?: number
}

export type GetAllOrdersOptions<T> = {
  isMobile?: boolean
  fetchAllPages?: boolean // Fetch all pages if true
  reqBody?: GetAllOrdersReqBody
  params?: PaginationParams
  updatePartialOrders?: (a: T[]) => void // Update partial orders if provided
  transformOrders?: (a: T[]) => T[]
}

export const getAllOrdersQuery = <T extends ChannelOrder | StockOrder = ChannelOrder>(
  builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>
) => {
  return builder.query<T[], GetAllOrdersOptions<T> | void>({
    queryFn: async (payload) => {
      const {
        params,
        isMobile,
        updatePartialOrders = noop,
        reqBody: payloadReqBody = {},
        transformOrders = (orders: T[]) => orders
      } = payload || {}
      const BATCH_SIZE = 3

      const fetchOrdersFn = (currentPage = 0) => fetchOrders<Orders>({ currentPage, params, isMobile, payloadReqBody })

      try {
        const { data } = await fetchOrdersFn()
        const transformedOrders = transformOrders((data.orders ?? []) as T[])
        const orders = (transformedOrders ?? []).map((order) => adjustDataForDisplay(order))

        const { totalPages, currentPage } = data.pagination
        if (totalPages - 1 > currentPage) {
          const pageRange = range(currentPage + 1, totalPages)

          // Process pages in batches
          for (let i = 0; i < pageRange.length; i += BATCH_SIZE) {
            const batch = pageRange.slice(i, i + BATCH_SIZE)
            const batchResults = await Promise.allSettled(batch.map((page) => fetchOrdersFn(page)))

            const batchOrders: T[] = []
            for (const ordersResults of batchResults) {
              if (ordersResults.status === 'fulfilled') {
                const orders = (ordersResults.value.data.orders ?? []).map((order) =>
                  adjustDataForDisplay(order)
                ) as T[]
                batchOrders.push(...orders)
              } else {
                throw new Error(ordersResults.reason)
              }
            }

            const transformedBatchOrders = transformOrders(batchOrders)
            orders.push(...transformedBatchOrders)
            updatePartialOrders(transformedBatchOrders)
          }
        }

        return { data: orders }
      } catch (error) {
        return { error }
      }
    },
    keepUnusedDataFor: 3600,
    serializeQueryArgs: ({ queryArgs }) => {
      if (!queryArgs) return 'orders'

      const { reqBody, params, isMobile } = queryArgs
      return `orders-${JSON.stringify({
        isMobile,
        reqBody,
        pagination: params
      })}`
    },
    providesTags: (_result, _error, arg) =>
      areArraysEqual(arg?.reqBody?.documentTypes, orderDocumentTypes.STOCK) ? ['StockOrders'] : ['Orders']
  })
}

export const getSeedGrowthOrdersQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<
    SeedGrowthOrdersResponse['orders'],
    {
      reqBody?: {
        currentPage?: number
        pageSize?: number
        startDate?: string
        endDate?: string
        soldToAccounts?: string[]
        documentTypes?: string[]
        salesYears?: string[]
      }
      params?: PaginationParams
      isMobile?: boolean
    } | void
  >({
    queryFn: async (payload) => {
      const axiosClient = getAxiosClient()
      const { params, isMobile } = payload || {}
      const { fields, ...restParams } = params || {}

      function fetchOrders(currentPage = 0) {
        const url = `${COMMERCE_CLOUD_API}/cbus${getUserPrefix()}/allorders-light${getParamsStr({ ...restParams }, isMobile)}`
        const reqBody = { ...(payload?.reqBody || {}), currentPage }
        return axiosClient.post<SeedGrowthOrdersResponse>(url, reqBody)
      }

      try {
        const { data } = await fetchOrders()
        let orders = data.orders ?? []

        const { totalPages, currentPage } = data.pagination
        if (totalPages - 1 > currentPage) {
          const pageRange = range(currentPage + 1, totalPages)

          // Generate an array of promises based on the remaining pages and run the api calls in parallel
          const totalResults = await Promise.allSettled(pageRange.map((page) => fetchOrders(page)))

          // Loop over results, combining orders and filtering undefined values
          for (const ordersResults of totalResults) {
            if (ordersResults.status === 'fulfilled') {
              orders.push(...ordersResults.value.data.orders)
            } else {
              throw new Error(ordersResults.reason)
            }
          }
        }

        const hasMatchingEntry = (consignment: SeedGrowthConsignment, entryNumber: string) =>
          consignment.entries.some((entry) => entry.salesOrderEntryNumber === entryNumber)

        const findMatchingIndexes = (consignments: SeedGrowthConsignment[] | undefined, entryNumber: string) =>
          consignments?.reduce(
            (indexes: number[], consignment, index) =>
              hasMatchingEntry(consignment, entryNumber) ? [...indexes, index] : indexes,
            []
          ) ?? []

        // Add consignment indexes to each order entry
        orders = orders.map((order) => ({
          ...order,
          entries:
            order.entries?.map((entry) => {
              return {
                ...entry,
                consignmentIndexes: findMatchingIndexes(order.consignments, entry.entryNumber?.toString())
              }
            }) ?? []
        }))

        return { data: orders }
      } catch (error) {
        return { error }
      }
    },
    keepUnusedDataFor: 3600,
    serializeQueryArgs: ({ queryArgs }) => `orders${JSON.stringify(_.get(queryArgs, 'reqBody', {}))}`,
    providesTags: ['SeedGrowthOrder']
  })
}

export const getOrderDetailsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<OrderDetailsCBUS, OrderDetailsCBUSReq>({
    query: (payload) => {
      const { isMyCropCentre } = getPortal()
      return {
        url: isMyCropCentre
          ? `${urlPrefixAU()}/${payload.orderId}` + getParamsStr({}, payload?.isMobile)
          : `${urlPrefix()}/${payload.orderId}` + getParamsStr({}, payload?.isMobile)
      }
    },
    transformResponse: (response: OrderDetailsCBUS) => {
      const modifiedOrderDetails = _.cloneDeep(response)
      const updatedEntries = adjustEntriesForDisplay(modifiedOrderDetails?.entries)
      const adjustedConsignments =
        modifiedOrderDetails.consignments?.map((consignment) => adjustDataForDisplay(consignment)) ?? []
      modifiedOrderDetails.entries = updatedEntries
      modifiedOrderDetails.consignments = adjustedConsignments
      return modifiedOrderDetails
    },
    providesTags: (_result, _error, arg) => [{ type: 'Order', id: arg.orderId }]
  })
}

export type ConvertCartToOrderPayload = {
  reqBody: {
    cartId: string
    lobLevelDetails: {
      lob: string
      poNumber?: string
      deliveryMode?: string
      comment?: string
    }
    termsChecked: boolean
  }
  skipOrdersRefetch?: boolean
  invalidateTag?: 'Orders' | 'StockOrders'
  orderCodes?: string[]
}

export const cartToOrderMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<StockOrder | { orders: OrderDetailsCBUS[] }, ConvertCartToOrderPayload>({
    query: ({ reqBody }) => ({
      url: `${urlPrefix()}?fields=${getOneDCECountry()}`,
      method: 'POST',
      data: reqBody
    }),
    invalidatesTags: (_result, error, { skipOrdersRefetch, invalidateTag, orderCodes }) => {
      if (skipOrdersRefetch || error) return []
      if (orderCodes?.length) {
        const tags = orderCodes.map((code) => ({ type: 'Order', id: code }))
        return [...tags, ...(invalidateTag ? [invalidateTag] : [])]
      } else {
        return [...(invalidateTag ? [invalidateTag] : [])]
      }
    }
  })
}

export const cancelOrderMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<void, { orderId: string }>({
    query: ({ orderId }) => ({
      url: `${urlPrefix()}?fields=ONEDCE_CBUS&orderNumber=${orderId}`,
      method: 'PUT',
      data: {}
    })
  })
}

type RetryOrderSubmitPayload = {
  reqBody: {
    codes: string[]
  }
  invalidateTags: ('Orders' | 'StockOrders' | { type: string; id: string })[]
}

export const retryOrderSubmitMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<void | { status: string; errorMessage?: string }, RetryOrderSubmitPayload>({
    query: ({ reqBody }) => ({
      url: `${COMMERCE_CLOUD_API}/cbus/users/${getUserName()}/batchprocess/order?fields=DEFAULT`,
      method: 'POST',
      data: reqBody
    }),
    invalidatesTags: (result, error, { invalidateTags }) => {
      // Only invalidate tags if mutation succeeded and status is SUCCESS
      if (error || result?.status?.toUpperCase() !== 'SUCCESS') {
        return []
      }

      return invalidateTags
    }
  })
}

type GetAllOrdersForDeliveryReqBody = {
  pageSize?: number
  soldToAccounts?: string[]
  currentPage?: number
  fromLocation: string
}

type GetAllOrdersForDeliveryOptions = {
  reqBody?: GetAllOrdersForDeliveryReqBody
  params?: PaginationParams
}

export const getAllOrdersForDeliveryQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<OrderForDelivery[], GetAllOrdersForDeliveryOptions | void>({
    queryFn: async (payload) => {
      const { params, reqBody: payloadReqBody = {} } = payload || {}
      const axiosClient = getAxiosClient()
      const url = `${COMMERCE_CLOUD_API}/${getCountryCode()}${getUserPrefix()}/orders-to-create-consignments${getParamsStr({ ...params })}`
      const reqBody = { ...payloadReqBody }

      try {
        const { data } = await axiosClient.post<OrdersForDeliveryResponse>(url, reqBody)
        return { data: data.orders }
      } catch (error) {
        return { error: (error as Error).message }
      }
    },
    keepUnusedDataFor: 3600,
    serializeQueryArgs: ({ queryArgs }) => {
      if (!queryArgs) return 'OrdersForCreateDelivery'

      const { reqBody, params } = queryArgs
      return `OrdersForCreateDelivery-${JSON.stringify({
        reqBody,
        pagination: params
      })}`
    },
    providesTags: ['OrdersForCreateDelivery']
  })
}

export const getOrderForModifyDeliveryQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<
    OrderForModifyDelivery,
    {
      orderCode: string
      fromLocation: string
      soldToAccounts: string[]
    }
  >({
    query: ({ fromLocation, orderCode, soldToAccounts }) => ({
      url: `${getUserPrefix()}/${orderCode}/modify-consignment-to-order?fields=ONEDCE_CBUS`,
      method: 'POST',
      data: {
        fromLocation,
        soldToAccounts
      }
    }),
    onQueryStarted: async ({ fromLocation }, { dispatch, queryFulfilled }) => {
      try {
        const { data: order } = await queryFulfilled
        // Add product inventories to the store for the consignment edit
        const inventories: ProductInventory[] = []
        order.entries.forEach((entry) => {
          entry.product.inventory.forEach((inventory) => {
            inventories.push({
              code: entry.product.code,
              batchNumber: inventory.batchNumber,
              storageLocationCode: fromLocation,
              available: inventory.available
            })
          })
        })
        dispatch({ type: 'inventory/addProductInventories', payload: inventories })
      } catch {
        noop()
      }
    },
    providesTags: ['OrderForModifyDelivery']
  })
}
