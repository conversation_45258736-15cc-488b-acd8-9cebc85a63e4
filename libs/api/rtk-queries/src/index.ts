// =============================================================================
// SHARED TYPES - Export first for better TypeScript resolution
// =============================================================================
export * from './query-utils'
export * from './shared-types'

// =============================================================================
// CORE MODULES - Most frequently used APIs
// =============================================================================
export * from './accounts'
export * from './cart'
export * from './orders'
export * from './products'

// =============================================================================
// FARMER & AGRICULTURE MODULES
// =============================================================================
export * from './farmers'
export * from './farmersByScor'
export * from './farmersByYear'
export * from './licFarmers'

// =============================================================================
// INVENTORY & LOGISTICS MODULES
// =============================================================================
export * from './consignments'
export * from './inventory'
export * from './inventoryOverview'

// =============================================================================
// FINANCIAL MODULES
// =============================================================================
export * from './discounts'
export * from './finance'
export * from './quotes'

// =============================================================================
// REPORTING & ANALYTICS MODULES
// =============================================================================
export * from './reports'

// =============================================================================
// SPECIALIZED MODULES
// =============================================================================
export * from './advanceApplication'
export * from './configData'
export * from './favorites'
export * from './internalPortal'

// =============================================================================
// CONTENT & EXTERNAL MODULES
// =============================================================================
export * from './aem'

// =============================================================================
// NAMESPACE-SPECIFIC MODULES
// =============================================================================
export * from './nb/hierarchy'
export * from './nb/orders'

// =============================================================================
// VERSIONED MODULES
// =============================================================================
export * from './v2'

// =============================================================================
// UTILITY EXPORTS - Helper functions and constants
// =============================================================================

// Export commonly used utility functions that work with these APIs
export { adjustDataForDisplay, adjustEntriesForDisplay, fetchStore, getOneDCECountry, getUserPrefix } from '@gc/utils'
