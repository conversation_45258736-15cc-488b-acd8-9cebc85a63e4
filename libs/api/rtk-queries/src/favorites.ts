/* eslint-disable @typescript-eslint/no-explicit-any */
import { GrowerFavorite } from '@gc/types'
import { fetchStore } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

type GrowerFavoriteInput = {
  isFavorite: boolean
  growerId: string
}

export function updateFavoriteGrowerMutation(builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) {
  return builder.mutation<void, GrowerFavoriteInput>({
    query(growerFavoriteInput) {
      const { sapAccountId = '' } = fetchStore('selectedAccount')
      const { portalKey = '' } = fetchStore('domainDef')
      const body = {
        isFavorite: growerFavoriteInput.isFavorite,
        userId: sapAccountId,
        portalKey,
        growerId: growerFavoriteInput.growerId
      }
      return {
        url: `/favorite/growers`,
        method: 'POST',
        responseHandler: 'content-type',
        data: body
      }
    }
  })
}

export const getIsFavoriteGrowerQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<boolean, string>({
    query: (growerId) => {
      const { sapAccountId = '' } = fetchStore('selectedAccount')
      const { portalKey = '' } = fetchStore('domainDef')
      return {
        url: `/favorite/growers`,
        method: 'GET',
        params: {
          growerId,
          userId: sapAccountId,
          portalKey
        }
      }
    },
    transformResponse: (response: GrowerFavorite[]) => {
      return response[0]?.isFavorite
    }
  })
}
