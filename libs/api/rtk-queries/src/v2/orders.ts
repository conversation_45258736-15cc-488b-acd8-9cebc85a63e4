import { getAxiosClient } from '@gc/api/client'
import { ChannelOrder, StockOrder } from '@gc/types'
import { adjustDataForDisplay } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

import { buildApiUrl, CACHE_CONFIGS, handlePaginatedQuery } from '../query-utils'
import { BaseQueryOptions, OrdersRequestBody, OrdersResponse } from '../shared-types'

const API_VERSION = 'v2'

// V2-specific field configuration
const V2_FIELDS = {
  MOBILE: 'ONEDCE_CBUS_DESKTOP',
  DESKTOP: 'ONEDCE_CBUS_DESKTOP'
}

/**
 * V2 Orders Query - Uses new generic types and utility functions
 * Provides better type safety and eliminates code duplication
 */
export const getAllOrdersQueryV2 = <T extends ChannelOrder | StockOrder = ChannelOrder>(
  builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>
) => {
  return builder.query<T[], BaseQueryOptions<T, OrdersRequestBody> | void>({
    queryFn: async (payload) => {
      const {
        params,
        isMobile,
        updatePartialData,
        reqBody = {} as OrdersRequestBody,
        transformResponse = (orders: T[]) => orders
      } = payload || {}

      // Create fetch function for paginated requests
      const fetchFn = (currentPage: number) => {
        const axiosClient = getAxiosClient()
        const url = buildApiUrl(
          `/${API_VERSION}/allorders`,
          { ...params },
          { isMobile, fields: V2_FIELDS, includeUserPrefix: true }
        )
        const requestBody = { ...reqBody, currentPage }

        return axiosClient.post<OrdersResponse<T>>(url, requestBody)
      }

      // Use generic pagination handler with proper typing
      const result = await handlePaginatedQuery<T, OrdersResponse<T>>(fetchFn, {
        updatePartialData,
        transformResponse: (data: T[]) => {
          // Apply display adjustments and custom transformation
          const adjustedData = data.map((order) => adjustDataForDisplay(order) as T)
          return transformResponse(adjustedData)
        },
        batchSize: 3,
        pageLimit: params?.pageLimit
      })

      return result
    },
    ...CACHE_CONFIGS.ONE_HOUR,
    serializeQueryArgs: ({ queryArgs }) => {
      if (!queryArgs) return 'orders-v2'
      const { reqBody, params, isMobile } = queryArgs
      return `orders-v2-${JSON.stringify({ isMobile, reqBody, params })}`
    },
    providesTags: ['Orders']
  })
}
