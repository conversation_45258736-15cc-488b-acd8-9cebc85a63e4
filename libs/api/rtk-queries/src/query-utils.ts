import { getAxiosClient } from '@gc/api/client'
import { COMMERCE_CLOUD_API } from '@gc/shared/env'
import { getParams, getUserPrefix, range } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

import {
  BaseQueryOptions,
  BaseRequestBody,
  CacheConfig,
  extractDataFromResponse,
  FieldConfiguration,
  PaginatedApiResponse
} from './shared-types'

// Default field configuration
const DEFAULT_FIELDS: FieldConfiguration = {
  MOBILE: 'DEFAULT',
  DESKTOP: 'DEFAULT'
}

// =============================================================================
// GENERIC PAGINATION HANDLER
// =============================================================================

/**
 * Generic function to handle paginated API calls with parallel processing
 */
export async function handlePaginatedQuery<TData, TResponse extends PaginatedApiResponse<TData>>(
  fetchFn: (currentPage: number) => Promise<{ data: TResponse }>,
  options: {
    updatePartialData?: (data: TData[]) => void
    transformResponse?: (data: TData[]) => TData[]
    batchSize?: number
    pageLimit?: number
  } = {}
): Promise<{ data: TData[] } | { error: unknown }> {
  const { updatePartialData, transformResponse = (data) => data, batchSize = 3, pageLimit = undefined } = options

  try {
    // Fetch first page
    const { data: firstResponse } = await fetchFn(0)
    const allData = extractDataFromResponse(firstResponse)

    // Transform and update partial data if callback provided
    const transformedData = transformResponse(allData)
    if (updatePartialData) {
      updatePartialData(transformedData)
    }

    // Handle additional pages if they exist
    const { totalPages = 1, currentPage = 0 } = firstResponse.pagination
    if (totalPages - 1 > currentPage) {
      // Limit the total number of pages to fetch based on pageLimit
      const maxPagesToFetch = pageLimit ? Math.min(pageLimit - 1, totalPages - 1) : totalPages - 1
      const pageRange = range(currentPage + 1, currentPage + 1 + maxPagesToFetch)

      // Process pages in batches to avoid overwhelming the server
      for (let i = 0; i < pageRange.length; i += batchSize) {
        const batch = pageRange.slice(i, i + batchSize)
        const batchResults = await Promise.allSettled(batch.map(fetchFn))

        const batchData: TData[] = []
        for (const result of batchResults) {
          if (result.status === 'fulfilled') {
            const responseData = extractDataFromResponse(result.value.data)
            batchData.push(...responseData)
          } else {
            throw new Error(result.reason)
          }
        }

        const transformedBatchData = transformResponse(batchData)
        allData.push(...transformedBatchData)

        if (updatePartialData) {
          updatePartialData(transformedBatchData)
        }
      }
    }

    return { data: allData }
  } catch (error) {
    return { error }
  }
}

// =============================================================================
// PARAMETER UTILITIES
// =============================================================================

/**
 * Generic function to build query parameter strings
 */
export function buildQueryParams(
  paramsObj: Record<string, unknown> = {},
  options: {
    isMobile?: boolean
    fields?: FieldConfiguration | string
  } = {}
): string {
  const { isMobile = false, fields } = options

  if (typeof fields === 'string') {
    return getParams(paramsObj, { isMobile, fields: { MOBILE: fields, DESKTOP: fields } })
  }

  const fieldsConfig = fields || DEFAULT_FIELDS
  return getParams(paramsObj, { isMobile, fields: fieldsConfig })
}

/**
 * Build URL with user prefix and parameters
 */
export function buildApiUrl(
  endpoint: string,
  params: Record<string, unknown> = {},
  options: {
    isMobile?: boolean
    fields?: FieldConfiguration | string
    includeUserPrefix?: boolean
    baseUrl?: string
  } = {}
): string {
  const { isMobile = false, fields, includeUserPrefix = true, baseUrl = COMMERCE_CLOUD_API } = options

  const userPrefix = includeUserPrefix ? getUserPrefix() : ''
  const queryParams = buildQueryParams(params, { isMobile, fields })

  return `${baseUrl}/cbus${userPrefix}${endpoint}${queryParams}`
}

// =============================================================================
// GENERIC QUERY BUILDER
// =============================================================================

/**
 * Creates a generic paginated query with common patterns
 */
export function createPaginatedQuery<
  TData,
  TRequestBody extends BaseRequestBody,
  TTag extends string,
  TResponse extends PaginatedApiResponse<TData> = PaginatedApiResponse<TData>
>(
  endpoint: string,
  options: {
    method?: 'GET' | 'POST'
    fields?: FieldConfiguration | string
    transformResponse?: (data: TData[]) => TData[]
    cacheConfig?: CacheConfig
    tags?: string[]
  } = {}
) {
  const {
    method = 'POST',
    fields,
    transformResponse = (data) => data,
    cacheConfig = { keepUnusedDataFor: 3600 },
    tags = []
  } = options

  return (builder: EndpointBuilder<BaseQueryFn, string, TTag>) => {
    return builder.query<TData[], BaseQueryOptions<TData, TRequestBody> | void>({
      queryFn: async (payload) => {
        const { params, isMobile, reqBody = {} as TRequestBody } = payload || {}

        const fetchFn = (currentPage: number) => {
          const axiosClient = getAxiosClient()
          const url = buildApiUrl(endpoint, { ...params }, { isMobile, fields })
          const requestBody = { ...reqBody, currentPage }

          return method === 'GET'
            ? axiosClient.get<TResponse>(url, { params: requestBody })
            : axiosClient.post<TResponse>(url, requestBody)
        }

        return handlePaginatedQuery<TData, TResponse>(fetchFn, {
          updatePartialData: payload?.updatePartialData,
          transformResponse: payload?.transformResponse || transformResponse
        })
      },
      ...cacheConfig,
      serializeQueryArgs: ({ queryArgs }) => {
        if (!queryArgs) return endpoint
        const { reqBody, params, isMobile } = queryArgs
        return `${endpoint}-${JSON.stringify({ isMobile, reqBody, params })}`
      },
      providesTags: tags.length > 0 ? tags : [endpoint]
    })
  }
}

// =============================================================================
// GENERIC MUTATION BUILDER
// =============================================================================

/**
 * Creates a generic mutation with common patterns
 */
export function createMutation<TResponse, TArgs, TTag extends string>(
  endpoint: string,
  options: {
    method?: 'POST' | 'PUT' | 'DELETE' | 'PATCH'
    fields?: FieldConfiguration | string
    invalidatesTags?: string[]
    transformResponse?: (data: unknown) => TResponse
  } = {}
) {
  const { method = 'POST', fields, invalidatesTags = [], transformResponse } = options

  return (builder: EndpointBuilder<BaseQueryFn, string, TTag>) => {
    return builder.mutation<TResponse, TArgs>({
      query: (args) => {
        const url = buildApiUrl(endpoint, {}, { fields })
        return {
          url,
          method,
          data: args
        }
      },
      ...(transformResponse && { transformResponse }),
      ...(invalidatesTags.length > 0 && { invalidatesTags })
    })
  }
}

// =============================================================================
// CACHE UTILITIES
// =============================================================================

/**
 * Standard cache configurations for different data types
 */
export const CACHE_CONFIGS = {
  // Fast-changing data (5 minutes)
  FIVE_MINUTES: { keepUnusedDataFor: 300 },

  // Medium-changing data (1 hour)
  ONE_HOUR: { keepUnusedDataFor: 3600 },

  // Slow-changing data (24 hours)
  ONE_DAY: { keepUnusedDataFor: 86400 },

  // Static data (indefinite)
  INDEFINITE: { keepUnusedDataFor: Number.POSITIVE_INFINITY }
} as const

/**
 * Creates a standardized cache key for queries
 */
export function createCacheKey(endpoint: string, args: Record<string, unknown> = {}): string {
  const sortedArgs = Object.keys(args)
    .sort()
    .reduce(
      (result, key) => {
        result[key] = args[key]
        return result
      },
      {} as Record<string, unknown>
    )

  return `${endpoint}-${JSON.stringify(sortedArgs)}`
}
