import { getAxiosClient, TokenType } from '@gc/api/client'
import { ccFieldsChannel } from '@gc/constants'
import { COMMERCE_CLOUD_API, GC_MIDDLEWARE_API } from '@gc/shared/env'
import { Cart, Quote, QuoteDetails, Quotes } from '@gc/types'
import { adjustDataForDisplay, adjustEntriesForDisplay, getParams, getUserPrefix, range } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'
import _, { noop } from 'lodash'

const quoteListFields = {
  MOBILE: 'ONEDCE_CBUS_MOBILE_MIN_LIGHT',
  DESKTOP: 'ONEDCE_CBUS_DESKTOP_MIN_LIGHT'
}

const getParamsStr = (paramsObj: object = {}, isMobile = false, fields: { MOBILE: string; DESKTOP: string }): string =>
  getParams(paramsObj, { isMobile, fields })
const urlPrefix = () => `${getUserPrefix()}/quotes`

export const getAllQuotesQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<
    Quote[],
    {
      reqBody?: {
        salesYear?: string[]
        soldToAccounts?: string[]
        statuses?: string[]
        agentSAPAccounts: string[]
      }
      params?: { currentPage?: number; fields?: string; pageSize?: number; lang?: string }
      isMobile?: boolean
      updatePartialQuotes?: (a: Quote[]) => void
    } | void
  >({
    // eslint-disable-next-line sonarjs/cognitive-complexity
    queryFn: async (payload) => {
      const { params, isMobile, updatePartialQuotes, reqBody = {} } = payload ?? {}
      const BATCH_SIZE = 3
      function fetchQuotes(currentPage = 0) {
        const axiosClient = getAxiosClient()
        const url = `${COMMERCE_CLOUD_API}/cbus${urlPrefix()}/bayer-quotes-light${getParamsStr(
          { ...params, currentPage },
          isMobile,
          quoteListFields
        )}`
        return axiosClient.post<Quotes>(url, reqBody)
      }

      try {
        const { data } = await fetchQuotes()
        const quotes = (data.quotes ?? []).map((quote) => adjustDataForDisplay(quote))
        if (updatePartialQuotes) {
          updatePartialQuotes(quotes)
        }

        const { totalPages, currentPage } = data.pagination
        if (totalPages - 1 > currentPage) {
          const pageRange = range(currentPage + 1, totalPages)

          // Process pages in batches
          for (let i = 0; i < pageRange.length; i += BATCH_SIZE) {
            const batch = pageRange.slice(i, i + BATCH_SIZE)
            const batchResults = await Promise.allSettled(batch.map((page) => fetchQuotes(page)))

            const batchQuotes: Quote[] = []
            for (const quotesResults of batchResults) {
              if (quotesResults.status === 'fulfilled') {
                const quotes = (quotesResults.value.data.quotes ?? []).map((quote) => adjustDataForDisplay(quote))
                batchQuotes.push(...quotes)
              } else {
                throw new Error(quotesResults.reason)
              }
            }

            quotes.push(...batchQuotes)
            if (updatePartialQuotes) {
              updatePartialQuotes(batchQuotes)
            }
          }
        }

        return { data: quotes }
      } catch (error) {
        return { error }
      }
    },
    keepUnusedDataFor: 3600,
    serializeQueryArgs: ({ queryArgs }) => {
      if (!queryArgs) return 'quotes'

      const { reqBody, params, isMobile } = queryArgs
      return `quotes-${JSON.stringify({
        isMobile,
        reqBody,
        pagination: params
      })}`
    }
  })
}

export const getQuoteDetailsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<
    QuoteDetails,
    {
      quoteId: string
      isMobile?: boolean
    }
  >({
    query: (payload) => ({
      url: `${urlPrefix()}/${payload.quoteId}` + getParamsStr({}, payload?.isMobile, ccFieldsChannel)
    }),
    transformResponse: (response: QuoteDetails) => {
      const modifiedQuoteDetails = _.cloneDeep(response)
      const updatedEntries = adjustEntriesForDisplay(modifiedQuoteDetails?.entries)
      modifiedQuoteDetails.entries = updatedEntries
      return modifiedQuoteDetails
    },
    providesTags: (_result, _error, arg) => [{ type: 'Quote', id: arg.quoteId }]
  })
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const updateQuote = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>, getApi: () => any) => {
  return builder.mutation<
    Quote | Cart,
    {
      quoteId: string
      reqBody?: {
        action: string
      }
      params?: { fields?: string; lang?: string }
      isMobile?: boolean
      skipQuoteDetailsRefetch?: boolean
    }
  >({
    query: (payload) => ({
      url:
        `${urlPrefix()}/${payload?.quoteId}/action` + getParamsStr(payload?.params, payload?.isMobile, ccFieldsChannel),
      method: 'POST',
      data: payload?.reqBody || {}
    }),
    async onQueryStarted({ quoteId, reqBody = {} }, { dispatch, queryFulfilled, getState }) {
      const api = getApi()

      const patchedResults = [
        dispatch(
          api.util.updateQueryData('getQuoteDetails', quoteId, (draft: QuoteDetails) => Object.assign(draft, reqBody))
        )
      ]

      if (reqBody?.action === 'CANCEL') {
        // Cancel is DELETE quote, so we need to remove it from the quotes list
        for (const [, info] of Object.entries(getState().ccApi.queries || {})) {
          if (info?.endpointName === 'getAllQuotes') {
            const quotes = info?.data as Quote[]
            const matchingQuote = quotes?.find((q) => q.code === quoteId)
            if (matchingQuote) {
              patchedResults.push(
                dispatch(
                  api.util.updateQueryData('getAllQuotes', info.originalArgs, () => {
                    return quotes.filter((q) => q.code !== quoteId)
                  })
                )
              )
            }
          }
        }
        queryFulfilled.then(() => dispatch({ type: 'quotes/setLastDeletedQuote', payload: quoteId })).catch(noop)
      } else if (reqBody?.action === 'SUBMIT') {
        // Submit is CREATE or UPDATE quote, so we need to update the quotes list accordingly
        queryFulfilled
          .then((res) => {
            for (const [, info] of Object.entries(getState().ccApi.queries || {})) {
              if (info?.endpointName === 'getAllQuotes') {
                const quotes = _.cloneDeep(info.data) as Quote[]
                const matchingQuoteIndex = quotes.findIndex((q) => q.code === quoteId)
                if (matchingQuoteIndex === -1) {
                  // Update the quotes list with the new quote created for CREATE action
                  quotes.unshift(res.data as Quote)
                } else {
                  // Update the quotes list with the updated quote created for UPDATE action
                  quotes[matchingQuoteIndex] = res.data as Quote
                }
                dispatch(api.util.updateQueryData('getAllQuotes', info.originalArgs, () => quotes))
              }
            }
          })
          .catch(noop)
      }

      queryFulfilled.catch(() => _.forEach(patchedResults, (patchResult) => patchResult.undo()))
    },
    invalidatesTags: (_result, _error, arg) => {
      if (arg.reqBody?.action === 'SUBMIT' && !_error && !arg.skipQuoteDetailsRefetch) {
        return [{ type: 'Quote', id: arg.quoteId }]
      } else {
        return []
      }
    }
  })
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const cartToQuoteMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>, getApi: () => any) => {
  return builder.mutation<
    Quote,
    {
      cartId: string
      updateQuotesList?: boolean
    }
  >({
    query: ({ cartId }) => ({
      url: `${urlPrefix()}?fields=ONEDCE_CBUS`,
      method: 'POST',
      data: { cartId }
    }),
    async onQueryStarted({ updateQuotesList = false }, { dispatch, queryFulfilled, getState }) {
      if (updateQuotesList) {
        const api = getApi()
        queryFulfilled
          .then((res) => {
            for (const [, info] of Object.entries(getState().ccApi.queries || {})) {
              if (info?.endpointName === 'getAllQuotes') {
                const quotes = _.cloneDeep(info.data) as Quote[]
                quotes.unshift(res.data)
                dispatch(api.util.updateQueryData('getAllQuotes', info.originalArgs, () => quotes))
              }
            }
          })
          .catch(noop)
      }
    }
  })
}

export const duplicateQuoteMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<Quote, string>({
    query: (quoteCode) => ({
      url: `${urlPrefix()}?fields=ONEDCE_CBUS`,
      method: 'POST',
      data: { quoteCode }
    })
  })
}

export const quotePDFMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<Quote, string>({
    query: (quoteCode) => ({
      url: `${GC_MIDDLEWARE_API}/agency/quote-pdf/${quoteCode}`,
      method: 'GET',
      data: { quoteCode }
    }),
    extraOptions: { tokenType: TokenType.Gigya }
  })
}
