import { InventoryPackagingMaterialsResponse, ProductInventory } from '@gc/types'
import { getUserPrefix } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'
import { noop } from 'lodash'

export const getPackagingMaterialsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<
    InventoryPackagingMaterialsResponse,
    {
      lob?: string
      agents: string[]
      fromLocation: string
      seedYear: string
    }
  >({
    query: (body) => ({
      url: `${getUserPrefix()}/inventory/packaging-materials?fields=ONEDCE_CBUS`,
      method: 'POST',
      data: { ...body, lob: 'SEED' }
    }),
    onQueryStarted: async ({ fromLocation }, { dispatch, queryFulfilled }) => {
      try {
        const response = await queryFulfilled
        const inventories: ProductInventory[] = []
        response.data.inventory.forEach((inventory) => {
          inventories.push({
            code: inventory.product.code,
            storageLocationCode: fromLocation,
            available: inventory.product.available
          })
        })
        // We are storing inventories in store to keep track of the available inventory as user creates/modifies the delivery!!
        dispatch({ type: 'inventory/addProductInventories', payload: inventories })
      } catch {
        noop()
      }
    }
  })
}
