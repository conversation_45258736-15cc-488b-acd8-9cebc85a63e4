{"name": "rtk-queries", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/api/rtk-queries/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/api/rtk-queries/jest.config.ts"}}}}