/* empty */

// Split each section into a separate div
.container {
  gap: 16px;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  & > * {
    background: #ffffff;

    &:not(:first-child) {
      padding: 16px;
    }
  }

  // Add a subtle divider between each section, mimicking a card cut-off
  & > *:not(:last-child) {
    border-bottom: 1px solid var(--lmnt-theme-outline-variant, rgba(16, 56, 79, 0.12));
  }
}
