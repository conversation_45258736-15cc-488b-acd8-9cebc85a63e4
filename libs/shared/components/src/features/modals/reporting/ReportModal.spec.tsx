import { act, render } from '@gc/utils'
import { screen } from '@testing-library/react'
import { FieldValues, FormProviderProps, UseFormHandleSubmit } from 'react-hook-form'

import { ReportModal, ReportModalProps } from './ReportModal'

// Mock styles import
jest.mock('./ReportModal.module.scss', () => ({
  container: 'container'
}))

// Mock hooks and redux
jest.mock('@gc/hooks', () => ({
  useModal: () => ({ closeModal: jest.fn() }),
  useMemoizedTranslation: () => (key: string) => key,
  useSelectedAccount: () => ({ sapAccountId: '123', accountName: 'Test Account' })
}))
jest.mock('@gc/redux-store', () => ({
  useGlobalDispatch: () => jest.fn(),
  setContingency: jest.fn(),
  setLoadingContingency: jest.fn(),
  useReportsQueries: () => ({
    useCreateUserReportMutation: () => [jest.fn().mockReturnValue(Promise.resolve()), { isLoading: false }],
    useUpdateUserReportMutation: () => [jest.fn().mockReturnValue(Promise.resolve()), { isLoading: false }],
    useGetUserReportQuery: () => [jest.fn().mockReturnValue(Promise.resolve()), { isLoading: false }]
  })
}))

// Mock form and form provider
jest.mock('react-hook-form', () => {
  const actual = jest.requireActual('react-hook-form')
  return {
    ...actual,
    useForm: () => {
      const formState = { isValid: true }
      const handleSubmit: UseFormHandleSubmit<FieldValues> = (cb) => async (e) => {
        if (e) await cb(e)
      }

      return {
        formState,
        handleSubmit,
        ...actual.useForm()
      }
    },
    FormProvider: ({ children }: FormProviderProps) => <div data-testid='form-provider'>{children}</div>
  }
})

// Mock children components
jest.mock('../../../ui-common/header/TopAppBar', () => ({
  __esModule: true,
  default: (props: { title: string }) => <div data-testid='top-app-bar'>{props.title}</div>
}))
jest.mock('../../../ui-common/modal/ModalActionSlot', () => ({
  __esModule: true,
  default: (props: {
    primaryAction: { disabled: boolean; className: string; label: string }
    secondaryAction: { className: string; label: string; onClick: () => void }
  }) => (
    <div>
      <button
        data-testid='save-btn'
        disabled={props.primaryAction?.disabled}
        className={props.primaryAction?.className}
      >
        {props.primaryAction?.label}
      </button>
      <button
        data-testid='discard-btn'
        className={props.secondaryAction?.className}
        onClick={props.secondaryAction?.onClick}
      >
        {props.secondaryAction?.label}
      </button>
    </div>
  )
}))

jest.mock('./common/ReportDescriptionForm', () => ({
  ReportDescriptionForm: () => <div data-testid='report-description-form'>ReportDescriptionForm</div>
}))

jest.mock('./common/SelectReportType', () => ({
  SelectReportType: () => <div data-testid='select-report-type'>SelectReportType</div>
}))

jest.mock('./common/ReportFields', () => ({
  ReportFields: () => <div data-testid='report-fields'>ReportFields</div>
}))

const defaultProps: ReportModalProps = {
  setModalProps: jest.fn(),
  navigateProps: {
    icon: 'close',
    handleClose: jest.fn()
  }
}

describe('ReportModal', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('calls setModalProps with correct values', async () => {
    const setModalProps = jest.fn()
    await act(async () => {
      render(<ReportModal {...defaultProps} setModalProps={setModalProps} />)
    })
    // setModalProps should have been called at least once
    expect(setModalProps).toHaveBeenCalled()
    const call = setModalProps.mock.calls[0][0]
    expect(call.sectionPadding).toBe('none')
    expect(call.footerActions).toBeTruthy()
    expect(call.headerActions).toBeTruthy()
    // Check that footerActions and headerActions are React elements
    expect(typeof call.footerActions).toBe('object')
    expect(typeof call.headerActions).toBe('object')
  })

  it('renders the modal with header and form components', async () => {
    await act(async () => {
      render(<ReportModal {...defaultProps} />)
    })
    expect(screen.getByTestId('form-provider')).toBeInTheDocument()
    expect(screen.getByTestId('select-report-type')).toBeInTheDocument()
    expect(screen.getByTestId('report-description-form')).toBeInTheDocument()
    expect(screen.getByTestId('report-fields')).toBeInTheDocument()
  })

  it('renders the modal container with correct ID', async () => {
    await act(async () => {
      render(<ReportModal {...defaultProps} />)
    })
    expect(screen.getByTestId('form-provider').parentElement).toHaveAttribute('id', 'report-modal')
  })
})
