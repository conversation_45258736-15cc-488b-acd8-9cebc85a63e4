# Reporting Modal Components

This module contains the reporting modal components for creating and managing custom reports across different brand applications (Channel, MyCrop, Seedsman, Australia).

## Components Overview

### Core Components

- **ReportModal** - Main modal component for creating/editing reports
- **ReportDescriptionForm** - Form component for report name and description
- **ReportFields** - Component for selecting and managing report fields
- **SelectReportType** - Component for selecting the type of report to create

## Architecture

The reporting modal system follows the monorepo's shared component architecture, providing reusable components that can be integrated across different brand applications. The components use Redux Toolkit with RTK Query for state management and API interactions.

### Report Type Table DTO (DynamoDB Model)

This table will store the configuration for each report type (e.g., "Orders", "Customers").

**Table Name:** `ReportTypeConfiguration`

**Partition Key (PK):** `reportTypeId` (String) - A unique identifier for the report type (e.g., "ORDERS", "CUSTOMERS").

**Attributes:**

- `reportTypeId`: String (PK)
- `name`: String (e.g., "Orders", "Customers")
- `description`: String (Optional, helpful for user understanding)
- `availableFields`: List of `ReportFieldDefinition` DTO
- `availableFilters`: List of `ReportFilterDefinition` DTO

**Nested DTOs:**

#### `ReportFieldDefinition` DTO

This defines a field that can be selected for a report.

- `fieldId`: String (Unique identifier for the field within this report type, e.g., "orderDate", "customerName")
- `displayName`: String (User-friendly name, e.g., "Order Date", "Customer Name")
- `dataType`: String (e.g., "STRING", "NUMBER", "DATE", "BOOLEAN") - Important for rendering and filtering logic.
- `isFilterable`: Boolean (Indicates if this field can be used as a filter)
- `filterOptions`: List of `FilterOption` DTO (Only present if `isFilterable` is true)

#### `FilterOption` DTO

This defines an available option for a specific filterable field. For example, for a "Status" field, options might be "Pending", "Shipped", "Delivered".

- `optionId`: String (Unique identifier for the filter option, e.g., "PENDING", "SHIPPED")
- `displayValue`: String (User-friendly value, e.g., "Pending Orders", "Shipped Orders")
- `internalValue`: String (The actual value to use in queries, e.g., "PENDING", "SHIPPED")

#### `ReportFilterDefinition` DTO

This defines a pre-defined filter that can be applied to a report. This is distinct from `ReportFieldDefinition`'s `filterOptions` in that it represents broader filter categories or pre-built complex filters.

- `filterId`: String (Unique identifier for the filter within this report type, e.g., "last30Days", "highValueCustomers")
- `displayName`: String (User-friendly name, e.g., "Last 30 Days", "High Value Customers")
- `description`: String (Optional, for more detail)
- `applicableFields`: List of String (List of `fieldId`s that this filter can apply to, e.g., ["orderDate"])
- `filterType`: String (e.g., "DATE_RANGE", "ENUM_SELECTION", "FREE_TEXT") - Helps UI render the correct input.
- `filterConfig`: Object (JSON object for specific filter configuration, e.g., `{ "operator": "GREATER_THAN", "value": 100 }` for a "High Value Customer" filter, or `{ "range": "LAST_30_DAYS" }` for a date range filter.)

---

### User Report Submission Table DTO (DynamoDB Model)

This table stores the specific reports created by users, referencing the report type configuration.

**Table Name:** `UserReports`

**Partition Key (PK):** `reportId` (String) - A unique identifier for this specific user-created report.

**Attributes:**

- `reportId`: String (PK)
- `userId`: String (ID of the user who created the report)
- `reportName`: String (User-defined name for the report)
- `reportTypeId`: String (References `reportTypeId` from `ReportTypeConfiguration` table)
- `selectedFields`: List of `SelectedField` DTO
- `selectedFilters`: List of `SelectedFilter` DTO
- `createdAt`: String (ISO 8601 timestamp)
- `lastModifiedAt`: String (ISO 8601 timestamp)

**Nested DTOs:**

#### `SelectedField` DTO

This represents a field the user has chosen for their report.

- `fieldId`: String (References `fieldId` from `ReportFieldDefinition`)
- `displayOrder`: Number (Optional, for controlling column order in the UI)
- `isHidden`: Boolean (Optional, if a field is selected but not visible by default)

#### `SelectedFilter` DTO

This represents a filter the user has applied to their report.

- `filterId`: String (References `filterId` from `ReportFilterDefinition` if a pre-defined filter is used, or a generated ID for custom filters)
- `fieldId`: String (The field this filter applies to, e.g., "orderDate", "status")
- `filterType`: String (e.g., "DATE_RANGE", "ENUM_SELECTION", "FREE_TEXT")
- `selectedOptions`: Array of Strings (The options chosen by the user. E.g., `["PENDING", "SHIPPED"]`.)
- `displayName`: String (Optional, for displaying a summary of the filter applied in the UI)

---

## System Design

Here's a high-level system design, focusing on the interactions and components.

### Core

1. **Frontend (UI/Client Application):**
   - Handles user interaction for creating, editing, viewing, and applying reports.
   - Makes API calls to the Backend Service.
   - Renders reports based on the `selectedFields` and `selectedFilters` data.

2. **Backend Service (API Gateway + Lambda/EC2/Container Service):**
   - Exposes RESTful or GraphQL APIs for report management.
   - Interacts with DynamoDB for data persistence.
   - Performs validation, authorization, and business logic.
   - Coordinates with the "Data Retrieval Service" to fetch actual report data.

3. **DynamoDB:**
   - `ReportTypeConfiguration` table: Stores report type definitions, available fields, and available filters.
   - `UserReports` table: Stores user-created report instances (metadata, selected fields, selected filters).

4. **Data Retrieval Service:**
   - Responsible for fetching the actual application data (Orders, Customers, etc.) from their respective data sources (e.g., your primary application database).
   - Receives `reportTypeId`, `selectedFields`, and `selectedFilters` as input from the Backend Service.
   - Translates the selected fields and filters into queries specific to the underlying data source.
   - Returns the raw data to the Backend Service. This could be a separate microservice, or functions within the same backend.

### Workflow

#### 1. Initial Setup/Configuration (Admin/Developer)

- An admin or developer populates the `ReportTypeConfiguration` table with definitions for "Orders", "Customers", etc., including all `availableFields` and `availableFilters`. This is a one-time or infrequent setup.

#### 2. User Creates a New Report

- **UI:** User clicks "Create Report".
- **UI:** User selects a `reportType` (e.g., "Orders").
- **Backend API Call:** UI calls `GET /report-types/{reportTypeId}` to fetch the `ReportTypeConfiguration` for "Orders".
- **Backend Service:** Retrieves data from `ReportTypeConfiguration` table.
- **UI:** Renders the modal with `reportName` input, and lists `availableFields` and `availableFilters` based on the fetched `ReportTypeConfiguration`.
- **UI:** For each `availableField`, if `isFilterable` is true, it displays filter options based on `filterOptions`.
- **UI:** User selects fields, potentially reorders them, and applies filters.
- **UI:** User enters a `reportName`.
- **Backend API Call:** UI calls `POST /reports` with the `reportName`, `reportTypeId`, `selectedFields`, and `selectedFilters` (as per the `UserReportSubmission` DTO).
- **Backend Service:**
  - Validates the input against the `ReportTypeConfiguration` (e.g., ensure `selectedFields` and `selectedFilters` are valid for the `reportTypeId`).
  - Generates a unique `reportId`.
  - Saves the `UserReportSubmission` DTO to the `UserReports` table.
  - Returns the `reportId` to the UI.

#### 3. User Views/Applies a Report (e.g., on an "Orders" table screen)

- **UI:** User navigates to the "Orders" section. The UI needs to know which report to apply. This could be a default report, or the user selects from a list of their created reports.
- **Backend API Call:** UI calls `GET /reports/{reportId}` to retrieve the `UserReportSubmission` DTO (metadata, selected fields, selected filters).
- **Backend Service:** Retrieves data from `UserReports` table.
- **Backend API Call:** Based on the retrieved `UserReportSubmission`, the Backend Service calls the `Data Retrieval Service`.
  - `POST /data/query` with `reportTypeId`, `selectedFields`, and `selectedFilters`.
- **Data Retrieval Service:**
  - Receives the request.
  - Translates the `selectedFields` into column projections for the underlying orders database.
  - Translates the `selectedFilters` into `WHERE` clauses for the underlying orders database.
  - Executes the query against the primary application's orders database.
  - Returns the raw orders data.
- **Backend Service:** Receives the raw data, potentially transforms it (e.g., renames columns to `displayName` from `fieldId`), and sends it back to the UI.
- **UI:** Renders the orders table with the specified columns and filtered data. The UI can also use the `selectedFilters` to display active filters to the user.

#### 4. Editing an Existing Report

- Similar to creating, but the UI would pre-populate the modal with the existing `UserReportSubmission` data.
- **Backend API Call:** UI calls `PUT /reports/{reportId}` with the updated `UserReportSubmission` DTO.
- **Backend Service:** Validates and updates the record in the `UserReports` table.

### DynamoDB Considerations

- **Single-Table Design:** While you mentioned two tables, consider if a single-table design could be beneficial for certain access patterns in DynamoDB, especially if you foresee needing to query reports and their types together frequently in complex ways. For this specific use case, two tables are straightforward and likely sufficient.
- **Access Patterns:**
  - `ReportTypeConfiguration`: Primarily accessed by `reportTypeId` (PK lookup).
  - `UserReports`: Primarily accessed by `reportId` (PK lookup), but you might also need to query `byUserId` to list all reports for a user. This would require a GSI (Global Secondary Index) on `userId`.
    - **GSI on `UserReports`:**
      - **Partition Key:** `userId`
      - **Sort Key (Optional but recommended):** `createdAt` (to list user's reports by creation date)
- **Data Size:** DynamoDB is great for high-throughput, low-latency key-value access. Ensure the size of your `availableFields` and `availableFilters` lists (and thus `selectedFields` and `selectedFilters`) doesn't push individual item sizes beyond DynamoDB's limits (400KB). For very large configurations, consider breaking them down or storing them in S3 with a reference in DynamoDB. Given typical reporting needs, this is unlikely to be an issue.

This design provides a solid foundation for your reporting system, separating concerns and leveraging DynamoDB effectively. Remember to consider authentication and authorization throughout the API endpoints.
