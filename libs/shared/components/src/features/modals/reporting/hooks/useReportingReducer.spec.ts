import { SelectedFilter } from '@gc/types'
import { act, renderHook } from '@testing-library/react'

import { useReportingReducer } from './useReportingReducer'

describe('useReportingReducer', () => {
  const mockFilterDefinition = {
    filterId: 'testFilter',
    fieldId: 'testField',
    displayName: 'Test Filter',
    filterType: 'enum' as const,
    options: []
  }

  const mockSelectedOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' }
  ]

  describe('initialization', () => {
    it('should initialize with empty filters when no initial filters provided', () => {
      const { result } = renderHook(() => useReportingReducer())

      expect(result.current.appliedFilters).toEqual({})
    })

    it('should initialize with provided initial filters', () => {
      const initialFilters: SelectedFilter = {
        filter1: [{ value: 'value1', label: 'Label 1' }]
      }

      const { result } = renderHook(() => useReportingReducer(initialFilters))

      expect(result.current.appliedFilters).toEqual(initialFilters)
    })
  })

  describe('UPDATE_FILTER action', () => {
    it('should add a new filter when filter does not exist', () => {
      const { result } = renderHook(() => useReportingReducer())

      act(() => {
        result.current.dispatch({
          type: 'UPDATE_FILTER',
          payload: {
            ...mockFilterDefinition,
            filterSelections: {
              testFilter: mockSelectedOptions
            }
          }
        })
      })

      expect(result.current.appliedFilters).toEqual({
        testFilter: mockSelectedOptions
      })
    })

    it('should update existing filter', () => {
      const initialFilters: SelectedFilter = {
        testFilter: [{ value: 'oldValue', label: 'Old Value' }]
      }

      const { result } = renderHook(() => useReportingReducer(initialFilters))

      act(() => {
        result.current.dispatch({
          type: 'UPDATE_FILTER',
          payload: {
            ...mockFilterDefinition,
            filterSelections: {
              testFilter: mockSelectedOptions
            }
          }
        })
      })

      expect(result.current.appliedFilters).toEqual({
        testFilter: mockSelectedOptions
      })
    })

    it('should remove filter when empty selections provided', () => {
      const initialFilters: SelectedFilter = {
        testFilter: mockSelectedOptions,
        otherFilter: [{ value: 'keep', label: 'Keep' }]
      }

      const { result } = renderHook(() => useReportingReducer(initialFilters))

      act(() => {
        result.current.dispatch({
          type: 'UPDATE_FILTER',
          payload: {
            ...mockFilterDefinition,
            filterSelections: {
              testFilter: []
            }
          }
        })
      })

      expect(result.current.appliedFilters).toEqual({
        otherFilter: [{ value: 'keep', label: 'Keep' }]
      })
    })

    it('should handle missing payload gracefully and log warning', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      const { result } = renderHook(() => useReportingReducer())

      act(() => {
        result.current.dispatch({
          type: 'UPDATE_FILTER'
        })
      })

      expect(result.current.appliedFilters).toEqual({})
      expect(consoleSpy).toHaveBeenCalledWith('No payload provided to update filter')

      consoleSpy.mockRestore()
    })

    it('should maintain state immutability', () => {
      const initialFilters: SelectedFilter = {
        existingFilter: [{ value: 'existing', label: 'Existing' }]
      }

      const { result } = renderHook(() => useReportingReducer(initialFilters))
      const initialState = result.current.appliedFilters

      act(() => {
        result.current.dispatch({
          type: 'UPDATE_FILTER',
          payload: {
            ...mockFilterDefinition,
            filterSelections: {
              testFilter: mockSelectedOptions
            }
          }
        })
      })

      expect(result.current.appliedFilters).not.toBe(initialState)
      expect(initialState).toEqual({ existingFilter: [{ value: 'existing', label: 'Existing' }] })
    })
  })

  describe('CLEAR_FILTER action', () => {
    it('should remove specific filter', () => {
      const initialFilters: SelectedFilter = {
        filter1: mockSelectedOptions,
        filter2: [{ value: 'keep', label: 'Keep' }]
      }

      const { result } = renderHook(() => useReportingReducer(initialFilters))

      act(() => {
        result.current.dispatch({
          type: 'CLEAR_FILTER',
          payload: {
            ...mockFilterDefinition,
            filterId: 'filter1',
            filterSelections: {}
          }
        })
      })

      expect(result.current.appliedFilters).toEqual({
        filter2: [{ value: 'keep', label: 'Keep' }]
      })
    })

    it('should handle clearing non-existent filter gracefully', () => {
      const initialFilters: SelectedFilter = {
        existingFilter: mockSelectedOptions
      }

      const { result } = renderHook(() => useReportingReducer(initialFilters))

      act(() => {
        result.current.dispatch({
          type: 'CLEAR_FILTER',
          payload: {
            ...mockFilterDefinition,
            filterId: 'nonExistentFilter',
            filterSelections: {}
          }
        })
      })

      expect(result.current.appliedFilters).toEqual(initialFilters)
    })

    it('should handle missing payload gracefully and log warning', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      const initialFilters: SelectedFilter = {
        testFilter: mockSelectedOptions
      }

      const { result } = renderHook(() => useReportingReducer(initialFilters))

      act(() => {
        result.current.dispatch({
          type: 'CLEAR_FILTER'
        })
      })

      expect(result.current.appliedFilters).toEqual(initialFilters)
      expect(consoleSpy).toHaveBeenCalledWith('No payload provided to clear filter')

      consoleSpy.mockRestore()
    })
  })

  describe('RESET_FILTERS action', () => {
    it('should reset all filters to empty state', () => {
      const initialFilters: SelectedFilter = {
        filter1: mockSelectedOptions,
        filter2: [{ value: 'value', label: 'Label' }],
        filter3: [{ value: 'another', label: 'Another' }]
      }

      const { result } = renderHook(() => useReportingReducer(initialFilters))

      act(() => {
        result.current.dispatch({
          type: 'RESET_FILTERS'
        })
      })

      expect(result.current.appliedFilters).toEqual({})
    })

    it('should work when already empty', () => {
      const { result } = renderHook(() => useReportingReducer())

      act(() => {
        result.current.dispatch({
          type: 'RESET_FILTERS'
        })
      })

      expect(result.current.appliedFilters).toEqual({})
    })
  })

  describe('unknown action', () => {
    it('should return current state for unknown action types', () => {
      const initialFilters: SelectedFilter = {
        testFilter: mockSelectedOptions
      }

      const { result } = renderHook(() => useReportingReducer(initialFilters))

      act(() => {
        result.current.dispatch({
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          type: 'UNKNOWN_ACTION' as any
        })
      })

      expect(result.current.appliedFilters).toEqual(initialFilters)
    })
  })

  describe('dispatch function stability', () => {
    it('should maintain dispatch function reference across renders', () => {
      const { result, rerender } = renderHook(() => useReportingReducer())
      const firstDispatch = result.current.dispatch

      rerender()

      expect(result.current.dispatch).toBe(firstDispatch)
    })
  })
})
