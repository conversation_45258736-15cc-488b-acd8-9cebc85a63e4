import { DragEndEvent } from '@dnd-kit/core'
import { ReportFieldDefinition, ReportFilterDefinition, SelectedFilter } from '@gc/types'
import { act, renderHook } from '@testing-library/react'
import { isEqual } from 'lodash'
import { Control, FieldValues, FormState, useFormContext } from 'react-hook-form'

import { convertFormFiltersToSelectedFilter, formatSelectedFields } from '../lib/utils'
import useReportFields from './useReportFields'
import { useReportingReducer } from './useReportingReducer'

jest.mock('@dnd-kit/sortable', () => ({
  arrayMove: jest.fn((array, oldIndex, newIndex) => {
    const result = [...array]
    const [removed] = result.splice(oldIndex, 1)
    result.splice(newIndex, 0, removed)
    return result
  })
}))

jest.mock('lodash', () => ({
  isEqual: jest.fn()
}))

jest.mock('./useReportingReducer', () => ({
  useReportingReducer: jest.fn()
}))

jest.mock('../lib/utils', () => ({
  convertFormFiltersToSelectedFilter: jest.fn(),
  formatSelectedFields: jest.fn()
}))

// Mock react-hook-form at the module level
jest.mock('react-hook-form', () => ({
  useFormContext: jest.fn()
}))

describe('useReportFields', () => {
  const mockSetValue = jest.fn()
  const mockGetValues = jest.fn()
  const mockDispatch = jest.fn()
  const mockWatch = jest.fn()
  const mockUseFormContext = useFormContext as jest.MockedFunction<typeof useFormContext>

  const mockReportFields: ReportFieldDefinition[] = [
    {
      fieldId: 'field1',
      displayName: 'Field One',
      dataType: 'string',
      accessor: 'field1',
      isHidden: false,
      isRequired: false,
      isFilterable: true
    },
    {
      fieldId: 'field2',
      displayName: 'Field Two',
      dataType: 'number',
      accessor: 'field2',
      isHidden: false,
      isRequired: false,
      isFilterable: true
    }
  ]

  const mockReportFilters: ReportFilterDefinition[] = [
    {
      filterId: 'filter1',
      fieldId: 'field1',
      displayName: 'Filter One',
      filterType: 'enum',
      filterOptions: [
        { optionId: 'option1', displayValue: 'Option 1', internalValue: 'option1' },
        { optionId: 'option2', displayValue: 'Option 2', internalValue: 'option2' }
      ]
    }
  ]

  const mockUseReportingReducer = useReportingReducer as jest.MockedFunction<typeof useReportingReducer>
  const mockIsEqual = isEqual as jest.MockedFunction<typeof isEqual>
  const mockConvertFormFiltersToSelectedFilter = convertFormFiltersToSelectedFilter as jest.MockedFunction<
    typeof convertFormFiltersToSelectedFilter
  >
  const mockFormatSelectedFields = formatSelectedFields as jest.MockedFunction<typeof formatSelectedFields>

  beforeEach(() => {
    jest.clearAllMocks()

    mockUseFormContext.mockReturnValue({
      watch: mockWatch,
      reset: jest.fn(),
      trigger: jest.fn(),
      register: jest.fn(),
      setError: jest.fn(),
      setFocus: jest.fn(),
      subscribe: jest.fn(),
      unregister: jest.fn(),
      resetField: jest.fn(),
      setValue: mockSetValue,
      clearErrors: jest.fn(),
      handleSubmit: jest.fn(),
      getValues: mockGetValues,
      getFieldState: jest.fn(),
      control: {} as Control<FieldValues>,
      formState: { errors: {} } as FormState<FieldValues>
    })

    mockUseReportingReducer.mockReturnValue({
      appliedFilters: {},
      dispatch: mockDispatch
    })

    mockGetValues.mockReturnValue([])
    mockConvertFormFiltersToSelectedFilter.mockReturnValue({})
    mockFormatSelectedFields.mockReturnValue([])
    mockIsEqual.mockReturnValue(false)
  })

  describe('initialization', () => {
    it('should initialize with form data when available', () => {
      const mockSelectedFields = [{ fieldId: 'field1', displayOrder: 0, displayName: 'Field One' }]

      mockGetValues.mockReturnValue(mockSelectedFields)

      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      expect(result.current.fieldState.selectedColumns.has('field1')).toBe(true)
      expect(result.current.fieldState.columns).toEqual(mockReportFields)
    })

    it('should initialize with empty selection when no form data', () => {
      mockGetValues.mockReturnValue([])

      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      expect(result.current.fieldState.selectedColumns.size).toBe(0)
      expect(result.current.fieldState.columns).toEqual(mockReportFields)
    })

    it('should create filters map from selected category filters', () => {
      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      expect(result.current.filtersMap.get('filter1')).toEqual(mockReportFilters[0])
    })

    it('should create field to filter mapping', () => {
      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      expect(result.current.fieldToFilterMap.get('field1')).toBe('filter1')
    })
  })

  describe('getFilteredColumns', () => {
    it('should filter columns by search value', () => {
      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      const filteredColumns = result.current.getFilteredColumns('one')

      expect(filteredColumns).toEqual([mockReportFields[0]])
    })

    it('should be case insensitive', () => {
      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      const filteredColumns = result.current.getFilteredColumns('ONE')

      expect(filteredColumns).toEqual([mockReportFields[0]])
    })

    it('should return all columns when search is empty', () => {
      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      const filteredColumns = result.current.getFilteredColumns('')

      expect(filteredColumns).toEqual(mockReportFields)
    })

    it('should maintain function reference stability', () => {
      const { result, rerender } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))
      const firstFunction = result.current.getFilteredColumns

      rerender()

      expect(result.current.getFilteredColumns).toBe(firstFunction)
    })
  })

  describe('handleFieldFilterUpdate', () => {
    it('should dispatch UPDATE_FILTER action with correct payload', () => {
      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      const filterSelections: SelectedFilter = {
        filter1: [{ value: 'option1', label: 'Option 1' }]
      }

      act(() => {
        result.current.handleFieldFilterUpdate('field1', filterSelections)
      })

      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'UPDATE_FILTER',
        payload: {
          ...mockReportFilters[0],
          fieldId: 'field1',
          filterSelections
        }
      })
    })

    it('should handle field with no corresponding filter', () => {
      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      const filterSelections: SelectedFilter = {
        nonExistentFilter: [{ value: 'option1', label: 'Option 1' }]
      }

      act(() => {
        result.current.handleFieldFilterUpdate('nonExistentField', filterSelections)
      })

      expect(mockDispatch).not.toHaveBeenCalled()
    })

    it('should handle filter definition not found', () => {
      const filtersWithMissingDefinition = [
        {
          ...mockReportFilters[0],
          filterId: 'missingFilter'
        }
      ]

      const { result } = renderHook(() => useReportFields(mockReportFields, filtersWithMissingDefinition))

      const filterSelections: SelectedFilter = {
        filter1: [{ value: 'option1', label: 'Option 1' }]
      }

      act(() => {
        result.current.handleFieldFilterUpdate('field1', filterSelections)
      })

      expect(mockDispatch).toHaveBeenCalled()
    })
  })

  describe('handleCheckItem', () => {
    it('should add field when not selected', () => {
      mockGetValues.mockReturnValue([])

      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      act(() => {
        result.current.handleCheckItem('field1')
      })

      expect(mockSetValue).toHaveBeenCalledWith('selectedFields', [{ ...mockReportFields[0], displayOrder: 0 }], {
        shouldDirty: true,
        shouldValidate: true
      })
    })

    it('should remove field when already selected', () => {
      const existingSelectedFields = [
        { fieldId: 'field1', displayOrder: 0 },
        { fieldId: 'field2', displayOrder: 1 }
      ]

      mockGetValues.mockReturnValue(existingSelectedFields)

      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      act(() => {
        result.current.handleCheckItem('field1')
      })

      expect(mockSetValue).toHaveBeenCalledWith('selectedFields', [{ fieldId: 'field2', displayOrder: 1 }], {
        shouldDirty: true,
        shouldValidate: true
      })
    })

    it('should handle unknown field gracefully', () => {
      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      act(() => {
        result.current.handleCheckItem('unknownField')
      })

      // Effect still runs to update selectedFilters
      expect(mockSetValue).toHaveBeenCalledWith('selectedFilters', [], {
        shouldDirty: true,
        shouldValidate: true
      })
    })

    it('should maintain correct display order when adding fields', () => {
      const existingSelectedFields = [{ fieldId: 'field1', displayOrder: 0 }]

      mockGetValues.mockReturnValue(existingSelectedFields)

      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      act(() => {
        result.current.handleCheckItem('field2')
      })

      expect(mockSetValue).toHaveBeenCalledWith(
        'selectedFields',
        [...existingSelectedFields, { ...mockReportFields[1], displayOrder: 1 }],
        { shouldDirty: true, shouldValidate: true }
      )
    })
  })

  describe('handleDragEnd', () => {
    it('should reorder fields on successful drag', () => {
      const selectedFields = [mockReportFields[0], mockReportFields[1]]
      const mockDragEvent: DragEndEvent = {
        active: {
          id: 'field1',
          data: {
            current: {}
          },
          rect: {
            current: {
              initial: { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 },
              translated: { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 }
            }
          }
        },
        over: {
          disabled: false,
          id: 'field2',
          data: { current: {} },
          rect: { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 }
        },
        activatorEvent: {} as Event,
        delta: { x: 0, y: 0 },
        collisions: []
      }

      mockFormatSelectedFields.mockReturnValue([
        { ...mockReportFields[1], displayOrder: 0 },
        { ...mockReportFields[0], displayOrder: 1 }
      ])

      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      act(() => {
        result.current.handleDragEnd(mockDragEvent, selectedFields)
      })

      expect(mockSetValue).toHaveBeenCalledWith('selectedFields', expect.any(Array), {
        shouldDirty: true,
        shouldValidate: true
      })
    })

    it('should handle drag with no over target', () => {
      const selectedFields = [mockReportFields[0]]
      const mockDragEvent: DragEndEvent = {
        active: {
          id: 'field1',
          data: { current: {} },
          rect: {
            current: {
              initial: { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 },
              translated: { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 }
            }
          }
        },
        over: null,
        activatorEvent: {} as Event,
        delta: { x: 0, y: 0 },
        collisions: []
      }

      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      act(() => {
        result.current.handleDragEnd(mockDragEvent, selectedFields)
      })

      // Effect still runs to update selectedFilters
      expect(mockSetValue).toHaveBeenCalledWith('selectedFilters', [], {
        shouldDirty: true,
        shouldValidate: true
      })
    })

    it('should handle drag with same source and target', () => {
      const selectedFields = [mockReportFields[0]]
      const mockDragEvent: DragEndEvent = {
        active: {
          id: 'field1',
          data: { current: {} },
          rect: {
            current: {
              initial: { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 },
              translated: { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 }
            }
          }
        },
        over: {
          disabled: false,
          id: 'field1',
          data: { current: {} },
          rect: { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 }
        },
        activatorEvent: {} as Event,
        delta: { x: 0, y: 0 },
        collisions: []
      }

      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      act(() => {
        result.current.handleDragEnd(mockDragEvent, selectedFields)
      })

      // Effect still runs to update selectedFilters
      expect(mockSetValue).toHaveBeenCalledWith('selectedFilters', [], {
        shouldDirty: true,
        shouldValidate: true
      })
    })

    it('should handle invalid field indices', () => {
      const selectedFields = [mockReportFields[0]]
      const mockDragEvent: DragEndEvent = {
        active: {
          id: 'nonExistentField',
          data: { current: {} },
          rect: {
            current: {
              initial: { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 },
              translated: { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 }
            }
          }
        },
        over: {
          disabled: false,
          id: 'field1',
          data: { current: {} },
          rect: { top: 0, left: 0, right: 0, bottom: 0, width: 0, height: 0 }
        },
        activatorEvent: {} as Event,
        delta: { x: 0, y: 0 },
        collisions: []
      }

      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      act(() => {
        result.current.handleDragEnd(mockDragEvent, selectedFields)
      })

      // Effect still runs to update selectedFilters
      expect(mockSetValue).toHaveBeenCalledWith('selectedFilters', [], {
        shouldDirty: true,
        shouldValidate: true
      })
    })
  })

  describe('effects and synchronization', () => {
    it('should sync field state when category fields change', () => {
      const { rerender } = renderHook(({ fields }) => useReportFields(fields, mockReportFilters), {
        initialProps: { fields: mockReportFields }
      })

      const newFields = [
        ...mockReportFields,
        {
          fieldId: 'field3',
          displayName: 'Field Three',
          dataType: 'string',
          accessor: 'field3',
          isHidden: false,
          isRequired: false,
          isFilterable: true
        }
      ] as ReportFieldDefinition[]

      mockIsEqual.mockReturnValue(false)

      rerender({ fields: newFields })

      expect(mockGetValues).toHaveBeenCalled()
    })

    it('should not sync when fields are equal', () => {
      mockIsEqual.mockReturnValue(true)

      const { rerender } = renderHook(({ fields }) => useReportFields(fields, mockReportFilters), {
        initialProps: { fields: mockReportFields }
      })

      const callCountBefore = mockGetValues.mock.calls.length

      rerender({ fields: mockReportFields })

      expect(mockGetValues.mock.calls.length).toBe(callCountBefore)
    })

    it('should initialize filters from form when available', () => {
      const mockFormFilters = [
        {
          filterId: 'filter1',
          fieldId: 'field1',
          filterType: 'enum',
          displayName: 'Filter One',
          filterOptions: [{ optionId: 'option1', displayValue: 'Option 1', internalValue: 'option1' }]
        }
      ]

      mockGetValues.mockImplementation((key) => {
        if (key === 'selectedFilters') return mockFormFilters
        return []
      })

      mockConvertFormFiltersToSelectedFilter.mockReturnValue({
        filter1: [{ value: 'option1', label: 'Option 1' }]
      })

      renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      expect(mockConvertFormFiltersToSelectedFilter).toHaveBeenCalledWith(mockFormFilters, expect.any(Map))
    })

    it('should update form with selected filters when they change', () => {
      // Setup isEqual to return false initially to trigger the effect
      mockIsEqual.mockReturnValueOnce(false)

      renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      // The effect should have triggered setValue during initialization
      expect(mockSetValue).toHaveBeenCalledWith('selectedFilters', expect.any(Array), {
        shouldDirty: true,
        shouldValidate: true
      })
    })
  })

  describe('return values', () => {
    it('should return all expected properties', () => {
      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      expect(result.current).toHaveProperty('fieldState')
      expect(result.current).toHaveProperty('appliedFilters')
      expect(result.current).toHaveProperty('filtersMap')
      expect(result.current).toHaveProperty('fieldToFilterMap')
      expect(result.current).toHaveProperty('getFilteredColumns')
      expect(result.current).toHaveProperty('handleFieldFilterUpdate')
      expect(result.current).toHaveProperty('handleCheckItem')
      expect(result.current).toHaveProperty('handleDragEnd')
    })

    it('should have correct fieldState structure', () => {
      const { result } = renderHook(() => useReportFields(mockReportFields, mockReportFilters))

      expect(result.current.fieldState).toHaveProperty('columns')
      expect(result.current.fieldState).toHaveProperty('selectedColumns')
      expect(result.current.fieldState).toHaveProperty('fieldsMap')
      expect(result.current.fieldState.selectedColumns).toBeInstanceOf(Set)
      expect(result.current.fieldState.fieldsMap).toBeInstanceOf(Map)
    })
  })

  describe('edge cases', () => {
    it('should handle empty fields and filters arrays', () => {
      const { result } = renderHook(() => useReportFields([], []))

      expect(result.current.fieldState.columns).toEqual([])
      expect(result.current.filtersMap.size).toBe(0)
      expect(result.current.fieldToFilterMap.size).toBe(0)
    })

    it('should handle filters without fieldId', () => {
      const filtersWithoutFieldId = [
        {
          filterId: 'filter1',
          displayName: 'Filter One',
          filterType: 'enum' as const,
          filterOptions: []
        }
      ]

      const { result } = renderHook(() => useReportFields(mockReportFields, filtersWithoutFieldId))

      expect(result.current.fieldToFilterMap.get('filter1')).toBe('filter1')
    })
  })
})
