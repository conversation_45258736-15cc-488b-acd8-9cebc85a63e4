// eslint-disable-next-line @nx/enforce-module-boundaries
import { useGetReportTypeConfigurationsQuery } from '@gc/redux-store'
import { Option } from '@gc/types'
import { useCallback, useMemo } from 'react'

export function useReportCategories() {
  const { data: reportCategories = [], isLoading, isFetching } = useGetReportTypeConfigurationsQuery()

  const reportCategoriesOptions: Option[] = useMemo(
    () => reportCategories.map((x) => ({ value: x.reportCategory, text: x.name })),
    [reportCategories]
  )

  const getReportCategoryFields = useCallback(
    (categoryKey: string) => {
      return reportCategories.find((x) => x.reportCategory === categoryKey)?.availableFields ?? []
    },
    [reportCategories]
  )

  const getReportCategoryFilters = useCallback(
    (categoryKey: string) => {
      return reportCategories.find((x) => x.reportCategory === categoryKey)?.availableFilters
    },
    [reportCategories]
  )

  return {
    isLoading,
    isFetching,
    getReportCategoryFields,
    getReportCategoryFilters,
    reportCategories,
    reportCategoriesOptions
  }
}
