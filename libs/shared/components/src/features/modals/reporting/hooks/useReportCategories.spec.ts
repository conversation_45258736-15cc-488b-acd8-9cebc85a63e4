import { renderHook } from '@testing-library/react'

import { useReportCategories } from './useReportCategories'

const mockUseGetReportTypeConfigurationsQuery = jest.fn()

jest.mock('@gc/redux-store', () => ({
  useGetReportTypeConfigurationsQuery: () => mockUseGetReportTypeConfigurationsQuery()
}))

describe('useReportCategories', () => {
  const mockReportCategories = [
    {
      reportCategory: 'ORDERS',
      name: 'Orders Report',
      availableFields: [
        {
          fieldId: 'orderId',
          displayName: 'Order ID',
          dataType: 'string',
          accessor: 'orderId',
          isHidden: false,
          isRequired: false
        },
        {
          fieldId: 'customerName',
          displayName: 'Customer Name',
          dataType: 'string',
          accessor: 'customerName',
          isHidden: false,
          isRequired: false
        }
      ],
      availableFilters: [
        {
          filterId: 'status',
          fieldId: 'status',
          displayName: 'Order Status',
          filterType: 'multi-select' as const,
          options: [
            { value: 'pending', label: 'Pending' },
            { value: 'complete', label: 'Complete' }
          ]
        }
      ]
    },
    {
      reportCategory: 'CUSTOMERS',
      name: 'Customer Report',
      availableFields: [
        {
          fieldId: 'customerId',
          displayName: 'Customer ID',
          dataType: 'string',
          accessor: 'customerId',
          isHidden: false,
          isRequired: false
        }
      ],
      availableFilters: [
        {
          filterId: 'region',
          fieldId: 'region',
          displayName: 'Region',
          filterType: 'single-select' as const,
          options: [
            { value: 'north', label: 'North' },
            { value: 'south', label: 'South' }
          ]
        }
      ]
    }
  ]

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('loading states', () => {
    it('should return loading state when data is loading', () => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        isFetching: false
      })

      const { result } = renderHook(() => useReportCategories())

      expect(result.current.isLoading).toBe(true)
      expect(result.current.isFetching).toBe(false)
      expect(result.current.reportCategories).toEqual([])
      expect(result.current.reportCategoriesOptions).toEqual([])
    })

    it('should return fetching state when data is fetching', () => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: mockReportCategories,
        isLoading: false,
        isFetching: true
      })

      const { result } = renderHook(() => useReportCategories())

      expect(result.current.isLoading).toBe(false)
      expect(result.current.isFetching).toBe(true)
      expect(result.current.reportCategories).toEqual(mockReportCategories)
    })

    it('should return loaded state when data is available', () => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: mockReportCategories,
        isLoading: false,
        isFetching: false
      })

      const { result } = renderHook(() => useReportCategories())

      expect(result.current.isLoading).toBe(false)
      expect(result.current.isFetching).toBe(false)
      expect(result.current.reportCategories).toEqual(mockReportCategories)
    })
  })

  describe('data transformation', () => {
    beforeEach(() => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: mockReportCategories,
        isLoading: false,
        isFetching: false
      })
    })

    it('should transform report categories to options format', () => {
      const { result } = renderHook(() => useReportCategories())

      expect(result.current.reportCategoriesOptions).toEqual([
        { value: 'ORDERS', text: 'Orders Report' },
        { value: 'CUSTOMERS', text: 'Customer Report' }
      ])
    })

    it('should return empty options when no data', () => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: [],
        isLoading: false,
        isFetching: false
      })

      const { result } = renderHook(() => useReportCategories())

      expect(result.current.reportCategoriesOptions).toEqual([])
    })

    it('should handle undefined data gracefully', () => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        isFetching: false
      })

      const { result } = renderHook(() => useReportCategories())

      expect(result.current.reportCategories).toEqual([])
      expect(result.current.reportCategoriesOptions).toEqual([])
    })
  })

  describe('getReportCategoryFields', () => {
    beforeEach(() => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: mockReportCategories,
        isLoading: false,
        isFetching: false
      })
    })

    it('should return fields for existing category', () => {
      const { result } = renderHook(() => useReportCategories())

      const fields = result.current.getReportCategoryFields('ORDERS')

      expect(fields).toEqual(mockReportCategories[0].availableFields)
    })

    it('should return empty array for non-existent category', () => {
      const { result } = renderHook(() => useReportCategories())

      const fields = result.current.getReportCategoryFields('NON_EXISTENT')

      expect(fields).toEqual([])
    })

    it('should return empty array when no data available', () => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: [],
        isLoading: false,
        isFetching: false
      })

      const { result } = renderHook(() => useReportCategories())

      const fields = result.current.getReportCategoryFields('ORDERS')

      expect(fields).toEqual([])
    })

    it('should maintain function reference stability', () => {
      const { result, rerender } = renderHook(() => useReportCategories())
      const firstFunction = result.current.getReportCategoryFields

      rerender()

      expect(result.current.getReportCategoryFields).toBe(firstFunction)
    })
  })

  describe('getReportCategoryFilters', () => {
    beforeEach(() => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: mockReportCategories,
        isLoading: false,
        isFetching: false
      })
    })

    it('should return filters for existing category', () => {
      const { result } = renderHook(() => useReportCategories())

      const filters = result.current.getReportCategoryFilters('ORDERS')

      expect(filters).toEqual(mockReportCategories[0].availableFilters)
    })

    it('should return undefined for non-existent category', () => {
      const { result } = renderHook(() => useReportCategories())

      const filters = result.current.getReportCategoryFilters('NON_EXISTENT')

      expect(filters).toBeUndefined()
    })

    it('should return undefined when no data available', () => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: [],
        isLoading: false,
        isFetching: false
      })

      const { result } = renderHook(() => useReportCategories())

      const filters = result.current.getReportCategoryFilters('ORDERS')

      expect(filters).toBeUndefined()
    })

    it('should handle category with undefined availableFilters', () => {
      const categoryWithoutFilters = [
        {
          ...mockReportCategories[0],
          availableFilters: undefined
        }
      ]

      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: categoryWithoutFilters,
        isLoading: false,
        isFetching: false
      })

      const { result } = renderHook(() => useReportCategories())

      const filters = result.current.getReportCategoryFilters('ORDERS')

      expect(filters).toBeUndefined()
    })

    it('should maintain function reference stability', () => {
      const { result, rerender } = renderHook(() => useReportCategories())
      const firstFunction = result.current.getReportCategoryFilters

      rerender()

      expect(result.current.getReportCategoryFilters).toBe(firstFunction)
    })
  })

  describe('memoization', () => {
    it('should memoize reportCategoriesOptions when data unchanged', () => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: mockReportCategories,
        isLoading: false,
        isFetching: false
      })

      const { result, rerender } = renderHook(() => useReportCategories())
      const firstOptions = result.current.reportCategoriesOptions

      rerender()

      expect(result.current.reportCategoriesOptions).toBe(firstOptions)
    })

    it('should update reportCategoriesOptions when data changes', () => {
      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: mockReportCategories,
        isLoading: false,
        isFetching: false
      })

      const { result, rerender } = renderHook(() => useReportCategories())
      const firstOptions = result.current.reportCategoriesOptions

      const newData = [
        {
          reportCategory: 'PRODUCTS',
          name: 'Products Report',
          availableFields: [],
          availableFilters: []
        }
      ]

      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: newData,
        isLoading: false,
        isFetching: false
      })

      rerender()

      expect(result.current.reportCategoriesOptions).not.toBe(firstOptions)
      expect(result.current.reportCategoriesOptions).toEqual([{ value: 'PRODUCTS', text: 'Products Report' }])
    })
  })

  describe('edge cases', () => {
    it('should handle empty strings in category names', () => {
      const categoriesWithEmptyName = [
        {
          reportCategory: 'EMPTY',
          name: '',
          availableFields: [],
          availableFilters: []
        }
      ]

      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: categoriesWithEmptyName,
        isLoading: false,
        isFetching: false
      })

      const { result } = renderHook(() => useReportCategories())

      expect(result.current.reportCategoriesOptions).toEqual([{ value: 'EMPTY', text: '' }])
    })

    it('should handle categories with null or undefined fields', () => {
      const categoriesWithNullFields = [
        {
          reportCategory: 'NULL_FIELDS',
          name: 'Null Fields Report',
          availableFields: null,
          availableFilters: null
        }
      ]

      mockUseGetReportTypeConfigurationsQuery.mockReturnValue({
        data: categoriesWithNullFields,
        isLoading: false,
        isFetching: false
      })

      const { result } = renderHook(() => useReportCategories())

      const fields = result.current.getReportCategoryFields('NULL_FIELDS')
      const filters = result.current.getReportCategoryFilters('NULL_FIELDS')

      expect(fields).toEqual([])
      expect(filters).toBeNull()
    })
  })
})
