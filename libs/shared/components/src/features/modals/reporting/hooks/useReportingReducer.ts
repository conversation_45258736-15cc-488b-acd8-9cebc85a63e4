import { ReportFilterDefinition, SelectedFilter } from '@gc/types'
import { useReducer } from 'react'

interface AppliedFilterPayload extends ReportFilterDefinition {
  filterSelections: SelectedFilter
}

// Filter state management with useReducer for better performance
interface FilterAction {
  type: 'UPDATE_FILTER' | 'CLEAR_FILTER' | 'RESET_FILTERS'
  payload?: AppliedFilterPayload
}

const filterReducer = (state: SelectedFilter, action: FilterAction): SelectedFilter => {
  switch (action.type) {
    case 'UPDATE_FILTER': {
      const newState = { ...state }
      if (!action.payload) {
        console.warn('No payload provided to update filter')
        return newState
      }

      const { filterId, filterSelections } = action.payload

      if (filterSelections[filterId]?.length > 0) {
        newState[filterId] = filterSelections[filterId]
      } else {
        delete newState[filterId]
      }

      return newState
    }
    case 'CLEAR_FILTER': {
      const newState = { ...state }
      if (!action.payload) {
        console.warn('No payload provided to clear filter')
        return newState
      }

      delete newState[action.payload.filterId]
      return newState
    }
    case 'RESET_FILTERS':
      return {}
    default:
      return state
  }
}

export const useReportingReducer = (initialFilters: SelectedFilter = {}) => {
  const [appliedFilters, dispatch] = useReducer(filterReducer, initialFilters)
  return { appliedFilters, dispatch }
}
