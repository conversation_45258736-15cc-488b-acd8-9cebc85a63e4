import { DragEndEvent, UniqueIdentifier } from '@dnd-kit/core'
import { arrayMove } from '@dnd-kit/sortable'
import {
  ReportFieldDefinition,
  ReportFilterDefinition,
  SelectedFilter,
  SelectedReportField,
  SelectedReportFilter,
  UserReportSubmissionData
} from '@gc/types'
import { isEqual } from 'lodash'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useFormContext } from 'react-hook-form'

import { convertFormFiltersToSelectedFilter, formatSelectedFields } from '../lib/utils'
import { useReportingReducer } from './useReportingReducer'

// Combined state interface to prevent race conditions
interface ReportFieldsState {
  columns: ReportFieldDefinition[]
  selectedColumns: Set<UniqueIdentifier>
  fieldsMap: Map<string, ReportFieldDefinition>
}

// Custom hook for filter transformation logic with deep comparison to prevent infinite rerenders
const useSelectedFiltersForForm = (appliedFilters: SelectedFilter, filtersMap: Map<string, ReportFilterDefinition>) => {
  return useMemo(() => {
    return Object.entries(appliedFilters).reduce<SelectedReportFilter[]>((acc, [filterId, selectedOptions]) => {
      // Type guard to ensure selectedOptions is the expected array type
      if (!Array.isArray(selectedOptions) || !selectedOptions.length) return acc

      const validSelectedOptions = selectedOptions.filter((so): so is NonNullable<typeof so> => so != null)
      if (!validSelectedOptions.length) return acc

      const filter = filtersMap.get(filterId)
      if (!filter) {
        return acc
      }

      acc.push({
        filterId,
        fieldId: filterId,
        filterType: filter.filterType,
        displayName: filter.displayName || filterId,
        selectedOptions: validSelectedOptions.map((so) => so.value)
      })

      return acc
    }, [])
  }, [appliedFilters, filtersMap])
}

// Custom hook to manage report fields state and logic
const useReportFields = (
  selectedCategoryFields: ReportFieldDefinition[],
  selectedCategoryFilters: ReportFilterDefinition[]
) => {
  // Create lookup maps for O(1) access
  const filtersMap = useMemo(() => {
    return new Map(selectedCategoryFilters.map((filter) => [filter.filterId, filter]))
  }, [selectedCategoryFilters])

  // Create fieldId-to-filterId mapping for UI lookups
  const fieldToFilterMap = useMemo(() => {
    return new Map(selectedCategoryFilters.map((filter) => [filter.fieldId ?? filter.filterId, filter.filterId]))
  }, [selectedCategoryFilters])

  const { setValue, getValues } = useFormContext<UserReportSubmissionData>()

  const hasInitializedFromForm = useRef(false)
  const hasInitializedFiltersFromForm = useRef(false)
  const prevSelectedCategoryFieldsRef = useRef<ReportFieldDefinition[]>([])
  const prevSelectedFiltersRef = useRef<SelectedReportFilter[]>([])

  // Initialize state with form's existing selectedFields for edit mode compatibility
  const [fieldState, setFieldState] = useState<ReportFieldsState>(() => {
    const formSelectedFields = getValues('selectedFields') || []
    const selectedFieldIds = new Set<UniqueIdentifier>(formSelectedFields.map((field) => field.fieldId))

    return {
      columns: selectedCategoryFields,
      selectedColumns: selectedFieldIds,
      fieldsMap: new Map(selectedCategoryFields.map((field) => [field.fieldId, field]))
    }
  })

  // Initialize reducer with form's existing selectedFilters for edit mode compatibility
  const initialFilters = useMemo(() => {
    const formSelectedFilters = getValues('selectedFilters') || []
    return convertFormFiltersToSelectedFilter(formSelectedFilters, filtersMap)
  }, [getValues, filtersMap])

  const { appliedFilters, dispatch } = useReportingReducer(initialFilters)
  const selectedFiltersForForm = useSelectedFiltersForForm(appliedFilters, filtersMap)

  // Memoize filtered columns
  const getFilteredColumns = useCallback(
    (searchValue: string) => {
      return fieldState.columns.filter((column) => column.displayName.toLowerCase().includes(searchValue.toLowerCase()))
    },
    [fieldState.columns]
  )

  // Optimized callback with stable dependency and Map lookup
  const handleFieldFilterUpdate = useCallback(
    (fieldId: string, filterSelections: SelectedFilter) => {
      const filterId = fieldToFilterMap.get(fieldId)
      if (!filterId) {
        return
      }

      const filter = filtersMap.get(filterId)
      if (!filter) {
        return
      }

      dispatch({
        type: 'UPDATE_FILTER',
        payload: { ...filter, fieldId, filterSelections }
      })
    },
    [dispatch, filtersMap, fieldToFilterMap]
  )

  // Memoized checkItem callback that preserves existing order
  const handleCheckItem = useCallback(
    (itemId: UniqueIdentifier) => {
      const currentSelectedFields = getValues('selectedFields') || []

      setFieldState((prevState) => {
        const isCurrentlySelected = prevState.selectedColumns.has(itemId)
        const fieldDefinition = prevState.fieldsMap.get(itemId as string)
        if (!fieldDefinition) {
          return prevState
        }

        const newSelectedColumns = new Set(prevState.selectedColumns)
        let updatedSelectedFields: SelectedReportField[]

        if (isCurrentlySelected) {
          newSelectedColumns.delete(itemId)
          updatedSelectedFields = currentSelectedFields.filter((field) => field.fieldId !== itemId)
        } else {
          newSelectedColumns.add(itemId)
          const newField: SelectedReportField = {
            ...fieldDefinition,
            displayOrder: currentSelectedFields.length
          }
          updatedSelectedFields = [...currentSelectedFields, newField]
        }

        setValue('selectedFields', updatedSelectedFields, {
          shouldDirty: true,
          shouldValidate: true
        })

        return {
          ...prevState,
          selectedColumns: newSelectedColumns
        }
      })
    },
    [setValue, getValues]
  )

  // Handle drag-and-drop for ungrouped selected fields
  const handleDragEnd = useCallback(
    (event: DragEndEvent, selectedFields: ReportFieldDefinition[]) => {
      const { active, over } = event

      if (over && active.id !== over.id) {
        const oldIndex = selectedFields.findIndex((item) => item.fieldId === active.id)
        const newIndex = selectedFields.findIndex((item) => item.fieldId === over.id)

        if (oldIndex !== -1 && newIndex !== -1) {
          const newSelected = arrayMove(selectedFields, oldIndex, newIndex)

          // Update the field state to maintain the new order
          setFieldState((prevState) => {
            const newSelectedIds = new Set(newSelected.map((col) => col.fieldId))

            // Update fieldState.columns to reflect the new drag-and-drop order
            // Put reordered selected fields first, then unselected fields in original order
            const unselectedFields = prevState.columns.filter((col) => !newSelectedIds.has(col.fieldId))
            const newColumns = [...newSelected, ...unselectedFields]

            return {
              ...prevState,
              columns: newColumns,
              selectedColumns: newSelectedIds
            }
          })

          // Convert to the new selectedFields format with updated order from drag-and-drop
          const selectedFieldsData = formatSelectedFields(newSelected, new Set(newSelected.map((col) => col.fieldId)))
          setValue('selectedFields', selectedFieldsData, { shouldDirty: true, shouldValidate: true })
        } else {
          // Handle case where drag target is invalid
        }
      } else {
        // Handle case where drag target is invalid
      }
    },
    [setValue]
  )

  // Sync form's selectedFields with local state on initialization and category changes
  useEffect(() => {
    if (selectedCategoryFields.length > 0 && !isEqual(selectedCategoryFields, prevSelectedCategoryFieldsRef.current)) {
      const formSelectedFields = getValues('selectedFields') || []
      const selectedFieldIds = new Set<UniqueIdentifier>(formSelectedFields.map((field) => field.fieldId))

      setFieldState({
        columns: selectedCategoryFields,
        selectedColumns: selectedFieldIds,
        fieldsMap: new Map(selectedCategoryFields.map((field) => [field.fieldId, field]))
      })
      prevSelectedCategoryFieldsRef.current = selectedCategoryFields
      hasInitializedFromForm.current = true
      hasInitializedFiltersFromForm.current = false // Reset filter initialization when category changes
    }
  }, [selectedCategoryFields, getValues])

  // Additional effect to sync form values with local state when form values change (for edit mode)
  useEffect(() => {
    if (hasInitializedFromForm.current) return
    const formSelectedFields = getValues('selectedFields') || []
    if (formSelectedFields.length > 0 && selectedCategoryFields.length > 0) {
      const selectedFieldIds = new Set<UniqueIdentifier>(formSelectedFields.map((field) => field.fieldId))

      setFieldState((prevState) => ({
        ...prevState,
        selectedColumns: selectedFieldIds
      }))
      hasInitializedFromForm.current = true
    }
  }, [getValues, selectedCategoryFields])

  // Initialize filters from form when category changes or on first load
  useEffect(() => {
    if (hasInitializedFiltersFromForm.current) return

    const formSelectedFilters = getValues('selectedFilters') || []
    if (formSelectedFilters.length > 0 && filtersMap.size > 0) {
      const initialFiltersFromForm = convertFormFiltersToSelectedFilter(formSelectedFilters, filtersMap)

      // Dispatch actions to set up the filters in the reducer
      Object.entries(initialFiltersFromForm).forEach(([filterId, selectedOptions]) => {
        if (selectedOptions?.length > 0) {
          const filter = filtersMap.get(filterId)
          if (filter) {
            dispatch({
              type: 'UPDATE_FILTER',
              payload: {
                ...filter,
                fieldId: filter.fieldId || filterId,
                filterSelections: { [filterId]: selectedOptions }
              }
            })
          }
        }
      })
      hasInitializedFiltersFromForm.current = true
    }
  }, [filtersMap, getValues, dispatch])

  // Update form with selected filters when they change (with deep comparison to prevent infinite rerenders)
  useEffect(() => {
    if (!isEqual(selectedFiltersForForm, prevSelectedFiltersRef.current)) {
      setValue('selectedFilters', selectedFiltersForForm, {
        shouldDirty: true,
        shouldValidate: true
      })
      prevSelectedFiltersRef.current = selectedFiltersForForm
    }
  }, [selectedFiltersForForm, setValue])

  return {
    fieldState,
    appliedFilters,
    filtersMap,
    fieldToFilterMap,
    getFilteredColumns,
    handleFieldFilterUpdate,
    handleCheckItem,
    handleDragEnd
  }
}

export default useReportFields
