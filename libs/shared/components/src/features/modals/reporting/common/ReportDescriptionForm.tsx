/* eslint-disable @nx/enforce-module-boundaries */
import { Select } from '@element/react-select'
import { Textfield } from '@element/react-textfield'
import { TypoOverline } from '@element/react-typography'
import { useMemoizedTranslation } from '@gc/hooks'
import { ReportCategoryIds, UserReportSubmissionData } from '@gc/types'
import { isUndefined } from 'es-toolkit'
import { useCallback } from 'react'
import { useFormContext } from 'react-hook-form'

import { SelectSkeleton } from '../../../../ui-common/skeleton/SkeletonComponents'
import { useReportCategories } from '../hooks/useReportCategories'
import styles from './ReportDescriptionForm.module.scss'

export function ReportDescriptionForm() {
  const t = useMemoizedTranslation()

  const { reportCategories, reportCategoriesOptions, isLoading, isFetching } = useReportCategories()

  const {
    register,
    setValue,
    watch,
    formState: { errors }
  } = useFormContext<UserReportSubmissionData>()

  const reportName = watch('reportName')
  const reportCategory = watch('reportCategory')

  // fallback to first type if nothing selected yet
  const isLoadingData = isLoading || isFetching

  // Handle input changes
  const handleInputChange = useCallback(
    (field: keyof UserReportSubmissionData) => (e: React.ChangeEvent<HTMLInputElement>) =>
      setValue(field, e.target.value, { shouldValidate: true }),
    [setValue]
  )

  const onReportCategoryChange = useCallback(
    (opt: { value: ReportCategoryIds; text: string }) => {
      setValue('reportCategory', opt.value, { shouldValidate: true })

      const reportConfiguration = reportCategories.find((x) => x.reportCategory === opt.value)
      if (reportConfiguration) {
        setValue('reportCategoryDisplayName', reportConfiguration.name, { shouldValidate: true })
        setValue('reportConfigurationVersion', reportConfiguration.version, { shouldValidate: true })
      } else {
        console.error(`Report Configuration not found for ${opt.value}`)
      }
    },
    [setValue, reportCategories]
  )

  return (
    <div id='report-description-form' role='form' aria-labelledby='form-title' className={styles.container}>
      <div id='form-title' className='sr-only'>
        Report Description Form
      </div>

      <div id='report-name-container' data-testid='report-name-container' className={styles.section}>
        <TypoOverline className={styles.sectionTitle}>{t('reporting.report_name.label')}</TypoOverline>

        <Textfield
          {...register('reportName')}
          fullWidth
          dense
          counter
          autoFocus
          maxlength={35}
          variant='outlined'
          value={reportName ?? ''}
          onChange={handleInputChange('reportName')}
          label={t('reporting.report_name.label')}
          helperText={errors.reportName?.message}
          placeholder={t('reporting.report_name.placeholder')}
          valid={isUndefined(errors.reportName)}
          style={{ height: '40px', alignContent: 'center' }}
          data-testid='report-name-input'
        />
      </div>

      <div id='report-type-container' data-testid='report-type-container' className={styles.section}>
        <TypoOverline className={styles.sectionTitle}>{t('reporting.report_category.label')}</TypoOverline>

        <div>
          {isLoadingData ? (
            <SelectSkeleton height='40px' />
          ) : (
            <Select
              {...register('reportCategory')}
              dense
              hoisted
              variant='outlined'
              label={t('reporting.report_category.placeholder')}
              value={reportCategory}
              options={reportCategoriesOptions}
              onChange={onReportCategoryChange}
              valid={isUndefined(errors.reportCategory)}
              helperText={errors.reportCategory?.message}
              data-testid='select-report-category'
              style={{ height: '40px', display: 'flex', alignItems: 'center' }}
            />
          )}
        </div>
      </div>
    </div>
  )
}
