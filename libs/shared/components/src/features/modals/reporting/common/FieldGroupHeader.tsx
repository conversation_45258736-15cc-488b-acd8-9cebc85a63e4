import { memo } from 'react'

import styles from './FieldGroupHeader.module.scss'

interface FieldGroupHeaderProps {
  groupName: string
  fieldCount: number
  selectedCount: number
}

export const FieldGroupHeader = memo(function FieldGroupHeader({
  groupName,
  fieldCount,
  selectedCount
}: FieldGroupHeaderProps) {
  let countText
  if (selectedCount > 0) {
    countText = `${selectedCount} of ${fieldCount} selected`
  } else {
    const fieldWord = fieldCount === 1 ? 'field' : 'fields'
    countText = `${fieldCount} ${fieldWord}`
  }

  return (
    <div className={styles.field_group_header}>
      <div className={styles.group_info}>
        <h3 className={styles.group_name}>{groupName}</h3>
        <span className={styles.field_count}>{countText}</span>
      </div>
    </div>
  )
})
