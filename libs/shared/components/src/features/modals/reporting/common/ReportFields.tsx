/* eslint-disable @nx/enforce-module-boundaries */
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import { restrictToParentElement, restrictToVerticalAxis } from '@dnd-kit/modifiers'
import { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { List } from '@element/react-list'
import { useSearch } from '@gc/hooks'
import { ReportFieldDefinition, ReportFilterDefinition, SelectedFilter, UserReportSubmissionData } from '@gc/types'
import { getFasteStoreKey } from '@gc/utils'
import { memo, useCallback, useMemo } from 'react'
import { useFormContext } from 'react-hook-form'

import Band from '../../../../ui-common/band/Band'
import { SortableListItem } from '../../../../ui-common/list/SortableListItem'
import { useReportCategories } from '../hooks/useReportCategories'
import useReportFields from '../hooks/useReportFields'
import { groupFieldsByGroupBy, partitionFieldsBySelection } from '../lib/fieldGrouping'
import { FieldGroupHeader } from './FieldGroupHeader'
import styles from './ReportFields.module.scss'

// Memoized List Item for performance
const MemoSortableListItem = memo(SortableListItem)

// Extracted component for grouped fields list - updated to separate selected fields
const GroupedFieldsList = memo(function GroupedFieldsList({
  groupedFields,
  selectedFields,
  selectedColumns,
  handleCheckItem,
  handleFieldFilterUpdate,
  appliedFilters,
  filtersMap,
  fieldToFilterMap
}: {
  groupedFields: ReturnType<typeof groupFieldsByGroupBy>
  selectedFields: ReportFieldDefinition[]
  selectedColumns: Set<UniqueIdentifier>
  handleCheckItem: (itemId: UniqueIdentifier) => void
  handleFieldFilterUpdate: (fieldId: string, filterSelections: SelectedFilter) => void
  appliedFilters: SelectedFilter
  filtersMap: Map<string, ReportFilterDefinition>
  fieldToFilterMap: Map<string, string>
}) {
  return useMemo(() => {
    const result = []

    // Render selected fields section using the passed selectedFields (which maintains drag order)
    if (selectedFields.length > 0) {
      const selectedItems = selectedFields.map((column) => {
        // Get filter options from the filtersMap if the field is filterable
        const filterId = fieldToFilterMap.get(column.fieldId)
        const filterOptions = filterId ? filtersMap.get(filterId)?.filterOptions : undefined

        return (
          <MemoSortableListItem
            checked={true}
            key={column.fieldId}
            id={column.fieldId}
            name={column.displayName}
            onToggle={handleCheckItem}
            isFilterable={column.isFilterable}
            filterOptions={filterOptions}
            onFilterUpdate={handleFieldFilterUpdate}
            appliedFilters={appliedFilters}
            filterId={filterId}
          />
        )
      })

      result.push(
        <div key='selected-fields' className={styles.field_group}>
          <FieldGroupHeader
            groupName='Selected Fields'
            fieldCount={selectedFields.length}
            selectedCount={selectedFields.length}
          />
          {selectedItems}
        </div>
      )
    }

    // Render groups with unselected fields only
    groupedFields.forEach((group) => {
      const { unselectedFields } = partitionFieldsBySelection(group.fields, selectedColumns)

      // Only show groups that have unselected fields
      if (unselectedFields.length > 0) {
        const unselectedItems = unselectedFields.map((column) => (
          <MemoSortableListItem
            disableDrag
            checked={false}
            key={column.fieldId}
            id={column.fieldId}
            name={column.displayName}
            onToggle={handleCheckItem}
          />
        ))

        result.push(
          <div key={group.groupId || 'ungrouped'} className={styles.field_group}>
            <FieldGroupHeader
              groupName={group.groupDisplayName}
              fieldCount={group.fields.length}
              selectedCount={0} // No selected fields in this section
            />
            {unselectedItems}
          </div>
        )
      }
    })

    return result
  }, [
    groupedFields,
    selectedFields,
    selectedColumns,
    filtersMap,
    fieldToFilterMap,
    handleCheckItem,
    handleFieldFilterUpdate,
    appliedFilters
  ])
})

export const ReportFieldsHeader = memo(function ReportFieldsHeader() {
  return (
    <Band
      containerClassName={styles.report_fields_header}
      backgroundColorClass=''
      placement='list'
      primaryText={{
        level: 1,
        bold: true,
        children: `Available Columns`
      }}
      secondaryText1={{
        bold: true,
        children: `Table columns display in the order below.`,
        className: styles.secondary_text
      }}
    />
  )
})

export const ReportFields = memo(function ReportFields() {
  const fasteStoreKey = getFasteStoreKey('reports', 'createReport')
  const [searchValue] = useSearch(fasteStoreKey)

  const { watch } = useFormContext<UserReportSubmissionData>()
  const { getReportCategoryFields, getReportCategoryFilters } = useReportCategories()

  // ==============================
  // Category Fields and Filters
  // ==============================
  const selectedCategory = watch('reportCategory')

  const selectedCategoryFields = useMemo(() => {
    return getReportCategoryFields(selectedCategory) ?? []
  }, [selectedCategory, getReportCategoryFields])

  const selectedCategoryFilters = useMemo(() => {
    return getReportCategoryFilters(selectedCategory) ?? []
  }, [selectedCategory, getReportCategoryFilters])

  // ==============================
  // State Management
  // ==============================
  // Use custom hook for state management
  const {
    fieldState,
    appliedFilters,
    filtersMap,
    fieldToFilterMap,
    getFilteredColumns,
    handleFieldFilterUpdate,
    handleCheckItem,
    handleDragEnd
  } = useReportFields(selectedCategoryFields, selectedCategoryFilters)

  const sensors = useSensors(
    // 5px movement required before drag starts
    useSensor(PointerSensor, { activationConstraint: { distance: 5 } }),
    useSensor(KeyboardSensor, { coordinateGetter: sortableKeyboardCoordinates })
  )

  // ==============================
  // Memoized Values
  // ==============================
  // Memoize filtered columns
  const filteredColumns = useMemo(() => {
    return getFilteredColumns(searchValue)
  }, [getFilteredColumns, searchValue])

  // Memoize grouped fields
  const groupedFields = useMemo(() => {
    return groupFieldsByGroupBy(filteredColumns)
  }, [filteredColumns])

  // Memoize selected fields for drag-and-drop - now extracts from all groups
  const selectedFields = useMemo(() => {
    // Simple approach: filter all columns by selected status to ensure cross-group functionality
    return fieldState.columns.filter((col) => fieldState.selectedColumns.has(col.fieldId))
  }, [fieldState.columns, fieldState.selectedColumns])

  // ==============================
  // Drag and Drop
  // ==============================
  // Optimized drag end handler
  const optimizedHandleDragEnd = useCallback(
    (event: DragEndEvent) => {
      handleDragEnd(event, selectedFields)
    },
    [handleDragEnd, selectedFields]
  )

  if (!selectedCategory) {
    return null
  }

  return (
    <div className={styles.report_fields}>
      {/* Only selected fields are draggable */}
      <DndContext
        sensors={sensors}
        onDragEnd={optimizedHandleDragEnd}
        collisionDetection={closestCenter}
        modifiers={[restrictToVerticalAxis, restrictToParentElement]}
      >
        <SortableContext items={selectedFields.map((field) => field.fieldId)} strategy={verticalListSortingStrategy}>
          <List>
            <ReportFieldsHeader />
            <div className={styles.report_fields_list}>
              <GroupedFieldsList
                handleCheckItem={handleCheckItem}
                groupedFields={groupedFields}
                appliedFilters={appliedFilters}
                selectedFields={selectedFields}
                selectedColumns={fieldState.selectedColumns}
                handleFieldFilterUpdate={handleFieldFilterUpdate}
                filtersMap={filtersMap}
                fieldToFilterMap={fieldToFilterMap}
              />
            </div>
          </List>
        </SortableContext>
      </DndContext>
    </div>
  )
})
