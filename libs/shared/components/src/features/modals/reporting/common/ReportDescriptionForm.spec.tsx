import { setUpStore } from '@gc/features-common-reporting'
import { Option, UserReportSubmissionData } from '@gc/types'
import { actAwait, render, screen } from '@gc/utils'
import userEvent from '@testing-library/user-event'
import { FieldErrors } from 'react-hook-form'

import { ReportDescriptionForm } from './ReportDescriptionForm' // Adjust path as needed

// --- Mocks ---

// Mock @gc/hooks
const mockT = jest.fn((key: string) => key)
jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useMemoizedTranslation: () => mockT
}))

// Mock @gc/redux-store - no longer using useGetReportTypesQuery since we use mock data
jest.mock('@gc/redux-store', () => ({
  ...jest.requireActual('@gc/redux-store')
}))

// Mock react-hook-form
const mockSetValue = jest.fn()
const mockWatch = jest.fn()
const mockRegister = jest.fn((name: string) => ({
  name,
  onChange: jest.fn(),
  onBlur: jest.fn(),
  ref: jest.fn()
}))
const mockFormState = { errors: {} as FieldErrors<UserReportSubmissionData> }

jest.mock('react-hook-form', () => {
  const actual = jest.requireActual('react-hook-form')
  return {
    ...actual,
    useFormContext: () => ({
      register: mockRegister,
      setValue: mockSetValue,
      watch: mockWatch,
      formState: mockFormState
    })
  }
})

// Mock child components
jest.mock('../../../../ui-common/skeleton/SkeletonComponents', () => ({
  SelectSkeleton: () => <div data-testid='select-skeleton'>Loading Select...</div>
}))

// Mock @element/react-typography
jest.mock('@element/react-typography', () => ({
  TypoOverline: ({ children, ...props }: Record<string, never>) => <div {...props}>{children}</div>
}))

// Mock @element/react-textfield
jest.mock('@element/react-textfield', () => ({
  Textfield: ({
    onChange,
    value,
    ...props
  }: Record<string, never> & { onChange: (e: React.ChangeEvent<HTMLInputElement>) => void; value: string }) => (
    <input
      data-testid={props['data-testid']}
      value={value || ''}
      onChange={onChange}
      placeholder={props.placeholder}
      {...props}
    />
  )
}))

// Mock @element/react-select
jest.mock('@element/react-select', () => ({
  Select: ({
    options,
    onChange,
    value,
    _name,
    _onBlur,
    _ref,
    ...props
  }: Record<string, never> & {
    options: Option[]
    onChange: (option: Option) => void
    value: string
  }) => (
    <select
      data-testid={props['data-testid']}
      value={value}
      onChange={(e) => {
        const selectedOption = options?.find((opt: Option) => opt.value === e.target.value)
        if (selectedOption && onChange) {
          onChange(selectedOption)
        }
      }}
    >
      {options?.map((opt: Option) => (
        <option key={opt.value} value={opt.value}>
          {opt.text}
        </option>
      ))}
    </select>
  )
}))

// Mock useReportCategories hook
jest.mock('../hooks/useReportCategories', () => ({
  useReportCategories: () => ({
    reportCategories: [
      { reportCategory: 'orders', version: 'current' },
      { reportCategory: 'quotes', version: 'current' },
      { reportCategory: 'customers', version: 'current' }
    ],
    reportCategoriesOptions: [
      { value: 'orders', text: 'Orders' },
      { value: 'quotes', text: 'Quotes' },
      { value: 'customers', text: 'Customers' }
    ],
    isLoading: false,
    isFetching: false
  })
}))

describe('ReportDescriptionForm', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFormState.errors = {}
    mockWatch.mockReturnValue({
      reportName: '',
      reportCategory: 'orders',
      reportConfigurationVersion: 'current'
    })
  })

  function renderDescriptionForm() {
    return render(<ReportDescriptionForm />, {
      store: setUpStore()
    })
  }

  it('should render form fields correctly when data is loaded', async () => {
    renderDescriptionForm()
    await actAwait(300)

    expect(screen.getByText('reporting.report_name.label')).toBeInTheDocument()
    expect(screen.getByText('reporting.report_category.label')).toBeInTheDocument()

    const nameInput = screen.getByTestId('report-name-input')
    expect(nameInput).toBeInTheDocument()

    const selectWrapper = screen.getByTestId('select-report-category')
    expect(selectWrapper).toBeInTheDocument()

    expect(mockRegister).toHaveBeenCalledWith('reportName')
    expect(mockRegister).toHaveBeenCalledWith('reportCategory')
  })

  it('should update report name and call setValue on input change', async () => {
    const user = userEvent.setup()

    renderDescriptionForm()
    await actAwait(300)

    const nameInput = screen.getByTestId('report-name-input') as HTMLInputElement
    await user.clear(nameInput)
    await user.type(nameInput, 'My New Report')

    // Verify setValue was called for reportName field
    const reportNameCalls = mockSetValue.mock.calls.filter((call) => call[0] === 'reportName')
    expect(reportNameCalls.length).toBeGreaterThan(0)
    expect(reportNameCalls[0][1]).toBeDefined() // Value was passed
    expect(reportNameCalls[0][2]).toEqual({ shouldValidate: true }) // Options were passed
  })

  it('should update report type and call setValue on select change', async () => {
    const user = userEvent.setup()

    renderDescriptionForm()
    await actAwait(300)

    const selectElement = screen.getByTestId('select-report-category') as HTMLSelectElement
    await user.selectOptions(selectElement, 'orders')

    expect(mockSetValue).toHaveBeenCalledWith('reportCategory', 'orders', { shouldValidate: true })
    expect(mockSetValue).toHaveBeenCalledWith('reportConfigurationVersion', 'current', { shouldValidate: true })
  })

  it('should display report categories options correctly', async () => {
    renderDescriptionForm()
    await actAwait(300)

    const selectElement = screen.getByTestId('select-report-category') as HTMLSelectElement
    const options = Array.from(selectElement.options).map((option) => ({
      value: option.value,
      text: option.textContent
    }))

    expect(options).toEqual([
      { value: 'orders', text: 'Orders' },
      { value: 'quotes', text: 'Quotes' },
      { value: 'customers', text: 'Customers' }
    ])
  })

  it('should handle form validation errors', async () => {
    mockFormState.errors = {
      reportName: { message: 'Report name is required', type: 'required' },
      reportCategory: { message: 'Report category is required', type: 'required' }
    }

    renderDescriptionForm()
    await actAwait(300)

    // The component should handle errors properly
    expect(screen.getByTestId('report-name-input')).toBeInTheDocument()
    expect(screen.getByTestId('select-report-category')).toBeInTheDocument()
  })

  it('should use the first report type as default when no selection is made', async () => {
    mockWatch.mockReturnValue({
      reportName: '',
      reportCategory: undefined
    })

    renderDescriptionForm()
    await actAwait(300)

    // Should fallback to first report type
    expect(screen.getByTestId('select-report-category')).toBeInTheDocument()
  })
})
