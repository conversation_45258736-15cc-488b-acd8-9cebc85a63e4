/* eslint-disable @nx/enforce-module-boundaries */
import { Group } from '@element/react-group'
import { List } from '@element/react-list'
import { TypoOverline } from '@element/react-typography'
import { useMemoizedTranslation } from '@gc/hooks'
import { ReportTypeIds, ReportTypeOptions, UserReportSubmissionData } from '@gc/types'
import { useCallback, useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'

import { RadioSkeleton } from '../../../../ui-common/skeleton/SkeletonComponents'
import styles from './SelectReportType.module.scss'

const reportTypes = [
  {
    id: ReportTypeOptions.Live,
    leadingBlockType: 'radio',
    primaryText: 'Live Report',
    secondaryText: 'Updates automatically as your data changes.'
  },
  {
    id: ReportTypeOptions.Snapshot,
    leadingBlockType: 'radio',
    primaryText: 'Snapshot Report',
    secondaryText: 'Planning & Sales Analysis (Updated Nightly)'
  }
]

function useReportTypes() {
  const [isLoading, setIsLoading] = useState(true)
  const [isFetching, setIsFetching] = useState(true)

  useEffect(() => {
    // Simulate loading and fetching
    setTimeout(() => {
      setIsLoading(false)
      setIsFetching(false)
    }, 0)
  }, [])

  return {
    isLoading,
    isFetching,
    reportTypes
  }
}

export function SelectReportType() {
  const t = useMemoizedTranslation()
  const { reportTypes, isLoading, isFetching } = useReportTypes()
  const { setValue, watch } = useFormContext<UserReportSubmissionData>()

  const isLoadingData = isLoading || isFetching
  const selectedTypeKey = watch('reportType') ?? ReportTypeOptions.Live

  const onChange = useCallback(
    (selected: ReportTypeIds) => {
      setValue('reportType', selected, { shouldValidate: true })
    },
    [setValue]
  )

  return (
    <div id='select-report-type' className={styles.container} data-testid='select-report-type'>
      <TypoOverline className={styles.sectionTitle}>{t('reporting.report_type.label')}</TypoOverline>

      {isLoadingData ? (
        <Group gap='dense' direction='vertical' fullWidth>
          <RadioSkeleton height='60px' showSubtitle />
          <RadioSkeleton height='60px' showSubtitle />
        </Group>
      ) : (
        <div className={styles.radioGroup}>
          <List
            showDivider
            singleList
            wrapFocus
            role='listbox'
            dividerVariant='full'
            leadingBlockType='radio'
            selected={selectedTypeKey}
            itemsKey='items'
            items={reportTypes}
            onAction={(selected: ReportTypeIds) => onChange(selected)}
          />
        </div>
      )}
    </div>
  )
}
