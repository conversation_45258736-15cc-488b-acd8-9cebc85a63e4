/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @nx/enforce-module-boundaries */
import { setUpStore } from '@gc/features-common-reporting'
import { useSearch } from '@gc/hooks'
import { ReportFieldDefinition } from '@gc/types'
import { render, screen } from '@gc/utils'
import { useFormContext } from 'react-hook-form'

import { FieldGroupHeader } from './FieldGroupHeader'
import { ReportFields, ReportFieldsHeader } from './ReportFields'

// Mo<PERSON> styles import
jest.mock('./ReportFields.module.scss', () => ({
  report_fields: 'report_fields',
  report_fields_header: 'report_fields_header',
  report_fields_list: 'report_fields_list',
  secondary_text: 'secondary_text',
  field_group: 'field_group'
}))

// Mock FieldGroupHeader styles
jest.mock('./FieldGroupHeader.module.scss', () => ({
  field_group_header: 'field_group_header',
  group_info: 'group_info',
  group_name: 'group_name',
  field_count: 'field_count'
}))

// Mock Band and SortableListItem to isolate tests
jest.mock('../../../../ui-common/band/Band', () => ({
  __esModule: true,
  default: (props: any) => <div data-testid='band-header'>{props.primaryText?.children || props.primaryText}</div>
}))
jest.mock('../../../../ui-common/list/SortableListItem', () => ({
  SortableListItem: (props: any) => <div data-testid={`sortable-list-item-${props.id}`}>{props.name}</div>
}))

// Mock DnD Kit components
jest.mock('@dnd-kit/core', () => ({
  DndContext: ({ children }: any) => <div data-testid='dnd-context'>{children}</div>,
  closestCenter: jest.fn(),
  useSensor: jest.fn(),
  useSensors: jest.fn(() => []),
  PointerSensor: jest.fn(),
  KeyboardSensor: jest.fn()
}))

jest.mock('@dnd-kit/sortable', () => ({
  SortableContext: ({ children }: any) => <div data-testid='sortable-context'>{children}</div>,
  arrayMove: jest.fn(),
  verticalListSortingStrategy: jest.fn(),
  sortableKeyboardCoordinates: jest.fn()
}))

jest.mock('@dnd-kit/modifiers', () => ({
  restrictToParentElement: jest.fn(),
  restrictToVerticalAxis: jest.fn()
}))

// Mock @element/react-list
jest.mock('@element/react-list', () => ({
  List: ({ children }: any) => <div data-testid='list'>{children}</div>
}))

// Mock useFormContext and useSearch
jest.mock('react-hook-form', () => ({
  useFormContext: jest.fn()
}))

jest.mock('@gc/hooks', () => ({
  useSearch: jest.fn()
}))

// Mock useReportCategories hook
jest.mock('../hooks/useReportCategories', () => ({
  useReportCategories: jest.fn()
}))

// Mock grouping utilities
jest.mock('../lib/fieldGrouping', () => ({
  groupFieldsByGroupBy: jest.fn(),
  partitionFieldsBySelection: jest.fn()
}))

// Mock useReportingReducer
jest.mock('../hooks/useReportingReducer', () => ({
  useReportingReducer: jest.fn()
}))

describe('ReportFields', () => {
  const setValue = jest.fn()
  const watch = jest.fn()
  const getValues = jest.fn()
  const mockUseFormContext = useFormContext as jest.Mock
  const mockUseSearch = useSearch as jest.Mock

  const mockReportFields: ReportFieldDefinition[] = [
    {
      dataType: 'string',
      displayName: 'Field One',
      fieldId: 'field1',
      isFilterable: false,
      isHidden: false,
      isRequired: false,
      groupBy: 'general'
    },
    {
      dataType: 'number',
      displayName: 'Field Two',
      fieldId: 'field2',
      isFilterable: true,
      isHidden: false,
      isRequired: false,
      groupBy: 'general'
    },
    {
      dataType: 'string',
      displayName: 'Entry Number',
      fieldId: 'entry.entryNumber',
      groupBy: 'entry',
      isFilterable: false,
      isHidden: false,
      isRequired: false
    },
    {
      dataType: 'string',
      displayName: 'Entry Package Size',
      fieldId: 'entry.packageSize',
      groupBy: 'entry',
      isFilterable: true,
      isHidden: false,
      isRequired: false
    }
  ]

  beforeEach(() => {
    mockUseFormContext.mockReturnValue({
      setValue,
      getValues,
      watch: watch.mockReturnValue({
        reportTypeId: 'ORDERS',
        reportCategory: 'test-category',
        selectedFields: []
      })
    })
    mockUseSearch.mockReturnValue(['', jest.fn()])

    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { useReportCategories } = require('../hooks/useReportCategories')
    useReportCategories.mockReturnValue({
      getReportCategoryFields: jest.fn().mockReturnValue(mockReportFields),
      getReportCategoryFilters: jest.fn().mockReturnValue([])
    })

    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { useReportingReducer } = require('../hooks/useReportingReducer')
    useReportingReducer.mockReturnValue({
      appliedFilters: {},
      dispatch: jest.fn()
    })

    // Mock grouping utilities
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { groupFieldsByGroupBy, partitionFieldsBySelection } = require('../lib/fieldGrouping')
    groupFieldsByGroupBy.mockReturnValue([
      {
        groupId: null,
        groupDisplayName: 'General Fields',
        fields: [mockReportFields[0], mockReportFields[1]]
      },
      {
        groupId: 'entry',
        groupDisplayName: 'Entry Fields',
        fields: [mockReportFields[2], mockReportFields[3]]
      }
    ])
    partitionFieldsBySelection.mockReturnValue({
      selectedFields: [],
      unselectedFields: [mockReportFields[0], mockReportFields[1]]
    })

    setValue.mockClear()
    getValues.mockClear()
    watch.mockClear()
  })

  function renderReportFields() {
    render(<ReportFields />, {
      store: setUpStore()
    })
  }

  it('renders with default props and displays header and list', () => {
    renderReportFields()
    expect(screen.getByTestId('band-header')).toHaveTextContent('Available Columns')
    expect(screen.getByTestId('dnd-context')).toBeInTheDocument()
    expect(screen.getByTestId('sortable-context')).toBeInTheDocument()
    expect(screen.getByTestId('list')).toBeInTheDocument()
  })

  it('should use the correct form context type', () => {
    renderReportFields()
    expect(mockUseFormContext).toHaveBeenCalled()
    // The component should be using UserReportSubmissionData type
  })

  it('should handle field selection correctly', () => {
    renderReportFields()
    // The component should render without errors with the new field structure
    expect(screen.getByTestId('dnd-context')).toBeInTheDocument()
  })

  it('should display grouped fields with group headers', () => {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { groupFieldsByGroupBy } = require('../lib/fieldGrouping')
    groupFieldsByGroupBy.mockReturnValue([
      {
        groupId: null,
        groupDisplayName: 'General Fields',
        fields: [mockReportFields[0], mockReportFields[1]]
      },
      {
        groupId: 'entry',
        groupDisplayName: 'Entry Fields',
        fields: [mockReportFields[2], mockReportFields[3]]
      }
    ])

    renderReportFields()
    expect(groupFieldsByGroupBy).toHaveBeenCalledWith(mockReportFields)
  })

  it('should partition fields by selection within groups', () => {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { partitionFieldsBySelection } = require('../lib/fieldGrouping')

    renderReportFields()
    expect(partitionFieldsBySelection).toHaveBeenCalled()
  })

  it('should render field groups with proper structure', () => {
    renderReportFields()
    // Should render the grouped structure without errors
    expect(screen.getByTestId('list')).toBeInTheDocument()
  })
})

describe('ReportFieldsHeader', () => {
  it('renders the correct header text', () => {
    render(<ReportFieldsHeader />)
    expect(screen.getByTestId('band-header')).toHaveTextContent('Available Columns')
  })
})

describe('FieldGroupHeader', () => {
  it('renders group name and field count correctly', () => {
    render(<FieldGroupHeader groupName='Entry Fields' fieldCount={3} selectedCount={0} />)

    expect(screen.getByText('Entry Fields')).toBeInTheDocument()
    expect(screen.getByText('3 fields')).toBeInTheDocument()
  })

  it('displays selected count when fields are selected', () => {
    render(<FieldGroupHeader groupName='General Fields' fieldCount={5} selectedCount={2} />)

    expect(screen.getByText('General Fields')).toBeInTheDocument()
    expect(screen.getByText('2 of 5 selected')).toBeInTheDocument()
  })

  it('handles singular field count correctly', () => {
    render(<FieldGroupHeader groupName='Single Field Group' fieldCount={1} selectedCount={0} />)

    expect(screen.getByText('1 field')).toBeInTheDocument()
  })

  it('handles selected singular field correctly', () => {
    render(<FieldGroupHeader groupName='Single Field Group' fieldCount={1} selectedCount={1} />)

    expect(screen.getByText('1 of 1 selected')).toBeInTheDocument()
  })
})
