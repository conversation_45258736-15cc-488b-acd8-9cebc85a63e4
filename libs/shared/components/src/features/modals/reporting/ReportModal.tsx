/* eslint-disable @nx/enforce-module-boundaries */
import { ButtonProps } from '@element/react-components'
import { useMemoizedTranslation, useModal, useSelectedAccount } from '@gc/hooks'
import { setContingency, setLoadingContingency, useGlobalDispatch, useReportsQueries } from '@gc/redux-store'
import { ReportTypeOptions, UserReport, UserReportSubmissionData, UserReportSubmissionSchema } from '@gc/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { memo, useCallback, useEffect, useMemo } from 'react'
import { FormProvider, useForm } from 'react-hook-form'

import TopAppBar from '../../../ui-common/header/TopAppBar'
import ModalActionSlot from '../../../ui-common/modal/ModalActionSlot'
import { ModalDefaultProps, ModalPropsType } from '../modalProps'
import { ReportDescriptionForm } from './common/ReportDescriptionForm'
import { ReportFields } from './common/ReportFields'
import { SelectReportType } from './common/SelectReportType'
import styles from './ReportModal.module.scss'

export type ReportModalProps = ModalDefaultProps & {
  isEditMode?: boolean
  userReport?: UserReport
}

// Modal Header Component
const ReportModalHeader = memo(
  ({ navigateProps, title }: { navigateProps: { icon: string; handleClose: () => void }; title: string }) => (
    <TopAppBar
      isModalTopBar
      title={title}
      leadingIconButtonProps={{
        icon: navigateProps.icon,
        onClick: () => {
          navigateProps.handleClose()
          // dispatch(setContingency())
          // openModal({ name: 'ABANDON_REPORT' })
        }
      }}
    />
  )
)

// Modal Footer Component
const ReportModalFooter = memo(
  ({ isValid, isCreatingReport, onSubmit }: { isValid: boolean; isCreatingReport: boolean; onSubmit: () => void }) => {
    const t = useMemoizedTranslation()

    const submitAction = useMemo(
      () =>
        ({
          onClick: onSubmit,
          disabled: !isValid || isCreatingReport,
          label: t('reporting.create_report.label')
        }) as ButtonProps,
      [onSubmit, isValid, isCreatingReport, t]
    )

    return <ModalActionSlot primaryAction={submitAction} />
  }
)

export function ReportModal({ setModalProps, navigateProps, isEditMode, userReport }: Readonly<ReportModalProps>) {
  const { closeModal } = useModal()
  const dispatch = useGlobalDispatch()
  const t = useMemoizedTranslation()

  const { sapAccountId, accountName } = useSelectedAccount()

  const { useCreateUserReportMutation, useUpdateUserReportMutation } = useReportsQueries()
  const [createUserReport, { isLoading: isCreatingReport }] = useCreateUserReportMutation()
  const [updateUserReport, { isLoading: isUpdatingReport }] = useUpdateUserReportMutation()

  const methods = useForm<UserReportSubmissionData>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(UserReportSubmissionSchema),
    defaultValues: isEditMode
      ? {
          version: 'current',
          userId: sapAccountId,
          createdBy: accountName,
          ...userReport
        }
      : {
          version: 'current',
          reportName: '',
          selectedFields: [],
          selectedFilters: [],
          userId: sapAccountId,
          createdBy: accountName,
          reportType: ReportTypeOptions.Live,
          reportCategory: undefined,
          reportConfigurationVersion: undefined
        }
  })

  const {
    handleSubmit,
    formState: { isValid }
  } = methods

  const onSubmit = useCallback(
    async (data: UserReportSubmissionData) => {
      dispatch(setLoadingContingency(t('reporting.creating_report.label')))

      const mutation = isEditMode
        ? updateUserReport({ ...data, reportId: userReport?.reportId ?? '' })
        : createUserReport(data)

      mutation.unwrap().finally(() => {
        dispatch(setContingency())
        closeModal()
      })
    },
    [closeModal, createUserReport, dispatch, isEditMode, t, updateUserReport, userReport?.reportId]
  )

  // Modal configuration
  const headerActions = useMemo(
    () => (
      <ReportModalHeader
        navigateProps={navigateProps}
        title={isEditMode ? t('reporting.edit_report.title') : t('reporting.create_report.title')}
      />
    ),
    [navigateProps, t, isEditMode]
  )

  const footerActions = useMemo(
    () => (
      <ReportModalFooter
        isValid={isValid}
        isCreatingReport={isCreatingReport || isUpdatingReport}
        onSubmit={handleSubmit(onSubmit)}
      />
    ),
    [isValid, isCreatingReport, isUpdatingReport, handleSubmit, onSubmit]
  )

  const modalProps: ModalPropsType = useMemo(
    () => ({
      headerActions,
      footerActions,
      sectionPadding: 'none' as const
    }),
    [headerActions, footerActions]
  )

  useEffect(() => {
    setModalProps(modalProps)
  }, [modalProps, setModalProps])

  return (
    <div id='report-modal' className={styles.container}>
      <FormProvider {...methods}>
        {/* Choose Report Type Section */}
        <SelectReportType />

        {/* Report Form Section */}
        <ReportDescriptionForm />

        {/* Report Fields Section */}
        <ReportFields />
      </FormProvider>
    </div>
  )
}
