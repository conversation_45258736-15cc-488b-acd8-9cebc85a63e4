import { ReportFieldDefinition } from '@gc/types'

import { groupFieldsByGroupBy, partitionFieldsBySelection } from './fieldGrouping'

describe('fieldGrouping utilities', () => {
  const mockFields: ReportFieldDefinition[] = [
    {
      fieldId: 'orderNumber',
      displayName: 'Order ID',
      dataType: 'string',
      isFilterable: false,
      isRequired: false,
      isHidden: false
    },
    {
      fieldId: 'grower',
      displayName: 'Farmer Name',
      dataType: 'string',
      isFilterable: false,
      isRequired: false,
      isHidden: false
    },
    {
      fieldId: 'entry.entryNumber',
      displayName: 'Entry Number',
      dataType: 'string',
      isFilterable: false,
      isRequired: false,
      isHidden: false,
      groupBy: 'entry'
    },
    {
      fieldId: 'entry.packageSize',
      displayName: 'Entry Package Size',
      dataType: 'string',
      isFilterable: true,
      isRequired: false,
      isHidden: false,
      groupBy: 'entry'
    }
  ]

  describe('groupFieldsByGroupBy', () => {
    it('should group fields by their groupBy property', () => {
      const result = groupFieldsByGroupBy(mockFields)

      expect(result).toHaveLength(2)

      // Check ungrouped fields (General Fields) come first
      expect(result[0].groupId).toBeNull()
      expect(result[0].groupDisplayName).toBe('General Fields')
      expect(result[0].fields).toHaveLength(2)
      expect(result[0].fields[0].fieldId).toBe('orderNumber')
      expect(result[0].fields[1].fieldId).toBe('grower')

      // Check entry group
      expect(result[1].groupId).toBe('entry')
      expect(result[1].groupDisplayName).toBe('Entry Fields')
      expect(result[1].fields).toHaveLength(2)
      expect(result[1].fields[0].fieldId).toBe('entry.entryNumber')
      expect(result[1].fields[1].fieldId).toBe('entry.packageSize')
    })

    it('should handle fields with no groupBy property', () => {
      const fieldsWithoutGroup = mockFields.filter((f) => !f.groupBy)
      const result = groupFieldsByGroupBy(fieldsWithoutGroup)

      expect(result).toHaveLength(1)
      expect(result[0].groupId).toBeNull()
      expect(result[0].groupDisplayName).toBe('General Fields')
      expect(result[0].fields).toHaveLength(2)
    })

    it('should handle empty fields array', () => {
      const result = groupFieldsByGroupBy([])
      expect(result).toHaveLength(0)
    })

    it('should sort groups with ungrouped first, then alphabetically', () => {
      const fieldsWithMultipleGroups: ReportFieldDefinition[] = [
        { ...mockFields[0], groupBy: 'zebra' },
        { ...mockFields[1], groupBy: 'alpha' },
        { ...mockFields[2], groupBy: undefined }, // no groupBy
        { ...mockFields[3], groupBy: 'beta' }
      ]

      const result = groupFieldsByGroupBy(fieldsWithMultipleGroups)

      expect(result).toHaveLength(4)
      expect(result[0].groupDisplayName).toBe('General Fields') // null groupBy first
      expect(result[1].groupDisplayName).toBe('Alpha Fields') // alpha
      expect(result[2].groupDisplayName).toBe('Beta Fields') // beta
      expect(result[3].groupDisplayName).toBe('Zebra Fields') // zebra
    })

    it('should generate correct display names for known groups', () => {
      const fieldsWithKnownGroups: ReportFieldDefinition[] = [
        { ...mockFields[0], groupBy: 'entry' },
        { ...mockFields[1], groupBy: 'farmer' },
        { ...mockFields[2], groupBy: 'order' },
        { ...mockFields[3], groupBy: 'pricing' }
      ]

      const result = groupFieldsByGroupBy(fieldsWithKnownGroups)

      expect(result.find((g) => g.groupId === 'entry')?.groupDisplayName).toBe('Entry Fields')
      expect(result.find((g) => g.groupId === 'farmer')?.groupDisplayName).toBe('Farmer Fields')
      expect(result.find((g) => g.groupId === 'order')?.groupDisplayName).toBe('Order Fields')
      expect(result.find((g) => g.groupId === 'pricing')?.groupDisplayName).toBe('Pricing Fields')
    })

    it('should generate display names for unknown groups', () => {
      const fieldsWithUnknownGroup: ReportFieldDefinition[] = [{ ...mockFields[0], groupBy: 'customGroup' }]

      const result = groupFieldsByGroupBy(fieldsWithUnknownGroup)

      expect(result[0].groupDisplayName).toBe('CustomGroup Fields')
    })
  })

  describe('partitionFieldsBySelection', () => {
    const selectedFieldIds = ['orderNumber', 'entry.entryNumber']

    it('should correctly partition fields into selected and unselected', () => {
      const result = partitionFieldsBySelection(mockFields, selectedFieldIds)

      expect(result.selectedFields).toHaveLength(2)
      expect(result.unselectedFields).toHaveLength(2)

      expect(result.selectedFields[0].fieldId).toBe('orderNumber')
      expect(result.selectedFields[1].fieldId).toBe('entry.entryNumber')

      expect(result.unselectedFields[0].fieldId).toBe('grower')
      expect(result.unselectedFields[1].fieldId).toBe('entry.packageSize')
    })

    it('should handle empty selection', () => {
      const result = partitionFieldsBySelection(mockFields, [])

      expect(result.selectedFields).toHaveLength(0)
      expect(result.unselectedFields).toHaveLength(4)
    })

    it('should handle all fields selected', () => {
      const allFieldIds = mockFields.map((f) => f.fieldId)
      const result = partitionFieldsBySelection(mockFields, allFieldIds)

      expect(result.selectedFields).toHaveLength(4)
      expect(result.unselectedFields).toHaveLength(0)
    })

    it('should handle numeric field IDs', () => {
      const numericSelectedIds = ['123', '456']
      const fieldsWithNumericIds = mockFields.map((field, index) => ({
        ...field,
        fieldId: (123 + index * 333).toString()
      }))

      const result = partitionFieldsBySelection(fieldsWithNumericIds, numericSelectedIds)

      expect(result.selectedFields).toHaveLength(2)
      expect(result.unselectedFields).toHaveLength(2)
    })

    it('should handle empty fields array', () => {
      const result = partitionFieldsBySelection([], selectedFieldIds)

      expect(result.selectedFields).toHaveLength(0)
      expect(result.unselectedFields).toHaveLength(0)
    })
  })
})
