import { UniqueIdentifier } from '@dnd-kit/core'
import {
  ReportFieldDefinition,
  ReportFilterDefinition,
  SelectedFilter,
  SelectedReportField,
  SelectedReportFilter
} from '@gc/types'

// Utility function moved outside component to prevent recreation
export const formatSelectedFields = (
  columns: ReportFieldDefinition[],
  selectedColumns: Set<UniqueIdentifier>
): SelectedReportField[] => {
  return columns
    .filter((col) => selectedColumns.has(col.fieldId))
    .map((col, index) => ({
      ...col,
      accessor: col.accessor,
      fieldId: col.fieldId,
      displayOrder: index,
      groupBy: col.groupBy
    }))
}

function normalizeValue(value: string): string {
  if (value === 'true') {
    return 'Yes'
  } else if (value === 'false') {
    return 'No'
  }
  return value
}

// Convert form's selectedFilters back to SelectedFilter format for reducer initialization
export const convertFormFiltersToSelectedFilter = (
  formSelectedFilters: SelectedReportFilter[],
  filtersMap: Map<string, ReportFilterDefinition>
): SelectedFilter => {
  return formSelectedFilters.reduce<SelectedFilter>((acc, formFilter) => {
    const filterDefinition = filtersMap.get(formFilter.filterId)
    if (!filterDefinition || !formFilter.selectedOptions?.length) {
      return acc
    }

    // Convert selectedOptions back to the format expected by the reducer
    acc[formFilter.filterId] = formFilter.selectedOptions.map((value) => {
      const stringValue = typeof value === 'string' ? value : String(value)
      const normalizedValue = normalizeValue(stringValue)

      return {
        value: stringValue,
        label: normalizedValue
      }
    })

    return acc
  }, {})
}
