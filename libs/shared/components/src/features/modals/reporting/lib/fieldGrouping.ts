import { ReportFieldDefinition } from '@gc/types'

export interface FieldGroup {
  groupId: string | null
  groupDisplayName: string
  fields: ReportFieldDefinition[]
}

/**
 * Groups fields by their groupBy property and returns organized groups
 * with proper display names and ordering
 */
export function groupFieldsByGroupBy(fields: ReportFieldDefinition[]): FieldGroup[] {
  // Create a map to group fields
  const groupMap = new Map<string | null, ReportFieldDefinition[]>()

  // Group fields by their groupBy property
  fields.forEach((field) => {
    const groupKey = field.groupBy || null
    const existingFields = groupMap.get(groupKey) || []
    groupMap.set(groupKey, [...existingFields, field])
  })

  // Convert to FieldGroup array with proper display names
  const groups: FieldGroup[] = Array.from(groupMap.entries()).map(([groupId, groupFields]) => ({
    groupId,
    groupDisplayName: getGroupDisplayName(groupId),
    fields: groupFields
  }))

  // Sort groups: ungrouped fields first, then alphabetically by display name
  return groups.sort((a, b) => {
    if (a.groupId === null) return -1
    if (b.groupId === null) return 1
    return a.groupDisplayName.localeCompare(b.groupDisplayName)
  })
}

/**
 * Maps group IDs to user-friendly display names
 */
function getGroupDisplayName(groupId: string | null): string {
  if (groupId === null) {
    return 'General Fields'
  }

  // Map known group IDs to display names
  const groupDisplayNames: Record<string, string> = {
    entry: 'Entry Fields',
    farmer: 'Farmer Fields',
    order: 'Order Fields',
    pricing: 'Pricing Fields'
  }

  return groupDisplayNames[groupId] || `${groupId.charAt(0).toUpperCase()}${groupId.slice(1)} Fields`
}

/**
 * Splits fields within a group into selected and unselected
 */
export function partitionFieldsBySelection(
  fields: ReportFieldDefinition[],
  selectedFieldIds: (string | number)[] | Set<string | number>
): { selectedFields: ReportFieldDefinition[]; unselectedFields: ReportFieldDefinition[] } {
  const selected: ReportFieldDefinition[] = []
  const unselected: ReportFieldDefinition[] = []

  // Convert to Set if array is provided for O(1) lookups
  const selectedSet = selectedFieldIds instanceof Set ? selectedFieldIds : new Set(selectedFieldIds)

  fields.forEach((field) => {
    if (selectedSet.has(field.fieldId)) {
      selected.push(field)
    } else {
      unselected.push(field)
    }
  })

  return { selectedFields: selected, unselectedFields: unselected }
}
