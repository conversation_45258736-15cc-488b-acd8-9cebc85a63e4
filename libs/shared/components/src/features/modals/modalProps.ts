import { OrderDetailsCBUS, Product, QuoteDetails, ReviewType, Usage } from '@gc/types'
import { ReactNode } from 'react'

import { ModalState } from '../../ui-common/modal/ModalContainer'
import AbandonModal, { AbandonModalProps } from './abandon/AbandonModal'
import AddFarmerModal, { AddFarmerModalProps } from './add-farmer/AddFarmerModal'
import FarmerSearchModal from './add-farmer/FarmerSearchModal'
import AdjustSplitModal, { AdjustSplitModalProps } from './adjust-split/AdjustSplitModal'
import AdvanceApplicationModal from './advance-application/AdvanceApplicationModal'
import AdvanceApplicationTermsAndConditionsModal from './advance-application/TermsAndConditionsModal'
import AttentionModal, { AttentionModalProps } from './attention/AttentionModal'
import { ContactModalProps } from './contact/ContactModal'
import CreateEditModal, { CreateEditModalProps } from './create-edit/CreateEditModal'
import AddToDeliveryModal, { AddToDeliveryModalProps } from './delivery/AddToDeliveryModal'
import CreateDeliveryModal, { CreateDeliveryModalProps } from './delivery/CreateDeliveryModal'
import ReviewDeliveriesModal, { ReviewDeliveriesModalProps } from './delivery/ReviewDeliveriesModal'
import AddDiscountQtyModal, { AddDiscountQtyModalProps } from './discounts/AddDiscountQtyModal'
import AddDiscountsModal, { AddDiscountsModalProps } from './discounts/AddDiscountsModal'
import { LocationPickerModalProps } from './location-picker/LocationPickerModal'
import PaymentTermsModal, { PaymentTermsModalProps } from './payment-terms/PaymentTermsModal'
import ProductDetails, { ProductDetailsProps } from './product-details/ProductDetails'
import SelectProductsModal, { SelectProductsModalProps } from './product-select/SelectProductsModal'
import SelectQuantity, { SelectQuantityModalProps } from './product-select/SelectQuantity'
import AddTransferQtyModal, { AddTransferQtyModalProps } from './product-transfer/AddTransferQtyModal'
import ReceivingOrderModal, { ReceivingOrderModalProps } from './product-transfer/ReceivingOrderModal'
import TransferTypeModal, { TransferTypeModalProps } from './product-transfer/TransferTypeModal'
import ReconfirmOrderModal, { ReconfirmOrderModalProps } from './reconfirm/ReconfirmOrderModal'
import { ReportModal, ReportModalProps } from './reporting/ReportModal'
import { ResumeProcessModalProps } from './resume-process/ResumeProcessModal'
import SelectAccountModal, { SelectAccountModalProps } from './select-account/SelectAccountModal'
import SelectFarmer, { SelectFarmerProps } from './select-farmer/SelectFarmer'
import AbandonThirdPartyFinancingModal, {
  AbandonThirdPartyFinancingModalProps
} from './third-party-financing/abandon/AbandonThirdPartyFinancingModal'
import CreateThirdPartyFinancingModal, {
  CreateThirdPartyFinancingModalProps
} from './third-party-financing/create/CreateThirdPartyFinancingModal'
import ViewThirdPartyFinancingModal, {
  ViewThirdPartyFinancingModalProps
} from './third-party-financing/view/ViewThirdPartyFinancingModal'

export type ModalPropsType = {
  headerActions?: ReactNode
  footerActions?: ReactNode
  modalSize?: string | undefined
  modalTitle?: string
  sectionPadding?: 'none' | 'elementDefault' // default is 16px
}
export type ModalDefaultProps = {
  onCloseTrigger?: () => void
  setModalProps: (props: ModalPropsType) => void
  navigateProps: { icon: string; handleClose: () => void }
}

export type SelectSupplierProps = {
  usage: Usage
  type: string
}

export type FiltersModalProps = {
  type: string
}

const commonModals = {
  CONFIRMATION: AttentionModal
}

const createEditModals = {
  SELECT_FARMER: SelectFarmer,
  CREATE_ORDER: CreateEditModal,
  CREATE_QUOTE: CreateEditModal,
  SELECT_PAYMENT_TERMS: PaymentTermsModal,
  SELECT_ADD_PAYER: SelectFarmer,
  ADJUST_SPLIT: AdjustSplitModal,
  PRODUCT_DETAILS: ProductDetails,
  SELECT_PRODUCTS: SelectProductsModal,
  SELECT_QUANTITY: SelectQuantity,
  ADD_DISCOUNTS: AddDiscountsModal,
  ADD_DISCOUNT_QTY: AddDiscountQtyModal,
  RECONFIRM_ORDER: ReconfirmOrderModal,
  CREATE_REPORT: ReportModal,
  SELECT_ACCOUNT: SelectAccountModal,
  VIEW_THIRD_PARTY_FINANCING: ViewThirdPartyFinancingModal,
  CREATE_THIRD_PARTY_FINANCING: CreateThirdPartyFinancingModal,
  ABANDON_THIRD_PARTY_FINANCING: AbandonThirdPartyFinancingModal
}

export const createEditDeliveryModals = {
  CREATE_DELIVERY: CreateDeliveryModal,
  ADD_TO_DELIVERY: AddToDeliveryModal,
  REVIEW_DELIVERIES: ReviewDeliveriesModal,
  ABANDON_DELIVERY: AbandonModal,
  SELECT_ACCOUNT: SelectAccountModal
}

export const selectAccountModals = {
  SELECT_ACCOUNT: SelectAccountModal
}

export const productTransferModals = {
  TRANSFER_TYPE: TransferTypeModal,
  ADD_TRANSFER_QTY: AddTransferQtyModal,
  RECEIVING_ORDER: ReceivingOrderModal,
  ABANDON_TRANSFER: AbandonModal
}

export const createEditFarmerModals = {
  ADD_FARMER: AddFarmerModal,
  FARMER_SEARCH: FarmerSearchModal
}

export const createEditAdvanceApplicationModals = {
  ADVANCE_APPLICATION: AdvanceApplicationModal,
  ADVANCE_APPLICATION_TERMS_AND_CONDITIONS: AdvanceApplicationTermsAndConditionsModal
}

export const createEditModalNames = Object.keys(createEditModals) as (keyof typeof createEditModals)[]
export const createEditDeliveryModalNames = Object.keys(
  createEditDeliveryModals
) as (keyof typeof createEditDeliveryModals)[]
export const createEditFarmerModalNames = Object.keys(createEditFarmerModals) as (keyof typeof createEditFarmerModals)[]
export const productTransferModalNames = Object.keys(productTransferModals) as (keyof typeof productTransferModals)[]
export const commonModalNames = Object.keys(commonModals) as (keyof typeof commonModals)[]
export const createEditAdvanceApplicationModalNames = Object.keys(
  createEditAdvanceApplicationModals
) as (keyof typeof createEditAdvanceApplicationModals)[]
export const selectAccountModalNames = Object.keys(selectAccountModals) as (keyof typeof selectAccountModals)[]

const ModalToBodyMap = {
  ...createEditModals,
  ...createEditDeliveryModals,
  ...createEditFarmerModals,
  ...createEditAdvanceApplicationModals,
  ...commonModals,
  ...selectAccountModals,
  ...productTransferModals
}

export const getModals = (modalNames: (keyof typeof ModalToBodyMap)[]): Record<string, ModalState> => {
  return modalNames.reduce(
    (acc, modalName) => {
      acc[modalName] = {
        modalBody: ModalToBodyMap[modalName]
      }
      return acc
    },
    {} as Record<string, ModalState>
  )
}

// these are types for modals specific to features
// ORDERS
export type ReviewOrderModalProps = {
  existingOrderDetails?: OrderDetailsCBUS
} & ModalDefaultProps

// QUOTES
export type ReviewEditQuoteModalProps = {
  existingQuoteDetails?: QuoteDetails
} & ModalDefaultProps

// NB ORDERS
export type SelectProductsTableProps = {
  inEditMode: boolean
  selectedCrop: string
  usage?: Usage
} & ModalDefaultProps

export type UpdateProductProps = {
  product: Product
  isUpdateFromReview?: boolean
  productDeleteEnabled?: boolean
} & ModalDefaultProps

export type ViewProductProps = {
  product: Product
  isUpdateFromReview?: boolean
  productDeleteEnabled?: boolean
} & ModalDefaultProps

export type ReviewProductsMobileProps = {
  title: string
  enableDeleteProduct?: boolean
  usage?: Usage
  type?: ReviewType
} & ModalDefaultProps

export type AbandonOrderProps = {
  type?: 'cancel' | 'abandon' | 'discard'
  usage: Usage
} & ModalDefaultProps

export type ShippingInfoProps = {
  type?: 'preference' | 'location'
  productCode: string
} & ModalDefaultProps

type OmitFields<T> = Omit<T, 'navigateProps' | 'setModalProps'> & { onCloseTrigger?: () => void }

export type ModalAndProps =
  | { name: 'SELECT_QUANTITY'; props: OmitFields<SelectQuantityModalProps> }
  | { name: 'SELECT_PRODUCTS'; props: OmitFields<SelectProductsModalProps> }
  | { name: 'ADD_DISCOUNTS'; props: OmitFields<AddDiscountsModalProps> }
  | { name: 'ADD_DISCOUNT_QTY'; props: OmitFields<AddDiscountQtyModalProps> }
  | { name: 'SELECT_PAYMENT_TERMS'; props: OmitFields<PaymentTermsModalProps> }
  | { name: 'CREATE_ORDER'; props: OmitFields<CreateEditModalProps> }
  | { name: 'CREATE_QUOTE'; props: OmitFields<CreateEditModalProps> }
  | { name: 'VIEW_CONTACT'; props: OmitFields<ContactModalProps> }
  | { name: 'ADJUST_SPLIT'; props: OmitFields<AdjustSplitModalProps> }
  | { name: 'ABANDON_ORDER'; props?: OmitFields<AbandonOrderProps> }
  | { name: 'ABANDON_STOCK_ORDER'; props?: undefined }
  | { name: 'REVIEW_ORDER'; props: OmitFields<ReviewOrderModalProps> }
  | { name: 'SELECT_ADD_PAYER'; props: OmitFields<SelectFarmerProps> }
  | { name: 'PRODUCT_DETAILS'; props: OmitFields<ProductDetailsProps> }
  | { name: 'ABANDON_QUOTE'; props?: undefined }
  | { name: 'ABANDON_REPORT'; props?: undefined }
  | { name: 'SELECT_FARMER'; props: OmitFields<SelectFarmerProps> }
  | { name: 'SELECT_FILTERS'; props: OmitFields<FiltersModalProps> }
  | { name: 'SELECT_SUPPLIER'; props: OmitFields<SelectSupplierProps> }
  | { name: 'REVIEW_EDIT_QUOTE'; props: OmitFields<ReviewEditQuoteModalProps> }
  | { name: 'CREATE_DELIVERY'; props: OmitFields<CreateDeliveryModalProps> }
  | { name: 'ADD_TO_DELIVERY'; props: OmitFields<AddToDeliveryModalProps> }
  | { name: 'REVIEW_DELIVERIES'; props: OmitFields<ReviewDeliveriesModalProps> }
  | { name: 'ABANDON_DELIVERY'; props: OmitFields<AbandonModalProps> }
  | { name: 'RECONFIRM_ORDER'; props: OmitFields<ReconfirmOrderModalProps> }
  | { name: 'ADD_FARMER'; props?: OmitFields<AddFarmerModalProps> }
  | { name: 'FARMER_SEARCH'; props?: undefined }
  | { name: 'ADD_PRODUCTS'; props: OmitFields<SelectProductsTableProps> }
  | { name: 'UPDATE_PRODUCT'; props: OmitFields<UpdateProductProps> }
  | { name: 'CART_REVIEW_PRODUCTS_MOBILE'; props: OmitFields<ReviewProductsMobileProps> }
  | { name: 'CONFIRMATION'; props: OmitFields<AttentionModalProps> }
  | { name: 'CREATE_REPORT'; props: OmitFields<ReportModalProps> }
  | { name: 'TRANSFER_TYPE'; props: OmitFields<TransferTypeModalProps> }
  | { name: 'ADD_TRANSFER_QTY'; props: OmitFields<AddTransferQtyModalProps> }
  | { name: 'RECEIVING_ORDER'; props: OmitFields<ReceivingOrderModalProps> }
  | { name: 'SELECT_LOCATION'; props?: OmitFields<LocationPickerModalProps> }
  | { name: 'VIEW_PRODUCT'; props: OmitFields<ViewProductProps> }
  | { name: 'SHIPPING_INFO'; props: OmitFields<ShippingInfoProps> }
  | { name: 'ADVANCE_APPLICATION'; props: OmitFields<ModalDefaultProps> }
  | { name: 'ADVANCE_APPLICATION_TERMS_AND_CONDITIONS'; props: OmitFields<ModalDefaultProps> }
  | { name: 'SELECT_ACCOUNT'; props: OmitFields<SelectAccountModalProps> }
  | { name: 'RESUME_PROCESS'; props: OmitFields<ResumeProcessModalProps> }
  | { name: 'VIEW_THIRD_PARTY_FINANCING'; props?: OmitFields<ViewThirdPartyFinancingModalProps> }
  | { name: 'CREATE_THIRD_PARTY_FINANCING'; props?: OmitFields<CreateThirdPartyFinancingModalProps> }
  | { name: 'ABANDON_THIRD_PARTY_FINANCING'; props?: OmitFields<AbandonThirdPartyFinancingModalProps> }
