/* eslint-disable @nx/enforce-module-boundaries */
import { ButtonProps } from '@element/react-button'
import { Typo<PERSON>aption, TypoOverline, TypoSubtitle } from '@element/react-components'
import { Group } from '@element/react-group'
import { Select } from '@element/react-select'
import { Textfield } from '@element/react-textfield'
import { useMemoizedTranslation, useSelectedAccount } from '@gc/hooks'
import { BillToParty, Option, ThirdPartyFinancingFormData, ThirdPartyFinancingSchema } from '@gc/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { isUndefined } from 'es-toolkit'
import { memo, useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'

import { CurrencyField } from '../../../../ui-common/form/CurrencyField/CurrencyField'
import { FileUploadField } from '../../../../ui-common/form/FileUploadField/FileUploadField'
import TopAppBar from '../../../../ui-common/header/TopAppBar'
import ModalActionSlot from '../../../../ui-common/modal/ModalActionSlot'
import { LineSkeleton } from '../../../../ui-common/skeleton/SkeletonComponents'
import { ModalDefaultProps, ModalPropsType } from '../../modalProps'
import { THIRD_PARTY_FINANCING_CONSTANTS } from '../constants'
import { usePaymentTerms } from '../hooks/usePaymentTerms'
import { useThirdPartyFinancingData } from '../hooks/useThirdPartyFinancingData'
import styles from './CreateThirdPartyFinancingModal.module.scss'

const TIMEOUT_DELAY = 175
const { VALIDATION, FORM_IDS, TEST_IDS } = THIRD_PARTY_FINANCING_CONSTANTS

export interface CreateThirdPartyFinancingModalProps extends ModalDefaultProps {
  billToParties: BillToParty[]
  editingTermId?: string
  orderId: string
}

// Modal Header Component
const CreateThirdPartyFinancingModalHeader = memo(
  ({ navigateProps, title }: { navigateProps: { icon: string; handleClose: () => void }; title: string }) => (
    <TopAppBar
      isModalTopBar
      title={title}
      leadingIconButtonProps={{
        icon: 'arrow_back',
        ariaLabel: 'Back',
        onClick: navigateProps.handleClose
      }}
    />
  )
)

// Modal Footer Component
const CreateThirdPartyFinancingModalFooter = memo(
  ({ onSubmit, onCancel }: { onSubmit: () => void; onCancel: () => void }) => {
    const t = useMemoizedTranslation()

    const submitAction = useMemo(
      () =>
        ({
          onClick: onSubmit,
          label: t('common.submit.label', 'Submit')
        }) as ButtonProps,
      [onSubmit, t]
    )

    const cancelAction = useMemo(
      () =>
        ({
          variant: 'outlined',
          onClick: onCancel,
          label: t('common.cancel.label', 'Cancel')
        }) as ButtonProps,
      [onCancel, t]
    )

    return <ModalActionSlot primaryAction={submitAction} secondaryAction={cancelAction} />
  }
)

const PaymentTermsSkeleton = memo(function PaymentTermsSkeleton(): JSX.Element {
  return (
    <>
      <LineSkeleton width='60%' />
      <LineSkeleton width='40%' />
    </>
  )
})

export function CreateThirdPartyFinancingModal({
  setModalProps,
  navigateProps,
  billToParties,
  orderId,
  editingTermId
}: CreateThirdPartyFinancingModalProps): JSX.Element {
  const isEditing = !!editingTermId

  const t = useMemoizedTranslation()
  const { sapAccountId } = useSelectedAccount()

  // Use the custom hook for data management
  const { requestedTerms, primaryBillToParty, addPendingForm, showErrorNotification, selectedPaymentTerm } =
    useThirdPartyFinancingData({ billToParties, orderId })
  const { paymentTermsOptions, isPaymentTermsLoading } = usePaymentTerms(primaryBillToParty)

  const defaultValues: ThirdPartyFinancingFormData = useMemo(() => {
    if (isEditing) {
      const term = requestedTerms.find((term) => term.id === editingTermId)
      if (term) {
        return {
          ...term,
          amount: term.amount.toString(),
          primaryAccountId: sapAccountId || '',
          farmer: term.farmer ?? primaryBillToParty?.sapAccountId ?? ''
        }
      }
    }

    return {
      amount: '',
      comments: '',
      memberName: '',
      accountNumber: '',
      financingTerm: '',
      financingTermDescription: '',
      creditAuthorizationLetter: undefined,
      farmer: primaryBillToParty?.sapAccountId ?? '',

      // Helper hidden fields
      orderId,
      primaryAccountId: sapAccountId || '',
      secondaryAccountId: primaryBillToParty?.sapAccountId ?? ''
    } as ThirdPartyFinancingFormData
  }, [isEditing, sapAccountId, primaryBillToParty?.sapAccountId, requestedTerms, editingTermId, orderId])

  // Form setup
  const {
    register,
    setValue,
    watch,
    handleSubmit,
    formState: { errors }
  } = useForm<ThirdPartyFinancingFormData>({
    defaultValues,
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(ThirdPartyFinancingSchema)
  })

  const watchedValues = watch()
  const farmerOptions = useMemo(
    () =>
      billToParties.map((party) => ({
        text: party.name,
        id: party.sapAccountId,
        value: party.sapAccountId
      })),
    [billToParties]
  )

  // Handle form submission
  const onSubmit = useCallback(
    (data: ThirdPartyFinancingFormData) => {
      try {
        addPendingForm(data)
        navigateProps.handleClose()
      } catch (error) {
        console.error('Error adding form to pending state:', error)
        showErrorNotification(t('third_party_financing.submission_error'))
      }
    },
    [addPendingForm, showErrorNotification, t, navigateProps]
  )

  // Handle input changes
  const handleInputChange = useCallback(
    (field: keyof ThirdPartyFinancingFormData) => (event: React.ChangeEvent<HTMLInputElement>) => {
      setValue(field, event.target.value, { shouldValidate: true })
    },
    [setValue]
  )

  // Handle select changes
  const handleSelectChange = useCallback(
    (field: keyof ThirdPartyFinancingFormData) => (option: Option) => {
      setValue(field, option.value, { shouldValidate: true })
    },
    [setValue]
  )

  const handleFinancingTermChange = useCallback(
    (option: Option) => {
      setValue('financingTerm', option.value, { shouldValidate: true })
      setValue('financingTermDescription', option.text, { shouldValidate: true })
    },
    [setValue]
  )

  // Special handler for account number - only allow digits and limit to 10
  const handleAccountNumberChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = event.target.value
      const numericValue = inputValue.replace(/\D/g, '').slice(0, VALIDATION.ACCOUNT_NUMBER_LENGTH)
      setValue('accountNumber', numericValue, { shouldValidate: true })
    },
    [setValue]
  )

  // Modal configuration
  const headerActions = useMemo(
    () => (
      <CreateThirdPartyFinancingModalHeader
        navigateProps={navigateProps}
        title={t('third_party_financing.new_request.label')}
      />
    ),
    [navigateProps, t]
  )

  const footerActions = useMemo(
    () => (
      <CreateThirdPartyFinancingModalFooter onSubmit={handleSubmit(onSubmit)} onCancel={navigateProps.handleClose} />
    ),
    [handleSubmit, onSubmit, navigateProps.handleClose]
  )

  const modalProps: ModalPropsType = useMemo(
    () => ({
      headerActions,
      footerActions,
      sectionPadding: 'none' as const
    }),
    [headerActions, footerActions]
  )

  useEffect(() => {
    setModalProps(modalProps)
  }, [setModalProps, modalProps])

  useEffect(() => {
    if (selectedPaymentTerm?.code && !watchedValues.financingTerm) {
      const timeoutId = setTimeout(() => {
        setValue('financingTerm', selectedPaymentTerm.code, {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true
        })
      }, TIMEOUT_DELAY)

      return () => clearTimeout(timeoutId)
    }
  }, [selectedPaymentTerm?.code, watchedValues.financingTerm, setValue])

  return (
    <div role='form' aria-labelledby='form-title' className={styles.container} id={FORM_IDS.FORM}>
      <div id='form-title' className='sr-only'>
        Third-Party Financing Application Form
      </div>

      {/* Farmer Section */}
      <div className={styles.section}>
        <Select
          {...register('farmer')}
          hoisted
          label={t('third_party_financing_form.farmer.label')}
          variant='outlined'
          value={watchedValues.farmer || ''}
          options={farmerOptions}
          onChange={handleSelectChange('farmer')}
          valid={isUndefined(errors.farmer)}
          helperText={errors.farmer?.message}
          style={{ display: 'flex', alignItems: 'center' }}
          data-testid={TEST_IDS.FARMER_SELECT}
        />
      </div>

      {/* Actual Payment Terms Section */}
      <div className={styles.section}>
        <TypoOverline className={styles.sectionTitle}>ACTUAL PAYMENT TERMS</TypoOverline>

        <div role='region' className={styles.paymentTermsCard} aria-labelledby='payment-terms-title'>
          <div id='payment-terms-title' className='sr-only'>
            Current Payment Terms Information
          </div>
          {isPaymentTermsLoading ? (
            <PaymentTermsSkeleton />
          ) : (
            <>
              <TypoSubtitle bold level={2}>
                {selectedPaymentTerm?.title || t('third_party_financing.no_payment_terms.label')}
              </TypoSubtitle>
              {selectedPaymentTerm?.description && <TypoCaption>{selectedPaymentTerm.description}</TypoCaption>}
            </>
          )}
        </div>
      </div>

      <div
        role='group'
        aria-labelledby='financing-section-title'
        className={`${styles.thirdPartyFinancingSection} ${styles.section}`}
      >
        <div className={styles.thirdPartyFinancingTitle}>
          <TypoOverline id='financing-section-title' className={styles.sectionTitle}>
            ADD THIRD-PARTY FINANCING
          </TypoOverline>
        </div>

        {/* Form Fields */}
        <Group fullWidth gap='airy' direction='vertical'>
          {/* Financing Option */}
          <div className={styles.field}>
            <Select
              {...register('financingTerm')}
              hoisted
              required
              variant='outlined'
              label={t('third_party_financing_form.financing_option.label')}
              helperText={errors.financingTerm?.message || 'Required'}
              value={watchedValues.financingTerm || ''}
              options={paymentTermsOptions}
              onChange={handleFinancingTermChange}
              valid={isUndefined(errors.financingTerm)}
              style={{ display: 'flex', alignItems: 'center' }}
              data-testid={TEST_IDS.FINANCING_TERM_SELECT}
            />
          </div>

          {/* Member Name */}
          <Textfield
            {...register('memberName')}
            fullWidth
            required
            helperTextPersistent
            variant='outlined'
            label={t('third_party_financing_form.member_name.label')}
            placeholder={t('third_party_financing_form.member_name.placeholder')}
            value={watchedValues.memberName ?? ''}
            helperText={errors.memberName?.message || 'Required'}
            onChange={handleInputChange('memberName')}
            valid={isUndefined(errors.memberName)}
            data-testid={TEST_IDS.MEMBER_NAME_TEXTFIELD}
          />

          {/* Amount */}
          <CurrencyField
            required
            name='amount'
            value={watchedValues.amount || ''}
            onChange={(value) => setValue('amount', value, { shouldValidate: true })}
            error={errors.amount?.message}
            label={t('third_party_financing_form.amount.label')}
            placeholder={t('third_party_financing_form.amount.placeholder')}
            helperText='Required'
            dataTestId={TEST_IDS.AMOUNT_TEXTFIELD}
          />

          {/* Account Number */}
          <Textfield
            {...register('accountNumber')}
            fullWidth
            required
            helperTextPersistent
            pattern='[0-9]*'
            inputMode='numeric'
            variant='outlined'
            label={t('third_party_financing_form.account_number.label')}
            helperText={errors.accountNumber?.message || 'Required'}
            placeholder={t('third_party_financing_form.account_number.placeholder')}
            onChange={handleAccountNumberChange}
            valid={isUndefined(errors.accountNumber)}
            value={watchedValues.accountNumber || ''}
            maxlength={VALIDATION.ACCOUNT_NUMBER_LENGTH}
            data-testid={TEST_IDS.ACCOUNT_NUMBER_TEXTFIELD}
          />

          {/* Credit Authorization Letter */}
          <FileUploadField
            required
            name='creditAuthorizationLetter'
            value={watchedValues.creditAuthorizationLetter || null}
            onChange={(file) => setValue('creditAuthorizationLetter', file, { shouldValidate: true })}
            error={errors.creditAuthorizationLetter?.message}
            ariaLabel='Upload credit authorization letter'
            helperText={t('third_party_financing_form.credit_authorization_letter.helper_text')}
            placeholder={t('third_party_financing_form.credit_authorization_letter.placeholder')}
            dataTestId={TEST_IDS.CREDIT_AUTHORIZATION_LETTER_FILE_UPLOAD}
          />

          {/* Comments */}
          <Textfield
            {...register('comments')}
            fullWidth
            textarea
            variant='outlined'
            placeholder={t('third_party_financing_form.comments.placeholder')}
            value={watchedValues.comments ?? ''}
            onChange={handleInputChange('comments')}
            noResize={false}
            data-testid={TEST_IDS.COMMENTS_TEXTFIELD}
          />
        </Group>
      </div>
    </div>
  )
}

export default CreateThirdPartyFinancingModal
