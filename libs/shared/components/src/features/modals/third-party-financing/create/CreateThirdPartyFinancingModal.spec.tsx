/* eslint-disable @nx/enforce-module-boundaries */
import { setUpStore } from '@gc/redux-store'
import { TestModal } from '@gc/shared/test'
import { BillToParty } from '@gc/types'
import { actAwait, render, screen } from '@gc/utils'
import userEvent from '@testing-library/user-event'

import { THIRD_PARTY_FINANCING_CONSTANTS } from '../constants'
import CreateThirdPartyFinancingModal, { CreateThirdPartyFinancingModalProps } from './CreateThirdPartyFinancingModal'

const { TEST_IDS } = THIRD_PARTY_FINANCING_CONSTANTS

// Mock the custom hook
jest.mock('../hooks/useThirdPartyFinancingData', () => ({
  useThirdPartyFinancingData: jest.fn(() => ({
    selectedFarmer: { name: 'Test Farmer', isPrimaryBillTo: true },
    selectedPaymentTerm: {
      code: 'NET30',
      title: 'Net 30 Days',
      description: 'Payment due in 30 days'
    },
    submitForm: jest.fn(),
    showSuccessNotification: jest.fn(),
    showErrorNotification: jest.fn()
  }))
}))

// Mock the payment terms hook
jest.mock('../hooks/usePaymentTerms', () => ({
  usePaymentTerms: jest.fn(() => ({
    paymentTerms: [
      { code: 'NET30', description: 'Net 30 Days' },
      { code: 'NET60', description: 'Net 60 Days' }
    ],
    paymentTermsOptions: [
      { id: 'NET30', text: 'Net 30 Days', value: 'NET30' },
      { id: 'NET60', text: 'Net 60 Days', value: 'NET60' }
    ],
    selectedPaymentTerm: {
      code: 'NET30',
      title: 'Net 30 Days',
      description: 'Payment due in 30 days'
    },
    isPaymentTermsLoading: false
  }))
}))

// Mock hooks
jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useModal: jest.fn(() => ({ closeModal: jest.fn() })),
  useMemoizedTranslation: jest.fn(() => (key: string, fallback?: string) => fallback || key)
}))

jest.mock('@gc/hooks/useFasteStore', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' })
}))

describe('CreateThirdPartyFinancingModal', () => {
  const mockNavigateProps = { handleClose: jest.fn(), icon: 'close' }
  const mockBillToParties: BillToParty[] = [
    {
      isPrimaryBillTo: true,
      name: 'Acme Corporation',
      actualPaymentTerm: 'Net 30',
      paymentTerm: 'N30',
      paymentTermDescription: 'Payment due in 30 days',
      percentage: 100.0,
      sapAccountId: 'SAP123456789',
      city: 'Metropolis',
      state: 'NY'
    },
    {
      isPrimaryBillTo: false,
      name: 'Globex Industries',
      actualPaymentTerm: 'Net 60',
      paymentTerm: 'N60',
      paymentTermDescription: 'Payment due in 60 days',
      percentage: 75.0,
      sapAccountId: 'SAP987654321',
      city: 'Gotham',
      state: 'NJ'
    }
  ]

  const defaultProps = {
    navigateProps: mockNavigateProps,
    billToParties: mockBillToParties,
    setModalProps: jest.fn(),
    orderId: '123'
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  const renderWithStore = (bodyProps: CreateThirdPartyFinancingModalProps) => {
    return render(<TestModal modalBody={CreateThirdPartyFinancingModal} bodyProps={bodyProps} />, {
      store: setUpStore()
    })
  }

  it('should render successfully', () => {
    const { baseElement } = renderWithStore(defaultProps)
    expect(baseElement).toBeTruthy()
  })

  it('should call setModalProps with correct structure', () => {
    const setModalProps = jest.fn()
    renderWithStore({ ...defaultProps, setModalProps })

    expect(setModalProps).toHaveBeenCalledWith(
      expect.objectContaining({
        sectionPadding: 'none',
        headerActions: expect.anything(),
        footerActions: expect.anything()
      })
    )
  })

  it('should display form fields', async () => {
    renderWithStore(defaultProps)
    await actAwait(300)

    expect(screen.getByTestId(TEST_IDS.FARMER_SELECT)).toBeInTheDocument()
    expect(screen.getByTestId(TEST_IDS.FINANCING_TERM_SELECT)).toBeInTheDocument()
    expect(screen.getByTestId(TEST_IDS.MEMBER_NAME_TEXTFIELD)).toBeInTheDocument()
    expect(screen.getByTestId(TEST_IDS.ACCOUNT_NUMBER_TEXTFIELD)).toBeInTheDocument()
    expect(screen.getByTestId(TEST_IDS.CREDIT_AUTHORIZATION_LETTER_FILE_UPLOAD)).toBeInTheDocument()
    expect(screen.getByTestId(TEST_IDS.COMMENTS_TEXTFIELD)).toBeInTheDocument()
    expect(screen.getByTestId(TEST_IDS.FINANCING_TERM_SELECT)).toBeInTheDocument()
    expect(screen.getByTestId(TEST_IDS.AMOUNT_TEXTFIELD)).toBeInTheDocument()
  })

  it('should validate account number input to only allow digits', async () => {
    const user = userEvent.setup()
    renderWithStore(defaultProps)

    const accountNumberInput = screen.getByTestId(TEST_IDS.ACCOUNT_NUMBER_TEXTFIELD)

    // Type mixed characters
    await user.type(accountNumberInput, 'abc123def456ghi')

    // Should only contain digits and be limited to 10 characters
    expect(accountNumberInput).toHaveValue('123456')
  })

  it('should include file upload field with proper validation', () => {
    renderWithStore(defaultProps)

    // Check for file upload field
    const fileUploadField = screen.getByTestId(TEST_IDS.CREDIT_AUTHORIZATION_LETTER_FILE_UPLOAD)
    expect(fileUploadField).toBeInTheDocument()

    // Check helper text mentions file types and size limit
    const helperText = screen.getByText('third_party_financing_form.credit_authorization_letter.helper_text')
    expect(helperText).toBeInTheDocument()
  })
})
