/* eslint-disable @nx/enforce-module-boundaries */
import {
  addPendingForm,
  addPendingRemoval,
  clearPendingChanges,
  initializeOrderState,
  removeOrderState,
  removePendingForm,
  useGlobalDispatch,
  useThirdPartyFinanceSelectors
} from '@gc/redux-store'
import { ThirdPartyFinancingFormData } from '@gc/types'
import { useCallback } from 'react'

export function usePendingFinancingState(orderId: string) {
  const dispatch = useGlobalDispatch()

  // Initialize state for this order if it doesn't exist
  const initializeState = useCallback(() => {
    dispatch(initializeOrderState({ orderId }))
  }, [dispatch, orderId])

  // Initialize on first use
  initializeState()

  // Use typed selectors
  const { pendingAdditions, pendingRemovals, hasPendingChanges } = useThirdPartyFinanceSelectors(orderId)

  // Action dispatchers
  const addPendingFormAction = useCallback(
    (data: ThirdPartyFinancingFormData) => {
      dispatch(addPendingForm({ orderId, data }))
    },
    [dispatch, orderId]
  )

  const addPendingRemovalAction = useCallback(
    (termId: string) => {
      dispatch(addPendingRemoval({ orderId, termId }))
    },
    [dispatch, orderId]
  )

  const clearPendingChangesAction = useCallback(() => {
    dispatch(clearPendingChanges({ orderId }))
  }, [dispatch, orderId])

  const removePendingFormAction = useCallback(
    (index: number) => {
      dispatch(removePendingForm({ orderId, index }))
    },
    [dispatch, orderId]
  )

  const removePendingStateAction = useCallback(() => {
    dispatch(removeOrderState({ orderId }))
  }, [dispatch, orderId])

  return {
    pendingAdditions,
    pendingRemovals,
    hasPendingChanges,
    addPendingForm: addPendingFormAction,
    addPendingRemoval: addPendingRemovalAction,
    removePendingForm: removePendingFormAction,
    clearPendingChanges: clearPendingChangesAction,
    removePendingState: removePendingStateAction
  } as const
}
