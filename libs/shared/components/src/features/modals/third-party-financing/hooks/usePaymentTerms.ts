// eslint-disable-next-line @nx/enforce-module-boundaries
import { useConfigDataQueries } from '@gc/redux-store'
import { BillToParty } from '@gc/types'
import { useMemo } from 'react'

export function usePaymentTerms(primaryBillToParty?: BillToParty) {
  const { useGetPaymentTermsQuery } = useConfigDataQueries()
  const { data: paymentTerms = [], isLoading: isPaymentTermsLoading } = useGetPaymentTermsQuery()

  const paymentTermsOptions = useMemo(() => {
    return paymentTerms.map((paymentTerm) => ({
      id: paymentTerm.code,
      text: paymentTerm.description,
      value: paymentTerm.code
    }))
  }, [paymentTerms])

  const selectedPaymentTerm = useMemo(() => {
    const farmerPaymentTerm = primaryBillToParty?.paymentTerm ?? primaryBillToParty?.actualPaymentTerm
    const term = paymentTermsOptions.find((term) => term.value === farmerPaymentTerm)
    if (!term) return undefined

    // Parse "Prepay/Standard Terms-Due July 25" format
    const [termType, termDescription] = term.text.split('-')

    return {
      code: term.value,
      title: termType?.trim() || term.text,
      description: termDescription?.trim() || ''
    }
  }, [paymentTermsOptions, primaryBillToParty?.paymentTerm, primaryBillToParty?.actualPaymentTerm])

  return { paymentTerms, paymentTermsOptions, selectedPaymentTerm, isPaymentTermsLoading }
}
