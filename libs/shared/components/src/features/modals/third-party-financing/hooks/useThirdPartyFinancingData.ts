/* eslint-disable @nx/enforce-module-boundaries */
import { useDataSource, useMemoizedTranslation } from '@gc/hooks'
import {
  setContingency,
  setLoadingContingency,
  setNotification,
  useGetThirdPartyFinancingQuery,
  useGlobalDispatch,
  useRemoveThirdPartyFinancingMutation,
  useSubmitBatchThirdPartyFinancingMutation,
  useSubmitThirdPartyFinancingMutation
} from '@gc/redux-store'
import { BillToParty, ThirdPartyFinancingFormData, ThirdPartyFinancingRequestedTerm } from '@gc/types'
import { useCallback, useMemo } from 'react'

import { usePaymentTerms } from './usePaymentTerms'
import { usePendingFinancingState } from './usePendingFinancingState'

export interface UseThirdPartyFinancingDataProps {
  orderId: string
  billToParties: BillToParty[]
}

/**
 * Custom hook for managing third-party financing data and operations.
 * This hook provides a centralized way to manage data sharing between
 * the view and create modals, with support for buffered operations.
 */
export function useThirdPartyFinancingData({ orderId, billToParties }: UseThirdPartyFinancingDataProps) {
  const t = useMemoizedTranslation()
  const dispatch = useGlobalDispatch()

  // Derived data
  const primaryBillToParty = useMemo(() => billToParties.find((party) => party.isPrimaryBillTo), [billToParties])
  const { selectedPaymentTerm } = usePaymentTerms(primaryBillToParty)

  // Pending state management
  const {
    pendingAdditions,
    pendingRemovals,
    hasPendingChanges,
    addPendingForm,
    addPendingRemoval,
    removePendingForm,
    clearPendingChanges
  } = usePendingFinancingState(orderId)

  // API hooks
  const {
    data: termsData,
    isLoading: isLoadingTerms,
    isFetching: isFetchingTerms,
    isSuccess: isTermsSuccess,
    refetch: refetchTerms
  } = useGetThirdPartyFinancingQuery(orderId, {
    skip: !orderId
  })

  const [submitThirdPartyFinancing, { isLoading: isSubmitting }] = useSubmitThirdPartyFinancingMutation()
  const [removeThirdPartyFinancing, { isLoading: isRemoving }] = useRemoveThirdPartyFinancingMutation()
  const [submitBatchThirdPartyFinancing, { isLoading: isBatchSubmitting }] = useSubmitBatchThirdPartyFinancingMutation()

  // Process terms data from API
  const { dataSource: apiTerms } = useDataSource(termsData ?? [], isTermsSuccess, isFetchingTerms)

  // Create combined terms list (API terms + pending additions, filtered by pending removals)
  const requestedTerms = useMemo(() => {
    const filteredApiTerms = apiTerms.filter((term) => !pendingRemovals.includes(term.id))

    // Convert pending additions to display format with temporary IDs
    const pendingTermsWithIds: ThirdPartyFinancingRequestedTerm[] = pendingAdditions.map((pendingTerm, index) => ({
      ...pendingTerm,
      id: `pending-${index}`,
      financingTermDescription: pendingTerm.financingTermDescription || ''
    }))

    return [...filteredApiTerms, ...pendingTermsWithIds]
  }, [apiTerms, pendingAdditions, pendingRemovals])

  // Notification helpers
  const showErrorNotification = useCallback(
    (errorMessage: string) => {
      dispatch(
        setNotification({
          open: true,
          icon: 'error',
          themeColor: 'danger',
          message: errorMessage
        })
      )
    },
    [dispatch]
  )

  const showSuccessNotification = useCallback(
    (message: string) => {
      dispatch(
        setNotification({
          open: true,
          message
        })
      )
    },
    [dispatch]
  )

  // Form submission handler
  const submitForm = useCallback(
    async (data: ThirdPartyFinancingFormData) => {
      try {
        const formData = createFormData(orderId, data)
        const result = await submitThirdPartyFinancing(formData)
        if (result.error) {
          throw result.error
        }

        showSuccessNotification(t('third_party_financing.submission_success'))

        // Refresh the terms data
        refetchTerms()
      } catch (error) {
        console.error('Error submitting third-party financing:', error)
        showErrorNotification(t('third_party_financing.submission_error'))
        throw error
      }
    },
    [submitThirdPartyFinancing, showSuccessNotification, showErrorNotification, t, refetchTerms, orderId]
  )

  // Batch submission handler
  const submitBatchChanges = useCallback(async () => {
    try {
      if (!hasPendingChanges) {
        return
      }

      // Prepare batch data
      const pendingRemovalsData = pendingRemovals.map((termId) => ({ orderId, termId }))

      // Create batch FormData
      const batchFormData = createBatchFormData(pendingAdditions, pendingRemovalsData)

      dispatch(setLoadingContingency(t('third_party_financing.saving_changes.label')))
      const result = await submitBatchThirdPartyFinancing(batchFormData)
      if (result.error) {
        throw result.error
      }

      dispatch(setContingency())
      showSuccessNotification(t('third_party_financing.batch_submission_success', 'Changes saved successfully'))

      // Clear pending state and refresh data
      clearPendingChanges()
      refetchTerms()
    } catch (error) {
      console.error('Error submitting batch changes:', error)
      showErrorNotification(t('third_party_financing.batch_submission_error', 'Error saving changes'))
      throw error
    }
  }, [
    t,
    hasPendingChanges,
    pendingRemovals,
    pendingAdditions,
    dispatch,
    submitBatchThirdPartyFinancing,
    showSuccessNotification,
    clearPendingChanges,
    refetchTerms,
    orderId,
    showErrorNotification
  ])

  // Term removal handler (now uses pending state)
  const removeTerm = useCallback(
    (termId: string) => {
      // Check if this is a pending addition (temporary ID)
      if (termId.startsWith('pending-')) {
        // For pending additions, remove from pending state entirely
        const index = parseInt(termId.replace('pending-', ''), 10)
        removePendingForm(index)
        return
      }

      // For existing terms, add to pending removals
      addPendingRemoval(termId)
    },
    [addPendingRemoval, removePendingForm]
  )

  // Legacy term removal handler (for backward compatibility)
  const removeTermImmediate = useCallback(
    async (termId: string) => {
      try {
        await removeThirdPartyFinancing({ orderId, termId }).unwrap()

        showSuccessNotification(
          t('third_party_financing.removal_success', 'Third-party financing term removed successfully')
        )

        // Refresh the terms data
        refetchTerms()
      } catch (error) {
        console.error('Error removing third-party financing term:', error)
        showErrorNotification(t('third_party_financing.removal_error', 'Error removing third-party financing term'))
        throw error
      }
    },
    [removeThirdPartyFinancing, orderId, showSuccessNotification, t, refetchTerms, showErrorNotification]
  )

  // Refresh terms data
  const refreshTerms = useCallback(() => {
    refetchTerms()
  }, [refetchTerms])

  return {
    // Data
    requestedTerms,
    billToParties,
    primaryBillToParty,
    selectedPaymentTerm,

    // Pending state
    pendingAdditions,
    pendingRemovals,
    hasPendingChanges,

    // Loading states
    isLoadingTerms,
    isFetchingTerms,
    isSubmitting,
    isRemoving,
    isBatchSubmitting,

    // Actions
    submitForm,
    removeTerm,
    removeTermImmediate,
    submitBatchChanges,
    addPendingForm,
    removePendingForm,
    clearPendingChanges,
    refreshTerms,

    // Notifications
    showSuccessNotification,
    showErrorNotification
  } as const
}

export function createFormData(orderId: string, data: ThirdPartyFinancingFormData): FormData {
  const formData = new FormData()

  formData.append('orderId', orderId)
  formData.append('primaryAccountId', data.primaryAccountId)
  formData.append('financingTermDescription', data.financingTermDescription)

  formData.append('amount', data.amount)
  formData.append('farmer', data.farmer)
  formData.append('memberName', data.memberName)
  formData.append('accountNumber', data.accountNumber)
  formData.append('financingTerm', data.financingTerm)

  if (data.creditAuthorizationLetter) {
    formData.append('file', data.creditAuthorizationLetter, data.creditAuthorizationLetter.name)
  }
  if (data.comments) {
    formData.append('comments', data.comments)
  }

  return formData
}

export function createBatchFormData(
  requestTerms: ThirdPartyFinancingFormData[],
  deletedTerms: { orderId: string; termId: string }[]
): FormData {
  const formData = new FormData()

  formData.append('deletedTerms', JSON.stringify(deletedTerms))
  formData.append('requestTerms', JSON.stringify(requestTerms))

  const files = requestTerms.filter((term) => term.creditAuthorizationLetter)
  if (files.length > 0) {
    files.forEach((file) => {
      if (file.creditAuthorizationLetter) {
        formData.append('files', file.creditAuthorizationLetter, file.creditAuthorizationLetter.name)
      }
    })
  }

  return formData
}
