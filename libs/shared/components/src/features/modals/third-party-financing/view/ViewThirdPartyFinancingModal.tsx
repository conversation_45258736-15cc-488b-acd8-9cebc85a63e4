/* eslint-disable @nx/enforce-module-boundaries */
import { ButtonProps } from '@element/react-button'
import { IconButton } from '@element/react-icon-button'
import { TypoCaption, TypoOverline, TypoSubtitle } from '@element/react-typography'
import { useMemoizedTranslation, useModal } from '@gc/hooks'
import { BillToParty, ThirdPartyFinancingRequestedTerm, UnknownObject } from '@gc/types'
import { memo, useCallback, useEffect, useMemo } from 'react'

import LoadingAndContingencySection from '../../../../sections/contingency/LoadingAndContingencySection'
import TopAppBar from '../../../../ui-common/header/TopAppBar'
import List, { ItemProps } from '../../../../ui-common/list/List'
import { ListItemProps } from '../../../../ui-common/list/ListItem'
import ModalActionSlot from '../../../../ui-common/modal/ModalActionSlot'
import { LineSkeleton } from '../../../../ui-common/skeleton/SkeletonComponents'
import { ModalDefaultProps, ModalPropsType } from '../../modalProps'
import { THIRD_PARTY_FINANCING_CONSTANTS } from '../constants'
import { useThirdPartyFinancingData } from '../hooks/useThirdPartyFinancingData'
import styles from './ViewThirdPartyFinancingModal.module.scss'

const { FORM_IDS, TEST_IDS, MODAL_NAMES } = THIRD_PARTY_FINANCING_CONSTANTS

export interface ViewThirdPartyFinancingModalProps extends ModalDefaultProps {
  billToParties: BillToParty[]
  orderId: string
}

// Skeleton component for loading state
const RequestedTermsSkeleton = memo(function RequestedTermsSkeleton(): JSX.Element {
  return (
    <div className={styles.skeletonContainer} role='status' aria-label='Loading requested terms'>
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className={styles.skeletonItem}>
          <LineSkeleton width='70%' />
          <LineSkeleton width='50%' />
          <LineSkeleton width='30%' />
        </div>
      ))}
      <span className='sr-only'>Loading requested financing terms...</span>
    </div>
  )
})

// Modal Header Component
const ViewThirdPartyFinancingModalHeader = memo(({ closeModal, title }: { closeModal: () => void; title: string }) => {
  return (
    <TopAppBar
      isModalTopBar
      title={title}
      leadingIconButtonProps={{
        icon: 'close',
        ariaLabel: 'Close modal',
        onClick: () => closeModal()
      }}
    />
  )
})

// Modal Footer Component
const ViewThirdPartyFinancingModalFooter = memo(
  ({
    onAddNewTerm,
    onSave,
    hasPendingChanges,
    isSaving
  }: {
    onAddNewTerm: () => void
    onSave: () => void
    hasPendingChanges: boolean
    isSaving: boolean
  }) => {
    const t = useMemoizedTranslation()

    const addNewTermAction = useMemo(
      () =>
        ({
          leadingIcon: 'add',
          onClick: onAddNewTerm,
          disabled: isSaving,
          label: t('third_party_financing.new_request.label')
        }) as ButtonProps,
      [onAddNewTerm, isSaving, t]
    )

    const saveAction = useMemo(
      () =>
        ({
          onClick: onSave,
          disabled: !hasPendingChanges || isSaving,
          label: t('common.save.label')
        }) as ButtonProps,
      [onSave, hasPendingChanges, isSaving, t]
    )

    return <ModalActionSlot primaryAction={saveAction} secondaryAction={addNewTermAction} />
  }
)

export function ViewThirdPartyFinancingModal({
  setModalProps,
  navigateProps,
  billToParties,
  orderId
}: ViewThirdPartyFinancingModalProps): JSX.Element {
  const t = useMemoizedTranslation()
  const { openModal } = useModal()

  // Use the custom hook for data management
  const {
    requestedTerms,
    isLoadingTerms,
    isFetchingTerms,
    removeTerm,
    showErrorNotification,
    primaryBillToParty,
    hasPendingChanges,
    submitBatchChanges,
    isBatchSubmitting,
    clearPendingChanges
  } = useThirdPartyFinancingData({ billToParties, orderId })

  const groupedByFarmerTerms = useMemo(() => {
    return requestedTerms.reduce(
      (acc, term) => {
        acc[term.farmer] = acc[term.farmer] || []
        acc[term.farmer].push(term)
        return acc
      },
      {} as Record<string, ThirdPartyFinancingRequestedTerm[]>
    )
  }, [requestedTerms])

  // Handle opening the create modal
  const handleAddNewTerm = useCallback(() => {
    openModal({
      name: MODAL_NAMES.CREATE_THIRD_PARTY_FINANCING,
      props: { orderId, billToParties }
    })
  }, [openModal, billToParties, orderId])

  // Handle term actions (edit/remove)
  const handleTermAction = useCallback(
    (code: string) => {
      openModal({
        name: MODAL_NAMES.CREATE_THIRD_PARTY_FINANCING,
        props: { orderId, billToParties, editingTermId: code }
      })
    },
    [openModal, billToParties, orderId]
  )

  const handleRemoveTerm = useCallback(
    (termId: string) => {
      // Remove term immediately from UI (buffered approach)
      removeTerm(termId)
    },
    [removeTerm]
  )

  const handleSave = useCallback(async () => {
    try {
      await submitBatchChanges()
      navigateProps.handleClose()
    } catch (error) {
      console.error('Error saving changes:', error)
      showErrorNotification('Failed to save changes. Please try again.')
    }
  }, [submitBatchChanges, navigateProps, showErrorNotification])

  const handleDiscard = useCallback(() => {
    // Clear pending changes
    clearPendingChanges()
    navigateProps.handleClose()
  }, [clearPendingChanges, navigateProps])

  const handleCloseModal = useCallback(() => {
    if (hasPendingChanges) {
      openModal({
        name: MODAL_NAMES.ABANDON_THIRD_PARTY_FINANCING,
        props: { onSave: handleSave, onDiscard: handleDiscard }
      })
    } else {
      navigateProps.handleClose()
    }
  }, [openModal, hasPendingChanges, handleDiscard, handleSave, navigateProps])

  const createTermListItems = useCallback(
    (requestedTerms: ThirdPartyFinancingRequestedTerm[]) => {
      const termItems: ListItemProps<ThirdPartyFinancingRequestedTerm>[] = []

      for (const term of requestedTerms) {
        termItems.push({
          code: term.id,
          primaryText: (
            <TypoSubtitle bold level={2} id={`term-title-${term.id}`}>
              {term.financingTermDescription}
            </TypoSubtitle>
          ),
          secondaryText: (
            <TypoCaption className={styles.termDetails}>
              Member: {term.memberName} (#: {term.accountNumber})
              <br />
              Amount: ${term.amount}
              <br />
            </TypoCaption>
          ),
          isCustomTrailingBlock: true,
          trailingBlockType: 'icon',
          trailingBlock: (
            <IconButton
              tabIndex={0}
              icon='cancel'
              variant='secondary-on-surface'
              onClick={() => handleRemoveTerm(term.id)}
            />
          )
        })
      }

      return termItems as ItemProps<UnknownObject>[]
    },
    [handleRemoveTerm]
  )

  // Loading and data states
  const showLoading = isLoadingTerms || isFetchingTerms
  const hasData = requestedTerms.length > 0

  // Modal configuration
  const headerActions = useMemo(
    () => (
      <ViewThirdPartyFinancingModalHeader
        closeModal={handleCloseModal}
        title={t('third_party_financing.requested_payment_terms.label', 'Requested Payment Terms')}
      />
    ),
    [handleCloseModal, t]
  )

  const footerActions = useMemo(
    () => (
      <ViewThirdPartyFinancingModalFooter
        onAddNewTerm={handleAddNewTerm}
        onSave={handleSave}
        hasPendingChanges={hasPendingChanges}
        isSaving={isBatchSubmitting}
      />
    ),
    [handleAddNewTerm, handleSave, hasPendingChanges, isBatchSubmitting]
  )

  const modalProps: ModalPropsType = useMemo(
    () => ({
      sectionPadding: 'none' as const,
      headerActions,
      footerActions
    }),
    [headerActions, footerActions]
  )

  useEffect(() => {
    setModalProps(modalProps)
  }, [setModalProps, modalProps])

  return (
    <div role='main' className={styles.container} id={FORM_IDS.INITIAL_PAGE}>
      <div className={styles.contentSection}>
        <LoadingAndContingencySection
          hasError={false}
          hasData={hasData}
          isLoading={showLoading}
          loadingComponent={<RequestedTermsSkeleton />}
          noDataClassName={styles.noDataContainer}
          noDataHeader={t('third_party_financing.no_data_header.label')}
          noDataDescription={t('third_party_financing.no_data_description.label')}
        >
          {Object.entries(groupedByFarmerTerms).map(([farmer, terms]) => (
            <div key={farmer} role='region'>
              <TypoOverline className={styles.farmerName} role='heading' aria-level={2}>
                {farmer} {primaryBillToParty?.name === farmer ? `(${t('common.primary.label')})` : ''}
              </TypoOverline>

              <List
                noPadding
                divider={true}
                items={createTermListItems(terms)}
                onAction={handleTermAction}
                trailingBlockType='icon'
                className={styles.termsList}
                data-testid={TEST_IDS.REQUESTED_TERMS_LIST}
              />
            </div>
          ))}
        </LoadingAndContingencySection>
      </div>
    </div>
  )
}

export default ViewThirdPartyFinancingModal
