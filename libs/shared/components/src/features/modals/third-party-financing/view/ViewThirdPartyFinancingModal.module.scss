.container {
  gap: 24px;
  display: flex;
  flex-direction: column;
}

.noDataContainer {
  padding: 80px 0px;

  :global(.mdc-typography--body-2) {
    color: var(--lmnt-theme-on-surface-inactive, rgba(16, 56, 79, 0.73));
  }

  span:last-child {
    font-weight: 400;
    color: var(--lmnt-theme-on-surface-inactive, rgba(16, 56, 79, 0.73));
  }
}

.contentSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px 16px;
}

.farmerName {
  gap: 16px;
  min-height: 48px;
  color: var(--lmnt-theme-on-surface-inactive);
}

.skeletonContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;

  .skeletonItem {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px 0px;
  }
}
