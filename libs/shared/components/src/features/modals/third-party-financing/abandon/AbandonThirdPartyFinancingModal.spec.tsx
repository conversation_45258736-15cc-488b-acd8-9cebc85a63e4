import { act, fireEvent, render } from '@gc/utils'
import { MemoryRouter as Router } from 'react-router-dom'

import AbandonThirdPartyFinancingModal from './AbandonThirdPartyFinancingModal'

describe('AbandonThirdPartyFinancingModal', () => {
  const mockHandleClose = jest.fn()
  const mockSetModal = jest.fn()
  const mockOnSave = jest.fn()
  const mockOnDiscard = jest.fn()

  const defaultProps = {
    navigateProps: { icon: 'close', handleClose: mockHandleClose },
    setModalProps: mockSetModal,
    onSave: mockOnSave,
    onDiscard: mockOnDiscard
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render the modal with correct content', () => {
    const { getByText } = render(
      <Router>
        <AbandonThirdPartyFinancingModal {...defaultProps} />
      </Router>
    )

    expect(getByText('common.discard_changes.label')).toBeInTheDocument()
    expect(getByText('third_party_financing.abandon_description')).toBeInTheDocument()
  })

  it('should render all three action buttons', () => {
    const { getByRole } = render(
      <Router>
        <AbandonThirdPartyFinancingModal {...defaultProps} />
      </Router>
    )

    const cancelButton = getByRole('button', { name: 'common.cancel.label' })
    const saveButton = getByRole('button', { name: 'third_party_financing.save_and_close.label' })
    const discardButton = getByRole('button', { name: 'common.discard.label' })

    expect(cancelButton).toBeInTheDocument()
    expect(saveButton).toBeInTheDocument()
    expect(discardButton).toBeInTheDocument()
  })

  it('should call handleClose when cancel button is clicked', async () => {
    const { getByRole } = render(
      <Router>
        <AbandonThirdPartyFinancingModal {...defaultProps} />
      </Router>
    )

    const cancelButton = getByRole('button', { name: 'common.cancel.label' })
    await act(async () => fireEvent.click(cancelButton))

    expect(mockHandleClose).toHaveBeenCalledTimes(1)
  })

  it('should call onSave and handleClose when save and close button is clicked', async () => {
    const { getByRole } = render(
      <Router>
        <AbandonThirdPartyFinancingModal {...defaultProps} />
      </Router>
    )

    const saveButton = getByRole('button', { name: 'third_party_financing.save_and_close.label' })
    await act(async () => fireEvent.click(saveButton))

    expect(mockOnSave).toHaveBeenCalledTimes(1)
    expect(mockHandleClose).toHaveBeenCalledTimes(1)
  })

  it('should call onDiscard and handleClose when discard button is clicked', async () => {
    const { getByRole } = render(
      <Router>
        <AbandonThirdPartyFinancingModal {...defaultProps} />
      </Router>
    )

    const discardButton = getByRole('button', { name: 'common.discard.label' })
    await act(async () => fireEvent.click(discardButton))

    expect(mockOnDiscard).toHaveBeenCalledTimes(1)
    expect(mockHandleClose).toHaveBeenCalledTimes(1)
  })

  it('should handle optional callbacks gracefully when not provided', async () => {
    const propsWithoutCallbacks = {
      navigateProps: { icon: 'close', handleClose: mockHandleClose },
      setModalProps: mockSetModal
    }

    const { getByRole } = render(
      <Router>
        <AbandonThirdPartyFinancingModal {...propsWithoutCallbacks} />
      </Router>
    )

    const saveButton = getByRole('button', { name: 'third_party_financing.save_and_close.label' })
    const discardButton = getByRole('button', { name: 'common.discard.label' })

    await act(async () => fireEvent.click(saveButton))
    await act(async () => fireEvent.click(discardButton))

    expect(mockHandleClose).toHaveBeenCalledTimes(2)
  })

  it('should display warning icon with correct styling', () => {
    const { container } = render(
      <Router>
        <AbandonThirdPartyFinancingModal {...defaultProps} />
      </Router>
    )

    const iconContainer = container.querySelector('.iconContainer')
    const warningIcon = container.querySelector('.warningIcon')

    expect(iconContainer).toBeInTheDocument()
    expect(warningIcon).toBeInTheDocument()
  })

  it('should call setModalProps with header actions on mount', () => {
    render(
      <Router>
        <AbandonThirdPartyFinancingModal {...defaultProps} />
      </Router>
    )

    expect(mockSetModal).toHaveBeenCalledWith(
      expect.objectContaining({
        headerActions: expect.any(Object)
      })
    )
  })
})
