.container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 400px;
  width: 100%;
  padding: 80px 16px;
  gap: 16px;
}

.iconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ffccce;
  padding: 8px;
}

.warningIcon {
  width: 24px;
  height: 24px;
}

.textContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  text-align: center;
  width: 100%;
}

.title {
  color: #10384f;
  font-family: 'Bayer Sans', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  margin: 0;
}

.description {
  color: rgba(16, 56, 79, 0.73);
  font-family: 'Bayer Sans', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  margin: 0;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;

  .discardButton {
    color: #cf2d22;
  }
}
