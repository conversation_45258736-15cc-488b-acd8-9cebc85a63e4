/* eslint-disable @nx/enforce-module-boundaries */
import { Button } from '@element/react-button'
import { Icon } from '@element/react-icon'
import { TypoBody, TypoSubtitle } from '@element/react-typography'
import { TopAppBar } from '@gc/components'
import { useMemoizedTranslation } from '@gc/hooks'
import { useCallback, useEffect } from 'react'

import { ModalDefaultProps } from '../../modalProps'
import styles from './AbandonThirdPartyFinancingModal.module.scss'

export interface AbandonThirdPartyFinancingModalProps extends ModalDefaultProps {
  onSave?: () => void
  onDiscard?: () => void
}

export function AbandonThirdPartyFinancingModal({
  onSave,
  onDiscard,
  setModalProps,
  navigateProps
}: AbandonThirdPartyFinancingModalProps) {
  const t = useMemoizedTranslation()

  const handleCancel = useCallback(() => {
    navigateProps.handleClose()
  }, [navigateProps])

  const handleSaveAndClose = useCallback(() => {
    onSave ? onSave() : navigateProps.handleClose()
  }, [onSave, navigateProps])

  const handleDiscard = useCallback(() => {
    onDiscard ? onDiscard() : navigateProps.handleClose()
  }, [onDiscard, navigateProps])

  useEffect(() => {
    setModalProps({
      headerActions: (
        <TopAppBar
          isModalTopBar
          title={t('common.attention.label')}
          leadingIconButtonProps={{
            icon: navigateProps.icon,
            onClick: handleCancel
          }}
        />
      )
    })
  }, [handleCancel, navigateProps.icon, setModalProps, t])

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {/* Warning Icon */}
        <div className={styles.iconContainer}>
          <Icon icon='error' className={styles.warningIcon} style={{ color: '#B3190D' }} />
        </div>

        {/* Text Content */}
        <div className={styles.textContent}>
          <TypoSubtitle level={1} bold className={styles.title}>
            {t('common.discard_changes.label')}
          </TypoSubtitle>
          <TypoBody level={2} className={styles.description}>
            {t('third_party_financing.abandon_description')}
          </TypoBody>
        </div>

        {/* Action Buttons */}
        <div className={styles.actions}>
          <Button variant='text' themeColor='danger' buttonSize='medium' onClick={handleCancel}>
            {t('common.cancel.label')}
          </Button>

          <Button variant='text' themeColor='primary' buttonSize='medium' onClick={handleSaveAndClose}>
            {t('third_party_financing.save_and_close.label', 'Save and Close')}
          </Button>

          <Button
            variant='text'
            themeColor='danger'
            buttonSize='medium'
            onClick={handleDiscard}
            className={styles.discardButton}
          >
            {t('common.discard.label')}
          </Button>
        </div>
      </div>
    </div>
  )
}

export default AbandonThirdPartyFinancingModal
