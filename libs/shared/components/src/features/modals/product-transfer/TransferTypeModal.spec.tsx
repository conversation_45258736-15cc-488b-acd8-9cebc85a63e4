/* eslint-disable @nx/enforce-module-boundaries */
import * as reduxStore from '@gc/redux-store'
import { OrdersState, setUpStore } from '@gc/redux-store'
import { handlers } from '@gc/shared/test'
import { StockOrder } from '@gc/types'
import { act, fireEvent, render, screen, waitFor } from '@gc/utils'
import { orders } from 'libs/shared/test/src/lib/mocks/orders'
import { http, HttpResponse } from 'msw'
import { setupServer } from 'msw/node'
import { MemoryRouter as Router } from 'react-router-dom'

import TransferTypeModal, { TransferTypeModalProps } from './TransferTypeModal'

const server = setupServer(...handlers)

const mockPortalConfig = {
  gcPortalConfig: {
    stockOrderConfig: {
      salesYear: '2024',
      documentType: 'STOCK_ORDER'
    }
  },
  ordersModule: {
    disableTransferStatuses: ['CANCELLED', 'CREDIT_BLOCKED']
  }
}

const mockNavigate = jest.fn()
const mockOpenModal = jest.fn()
const mockCloseModal = jest.fn()
const mockOpenAbandonModal = jest.fn()

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}))

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useMemoizedTranslation: () => jest.fn((key) => key),
  useIsDesktop: () => true,
  usePortalConfig: () => mockPortalConfig,
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig,
  useModal: () => ({ openModal: mockOpenModal, closeModal: mockCloseModal }),
  useSelectedAccount: () => ({ sapAccountId: '123456' }),
  useCreateStockOrder: () => jest.fn(),
  useOpenAbandonModal: () => mockOpenAbandonModal
}))

describe('TransferTypeModal', () => {
  let mockStore: ReturnType<typeof setUpStore>
  const mockHandleClose = jest.fn()
  const mockSetModal = jest.fn()
  const mockDispatch = jest.fn()

  const defaultProps: TransferTypeModalProps = {
    orderCode: 'TEST-ORDER-123',
    navigateProps: { icon: 'close', handleClose: mockHandleClose },
    setModalProps: mockSetModal
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockStore = setUpStore(undefined, {
      injectConfigDataApi: true,
      injectOrdersApi: true
    })
    jest.spyOn(reduxStore, 'useGlobalDispatch').mockReturnValue(mockDispatch)
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('should render successfully', () => {
    const { baseElement } = render(
      <Router>
        <TransferTypeModal {...defaultProps} />
      </Router>,
      { store: mockStore }
    )
    expect(baseElement).toBeTruthy()
  })

  it('should show loading state when fetching orders', () => {
    render(
      <Router>
        <TransferTypeModal {...defaultProps} />
      </Router>,
      { store: mockStore }
    )

    expect(screen.getByText('orders.loading_order_message.label')).toBeDefined()
  })

  it('should display error alert when orders API fails', async () => {
    server.use(http.post(/\/allorders/, () => HttpResponse.error()))

    render(
      <Router>
        <TransferTypeModal {...defaultProps} />
      </Router>,
      { store: mockStore }
    )
    await waitFor(() => expect(screen.queryByText('orders.loading_order_message.label')).toBeNull())

    expect(screen.getByText('orders.could_not_load_order.label')).toBeDefined()
    expect(screen.getByText('quotes.error_msg_description.label')).toBeDefined()
    expect(screen.getByText('common.try_again.label')).toBeDefined()
  })

  it('should show info alert when no stock orders are available', async () => {
    server.use(http.post(/\/allorders/, () => HttpResponse.json({ ...orders, orders: [] })))

    render(
      <Router>
        <TransferTypeModal {...defaultProps} />
      </Router>,
      { store: mockStore }
    )
    await waitFor(() => expect(screen.queryByText('orders.loading_order_message.label')).toBeNull())

    // Check for alert content
    await waitFor(() => {
      expect(screen.getByText('common.you_do_not_have.label 2024 orders.stock_order.label.')).toBeDefined()
      expect(screen.getByText('orders.create_stock_order.label')).toBeDefined()
    })
  })

  it('should show info alert when stock order has errors', async () => {
    // Mock stock order with a status that's in the disableTransferStatuses list
    const mockStockOrder: StockOrder = {
      code: 'STOCK-123',
      status: 'CANCELLED',
      statusText: 'Cancelled',
      entries: []
    } as unknown as StockOrder
    server.use(http.post(/\/allorders/, () => HttpResponse.json({ ...orders, orders: [mockStockOrder] })))

    render(
      <Router>
        <TransferTypeModal {...defaultProps} />
      </Router>,
      { store: mockStore }
    )

    // Check for alert content
    await waitFor(() => {
      expect(screen.getByText('common.your.label 2024 orders.stock_order.label common.has_errors.label.')).toBeDefined()
      expect(screen.getByText('common.view.label orders.stock_order.label')).toBeDefined()
    })
  })

  it('should render transfer options with correct data', async () => {
    render(
      <Router>
        <TransferTypeModal {...defaultProps} />
      </Router>,
      { store: mockStore }
    )
    await waitFor(() => expect(screen.queryByText('orders.loading_order_message.label')).toBeNull())

    // Check for list items
    expect(screen.getByText('orders.transfer_to_farmer.label')).toBeDefined()
    expect(screen.getByText('orders.transfer_to_stock_order.label')).toBeDefined()
  })

  it('should open farmer transfer modal when clicking on farmer transfer option', async () => {
    render(
      <Router>
        <TransferTypeModal {...defaultProps} />
      </Router>,
      { store: mockStore }
    )
    await waitFor(() => expect(screen.queryByText('orders.loading_order_message.label')).toBeNull())

    // Click on farmer transfer option
    fireEvent.click(screen.getByText('orders.transfer_to_farmer.label'))

    // Verify correct actions were dispatched
    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({ type: expect.stringContaining('setPreShipTransferType') })
    )
    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({ type: expect.stringContaining('setPreShipTransferFromOrderCode') })
    )
    expect(mockOpenModal).toHaveBeenCalledWith({ name: 'ADD_TRANSFER_QTY', props: {} })
  })

  it('should open stock order transfer modal when clicking on stock order transfer option', async () => {
    render(
      <Router>
        <TransferTypeModal {...defaultProps} />
      </Router>,
      { store: mockStore }
    )
    await waitFor(() => expect(screen.queryByText('orders.loading_order_message.label')).toBeNull())

    // Click on stock order transfer option
    fireEvent.click(screen.getByText('orders.transfer_to_stock_order.label'))

    // Verify correct actions were dispatched
    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({ type: expect.stringContaining('setPreShipTransferType') })
    )
    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({ type: expect.stringContaining('setPreShipTransferFromOrderCode') })
    )
    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({ type: expect.stringContaining('setPreShipTransferToOrderCode') })
    )
    expect(mockOpenModal).toHaveBeenCalledWith({ name: 'ADD_TRANSFER_QTY', props: {} })
  })

  it('should call openAbandonModal when back button is clicked with transfer entries', async () => {
    const mockStore = setUpStore(
      { orders: { preShipTransfer: { transferEntries: [{}] } } as OrdersState },
      {
        injectConfigDataApi: true,
        injectOrdersApi: true
      }
    )

    render(
      <Router>
        <TransferTypeModal {...defaultProps} />
      </Router>,
      { store: mockStore }
    )
    await waitFor(() => expect(screen.queryByText('orders.loading_order_message.label')).toBeNull())

    // Simulate rendering and effect calls
    await act(async () => {
      // Call the mocked onClick handler for the back button
      const backButtonProps = mockSetModal.mock.calls[0][0].headerActions.props.leadingIconButtonProps
      backButtonProps.onClick()
    })

    // Verify openAbandonModal was called
    expect(mockOpenAbandonModal).toHaveBeenCalled()
    // Verify handleClose was not called directly
    expect(mockHandleClose).not.toHaveBeenCalled()
  })

  it('should call handleClose when back button is clicked without transfer entries', async () => {
    const mockStore = setUpStore(
      { orders: { preShipTransfer: { transferEntries: [] } } as OrdersState },
      {
        injectConfigDataApi: true,
        injectOrdersApi: true
      }
    )

    render(
      <Router>
        <TransferTypeModal {...defaultProps} />
      </Router>,
      { store: mockStore }
    )

    // Simulate rendering and effect calls
    await act(async () => {
      // Call the mocked onClick handler for the back button
      const backButtonProps = mockSetModal.mock.calls[0][0].headerActions.props.leadingIconButtonProps
      backButtonProps.onClick()
    })

    // Verify handleClose was called
    expect(mockHandleClose).toHaveBeenCalled()
    // Verify openAbandonModal was not called
    expect(mockOpenAbandonModal).not.toHaveBeenCalled()
  })
})
