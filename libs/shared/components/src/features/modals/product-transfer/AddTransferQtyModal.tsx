/* eslint-disable @nx/enforce-module-boundaries */
import { <PERSON><PERSON>, Toolt<PERSON> } from '@element/react-components'
import { TypoSubtitle } from '@element/react-typography'
import { CHANGE_TYPES } from '@gc/constants'
import {
  useConvertCartToOrder,
  useFetchPreShipCart,
  useIsDesktop,
  useMemoizedTranslation,
  useModal,
  useOpenAbandonModal,
  useSaveEntries,
  useSetTransferFailContingency
} from '@gc/hooks'
import {
  getPreShipTransfer,
  resetPreShipTransfer,
  setContingency,
  setLoadingContingency,
  setOrdersCurrentTab,
  setPreShipTransferEntries,
  setPreShipTransferIsSuccess,
  useGlobalDispatch,
  useOrdersQueries
} from '@gc/redux-store'
import { ModifyCartEntry, OrderDetailsCBUS, StockOrder } from '@gc/types'
import _ from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import MediaQuery from 'react-responsive'
import { useNavigate } from 'react-router-dom'

import Badge from '../../../ui-common/badge/Badge'
import { TopAppBar } from '../../../ui-common/header/TopAppBar'
import { List } from '../../../ui-common/list/List'
import Loading from '../../../ui-common/loading/Loading'
import MessageWithAction from '../../../ui-common/message-with-action/MessageWithAction'
import { ModalActionSlot } from '../../../ui-common/modal/ModalActionSlot'
import { Table } from '../../../ui-common/table/Table'
import DecimalTextfield from '../../../ui-common/textfield/DecimalTextfield'
import AlertContainer from '../../common/alert-container/AlertContainer'
import { ModalDefaultProps, ModalPropsType } from '../modalProps'
import styles from './AddTransferQtyModal.module.scss'

interface TransferProductsListProps {
  entries: OrderDetailsCBUS['entries']
  quantities: { [key: number]: string | undefined }
  invalidEntries: number[]
  onQuantityChange: (entryNumber: number, value: string) => void
  onBlur: (entryNumber: number, value: string) => void
}

function TransferProductsList({
  entries,
  quantities,
  invalidEntries,
  onQuantityChange,
  onBlur
}: TransferProductsListProps) {
  const t = useMemoizedTranslation()

  const titleListItem = useMemo(
    () => ({
      primaryText: (
        <TypoSubtitle level={2} bold>
          {t('common.product.label')}
        </TypoSubtitle>
      ),
      trailingBlock: (
        <TypoSubtitle level={2} bold>
          {t('orders.qty_to_transfer_label')}
        </TypoSubtitle>
      ),
      trailingBlockType: 'text',
      showDivider: false
    }),
    [t]
  )

  const getListItems = useCallback(
    () =>
      entries.map((entry) => {
        const isInvalidEntry = invalidEntries.includes(entry.entryNumber)
        return {
          isCustomTrailingBlock: true,
          primaryText: (
            <TypoSubtitle level={2} bold>
              {entry.product.name}
            </TypoSubtitle>
          ),
          secondaryText: `${entry.quantity} ${t('common.ssu.label')} ${t('common.available.label')}`,
          trailingBlock: (
            <DecimalTextfield
              className={`${styles.textfield} ${isInvalidEntry ? styles.textfield_mobile_error : ''}`}
              variant='outlined'
              value={quantities[entry.entryNumber]}
              onChange={(value) => onQuantityChange(entry.entryNumber, value)}
              onBlur={(value) => onBlur(entry.entryNumber, value)}
            />
          ),
          className: isInvalidEntry ? styles.list_item_error : ''
        }
      }),
    [entries, quantities, invalidEntries, onQuantityChange, onBlur, t]
  )

  return <List noHover divider items={[titleListItem, ...getListItems()]} listItemClassName={styles.list_item} />
}

interface TransferProductsTableProps {
  entries: OrderDetailsCBUS['entries']
  quantities: { [key: number]: string | undefined }
  invalidEntries: number[]
  onQuantityChange: (entryNumber: number, value: string) => void
  onBlur: (entryNumber: number, value: string) => void
}

function TransferProductsTable({ entries, quantities, onQuantityChange, onBlur }: TransferProductsTableProps) {
  const t = useMemoizedTranslation()

  const handleQuantityValidation = useCallback(
    (quantity: string, row: { available: number }) => {
      if (Number(quantity) > row.available) {
        return {
          leadingIcon: (
            <Tooltip text={t('S')} hoisted>
              <Icon icon='error' className={styles.quantity_leading_icon_with_tooltip} />
            </Tooltip>
          ),
          className: `${styles.textfield} ${styles.textfield_desktop_error}`,
          error: true
        }
      } else {
        return {
          leadingIcon: null,
          className: styles.textfield,
          error: false
        }
      }
    },
    [t]
  )

  const headers = [
    {
      header: t('common.product_name.label'),
      accessor: 'name',
      id: 'name',
      displayTemplate: (_value: string, row: { name: string; quantity: string | undefined; available: number }) => {
        return (
          <>
            {row.name}&nbsp;
            {!!row.quantity && Number(row.quantity) > 0 && Number(row.quantity) <= row.available && (
              <Badge labelText={t('common.added.label')} />
            )}
            &nbsp;
          </>
        )
      },
      widthPercentage: 40
    },
    {
      header: t('common.units_available.label'),
      accessor: 'available',
      id: 'available',
      widthPercentage: 30,
      align: 'right' as const,
      displayType: 'decimal'
    },
    {
      header: 'Quantity',
      accessor: 'quantity',
      id: 'quantity',
      widthPercentage: 30,
      align: 'right' as const,
      editProps: {
        editType: 'textfield' as const,
        textfieldProps: {
          className: styles.textfield,
          type: 'number',
          placeholder: '0.00',
          decimalPlaces: 2,
          onChange: (value: string, row: { entryNumber: number }) => onQuantityChange(row.entryNumber, value),
          onBlur: (value: string, row: { entryNumber: number }) => onBlur(row.entryNumber, value),
          validateFn: handleQuantityValidation
        }
      }
    }
  ]

  const tableData = useMemo(
    () =>
      entries.map((entry) => ({
        entryNumber: entry.entryNumber,
        name: entry.product.name,
        available: entry.quantity,
        quantity: quantities[entry.entryNumber]
      })),
    [entries, quantities]
  )

  return <Table className={styles.table} editable headers={headers} data={tableData} />
}

export interface AddTransferQtyModalProps extends ModalDefaultProps {
  stockOrderDetails?: StockOrder
}

export function AddTransferQtyModal({ setModalProps, navigateProps, stockOrderDetails }: AddTransferQtyModalProps) {
  const { openModal, closeModal } = useModal()
  const navigate = useNavigate()
  const isDesktop = useIsDesktop()
  const dispatch = useGlobalDispatch()
  const t = useMemoizedTranslation()
  const openAbandonModal = useOpenAbandonModal()
  const [saveEntries] = useSaveEntries()
  const [convertCartToOrder] = useConvertCartToOrder()

  const preShipTransfer = useSelector(getPreShipTransfer)
  const [primaryActionClicked, setPrimaryActionClicked] = useState(false)
  const { useGetOrderDetailsQuery } = useOrdersQueries()
  const {
    data: farmerOrderDetails,
    isLoading,
    isFetching,
    refetch: refetchOrderDetails
  } = useGetOrderDetailsQuery(
    { orderId: preShipTransfer?.fromOrderCode ?? '', isMobile: !isDesktop },
    { skip: !!stockOrderDetails || !preShipTransfer?.fromOrderCode }
  )
  const setTransferFailContingency = useSetTransferFailContingency()
  const fetchPreShipCart = useFetchPreShipCart()

  const [quantities, setQuantities] = useState<{ [key: number]: string | undefined }>(
    preShipTransfer?.transferEntries
      ? preShipTransfer.transferEntries.reduce(
          (acc, entry) => ({ ...acc, [(entry as ModifyCartEntry).refDocIt ?? -1]: entry.quantity }),
          {}
        )
      : {}
  )
  const [invalidEntries, setInvalidEntries] = useState<number[]>([])

  const orderDetails = useMemo(
    () => (stockOrderDetails ? stockOrderDetails : farmerOrderDetails),
    [stockOrderDetails, farmerOrderDetails]
  )

  const validEntries = useMemo(() => {
    const entries = orderDetails?.entries?.filter((entry) => !entry.product.isPackagingMaterial) ?? []
    return entries.map((e) => ({
      ...e,
      quantity: e.quantity - ((e as OrderDetailsCBUS['entries'][0]).consignedQuantity ?? 0)
    }))
  }, [orderDetails?.entries])

  useEffect(() => {
    if (orderDetails && !quantities) {
      setQuantities(validEntries.reduce((acc, entry) => ({ ...acc, [entry.entryNumber]: undefined }), {}))
    }
  }, [orderDetails, quantities, validEntries])

  const handleQuantityChange = useCallback(
    (entryNumber: number, value: string) => {
      const entry = validEntries.find((e) => e.entryNumber === entryNumber)
      if (!entry) return

      if (value && Number(value) > entry.quantity) {
        setInvalidEntries((prev) => [...prev, entryNumber])
      } else {
        setInvalidEntries((prev) => prev.filter((e) => e !== entryNumber))
      }

      setQuantities((prev) => ({
        ...prev,
        [entryNumber]: value === '' ? undefined : value
      }))
    },
    [validEntries]
  )

  const handleBlur = useCallback((entryNumber: number, value: string) => {
    setQuantities((prev) => ({
      ...prev,
      [entryNumber]: value === '' ? undefined : value
    }))
  }, [])

  const selectedEntries = useMemo(() => {
    return validEntries.filter((entry) => Number(quantities[entry.entryNumber]) > 0) || []
  }, [validEntries, quantities])

  const getTransferButtonLabel = useCallback(
    (count: number) => {
      const transferText = t('inventory.transfer.label')
      const productText = t('common.product.label', { count })
      return `${transferText} ${count} ${productText}`
    },
    [t]
  )

  const handleTransferToStockOrder = useCallback(
    async (transferEntries: OrderDetailsCBUS['entries']) => {
      dispatch(setLoadingContingency(t('common.transferring_products.label')))
      closeModal()
      let cartId = ''
      const { toOrderCode: stockOrderCode = '', fromOrderCode = '' } = preShipTransfer

      const placeOrder = async () => {
        convertCartToOrder(
          {
            reqBody: {
              cartId,
              lobLevelDetails: { lob: 'SEED', deliveryMode: 'delivery' },
              termsChecked: true
            },
            invalidateTag: 'StockOrders',
            ...(stockOrderCode ? { orderCodes: [fromOrderCode], isSaveOrder: true } : {})
          },
          {
            onResolve: (result) => {
              if (result?.isSuccess) {
                dispatch(setContingency())
                dispatch(setPreShipTransferIsSuccess(true))
                dispatch(setOrdersCurrentTab(1))
                navigate('/')
              }
            }
          }
        ).catch(() => setTransferFailContingency(placeOrder))
      }

      const saveTransferEntries = async () => {
        const result = await saveEntries(
          { cartId: cartId ?? '', data: { orderEntries: transferEntries }, updateMethod: 'POST' },
          { contingencyType: 'none' }
        )
        if (result.error) {
          setTransferFailContingency(saveTransferEntries, cartId)
        } else {
          await placeOrder()
        }
      }
      const startTransfer = async () => {
        const res = await fetchPreShipCart({
          fromOrderCode: preShipTransfer?.fromOrderCode ?? '',
          toOrderCode: stockOrderCode
        })
        if (res.data) {
          cartId = res.data.code
          await saveTransferEntries()
        } else {
          setTransferFailContingency(startTransfer)
        }
      }

      await startTransfer()
    },
    [
      closeModal,
      convertCartToOrder,
      dispatch,
      fetchPreShipCart,
      navigate,
      preShipTransfer,
      saveEntries,
      setTransferFailContingency,
      t
    ]
  )

  const updatePreShipTransferEntries = useCallback(() => {
    if (invalidEntries.length > 0) return []
    const transferEntries = selectedEntries.map((entry) => {
      const newEntry = {
        ...entry,
        quantity: Number(quantities[entry.entryNumber])
      }

      return {
        ..._.pick(newEntry, ['entryNumber', 'cropCode', 'cropName', 'quantity', 'product', 'storageLocation']),
        changeType: CHANGE_TYPES.transfer,
        masterOrderNumber: preShipTransfer?.fromOrderCode ?? '',
        refDocIt: entry?.entryNumber ?? '',
        entryNumber: undefined,
        createNewEntry: preShipTransfer?.transferType === 'order'
      }
    })
    dispatch(setPreShipTransferEntries(transferEntries as unknown as OrderDetailsCBUS['entries']))
    return transferEntries
  }, [
    dispatch,
    invalidEntries.length,
    preShipTransfer?.fromOrderCode,
    preShipTransfer?.transferType,
    quantities,
    selectedEntries
  ])

  const handleTransfer = useCallback(async () => {
    setPrimaryActionClicked(true)
    if (invalidEntries.length > 0) return

    const transferEntries = updatePreShipTransferEntries()

    if (preShipTransfer?.transferType === 'order') {
      openModal({ name: 'SELECT_FARMER', props: { usage: 'order' } })
    } else {
      handleTransferToStockOrder(transferEntries as unknown as OrderDetailsCBUS['entries'])
    }
  }, [
    invalidEntries.length,
    updatePreShipTransferEntries,
    preShipTransfer?.transferType,
    openModal,
    handleTransferToStockOrder
  ])

  const handleCloseModal = useCallback(() => {
    if (selectedEntries.length) {
      updatePreShipTransferEntries()
      openAbandonModal()
    } else {
      dispatch(resetPreShipTransfer())
      closeModal()
    }
  }, [closeModal, dispatch, openAbandonModal, selectedEntries.length, updatePreShipTransferEntries])

  const handleGoBack = useCallback(() => {
    if (navigateProps?.icon === 'close') {
      handleCloseModal()
    } else {
      updatePreShipTransferEntries()
      if (stockOrderDetails) {
        openAbandonModal()
      }
      navigateProps.handleClose()
    }
  }, [handleCloseModal, navigateProps, openAbandonModal, stockOrderDetails, updatePreShipTransferEntries])

  useEffect(() => {
    setModalProps({
      sectionPadding: !isDesktop ? 'none' : undefined,
      modalSize: isDesktop ? 'xlarge' : undefined,
      headerActions: (
        <TopAppBar
          isModalTopBar
          title={t('common.qty_to_transfer.label')}
          leadingIconButtonProps={{
            icon: navigateProps.icon,
            onClick: handleGoBack
          }}
        />
      ),
      footerActions: (
        <ModalActionSlot
          primaryAction={{
            label: getTransferButtonLabel(selectedEntries.length),
            onClick: handleTransfer,
            disabled: selectedEntries.length === 0 || invalidEntries.length > 0 || primaryActionClicked
          }}
          secondaryAction={{
            label: 'Cancel',
            onClick: handleCloseModal,
            disabled: primaryActionClicked
          }}
        />
      )
    } as ModalPropsType)
  }, [
    t,
    handleCloseModal,
    handleTransfer,
    navigateProps.icon,
    selectedEntries.length,
    setModalProps,
    getTransferButtonLabel,
    isDesktop,
    invalidEntries.length,
    primaryActionClicked,
    handleGoBack
  ])

  const hasErrors = invalidEntries.length > 0

  if (!orderDetails) {
    return (
      <div className={styles.loading}>
        {isLoading || isFetching ? (
          <Loading label={t('orders.loading_order_message.label')} />
        ) : (
          <MessageWithAction
            messageHeader={t('orders.could_not_load_order.label')}
            messageDescription={t('orders.could_not_load_order.description')}
            iconProps={{
              icon: 'info',
              variant: 'filled-secondary',
              className: 'gc-icon-info'
            }}
            primaryButtonProps={{
              label: t('common.try_again.label'),
              variant: 'text',
              themeColor: 'danger',
              onClick: refetchOrderDetails
            }}
          />
        )}
      </div>
    )
  }

  return (
    <div className={styles.container}>
      {hasErrors && (
        <AlertContainer
          modalSize='small'
          alerts={[
            {
              type: 'error',
              title: `${invalidEntries.length} ${t('common.product.label', { count: invalidEntries.length })} ${t('common.has_error.label')}`,
              description:
                invalidEntries.length === 1
                  ? t('orders.insufficient_product.description')
                  : t('orders.insufficient_products.description'),
              isActive: true
            }
          ]}
        />
      )}
      <MediaQuery maxWidth={1023}>
        <TransferProductsList
          entries={validEntries as OrderDetailsCBUS['entries']}
          quantities={quantities}
          invalidEntries={invalidEntries}
          onQuantityChange={handleQuantityChange}
          onBlur={handleBlur}
        />
      </MediaQuery>
      <MediaQuery minWidth={1024}>
        <TransferProductsTable
          entries={validEntries as OrderDetailsCBUS['entries']}
          quantities={quantities}
          invalidEntries={invalidEntries}
          onQuantityChange={handleQuantityChange}
          onBlur={handleBlur}
        />
      </MediaQuery>
    </div>
  )
}

export default AddTransferQtyModal
