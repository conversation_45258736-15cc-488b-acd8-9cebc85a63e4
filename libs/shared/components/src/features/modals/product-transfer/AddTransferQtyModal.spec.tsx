/* eslint-disable @nx/enforce-module-boundaries */
import * as reduxStore from '@gc/redux-store'
import { setUpStore } from '@gc/redux-store'
import { handlers, TestModal } from '@gc/shared/test'
import { act, actAwait, fireEvent, render, screen, waitFor } from '@gc/utils'
import { http, HttpResponse } from 'msw'
// MSW will be used via handlers
import { setupServer } from 'msw/node'
import { MemoryRouter as Router } from 'react-router-dom'

import AddTransferQtyModal from './AddTransferQtyModal'

const server = setupServer(...handlers)

const mockStockOrder = {
  code: 'STOCK-12345',
  entries: [
    {
      entryNumber: 10,
      product: {
        code: 'PROD-001',
        name: '192-08VT2PRIB 80M BAG BAS250',
        isPackagingMaterial: false,
        crop: 'corn'
      },
      quantity: 500
    }
  ],
  status: 'OPEN',
  statusText: 'Open'
}

const mockPortalConfig = {
  gcPortalConfig: {
    stockOrderConfig: {
      salesYear: '2024',
      documentType: 'STOCK_ORDER'
    }
  },
  ordersModule: {
    disableTransferStatuses: ['CANCELLED', 'CREDIT_BLOCKED']
  }
}

const mockNavigate = jest.fn()
const mockOpenModal = jest.fn()
const mockCloseModal = jest.fn()
const mockOpenAbandonModal = jest.fn()
const mockHandleClose = jest.fn()
const mockDispatch = jest.fn()

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}))

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useMemoizedTranslation: () => jest.fn((key) => key),
  useIsDesktop: () => true,
  usePortalConfig: () => mockPortalConfig,
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig,
  useModal: () => ({ openModal: mockOpenModal, closeModal: mockCloseModal }),
  useSelectedAccount: () => ({ sapAccountId: '123456' }),
  useCreateStockOrder: () => jest.fn(),
  useOpenAbandonModal: () => mockOpenAbandonModal,
  useSaveEntries: () => [jest.fn().mockResolvedValue(true)],
  useConvertCartToOrder: () => [jest.fn().mockResolvedValue(true)]
}))

jest.mock('@gc/redux-store', () => ({
  ...jest.requireActual('@gc/redux-store'),
  useGlobalDispatch: () => mockDispatch
}))

describe('AddTransferQtyModal', () => {
  let mockStore: ReturnType<typeof setUpStore>

  const defaultProps = {
    navigateProps: { icon: 'close', handleClose: mockHandleClose }
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockStore = setUpStore(
      { orders: { preShipTransfer: { transferType: 'order', fromOrderCode: '1234' } } as reduxStore.OrdersState },
      {
        injectConfigDataApi: true,
        injectOrdersApi: true
      }
    )
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  describe('Basic rendering', () => {
    it('should render successfully', () => {
      const { baseElement } = render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )
      expect(baseElement).toBeTruthy()
    })

    it('should show loading state when fetching order details', () => {
      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )

      expect(screen.getByText('orders.loading_order_message.label')).toBeDefined()
    })

    it('should render with stockOrderDetails when provided', () => {
      const { baseElement } = render(
        <Router>
          <TestModal
            modalBody={AddTransferQtyModal}
            bodyProps={{
              ...defaultProps,
              stockOrderDetails: mockStockOrder
            }}
          />
        </Router>,
        { store: mockStore }
      )
      expect(baseElement).toBeTruthy()
    })
  })

  describe('Product list rendering', () => {
    it('should display list of products from order', async () => {
      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )

      await waitFor(() => {
        expect(screen.getByText('180-33VT2PRIB 80M BAG BAS500 B')).toBeDefined()
        expect(screen.getByText('4023RXF/SR 140M BAG STAND-FI')).toBeDefined()
        expect(screen.getByText('SWEETLEAFII 50#BAG BASIC')).toBeDefined()
      })
    })

    it('should display available quantities for each product', async () => {
      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )

      await waitFor(() => {
        expect(screen.getByText('4')).toBeDefined()
        expect(screen.getByText('3')).toBeDefined() // 300 - 100 consigned
        expect(screen.getByText('5')).toBeDefined() // 300 - 100 consigned
      })
    })

    it('should render title for the products list', async () => {
      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )

      await waitFor(() => {
        expect(screen.getByText('common.product_name.label')).toBeDefined()
        expect(screen.getByText('common.qty_to_transfer.label')).toBeDefined()
      })
    })
  })

  describe('Quantity input handling', () => {
    it('should allow entering valid quantities', async () => {
      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )

      const inputs = await screen.findAllByRole('spinbutton')
      fireEvent.change(inputs[0], { target: { value: '100' } })

      expect(inputs[0]).toHaveValue(100)
    })

    it('should mark invalid entry when quantity exceeds available', async () => {
      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )

      const inputs = await screen.findAllByRole('spinbutton')
      fireEvent.change(inputs[0], { target: { value: '600' } }) // exceeds available 500
      fireEvent.blur(inputs[0])

      expect(screen.getByText('1 common.product.label common.has_error.label')).toBeDefined()
    })
  })

  describe('Button states and actions', () => {
    it('should disable transfer button when no quantities entered', async () => {
      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )

      const transferButton = await screen.findByText('inventory.transfer.label 0 common.product.label')
      expect(transferButton.closest('button')).toBeDisabled()
    })

    it('should enable transfer button when valid quantities entered', async () => {
      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )

      const inputs = await screen.findAllByRole('spinbutton')
      await act(async () => {
        fireEvent.change(inputs[0], { target: { value: '1' } })
        fireEvent.blur(inputs[0])
      })

      const transferButton = await screen.findByText('inventory.transfer.label 1 common.product.label')
      expect(transferButton.closest('button')).not.toBeDisabled()
    })

    it('should disable transfer button when any entry has invalid quantity', async () => {
      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )

      const inputs = await screen.findAllByRole('spinbutton')
      await act(async () => {
        fireEvent.change(inputs[0], { target: { value: '600' } }) // exceeds available 500
        fireEvent.blur(inputs[0])
      })

      const transferButton = await screen.findByText('inventory.transfer.label 1 common.product.label')
      expect(transferButton.closest('button')).toBeDisabled()
    })

    it('should call updatePreShipTransferEntries when transfer button clicked', async () => {
      jest.spyOn(reduxStore, 'setPreShipTransferEntries')

      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )

      const inputs = await screen.findAllByRole('spinbutton')
      await act(async () => {
        fireEvent.change(inputs[0], { target: { value: '1' } })
        fireEvent.blur(inputs[0])
      })

      const transferButton = await screen.findByText('inventory.transfer.label 1 common.product.label')

      fireEvent.click(transferButton)

      expect(mockDispatch).toHaveBeenCalledWith({
        payload: [
          {
            changeType: 'SFDCTRGT',
            createNewEntry: true,
            cropCode: 'seed_corn',
            cropName: 'Corn',
            entryNumber: undefined,
            masterOrderNumber: '1234',
            product: {
              code: '000000000089231108',
              isPackagingMaterial: false,
              name: '180-33VT2PRIB 80M BAG BAS500 B'
            },
            quantity: 1,
            refDocIt: 10,
            storageLocation: {
              address: {
                city: 'Kimball',
                line1: '23625 365th Ave',
                postalcode: '57355-6004',
                region: 'SD',
                streetName: '23625 365th Ave',
                uid: '8V2U_WH01-L'
              },
              code: '8V2U_WH01',
              locationCode: 'WH01',
              locationName: 'SM Kimball SD - Brian Havlik',
              plant: 'SM Kimball SD - Brian Havlik'
            }
          }
        ],
        type: 'orders/setPreShipTransferEntries'
      })
    })
  })

  describe('Transfer type handling', () => {
    it('should open TransferToFarmerModal when transfer type is order', async () => {
      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )

      const inputs = await screen.findAllByRole('spinbutton')
      await act(async () => {
        fireEvent.change(inputs[0], { target: { value: '1' } })
        fireEvent.blur(inputs[0])
      })

      const transferButton = await screen.findByText('inventory.transfer.label 1 common.product.label')
      fireEvent.click(transferButton)

      expect(mockOpenModal).toHaveBeenCalledWith({ name: 'SELECT_FARMER', props: { usage: 'order' } })
    })

    it('should handle stock order transfer when transfer type is stockOrder', async () => {
      const mockStore = setUpStore(
        {
          orders: { preShipTransfer: { transferType: 'stockOrder', fromOrderCode: '1234' } } as reduxStore.OrdersState
        },
        {
          injectConfigDataApi: true,
          injectOrdersApi: true
        }
      )
      const cartFromOrderMock = jest.fn().mockResolvedValue(true)
      jest.spyOn(reduxStore, 'useCartQueries').mockReturnValue({
        useCartFromOrderMutation: () => [cartFromOrderMock],
        useDeleteCartMutation: () => [cartFromOrderMock],
        useLazyGetPreShipCartQuery: () => [cartFromOrderMock]
      } as unknown as ReturnType<typeof reduxStore.useCartQueries>)

      render(
        <Router>
          <TestModal
            modalBody={AddTransferQtyModal}
            bodyProps={{
              ...defaultProps,
              stockOrderDetails: mockStockOrder
            }}
          />
        </Router>,
        { store: mockStore }
      )

      const inputs = await screen.findAllByRole('spinbutton')
      await act(async () => {
        fireEvent.change(inputs[0], { target: { value: '100' } })
        fireEvent.blur(inputs[0])
      })

      const transferButton = await screen.findByText('inventory.transfer.label 1 common.product.label')
      await act(async () => {
        fireEvent.click(transferButton)
      })

      expect(cartFromOrderMock).toHaveBeenCalled()
    })
  })

  describe('Error handling', () => {
    it('should handle API error when loading order details', async () => {
      server.use(http.get(/\/orders\/\d/, () => HttpResponse.error()))
      render(
        <Router>
          <TestModal modalBody={AddTransferQtyModal} bodyProps={defaultProps} />
        </Router>,
        { store: mockStore }
      )
      await actAwait(100)

      expect(screen.getByText('orders.could_not_load_order.label')).toBeDefined()
      expect(screen.getByText('orders.could_not_load_order.description')).toBeDefined()
    })
  })
})
