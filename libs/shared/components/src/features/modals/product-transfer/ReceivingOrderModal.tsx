/* eslint-disable @nx/enforce-module-boundaries */
import { TypoCaption } from '@element/react-components'
import { ExpansionPanel } from '@element/react-expansion-panel'
import { Radio } from '@element/react-radio'
import { TypoSubtitle } from '@element/react-typography'
import { AlertContainer, List, Loading } from '@gc/components'
import { interpunct, space } from '@gc/constants'
import {
  useFetchPreShipCart,
  useGcPortalConfig,
  useIsMobile,
  useMemoizedTranslation,
  useModal,
  useOpenAbandonModal,
  usePortalConfig,
  useSaveEntries,
  useSelectedAccount,
  useSetTransferFailContingency
} from '@gc/hooks'
import {
  extendedCartApiSlice,
  getCartId,
  getPreShipTransfer,
  GlobalRootState,
  OrdersState,
  setCartId,
  setCode,
  setContingency,
  setInEditMode,
  setLoadingContingency,
  setPreShipTransferToOrderCode,
  useCartQueries,
  useGlobalDispatch,
  useOrdersQueries
} from '@gc/redux-store'
import type { <PERSON>t, ChannelOrder, Farmer, OrderDetailsCBUS } from '@gc/types'
import { fasteRoute, filterByIsNotPackagingMaterial } from '@gc/utils'
import _ from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'

import { TopAppBar } from '../../../ui-common/header/TopAppBar'
import { ModalActionSlot } from '../../../ui-common/modal/ModalActionSlot'
import { ModalDefaultProps } from '../modalProps'
import styles from './ReceivingOrderModal.module.scss'

export interface ReceivingOrderModalProps extends ModalDefaultProps {
  farmer: Farmer
}

export function ReceivingOrderModal({ setModalProps, navigateProps, farmer }: ReceivingOrderModalProps): JSX.Element {
  const dispatch = useGlobalDispatch()
  const t = useMemoizedTranslation()

  const [primaryActionClicked, setPrimaryActionClicked] = useState(false)
  const preShipTransfer = useSelector(getPreShipTransfer) as OrdersState['preShipTransfer']
  const preShipTransferEntries = preShipTransfer?.transferEntries

  const [selectedOrderCode, setSelectedOrderCode] = useState<string>(preShipTransfer?.toOrderCode ?? 'newOrder')
  const [previousOrderCode] = useState<string | undefined>(preShipTransfer?.toOrderCode)
  const gcPortalConfig = useGcPortalConfig()
  const portalConfig = usePortalConfig()
  const { sapAccountId } = useSelectedAccount()
  const { openModal, closeModal } = useModal()
  const isMobile = useIsMobile()
  const openAbandonModal = useOpenAbandonModal()
  const setTransferFailContingency = useSetTransferFailContingency()
  const { useGetAllOrdersQuery } = useOrdersQueries<ChannelOrder>()
  const [saveEntries] = useSaveEntries()
  const fetchPreShipCart = useFetchPreShipCart()
  const { useDeleteCartMutation } = useCartQueries()
  const [deleteCart] = useDeleteCartMutation()

  const cartId = useSelector(getCartId)
  const currentCart = useSelector(
    (state: GlobalRootState) => extendedCartApiSlice.endpoints.getCurrentCart.select(cartId)(state)?.data
  )
  const {
    data: orders,
    isLoading,
    isFetching,
    error: allOrdersError,
    refetch: refetchAllOrders
  } = useGetAllOrdersQuery({
    isMobile,
    reqBody: {
      pageSize: gcPortalConfig?.ordersPageSize ?? 100,
      salesYears: gcPortalConfig?.salesYear,
      documentTypes: ['ZU3O'], // Standard order type
      agents: [sapAccountId],
      soldToAccounts: [farmer.sourceId]
    }
  })

  const filteredOrders = useMemo(
    () =>
      (orders ?? [])?.filter(
        (o) =>
          o.salesYear === gcPortalConfig?.orderConfig.salesYear &&
          !(portalConfig?.ordersModule?.disableTransferStatuses ?? []).includes(o.status) &&
          o.code !== preShipTransfer?.fromOrderCode
      ),
    [
      gcPortalConfig?.orderConfig.salesYear,
      orders,
      portalConfig?.ordersModule?.disableTransferStatuses,
      preShipTransfer?.fromOrderCode
    ]
  )

  const handleOrderSelect = useCallback(
    (orderCode: string) => {
      setSelectedOrderCode(orderCode)
      // This dispatch is in case user clicks cancel go to Abandon and then comes back here!!
      dispatch(setPreShipTransferToOrderCode(orderCode === 'newOrder' ? undefined : orderCode))
    },
    [dispatch]
  )

  const handleEditTargetOrder = useCallback(
    async (cartId: string, order: OrderDetailsCBUS | ChannelOrder) => {
      const res = await saveEntries(
        {
          cartId: cartId,
          data: { orderEntries: preShipTransfer?.transferEntries ?? [] },
          updateMethod: 'POST'
        },
        { contingencyType: 'none' }
      )
      if (res.error) {
        setTransferFailContingency(() => {
          dispatch(setLoadingContingency(t('common.transferring_products.label')))
          handleEditTargetOrder(cartId, order)
        }, cartId)
      } else {
        dispatch(setCode(preShipTransfer?.toOrderCode ?? ''))
        dispatch(setContingency())
        if (isMobile) {
          dispatch(setCartId(cartId))
          dispatch(setInEditMode(true))
          openModal({ name: 'REVIEW_ORDER', props: { existingOrderDetails: order as OrderDetailsCBUS } })
        } else {
          fasteRoute(`/orders/${order.code}`, {
            cartId: cartId,
            inEditMode: true,
            redirectToFarmers: false
          })
        }
      }
    },
    [
      dispatch,
      isMobile,
      openModal,
      preShipTransfer?.toOrderCode,
      preShipTransfer?.transferEntries,
      saveEntries,
      setTransferFailContingency,
      t
    ]
  )

  const handleCreateNewOrder = useCallback(async () => {
    dispatch(setContingency())
    openModal({ name: 'CREATE_ORDER', props: { usage: 'order', farmer } })
  }, [dispatch, farmer, openModal])

  const getPreShipCart = useCallback(async () => {
    const farmerMismatch = currentCart?.billToParties?.[0].sapAccountId !== farmer.sourceId
    let cart: Cart | undefined = currentCart

    // In case when there is a cart in cache, the previously selected orderCode (newOrder in case of undefined) must be different from selectedOrderCode in order to re fetch cart for preship
    if (farmerMismatch || !cart || (previousOrderCode || 'newOrder') !== selectedOrderCode) {
      closeModal()

      const loaderMsg =
        selectedOrderCode === 'newOrder'
          ? 'orders.creating_order_loader_message.label'
          : 'common.transferring_products.label'
      dispatch(setLoadingContingency(t(loaderMsg)))

      if (cart) {
        deleteCart({ cartId, skipCartRefetch: true }) // no need to wait on this as this is only cleanup on CC side.
      }
      const res = await fetchPreShipCart({
        fromOrderCode: preShipTransfer?.fromOrderCode ?? '',
        toOrderCode: selectedOrderCode === 'newOrder' ? undefined : selectedOrderCode
      })
      if (res.error) {
        return { cart: null, error: res.error }
      }

      cart = res.data as Cart
    }

    return { cart }
  }, [
    cartId,
    closeModal,
    currentCart,
    deleteCart,
    dispatch,
    farmer.sourceId,
    fetchPreShipCart,
    preShipTransfer?.fromOrderCode,
    previousOrderCode,
    selectedOrderCode,
    t
  ])

  const handleReviewOrder = useCallback(async () => {
    setPrimaryActionClicked(true)
    const { cart: preShipCart, error } = await getPreShipCart()
    if (error) {
      setTransferFailContingency(handleReviewOrder)
      return
    }

    const cart = { ...(preShipCart ?? {}) }
    if (selectedOrderCode === 'newOrder' && !!cart) {
      cart.draftEntries = preShipTransferEntries ?? []
    }
    // This is to set cache key
    dispatch(extendedCartApiSlice.util.upsertQueryData('getCurrentCart', cart?.code ?? '', cart as Cart))
    dispatch(setCartId(cart?.code))

    if (selectedOrderCode !== 'newOrder') {
      closeModal()
      const orderDetails = orders?.find((order) => order.code === selectedOrderCode)
      if (orderDetails) {
        handleEditTargetOrder(cart?.code ?? '', orderDetails)
      }
    } else {
      setTimeout(handleCreateNewOrder, 100)
    }
    dispatch(setPreShipTransferToOrderCode(selectedOrderCode === 'newOrder' ? undefined : selectedOrderCode))
  }, [
    closeModal,
    dispatch,
    getPreShipCart,
    handleCreateNewOrder,
    handleEditTargetOrder,
    orders,
    preShipTransferEntries,
    selectedOrderCode,
    setTransferFailContingency
  ])

  const renderProductList = useCallback(
    (entries: ChannelOrder['entries']) => {
      return (
        <List
          nonInteractive
          className={styles.product_list}
          items={(entries ?? [])
            .filter((e) => !e.product.isPackagingMaterial)
            .map((entry) => {
              return {
                primaryText: (
                  <TypoSubtitle bold level={2}>
                    {entry.product.name}
                  </TypoSubtitle>
                ),
                secondaryText: (
                  <TypoCaption>
                    {`${entry.confirmedQuantity ?? -1} ${t('common.confirmed.label')}${interpunct}${entry.unconfirmedQuantity ?? -1} ${t('common.unconfirmed.label')}`}
                  </TypoCaption>
                )
              }
            })}
        />
      )
    },
    [t]
  )

  const getOrderListItems = useCallback(() => {
    const orderItems =
      filteredOrders?.map((order) => ({
        key: order.code,
        code: order.code,
        row: order,
        noPadding: true,
        isCustomTrailingBlock: true,
        leadingBlock: (
          <Radio
            checked={preShipTransfer?.toOrderCode === order.code}
            defaultChecked={false}
            name='order-selection'
            value={order.code}
            themeColor='secondary'
            data-testid={`order-radio-${order.code}`}
          />
        ),
        leadingBlockType: 'radio',
        primaryText: (
          <TypoSubtitle bold level={2}>
            {`${t('orders.order.label')} ${order.orderNumber}`}
          </TypoSubtitle>
        ),
        secondaryText: (
          <TypoCaption>
            {!_.isEmpty(order.name) && (
              <>
                {order.name}
                <br />
              </>
            )}
            {filterByIsNotPackagingMaterial(order.entries ?? []).length}
            {space}
            {t('common.product.label', { count: order.entries?.length })}
          </TypoCaption>
        )
      })) ?? []
    return [
      {
        key: 'newOrder',
        code: 'newOrder',
        noPadding: true,
        noExpandedRow: true,
        primaryText: (
          <TypoSubtitle bold level={2}>
            {t('common.new_order.label')}
          </TypoSubtitle>
        ),
        leadingBlock: (
          <Radio
            checked={true}
            name='order-selection'
            value={'newOrder'}
            themeColor='secondary'
            data-testid={`order-radio-new`}
          />
        ),
        leadingBlockType: 'radio'
      },
      ...orderItems
    ]
  }, [filteredOrders, preShipTransfer?.toOrderCode, t])

  const receivingOrdersList = useMemo(
    () => (
      <List<ChannelOrder>
        items={getOrderListItems()}
        listItemClassName={styles.list_item}
        className={styles.list_container}
        expandedRowTemplate={(order) => renderProductList((order as ChannelOrder)?.entries ?? [])}
        onAction={handleOrderSelect}
      />
    ),
    [getOrderListItems, handleOrderSelect, renderProductList]
  )

  useEffect(() => {
    setModalProps({
      sectionPadding: 'none',
      modalSize: !isMobile ? 'xlarge' : undefined,
      headerActions: (
        <TopAppBar
          title={t('orders.receiving_order.label')}
          isModalTopBar
          leadingIconButtonProps={{
            icon: navigateProps.icon,
            onClick: () => {
              dispatch(setPreShipTransferToOrderCode())
              navigateProps.handleClose()
            }
          }}
        />
      ),
      footerActions: (
        <ModalActionSlot
          primaryAction={{
            label: t(selectedOrderCode === 'newOrder' ? 'orders.create_order.label' : 'orders.review_order.label'),
            onClick: handleReviewOrder,
            disabled: !selectedOrderCode || primaryActionClicked || isLoading || isFetching,
            variant: 'filled'
          }}
          secondaryAction={{
            label: t('common.cancel.label'),
            onClick: openAbandonModal,
            variant: 'outlined',
            disabled: primaryActionClicked || isLoading || isFetching
          }}
        />
      )
    })
  }, [
    t,
    handleReviewOrder,
    isMobile,
    navigateProps.icon,
    selectedOrderCode,
    setModalProps,
    primaryActionClicked,
    isLoading,
    isFetching,
    navigateProps.handleClose,
    openAbandonModal,
    navigateProps,
    dispatch
  ])

  if (isLoading) {
    return (
      <div className={styles.loading}>
        <Loading label={t('orders.loading_orders_message.label')} />
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <AlertContainer
        alerts={[
          {
            type: 'warning',
            title: t('orders.error_fetching_open_orders.label'),
            description: t('orders.error_fetching_open_orders.description'),
            actionButtonProps: {
              label: t('common.try_again.label'),
              onClick: refetchAllOrders
            },
            isActive: !!allOrdersError
          }
        ]}
        modalSize='small'
      />
      <ExpansionPanel>{receivingOrdersList}</ExpansionPanel>
    </div>
  )
}

export default ReceivingOrderModal
