/* eslint-disable @nx/enforce-module-boundaries */
import * as reduxStore from '@gc/redux-store'
import { OrdersState, setUpStore } from '@gc/redux-store'
import { handlers, TestModal } from '@gc/shared/test'
import { Farmer } from '@gc/types'
import { actAwait, fireEvent, render, screen, waitFor } from '@gc/utils'
import { orders } from 'libs/shared/test/src/lib/mocks/orders'
import { http, HttpResponse } from 'msw'
import { setupServer } from 'msw/node'
import { MemoryRouter as Router } from 'react-router-dom'

import ReceivingOrderModal from './ReceivingOrderModal'

const server = setupServer(...handlers)

const mockPortalConfig = {
  gcPortalConfig: {
    ordersPageSize: 100,
    salesYear: '2026',
    orderConfig: {
      salesYear: '2026'
    }
  },
  ordersModule: {
    disableTransferStatuses: ['CANCELLED', 'CREDIT_BLOCKED']
  }
}

const mockFarmer = {
  sourceId: '456',
  name: '<PERSON>'
}

const mockCart = {
  code: 'CART-123',
  billToParties: [{ sapAccountId: '456' }],
  draftEntries: []
}

const mockNavigate = jest.fn()
const mockOpenModal = jest.fn()
const mockCloseModal = jest.fn()
const mockOpenAbandonModal = jest.fn()
const mockHandleClose = jest.fn()
const mockFetchPreShipCart = jest.fn().mockResolvedValue({ data: mockCart })
const mockSaveEntries = jest.fn().mockResolvedValue({})

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}))

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useMemoizedTranslation: () => jest.fn((key) => key),
  useIsMobile: () => false,
  usePortalConfig: () => mockPortalConfig,
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig,
  useModal: () => ({ openModal: mockOpenModal, closeModal: mockCloseModal }),
  useSelectedAccount: () => ({ sapAccountId: '123456' }),
  useOpenAbandonModal: () => mockOpenAbandonModal,
  useFetchPreShipCart: () => mockFetchPreShipCart,
  useSaveEntries: () => [mockSaveEntries],
  useSetTransferFailContingency: () => jest.fn()
}))

describe('ReceivingOrderModal', () => {
  let mockStore: ReturnType<typeof setUpStore>
  const mockDispatch = jest.fn()

  const defaultProps = {
    farmer: mockFarmer as Farmer,
    navigateProps: { icon: 'close', handleClose: mockHandleClose }
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockStore = setUpStore(undefined, {
      injectConfigDataApi: true,
      injectOrdersApi: true,
      injectInventoryApi: true
    })
    jest.spyOn(reduxStore, 'useGlobalDispatch').mockReturnValue(mockDispatch)
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('should render successfully', () => {
    const { baseElement } = render(
      <Router>
        <TestModal modalBody={ReceivingOrderModal} bodyProps={defaultProps} />
      </Router>,
      { store: mockStore }
    )
    expect(baseElement).toBeTruthy()
  })

  it('should show loading state when fetching orders', () => {
    render(
      <Router>
        <TestModal modalBody={ReceivingOrderModal} bodyProps={defaultProps} />
      </Router>,
      { store: mockStore }
    )

    expect(screen.getByText('orders.loading_orders_message.label')).toBeDefined()
  })

  it('should display error alert when orders API fails', async () => {
    server.use(http.post(/\/allorders/, () => HttpResponse.error()))
    render(
      <Router>
        <TestModal modalBody={ReceivingOrderModal} bodyProps={defaultProps} />
      </Router>,
      { store: mockStore }
    )
    await waitFor(() => expect(screen.queryByText('orders.loading_order_message.label')).toBeNull())

    // Check for alert content
    expect(screen.getByText('orders.error_fetching_open_orders.label')).toBeDefined()
    expect(screen.getByText('orders.error_fetching_open_orders.description')).toBeDefined()
    expect(screen.getByText('common.try_again.label')).toBeDefined()
  })

  it('should render the list of orders with current sales year of 2025', async () => {
    const updatedOrders = { ...orders }
    updatedOrders.orders[0].salesYear = '2026'
    server.use(http.post(/\/allorders/, () => HttpResponse.json(updatedOrders)))

    render(
      <Router>
        <TestModal modalBody={ReceivingOrderModal} bodyProps={defaultProps} />
      </Router>,
      { store: mockStore }
    )

    // Check for new order option and existing order
    await waitFor(() => {
      expect(screen.getByText('common.new_order.label')).toBeDefined()
      expect(screen.getByText('orders.order.label 0002232376')).toBeDefined()
      expect(screen.queryByText('orders.order.label 0002232137')).toBeNull()
      expect(screen.queryByText('orders.order.label 0002232128')).toBeNull()
    })
  })

  it('should render the list of orders with status not part of disable transfer', async () => {
    const updatedOrders = { ...orders }
    updatedOrders.orders[0].salesYear = '2026'
    updatedOrders.orders[1].status = 'CREDIT_BLOCKED'
    updatedOrders.orders[2].status = 'CANCELLED'
    server.use(http.post(/\/allorders/, () => HttpResponse.json(updatedOrders)))

    render(
      <Router>
        <TestModal modalBody={ReceivingOrderModal} bodyProps={defaultProps} />
      </Router>,
      { store: mockStore }
    )

    // Check for new order option and existing order
    await waitFor(() => {
      expect(screen.getByText('common.new_order.label')).toBeDefined()
      expect(screen.getByText('orders.order.label 0002232376')).toBeDefined()
      expect(screen.queryByText('orders.order.label 0002232137')).toBeNull()
      expect(screen.queryByText('orders.order.label 0002232128')).toBeNull()
    })
  })

  it('should select an existing order when clicked', async () => {
    const updatedOrders = { ...orders }
    updatedOrders.orders[0].salesYear = '2026'
    server.use(http.post(/\/allorders/, () => HttpResponse.json(updatedOrders)))

    render(
      <Router>
        <TestModal modalBody={ReceivingOrderModal} bodyProps={defaultProps} />
      </Router>,
      { store: mockStore }
    )

    // Click on the existing order
    await waitFor(() => {
      fireEvent.click(screen.getByText('orders.order.label 0002232376'))
    })

    // Check that the review button label is updated
    expect(screen.getByText('orders.review_order.label')).toBeDefined()
  })

  it('should handle create new order action', async () => {
    render(
      <Router>
        <TestModal modalBody={ReceivingOrderModal} bodyProps={defaultProps} />
      </Router>,
      { store: mockStore }
    )
    await actAwait(100)

    // Click the create order button
    const createOrderButton = await screen.findByText('orders.create_order.label')
    fireEvent.click(createOrderButton)

    // Check that the correct actions were called
    expect(mockCloseModal).toHaveBeenCalled()
    expect(mockFetchPreShipCart).toHaveBeenCalled()
    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        payload: 'orders.creating_order_loader_message.label',
        type: 'app/setLoadingContingency'
      })
    )
  })

  it('should handle edit target order action for existing order', async () => {
    const updatedOrders = { ...orders }
    updatedOrders.orders[0].salesYear = '2026'
    server.use(http.post(/\/allorders/, () => HttpResponse.json(updatedOrders)))

    render(
      <Router>
        <TestModal modalBody={ReceivingOrderModal} bodyProps={defaultProps} />
      </Router>,
      { store: mockStore }
    )

    // Click on the existing order
    await waitFor(() => {
      fireEvent.click(screen.getByText('orders.order.label 0002232376'))
    })

    // Click the review order button
    const reviewOrderButton = screen.getByText('orders.review_order.label')
    fireEvent.click(reviewOrderButton)

    // Check that the correct actions were called
    expect(mockCloseModal).toHaveBeenCalled()
    expect(mockFetchPreShipCart).toHaveBeenCalled()
    expect(mockDispatch).toHaveBeenNthCalledWith(
      1,
      expect.objectContaining({ payload: 'BC-0002232376', type: 'orders/setPreShipTransferToOrderCode' })
    )
    expect(mockDispatch).toHaveBeenNthCalledWith(
      2,
      expect.objectContaining({ payload: 'common.transferring_products.label', type: 'app/setLoadingContingency' })
    )
  })

  it('should handle abandon modal when Cancel is clicked', async () => {
    const mockStore = setUpStore(
      { orders: { preShipTransfer: { transferEntries: [{}] } } as OrdersState },
      {
        injectConfigDataApi: true,
        injectOrdersApi: true
      }
    )
    render(
      <Router>
        <TestModal modalBody={ReceivingOrderModal} bodyProps={defaultProps} />
      </Router>,
      { store: mockStore }
    )
    await actAwait(100)

    // Wait for the modal to render
    await waitFor(() => {
      // Click the Cancel button
      const cancelButton = screen.getByText('common.cancel.label')
      fireEvent.click(cancelButton)
    })

    // Check that the abandon modal was opened
    expect(mockOpenAbandonModal).toHaveBeenCalled()
  })
})
