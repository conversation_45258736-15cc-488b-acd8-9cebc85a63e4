.container {
  :global(.mdc-data-table) {
    border: 0px !important;
  }
}

.textfield {
  width: 136px;
  height: 40px;

  @media (max-width: 1023px) {
    width: 96px;
  }

  input {
    text-align: right;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

.list_item_error {
  background-color: #ffeaed;
  span {
    color: #cf2d22 !important;
  }
}

.textfield_mobile_error {
  border: 2px solid #b85302 !important;
}

.textfield_desktop_error {
  border: 2px solid #b85302 !important;
  color: #b85302 !important;
  input {
    color: #b85302 !important;
  }
}

.quantity_leading_icon_with_tooltip {
  padding: 0px 8px 0px 16px;
  margin-top: 5px;
}

.loading {
  margin-top: 200px !important;
}
