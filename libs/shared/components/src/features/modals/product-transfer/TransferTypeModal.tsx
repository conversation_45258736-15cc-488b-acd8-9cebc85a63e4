/* eslint-disable @nx/enforce-module-boundaries */
import { Icon } from '@element/react-icon'
import { TypoSubtitle } from '@element/react-typography'
import {
  useCreateStockOrder,
  useGcPortalConfig,
  useIsDesktop,
  useMemoizedTranslation,
  useModal,
  useOpenAbandonModal,
  usePortalConfig,
  useSelectedAccount
} from '@gc/hooks'
import {
  getPreShipTransfer,
  resetPreShipTransfer,
  setOrdersCurrentTab,
  setPreShipTransferFromOrderCode,
  setPreShipTransferToOrderCode,
  setPreShipTransferType,
  useGlobalDispatch,
  useOrdersQueries
} from '@gc/redux-store'
import { AlertProps, StockOrder } from '@gc/types'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'

import { TopAppBar } from '../../../ui-common/header/TopAppBar'
import { List } from '../../../ui-common/list/List'
import Loading from '../../../ui-common/loading/Loading'
import AlertContainer, { AlertContainerProps } from '../../common/alert-container/AlertContainer'
import { ModalDefaultProps, ModalPropsType } from '../modalProps'
import styles from './TransferTypeModal.module.scss'

export interface TransferTypeModalProps extends ModalDefaultProps {
  orderCode: string
}

export function TransferTypeModal({ setModalProps, navigateProps, orderCode }: TransferTypeModalProps) {
  const t = useMemoizedTranslation()
  const isDesktop = useIsDesktop()
  const dispatch = useGlobalDispatch()
  const navigate = useNavigate()
  const { sapAccountId } = useSelectedAccount()
  const gcPortalConfig = useGcPortalConfig()
  const portalConfig = usePortalConfig()
  const stockOrderConfig = gcPortalConfig?.stockOrderConfig
  const initCreateStockOrder = useCreateStockOrder()
  const [invalidStockOrderAlert, setInvalidStockOrderAlert] = useState<AlertProps | undefined>()
  const preShipTransfer = useSelector(getPreShipTransfer)
  const openAbandonModal = useOpenAbandonModal()

  const handleClose = useCallback(() => {
    dispatch(resetPreShipTransfer())
    navigateProps.handleClose()
  }, [dispatch, navigateProps])

  const handleGoBack = useCallback(() => {
    if (preShipTransfer.transferEntries?.length) {
      openAbandonModal()
    } else {
      handleClose()
    }
  }, [handleClose, openAbandonModal, preShipTransfer.transferEntries?.length])

  const { useGetAllOrdersQuery } = useOrdersQueries<StockOrder>()

  const {
    data = [],
    error,
    isLoading,
    isFetching,
    refetch
  } = useGetAllOrdersQuery({
    isMobile: !isDesktop,
    reqBody: {
      pageSize: 100,
      agents: [sapAccountId],
      salesYears: [stockOrderConfig?.salesYear],
      documentTypes: [stockOrderConfig?.documentType]
    }
  })

  const { openModal, closeModal } = useModal()

  const handleFarmerTransfer = useCallback(() => {
    dispatch(setPreShipTransferType('order'))
    dispatch(setPreShipTransferFromOrderCode(orderCode))
    openModal({ name: 'ADD_TRANSFER_QTY', props: {} })
  }, [dispatch, openModal, orderCode])

  const handleStockOrderTransfer = useCallback(() => {
    dispatch(setPreShipTransferType('stockOrder'))
    dispatch(setPreShipTransferFromOrderCode(orderCode))
    dispatch(setPreShipTransferToOrderCode(data[0]?.code ?? ''))
    openModal({ name: 'ADD_TRANSFER_QTY', props: {} })
  }, [data, dispatch, openModal, orderCode])

  const alerts = useMemo(() => {
    const alerts: AlertContainerProps['alerts'] = []

    if (invalidStockOrderAlert) {
      alerts.push({ ...invalidStockOrderAlert, isActive: true })
    }
    if (error) {
      alerts.push({
        type: 'error',
        title: t('orders.could_not_load_order.label'),
        description: t('quotes.error_msg_description.label'),
        actionButtonProps: {
          label: t('common.try_again.label'),
          onClick: refetch
        },
        isActive: true
      })
    }

    return alerts
  }, [error, invalidStockOrderAlert, refetch, t])

  const goToStockOrderDetails = useCallback(() => {
    dispatch(setOrdersCurrentTab(1))
    navigate('/')
    handleClose()
  }, [dispatch, handleClose, navigate])

  useEffect(() => {
    if (data && !isLoading && !isFetching && !error) {
      if (data.length === 0) {
        setInvalidStockOrderAlert({
          type: 'info',
          description: `${t('common.you_do_not_have.label')} ${stockOrderConfig?.salesYear ?? 0} ${t('orders.stock_order.label')}.`,
          actionButtonProps: {
            label: t('orders.create_stock_order.label'),
            onClick: () => {
              goToStockOrderDetails()
              initCreateStockOrder()
            }
          }
        })
      } else if (data.length > 0 && portalConfig.ordersModule.disableTransferStatuses.includes(data[0].status)) {
        setInvalidStockOrderAlert({
          type: 'info',
          title: `${t('common.your.label')} ${stockOrderConfig?.salesYear ?? 0} ${t('orders.stock_order.label')} ${t('common.has_errors.label')}.`,
          actionButtonProps: {
            label: `${t('common.view.label')} ${t('orders.stock_order.label')}`,
            onClick: goToStockOrderDetails
          }
        })
      }
    }
  }, [
    closeModal,
    isFetching,
    data,
    dispatch,
    goToStockOrderDetails,
    initCreateStockOrder,
    isDesktop,
    isLoading,
    navigate,
    navigateProps,
    portalConfig.ordersModule.disableTransferStatuses,
    refetch,
    stockOrderConfig?.salesYear,
    t,
    error
  ])

  useEffect(() => {
    setModalProps({
      sectionPadding: 'none',
      modalSize: isDesktop ? 'xlarge' : undefined,
      headerActions: (
        <TopAppBar
          isModalTopBar
          title={t('inventory.transfer_type.label')}
          leadingIconButtonProps={{
            icon: navigateProps.icon,
            onClick: handleGoBack
          }}
        />
      )
    } as ModalPropsType)
  }, [t, handleGoBack, isDesktop, navigateProps.icon, setModalProps])

  const listItems = [
    {
      leadingBlock: <Icon icon='person' iconSize='medium' variant='filled-primary' />,
      onClick: handleFarmerTransfer,
      primaryText: <TypoSubtitle level={2}>{t('orders.transfer_to_farmer.label')}</TypoSubtitle>,
      trailingBlock: <Icon icon='chevron_right' />,
      leadingBlockType: 'icon-large',
      trailingBlockType: 'icon'
    },
    {
      leadingBlock: <Icon icon='receipt' iconSize='medium' variant='filled-primary' />,
      onClick: handleStockOrderTransfer,
      primaryText: <TypoSubtitle level={2}>{t('orders.transfer_to_stock_order.label')}</TypoSubtitle>,
      trailingBlock: <Icon icon='chevron_right' />,
      leadingBlockType: 'icon-large',
      trailingBlockType: 'icon',
      disabled: !!invalidStockOrderAlert || !!error
    }
  ]

  if (isLoading) {
    return <Loading className={styles.loading} label={t('orders.loading_order_message.label')} />
  }

  return (
    <div className={styles.container}>
      <AlertContainer alerts={alerts} modalSize='small' />
      <List className={styles.list_container} items={listItems} listItemClassName={styles.list_item} />
    </div>
  )
}

export default TransferTypeModal
