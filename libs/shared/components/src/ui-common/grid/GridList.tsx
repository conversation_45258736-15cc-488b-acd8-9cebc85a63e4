import { Grid, GridCol, GridRow } from '@element/react-grid'
import { useLoadingContingency } from '@gc/hooks'
import { useMemo } from 'react'

import LoadingAndContingencySection from '../../sections/contingency/LoadingAndContingencySection'
import styles from './GridList.module.scss'

export interface GridListProps extends React.PropsWithChildren {
  contingency: ReturnType<typeof useLoadingContingency>
  classNames?: {
    grid?: string
    content?: string
    containerContingency?: string
  }
}

export function GridList({ contingency, children, classNames = {} }: Readonly<GridListProps>) {
  const { hasError, hasNoData, dataLength } = contingency

  const verticalAlignClassName = useMemo(() => {
    if (hasError) {
      return 'middle'
    }
    if (!dataLength) {
      return 'middle'
    }
    return 'top'
  }, [dataLength, hasError])

  const gridColClassName = useMemo(() => {
    if (hasNoData || hasError) {
      return classNames.containerContingency ?? styles['container-contingency']
    }
    return ''
  }, [hasNoData, hasError, classNames.containerContingency])

  return (
    <Grid className={classNames.grid ?? styles.grid}>
      <GridRow className={classNames.content ?? styles.content}>
        <GridCol
          phoneCol={4}
          tabletCol={8}
          desktopCol={12}
          className={gridColClassName}
          verticalAlign={verticalAlignClassName}
        >
          <LoadingAndContingencySection
            {...contingency.errorProps}
            {...contingency.noDataProps}
            hasData={!contingency.hasNoData}
            hasError={contingency.hasError}
            isLoading={contingency.isDataLoading}
            loadingMessage={contingency.loadingMessage}
            onRetry={contingency.refetchData}
          >
            {children}
          </LoadingAndContingencySection>
        </GridCol>
      </GridRow>
    </Grid>
  )
}

export default GridList
