/* eslint-disable @typescript-eslint/no-explicit-any */
import { Checkbox } from '@element/react-components'
import { SwitchProps } from '@element/react-switch'
import { useImperativeHandle, useState } from 'react'

import { DataUpdateHandle, refWrapper, RowData } from '../RefWrapper'

/* eslint-disable-next-line */
export interface CheckboxColumnProps extends Omit<SwitchProps, 'disabled' | 'onChange' | 'label'> {
  checked?: boolean
  onChange: (val: boolean) => void
  disabled?: boolean | undefined | ((data: any, updatedData?: any) => boolean)
  row: RowData
  isCheckAll?: boolean
  onToggleAll?: (val: boolean) => void
  accessor: string
}

function CheckboxColumn(props: CheckboxColumnProps, ref: React.ForwardedRef<DataUpdateHandle>) {
  const {
    checked: propsChecked,
    onChange,
    row,
    isCheckAll,
    onToggleAll,
    disabled,
    accessor,
    ...elementCheckboxProps
  } = props
  const [checked, setChecked] = useState<boolean>(propsChecked || false)
  const [disabledVal, setDisabledVal] = useState(typeof disabled === 'function' ? disabled(row) : disabled)

  useImperativeHandle(ref, () => ({
    updateMatchingData: (updatedRows: RowData[]) => {
      const matching = updatedRows[row.rowIndex]
      if (isCheckAll) {
        setChecked(updatedRows.every((row) => row[accessor as keyof typeof matching]))
        typeof disabled === 'function' && setDisabledVal(updatedRows.every((row) => disabled(row, updatedRows)))
      } else if (matching) {
        const val = Boolean(matching[accessor as keyof typeof matching])
        if (val !== checked) {
          setChecked(val)
        }
        typeof disabled === 'function' && setDisabledVal(disabled(matching, updatedRows))
      }
    }
  }))

  return (
    <Checkbox
      {...elementCheckboxProps}
      data-testid={'checkbox'}
      checked={checked}
      onChange={(newValue: boolean) => {
        setChecked(newValue)
        if (isCheckAll) {
          onToggleAll?.(newValue)
        } else {
          onChange(newValue)
        }
      }}
      disabled={disabledVal}
    />
  )
}

export default refWrapper<CheckboxColumnProps>(CheckboxColumn)
