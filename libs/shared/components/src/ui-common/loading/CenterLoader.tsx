import { useMemo } from 'react'

import Loading, { LoadingProps } from './Loading'

export const CenterLoader = ({ label, className, type = 'linear', size = 'lg' }: Readonly<LoadingProps>) => {
  const resolvedClassName = useMemo(() => {
    return className ? { className } : {}
  }, [className])

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: '100%',
        ...resolvedClassName
      }}
    >
      <Loading type={type} size={size} label={label ?? 'Loading...'} />
    </div>
  )
}
