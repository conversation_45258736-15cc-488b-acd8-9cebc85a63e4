/* eslint-disable @nx/enforce-module-boundaries */
import { Modal } from '@element/react-modal'
import { resolutions } from '@gc/constants'
import { useScreenRes } from '@gc/hooks'
import React, { type ReactNode, useCallback, useEffect, useMemo, useState } from 'react'
import isEqual from 'react-fast-compare'

import { ModalPropsType } from '../../features/modals/modalProps'
import styles from './ModalContainer.module.scss'

export type ModalState = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  modalBody?: (props: any) => ReactNode
  props?: Record<string, unknown>
}

export type ModalContainerProps = {
  open: boolean
  modalName?: string
  modalProps?: ModalState['props']
  modals: Record<string, ModalState>
  className?: string
  getModalSize?: (
    modalBody: ModalState['modalBody'],
    screenRes: number
  ) => 'fullscreen' | 'max' | 'medium' | 'small' | 'xlarge'
  customInitialFocus?: boolean
  useDefaultHeight?: boolean
}

const defaultGetModalSize = (_modalBody: ModalState['modalBody'], screenRes: number) =>
  screenRes <= resolutions.M719 ? 'fullscreen' : 'medium'

export function ModalContainer(props: Readonly<ModalContainerProps>) {
  const {
    modals,
    className,
    getModalSize = defaultGetModalSize,
    customInitialFocus = true,
    useDefaultHeight = false
  } = props
  const res = useScreenRes()
  const [modalProps, setModalProps] = useState<ModalPropsType | null>(null)
  const [modal, setModal] = useState<ModalState | null>(
    props.modalName
      ? {
          ...modals[props.modalName],
          props: props.modalProps
        }
      : null
  )

  const modalSize = useMemo(() => {
    // modalSize is implemented at Modal level
    if (modalProps?.modalSize) return modalProps?.modalSize

    // getModalSize is implemented at module modal container level i.e. OrdersModalContainer
    return getModalSize ? getModalSize(modal?.modalBody, res) : defaultGetModalSize(modal?.modalBody, res)
  }, [getModalSize, modal?.modalBody, modalProps, res])

  const openModal = useCallback(
    (modalName: string, _props?: ModalState['props']) => {
      if (modalName) {
        setModal({
          props: { ..._props },
          ...modals[modalName]
        })
      }
    },
    [setModal, modals]
  )

  useEffect(() => {
    openModal(props.modalName as string, props.modalProps)
  }, [openModal, props.modalName, props.modalProps])

  useEffect(() => {
    if (!props.open) {
      setModalProps(null)
      setModal(null)
    }
  }, [props.open])

  const modalHeightCSS = useMemo(() => (useDefaultHeight ? styles.auto_section_height : ''), [useDefaultHeight])
  const modalFooterCSS = useMemo(
    () => (modalProps && !modalProps?.footerActions ? styles.modal_without_footer : ''),
    [modalProps]
  )
  const modalSectionPaddingCSS = useMemo(() => {
    if (modalProps?.sectionPadding === 'elementDefault') return ''
    return modalProps?.sectionPadding === 'none'
      ? styles.modal_section_no_padding
      : styles.modal_section_default_padding
  }, [modalProps?.sectionPadding])
  // Needed to add mdc-dialog--open to the className
  // because while switching between classNames somehow EDS modal is dropping the class name which keeps modal open!!
  const modalClassName = useMemo(
    () => `mdc-dialog--open ${modalHeightCSS} ${className} ${styles.modal} ${modalFooterCSS} ${modalSectionPaddingCSS}`,
    [className, modalFooterCSS, modalHeightCSS, modalSectionPaddingCSS]
  )

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Enter') {
        event.preventDefault()
        event.stopPropagation()

        // Blur the active input to close the keyboard on mobile
        if (document.activeElement instanceof HTMLElement) {
          document.activeElement.blur()
        }
        window.scrollTo(0, 0)
      }
    }
    document.addEventListener('keydown', handleKeyDown, true)
    return () => {
      document.removeEventListener('keydown', handleKeyDown, true)
    }
  }, [])

  if (!props.open || !modal?.modalBody) {
    return null
  }

  return (
    <div hidden={!modalProps}>
      <Modal
        open={props.open}
        hideCloseIcon
        preventClose
        className={modalClassName}
        modalSize={modalSize}
        title={modalProps?.modalTitle}
        headerActions={modalProps?.headerActions}
        nextButton={modalProps?.footerActions}
      >
        <modal.modalBody setModalProps={setModalProps} {...modal.props} openModal={openModal} />
        {/* This dummy button is used to remove the initial focus from the modal */}
        {customInitialFocus && (
          <button
            data-mdc-dialog-action='accept'
            data-mdc-dialog-button-default
            data-mdc-dialog-initial-focus
            style={{ opacity: 0, position: 'absolute', top: 0, left: 0 }}
          />
        )}
      </Modal>
    </div>
  )
}

export default React.memo(ModalContainer, isEqual)
