import 'react-loading-skeleton/dist/skeleton.css'

import React from 'react'
import Skeleton from 'react-loading-skeleton'

import styles from './TableSkeleton.module.scss'
import { TopBarSkeleton } from './TopBarSkeleton'

interface TableSkeletonProps {
  rows?: number
  columns?: number
  searchable?: boolean
  enableCsvDownload?: boolean
  showColumns?: boolean
  showTitle?: boolean
  showPagination?: boolean
  className?: string
}

export const TableSkeleton = ({
  rows = 7,
  columns = 6,
  searchable = false,
  enableCsvDownload = false,
  showColumns = false,
  showTitle = true,
  showPagination = true,
  className = styles.tableSkeletonWrapper
}: TableSkeletonProps) => {
  return (
    <div className={className}>
      {/* Table Header Skeleton */}
      <TopBarSkeleton showTitle={showTitle} searchable={searchable} enableCsvDownload={enableCsvDownload} />

      {/* Table Body Skeleton Rows */}
      {/* Table body */}
      {[...Array(rows)].map((_, rowIndex) =>
        showColumns ? (
          <div
            key={`row-${rowIndex}`}
            className={styles.tableRowSkeleton}
            style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
          >
            {[...Array(columns)].map((_, colIndex) => (
              <Skeleton
                key={`row-${rowIndex}-col-${colIndex}`}
                height={24}
                width={240}
                className={styles.cellSkeleton}
              />
            ))}
          </div>
        ) : (
          <div key={`row-${rowIndex}`} className={styles.fullRowSkeleton}>
            <Skeleton height={32} width='100%' />
          </div>
        )
      )}

      {/* Pagination Skeleton */}
      {showPagination && (
        <div className={styles.paginationSkeleton}>
          {/* Rows per page label */}
          <Skeleton height={32} width={60} />

          {/* Rows per page select skeleton */}
          <Skeleton height={32} width={60} />

          {/* Total rows count skeleton */}
          <Skeleton height={32} width={60} />

          {/* Pagination navigation buttons skeleton */}
          <div className={styles.paginationButtons}>
            {[...Array(4)].map((_, i) => (
              <Skeleton key={`pag-btn-${i}`} circle={true} height={24} width={24} />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
