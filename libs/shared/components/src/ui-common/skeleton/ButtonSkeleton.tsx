import styles from './ButtonSkeleton.module.scss'

export interface ButtonSkeletonProps {
  /** Button size - matches @element button sizes */
  buttonSize?: 'small' | 'medium' | 'large'
  /** Custom width for the skeleton */
  width?: number | string
  /** Custom height for the skeleton */
  height?: number
  /** Whether to use full width of the parent container */
  fullWidth?: boolean
}

const getDimensions = (
  width: ButtonSkeletonProps['width'],
  height: ButtonSkeletonProps['height'],
  buttonSize: ButtonSkeletonProps['buttonSize']
) => {
  switch (buttonSize) {
    case 'small':
      return { width: width ?? '80px', height: height ?? 32 }
    case 'large':
      return { width: width ?? '120px', height: height ?? 48 }
    case 'medium':
    default:
      return { width: width ?? '100px', height: height ?? 40 }
  }
}

export function ButtonSkeleton({
  buttonSize = 'medium',
  width,
  height,
  fullWidth = false
}: Readonly<ButtonSkeletonProps>) {
  const dimensions = getDimensions(width, height, buttonSize)

  return (
    <div
      aria-hidden='true'
      data-testid='button-skeleton'
      className={styles.button_skeleton}
      style={{
        width: dimensions.width,
        height: dimensions.height,
        minWidth: fullWidth ? '100%' : dimensions.width
      }}
    />
  )
}
