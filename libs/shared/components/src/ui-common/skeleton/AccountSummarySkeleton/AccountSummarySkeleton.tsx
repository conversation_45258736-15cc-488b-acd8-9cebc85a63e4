import 'react-loading-skeleton/dist/skeleton.css'

import Skeleton from 'react-loading-skeleton'

import defaultStyles from './AccountSummarySkeleton.module.scss'

interface AccountSummarySkeletonProps {
  className?: Record<string, string>
  rowCount?: number
  cellsPerRow?: number
}

export function AccountSummarySkeleton({ className, rowCount = 4, cellsPerRow = 2 }: AccountSummarySkeletonProps) {
  const styles = className ?? defaultStyles
  return (
    <div className={styles.container}>
      {/* Skeleton for Total Row */}
      <div className={styles.totalRow}>
        <Skeleton width={100} height={24} className={styles.totalLabel} />
        <Skeleton width={160} height={24} className={styles.totalValue} />
      </div>

      {/* Skeleton for summary rows */}
      <div className={styles.rows}>
        {[...Array(rowCount)].map((_, rowIdx) => (
          <div className={styles.row} key={rowIdx}>
            {[...Array(cellsPerRow)].map((_, cellIdx) => (
              <div className={styles.cell} key={cellIdx}>
                <Skeleton width={120} height={18} className={styles.label} />
                <Skeleton width={100} height={20} className={styles.value} />
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  )
}
