.container {
  border-radius: 6px;
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
}

.header {
  font-size: 2rem;
  font-weight: 600;
  color: #22313f;
  margin-bottom: 24px;
}

.totalRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 4px;
  padding: 10px 0;
  border-bottom: 2px solid #e1e6ea;
}

.totalLabel {
  font-weight: 700;
  font-size: 1.5rem;
  padding: 0 19px;
}

.totalValue {
  font-size: 1.5rem;
  padding: 0 20px;
}

.row {
  display: flex;
  border-bottom: 1px solid #e1e6ea;
  &:last-child {
    border-bottom: none;
  }
}

.cell {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 20px;
}

.label {
  font-size: 1rem;
}

.value {
  font-size: 1rem;
}

@media (max-width: 768px) {
  .totalRow {
    flex-direction: row;
    align-items: center;
    padding: 10px 0;
  }

  .totalLabel {
    font-size: 1.5rem;
    padding: 0;
  }

  .totalValue {
    font-size: 1.5rem;
    padding: 0;
    margin-left: 10px;
  }

  .row {
    flex-direction: column;
    border-bottom: none;

    .cell {
      justify-content: space-between;
      padding: 10px 0;
    }

    .label {
      font-size: 1rem;
    }

    .value {
      font-size: 1rem;
    }
  }
}
