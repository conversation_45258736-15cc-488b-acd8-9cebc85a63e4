import 'react-loading-skeleton/dist/skeleton.css'

import Skeleton from 'react-loading-skeleton'

export interface ChartListSkeletonProps {
  titleWidth?: number
  subtitleWidth?: number
  showChart?: boolean
  chartSections?: number[]
  itemCount?: number
  showDot?: boolean
  showIcon?: boolean
  showBadge?: boolean
  showDivider?: boolean
  showText?: boolean
  showSubtext?: boolean
  showTitle?: boolean
  showSubTitle?: boolean
}

export const ChartListSkeleton = ({
  titleWidth = 180,
  subtitleWidth = 140,
  showChart = true,
  chartSections = [80, 20, 10],
  itemCount = 4,
  showDot = false,
  showIcon = false,
  showBadge = true,
  showDivider = true,
  showText = true,
  showSubtext = false,
  showTitle = false,
  showSubTitle = false
}: ChartListSkeletonProps) => {
  const textHeight = 16
  const subTextHeight = 12
  const gap = '0.75rem'
  const iconHeight = `calc(${textHeight + subTextHeight}px + ${gap})`
  const iconWidth = iconHeight

  return (
    <div style={{ padding: '1rem' }}>
      {/* Title */}
      {showTitle && <Skeleton width={titleWidth} height={24} style={{ marginBottom: '0.25rem' }} />}

      {/* Subtitle */}
      {showSubTitle && subtitleWidth > 0 && (
        <Skeleton width={subtitleWidth} height={16} style={{ marginBottom: showChart ? '1.5rem' : '1.25rem' }} />
      )}

      {/* Bar Chart Skeleton */}
      {showChart && (
        <div
          style={{
            display: 'flex',
            height: '24px',
            borderRadius: '6px',
            overflow: 'hidden',
            marginBottom: '1.5rem'
          }}
        >
          {chartSections.map((width, i) => (
            <div
              key={i}
              style={{
                width: `${width}%`,
                marginRight: i < chartSections.length - 1 ? '4px' : '0'
              }}
            >
              <Skeleton height='100%' />
            </div>
          ))}
        </div>
      )}

      {/* List Items */}
      {[...Array(itemCount)].map((_, index) => (
        <div key={index} style={{ marginBottom: index < itemCount - 1 ? '1.5rem' : 0 }}>
          {index !== 0 && showDivider && (
            <div
              style={{
                width: '100%',
                height: '1px',
                backgroundColor: '#ddd',
                marginBottom: '0.55rem'
              }}
            />
          )}

          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {/* Left section: dot + text */}
            <div style={{ display: 'flex', alignItems: 'center', gap: gap }}>
              {showDot && <Skeleton width={12} height={12} circle />}
              {showIcon && <Skeleton width={iconWidth} height={iconHeight} circle />}
              <div>
                {showText && <Skeleton width={100} height={textHeight} />}
                {showSubtext && <Skeleton width={60} height={subTextHeight} style={{ marginTop: '0.25rem' }} />}
              </div>
            </div>

            {/* Right section: badge */}
            {showBadge && <Skeleton width={60} height={24} style={{ borderRadius: '999px' }} />}
          </div>
        </div>
      ))}
    </div>
  )
}

export default ChartListSkeleton
