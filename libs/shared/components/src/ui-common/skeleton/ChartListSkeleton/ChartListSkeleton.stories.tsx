import { ChartListSkeleton } from './ChartListSkeleton'

export default {
  title: 'Common/Skeletons/ChartListSkeleton',
  component: ChartListSkeleton,
  tags: ['autodocs']
}

export const Default = {
  args: {
    showChart: true,
    chartSections: [80, 20, 10],
    itemCount: 4,
    showDot: true,
    showIcon: true,
    showBadge: true,
    showDivider: true,
    showText: true,
    showSubtext: true,
    showTitle: true,
    showSubTitle: true
  }
}

export const WithoutBarChart = {
  args: {
    showIcon: true,
    showChart: false,
    showSubtext: true,
    itemCount: 3
  }
}

export const WithBarChart = {
  args: {
    showChart: true,
    showDot: true
  }
}
