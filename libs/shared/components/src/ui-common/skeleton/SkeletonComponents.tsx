import buttonStyles from './ButtonSkeleton.module.scss'
import lineStyles from './LineSkeleton.module.scss'
import selectStyles from './SelectSkeleton.module.scss'

export interface ButtonSkeletonProps {
  /** Button size - matches @element button sizes */
  buttonSize?: 'small' | 'medium' | 'large'
  /** Custom width for the skeleton */
  width?: number | string
  /** Custom height for the skeleton */
  height?: number
  /** Whether to use full width of the parent container */
  fullWidth?: boolean
}

const getDimensions = (
  width: ButtonSkeletonProps['width'],
  height: ButtonSkeletonProps['height'],
  buttonSize: ButtonSkeletonProps['buttonSize']
) => {
  switch (buttonSize) {
    case 'small':
      return { width: width ?? '80px', height: height ?? 32 }
    case 'large':
      return { width: width ?? '120px', height: height ?? 48 }
    case 'medium':
    default:
      return { width: width ?? '100px', height: height ?? 40 }
  }
}

export function ButtonSkeleton({
  buttonSize = 'medium',
  width,
  height,
  fullWidth = false
}: Readonly<ButtonSkeletonProps>) {
  const dimensions = getDimensions(width, height, buttonSize)

  return (
    <div
      aria-hidden='true'
      data-testid='button-skeleton'
      className={buttonStyles.button_skeleton}
      style={{
        width: dimensions.width,
        height: dimensions.height,
        minWidth: fullWidth ? '100%' : dimensions.width
      }}
    />
  )
}

interface LineSkeletonProps {
  width?: string
  height?: string
}

export function LineSkeleton({ width = '100%', height = '12px' }: Readonly<LineSkeletonProps>) {
  return (
    <div
      className={lineStyles.skeletonLine}
      style={{ width, height }}
      role='progressbar'
      aria-label='Loading content'
    />
  )
}

interface SelectSkeletonProps {
  height?: string
  width?: string
  className?: string
}

export function SelectSkeleton({ height = '40px', width = '100%', className = '' }: Readonly<SelectSkeletonProps>) {
  const combinedClassName = `${selectStyles.select_skeleton} ${className}`.trim()

  return (
    <div
      className={combinedClassName}
      style={{ height, width }}
      role='progressbar'
      aria-label='Loading select options'
      data-testid='select-skeleton'
    >
      <div className={selectStyles.select_skeleton__circle} />
      <div className={selectStyles.select_skeleton__line} />
    </div>
  )
}

interface RadioSkeletonProps {
  height?: string
  width?: string
  className?: string
  showSubtitle?: boolean
}

export function RadioSkeleton({
  height = '40px',
  width = '100%',
  className = '',
  showSubtitle = false
}: Readonly<RadioSkeletonProps>) {
  const combinedClassName = `${selectStyles.radio_skeleton} ${className}`.trim()

  return (
    <div
      className={combinedClassName}
      style={{ height, width }}
      role='progressbar'
      aria-label='Loading radio options'
      data-testid='radio-skeleton'
    >
      <div className={selectStyles.radio_skeleton__circle} />
      <div className={selectStyles.radio_skeleton__content}>
        <div className={selectStyles.radio_skeleton__line} />
        {showSubtitle && <div className={selectStyles.radio_skeleton__subtitle} />}
      </div>
    </div>
  )
}
