import 'react-loading-skeleton/dist/skeleton.css'

import Skeleton from 'react-loading-skeleton'

import defaultStyles from './WidgetChartSkeleton.module.scss'

export interface WidgetChartSkeletonProps {
  legendCount?: number
  className?: Record<string, string>
}

export const WidgetChartSkeleton = ({ legendCount = 3, className }: WidgetChartSkeletonProps) => {
  const styles = className ?? defaultStyles

  return (
    <div className={styles.chartWrapper}>
      <div className={styles.chartContainer}>
        <Skeleton circle height={180} width={180} />

        <div className={styles.totalOrders}>
          <Skeleton height={24} width={60} />
          <div className={styles.totalOrdersLabel}>
            <Skeleton height={16} width={100} />
          </div>
        </div>
      </div>

      <div className={styles.legendContainer}>
        {[...Array(legendCount)].map((_, index) => (
          <div key={index}>
            <div style={{ width: '100%', height: '1px', backgroundColor: '#ddd', margin: '0.5rem 0' }} />
            <div
              className={styles.legendItem}
              style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
            >
              <span className={styles.legendLabel} style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <Skeleton circle height={12} width={12} />
                <Skeleton width={80} height={16} />
              </span>
              <span className={styles.legendValue}>
                <Skeleton width={30} height={16} />
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default WidgetChartSkeleton
