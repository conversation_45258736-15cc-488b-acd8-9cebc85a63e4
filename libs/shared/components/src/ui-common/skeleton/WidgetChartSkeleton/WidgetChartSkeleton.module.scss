.chartWrapper {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  font-family: 'Segoe UI', sans-serif;
  margin-top: 16px;
  overflow-x: hidden;
  max-width: 100%;
  flex-wrap: nowrap;
}

.chartContainer {
  position: relative;
  width: 250px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  min-width: 220px;
  min-height: 200px;
  margin-left: 0;
  margin-right: 10px;
}

.totalText {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: 600;
  font-size: 14px;
  color: #333;
  text-align: center;
}

.legendContainer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  text-align: left;
  flex-shrink: 0;
  max-width: 160px;
}

.legendItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  font-size: 14px;
  color: #333;
}

.legendLabel {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legendColor {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  display: inline-block;
}

.legendValue {
  font-weight: 500;
  margin-left: 10px;
}

@media (max-width: 768px) {
  .chartWrapper {
    flex-direction: column;
    align-items: center;
    margin-top: 0;
  }

  .chartContainer {
    width: 100%;
    height: 200px;
    margin: 0;
  }

  .legendContainer {
    width: 100%;
    max-width: none;
    margin-top: 12px;
  }

  .legendItem {
    font-size: 12px;
  }

  .totalText {
    font-size: 16px;
  }
}

.totalOrders {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px; // Adjust size as necessary
  font-weight: bold;
  color: #000; // Adjust color as necessary
  text-align: center;
}

.totalOrdersLabel {
  display: block;
  font-size: 14px; // Adjust size as necessary
  color: #666; // Adjust color as necessary
}
