import { render, screen } from '@testing-library/react'

import { ButtonSkeleton, LineSkeleton, RadioSkeleton, SelectSkeleton } from './SkeletonComponents'

describe('SkeletonComponents', () => {
  describe('ButtonSkeleton', () => {
    it('should render successfully', () => {
      render(<ButtonSkeleton />)

      const buttonSkeleton = screen.getByTestId('button-skeleton')
      expect(buttonSkeleton).toBeInTheDocument()
    })

    it('should have proper accessibility attributes', () => {
      render(<ButtonSkeleton />)

      const buttonSkeleton = screen.getByTestId('button-skeleton')
      expect(buttonSkeleton).toHaveAttribute('aria-hidden', 'true')
    })

    it('should apply medium size by default', () => {
      render(<ButtonSkeleton />)

      const buttonSkeleton = screen.getByTestId('button-skeleton')
      expect(buttonSkeleton).toHaveStyle({ width: '100px', height: '40px' })
    })

    it('should apply small size when specified', () => {
      render(<ButtonSkeleton buttonSize='small' />)

      const buttonSkeleton = screen.getByTestId('button-skeleton')
      expect(buttonSkeleton).toHaveStyle({ width: '80px', height: '32px' })
    })

    it('should apply large size when specified', () => {
      render(<ButtonSkeleton buttonSize='large' />)

      const buttonSkeleton = screen.getByTestId('button-skeleton')
      expect(buttonSkeleton).toHaveStyle({ width: '120px', height: '48px' })
    })

    it('should accept custom width and height', () => {
      render(<ButtonSkeleton width='150px' height={50} />)

      const buttonSkeleton = screen.getByTestId('button-skeleton')
      expect(buttonSkeleton).toHaveStyle({ width: '150px', height: '50px' })
    })
  })

  describe('LineSkeleton', () => {
    it('renders with default width and height', () => {
      render(<LineSkeleton />)

      const skeleton = screen.getByRole('progressbar')
      expect(skeleton).toBeInTheDocument()
      expect(skeleton).toHaveStyle({
        width: '100%',
        height: '12px'
      })
    })

    it('renders with custom width', () => {
      render(<LineSkeleton width='200px' />)

      const skeleton = screen.getByRole('progressbar')
      expect(skeleton).toHaveStyle({
        width: '200px',
        height: '12px'
      })
    })

    it('renders with custom height', () => {
      render(<LineSkeleton height='24px' />)

      const skeleton = screen.getByRole('progressbar')
      expect(skeleton).toHaveStyle({
        width: '100%',
        height: '24px'
      })
    })

    it('renders with both custom width and height', () => {
      render(<LineSkeleton width='150px' height='8px' />)

      const skeleton = screen.getByRole('progressbar')
      expect(skeleton).toHaveStyle({
        width: '150px',
        height: '8px'
      })
    })

    it('handles percentage values', () => {
      render(<LineSkeleton width='50%' height='1.5rem' />)

      const skeleton = screen.getByRole('progressbar')
      expect(skeleton).toHaveStyle({
        width: '50%',
        height: '1.5rem'
      })
    })

    it('handles very small dimensions (skinny line)', () => {
      render(<LineSkeleton width='10px' height='1px' />)

      const skeleton = screen.getByRole('progressbar')
      expect(skeleton).toHaveStyle({
        width: '10px',
        height: '1px'
      })
    })

    it('handles very large dimensions (fat line)', () => {
      render(<LineSkeleton width='1000px' height='100px' />)

      const skeleton = screen.getByRole('progressbar')
      expect(skeleton).toHaveStyle({
        width: '1000px',
        height: '100px'
      })
    })

    it('applies the correct CSS class', () => {
      render(<LineSkeleton />)

      const skeleton = screen.getByRole('progressbar')
      expect(skeleton).toHaveClass('skeletonLine')
    })

    it('has proper accessibility attributes', () => {
      render(<LineSkeleton />)

      const skeleton = screen.getByRole('progressbar')
      expect(skeleton).toHaveAttribute('role', 'progressbar')
      expect(skeleton).toHaveAttribute('aria-label', 'Loading content')
    })

    it('handles zero dimensions', () => {
      render(<LineSkeleton width='0px' height='0px' />)

      const skeleton = screen.getByRole('progressbar')
      expect(skeleton).toHaveStyle({
        width: '0px',
        height: '0px'
      })
    })

    it('handles different unit types', () => {
      render(<LineSkeleton width='10em' height='2rem' />)

      const skeleton = screen.getByRole('progressbar')
      expect(skeleton).toHaveStyle({
        width: '10em',
        height: '2rem'
      })
    })
  })

  describe('SelectSkeleton', () => {
    it('should render successfully with default props', () => {
      render(<SelectSkeleton />)

      const selectSkeleton = screen.getByTestId('select-skeleton')
      expect(selectSkeleton).toBeInTheDocument()
    })

    it('should have proper accessibility attributes', () => {
      render(<SelectSkeleton />)

      const selectSkeleton = screen.getByTestId('select-skeleton')
      expect(selectSkeleton).toHaveAttribute('role', 'progressbar')
      expect(selectSkeleton).toHaveAttribute('aria-label', 'Loading select options')
    })

    it('should apply default dimensions', () => {
      render(<SelectSkeleton />)

      const selectSkeleton = screen.getByTestId('select-skeleton')
      expect(selectSkeleton).toHaveStyle({ height: '40px', width: '100%' })
    })

    it('should accept custom height and width', () => {
      render(<SelectSkeleton height='50px' width='200px' />)

      const selectSkeleton = screen.getByTestId('select-skeleton')
      expect(selectSkeleton).toHaveStyle({ height: '50px', width: '200px' })
    })

    it('should apply custom className', () => {
      const customClass = 'custom-skeleton-class'
      render(<SelectSkeleton className={customClass} />)

      const selectSkeleton = screen.getByTestId('select-skeleton')
      expect(selectSkeleton).toHaveClass(customClass)
    })

    it('should render circle and line elements', () => {
      render(<SelectSkeleton />)

      const selectSkeleton = screen.getByTestId('select-skeleton')
      const childDivs = selectSkeleton.querySelectorAll('div')

      // Should have exactly 2 child divs (circle and line)
      expect(childDivs).toHaveLength(2)
    })

    it('should have correct CSS classes', () => {
      render(<SelectSkeleton />)

      const selectSkeleton = screen.getByTestId('select-skeleton')
      expect(selectSkeleton.className).toContain('select_skeleton')
    })
  })

  describe('RadioSkeleton', () => {
    it('should render successfully with default props', () => {
      render(<RadioSkeleton />)

      const radioSkeleton = screen.getByTestId('radio-skeleton')
      expect(radioSkeleton).toBeInTheDocument()
    })

    it('should have proper accessibility attributes', () => {
      render(<RadioSkeleton />)

      const radioSkeleton = screen.getByTestId('radio-skeleton')
      expect(radioSkeleton).toHaveAttribute('role', 'progressbar')
      expect(radioSkeleton).toHaveAttribute('aria-label', 'Loading radio options')
    })

    it('should apply default dimensions', () => {
      render(<RadioSkeleton />)

      const radioSkeleton = screen.getByTestId('radio-skeleton')
      expect(radioSkeleton).toHaveStyle({ height: '40px', width: '100%' })
    })

    it('should accept custom height and width', () => {
      render(<RadioSkeleton height='60px' width='300px' />)

      const radioSkeleton = screen.getByTestId('radio-skeleton')
      expect(radioSkeleton).toHaveStyle({ height: '60px', width: '300px' })
    })

    it('should apply custom className', () => {
      const customClass = 'custom-radio-skeleton'
      render(<RadioSkeleton className={customClass} />)

      const radioSkeleton = screen.getByTestId('radio-skeleton')
      expect(radioSkeleton).toHaveClass(customClass)
    })

    it('should render without subtitle by default', () => {
      render(<RadioSkeleton />)

      const radioSkeleton = screen.getByTestId('radio-skeleton')
      const childDivs = radioSkeleton.querySelectorAll('div')

      // Should have circle, content container, and line (no subtitle)
      expect(childDivs).toHaveLength(3)
    })

    it('should render with subtitle when showSubtitle is true', () => {
      render(<RadioSkeleton showSubtitle={true} />)

      const radioSkeleton = screen.getByTestId('radio-skeleton')
      const childDivs = radioSkeleton.querySelectorAll('div')

      // Should have circle, content container, line, and subtitle
      expect(childDivs).toHaveLength(4)
    })

    it('should have correct CSS classes', () => {
      render(<RadioSkeleton />)

      const radioSkeleton = screen.getByTestId('radio-skeleton')
      expect(radioSkeleton.className).toContain('radio_skeleton')
    })

    it('should handle showSubtitle prop correctly', () => {
      const { rerender } = render(<RadioSkeleton showSubtitle={false} />)

      let radioSkeleton = screen.getByTestId('radio-skeleton')
      let childDivs = radioSkeleton.querySelectorAll('div')
      expect(childDivs).toHaveLength(3) // No subtitle

      rerender(<RadioSkeleton showSubtitle={true} />)

      radioSkeleton = screen.getByTestId('radio-skeleton')
      childDivs = radioSkeleton.querySelectorAll('div')
      expect(childDivs).toHaveLength(4) // With subtitle
    })
  })
})
