/* SelectSkeleton.scss */

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.select_skeleton {
  width: 100%;
  background-color: #e5e7eb; /* <PERSON><PERSON><PERSON>'s bg-gray-200 */
  border-radius: 0.375rem; /* rounded-md */
  display: flex;
  align-items: center;
  padding: 0 0.75rem; /* px-3 */
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  min-height: 40px; /* Ensure minimum height */
  box-sizing: border-box; /* Include padding in height calculation */

  &__circle {
    width: 1.25rem; /* w-5 */
    height: 1.25rem; /* h-5 */
    background-color: #d1d5db; /* bg-gray-300 */
    border-radius: 9999px; /* rounded-full */
    margin-right: 0.75rem; /* mr-3 */
    flex-shrink: 0; /* Prevent shrinking */
  }

  &__line {
    height: 0.75rem; /* h-3 */
    width: 33.3333%; /* w-1/3 */
    background-color: #d1d5db; /* bg-gray-300 */
    border-radius: 0.375rem; /* rounded */
    flex-shrink: 0; /* Prevent shrinking */
  }
}

.radio_skeleton {
  width: 100%;
  background-color: transparent; /* Remove background to look more like radio */
  border-radius: 0.375rem; /* rounded-md */
  display: flex;
  align-items: center;
  padding: 0 0.75rem; /* px-3 */
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  min-height: 40px; /* Ensure minimum height */
  box-sizing: border-box; /* Include padding in height calculation */

  &__circle {
    width: 1.25rem; /* w-5 */
    height: 1.25rem; /* h-5 */
    background-color: #f3f4f6; /* bg-gray-100 */
    border: 2px solid #d1d5db; /* border-gray-300 */
    border-radius: 9999px; /* rounded-full - radio button shape */
    margin-right: 0.75rem; /* mr-3 */
    flex-shrink: 0; /* Prevent shrinking */
    position: relative;

    /* Add inner circle to simulate radio button selection state */
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 0.375rem; /* w-1.5 */
      height: 0.375rem; /* h-1.5 */
      background-color: #d1d5db; /* bg-gray-300 */
      border-radius: 9999px; /* rounded-full */
      opacity: 0.6;
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem; /* gap-1 */
    flex: 1;
  }

  &__line {
    height: 1rem; /* h-4 - main title line */
    width: 60%; /* Width for main title */
    background-color: #d1d5db; /* bg-gray-300 */
    border-radius: 0.25rem; /* rounded-sm */
    flex-shrink: 0; /* Prevent shrinking */
  }

  &__subtitle {
    height: 0.75rem; /* h-3 - smaller subtitle line */
    width: 80%; /* Wider for subtitle */
    background-color: #d1d5db; /* bg-gray-300 */
    border-radius: 0.25rem; /* rounded-sm */
    flex-shrink: 0; /* Prevent shrinking */
  }
}
