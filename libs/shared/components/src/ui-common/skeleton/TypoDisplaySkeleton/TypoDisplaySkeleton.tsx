import 'react-loading-skeleton/dist/skeleton.css'

import Skeleton from 'react-loading-skeleton'

import defaultStyles from './TypoDisplaySkeleton.module.scss'

export interface TypoDisplaySkeletonProps {
  className?: Record<string, string>
}

export const TypoDisplaySkeleton = ({ className }: TypoDisplaySkeletonProps) => {
  const styles = className ?? defaultStyles
  return <Skeleton height={32} width={220} className={styles.title} />
}
