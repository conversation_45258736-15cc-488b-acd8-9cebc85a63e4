import type { Meta, StoryObj } from '@storybook/react'

import { ButtonSkeleton } from './SkeletonComponents'

const meta: Meta<typeof ButtonSkeleton> = {
  title: 'UI Common/Skeleton/ButtonSkeleton',
  component: ButtonSkeleton,
  parameters: {
    layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    buttonSize: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large']
    },
    width: {
      control: { type: 'text' }
    },
    height: {
      control: { type: 'number' }
    }
  }
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {}
}

export const Small: Story = {
  args: {
    buttonSize: 'small'
  }
}

export const Medium: Story = {
  args: {
    buttonSize: 'medium'
  }
}

export const Large: Story = {
  args: {
    buttonSize: 'large'
  }
}

export const CustomSize: Story = {
  args: {
    width: '200px',
    height: 60
  }
}

export const MultipleButtons: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
      <ButtonSkeleton buttonSize='small' />
      <ButtonSkeleton buttonSize='medium' />
      <ButtonSkeleton buttonSize='large' />
    </div>
  )
}
