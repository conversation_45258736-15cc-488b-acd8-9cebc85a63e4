.tableSkeletonWrapper {
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px;
}

.tableRowSkeleton {
  display: grid;
  gap: 1rem;
  border-bottom: 1px solid #ccc;
  padding-bottom: 8px;
  margin-bottom: 12px;
}

.cellSkeleton {
  border-radius: 4px;
}

.paginationSkeleton {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
}

.paginationButtons {
  display: flex;
  gap: 4px;
}

.fullRowSkeleton {
  margin-bottom: 12px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 8px;
}

.widget-table {
  border: 0;
  height: 310px;
  overflow: auto;
}
