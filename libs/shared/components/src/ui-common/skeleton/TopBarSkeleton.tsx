import 'react-loading-skeleton/dist/skeleton.css'

import Skeleton from 'react-loading-skeleton'

import styles from './TopBarSkeleton.module.scss'

interface TopBarSkeletonProps {
  showTitle?: boolean
  searchable?: boolean
  enableCsvDownload?: boolean
}

export const TopBarSkeleton = ({
  showTitle = true,
  searchable = false,
  enableCsvDownload = false
}: TopBarSkeletonProps) => {
  return (
    <div className={styles.topbar}>
      <div className={styles.title}>
        {showTitle ? <Skeleton height={36} width={240} /> : <div style={{ height: 36 }} />}
      </div>

      <div className={styles.actions}>
        {searchable && <Skeleton height={36} width={190} className={styles.search} />}
        {enableCsvDownload && <Skeleton height={36} width={36} circle className={styles.icon} />}
      </div>
    </div>
  )
}
