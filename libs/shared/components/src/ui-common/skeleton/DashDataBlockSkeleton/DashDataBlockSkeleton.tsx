import 'react-loading-skeleton/dist/skeleton.css'

import Skeleton from 'react-loading-skeleton'

import styles from './DashDataBlockSkeleton.module.scss'

type DashDataBlockSkeletonProps = {
  showLabel?: boolean
  blocks?: number
  itemsPerBlock?: number
  trailingBlockType?: string
  leadingBlockType?: string
}

export const DashDataBlockSkeleton = ({
  showLabel = true,
  blocks = 3,
  itemsPerBlock = 4,
  trailingBlockType,
  leadingBlockType
}: DashDataBlockSkeletonProps) => {
  return (
    <div className={styles.container}>
      {/* Optional block label */}
      {showLabel && (
        <div style={{ marginBottom: '1rem' }}>
          <Skeleton height={32} width={180} />
        </div>
      )}

      {/* Grid of blocks */}
      <div className={styles.gridContainer}>
        {[...Array(blocks)].map((_, blockIndex) => (
          <div key={blockIndex} className={styles.gridItem}>
            <div className={styles.listContainer}>
              {[...Array(itemsPerBlock)].map((_, itemIndex) => (
                <div key={itemIndex}>
                  <div
                    className={styles.listItem}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '0.75rem'
                    }}
                  >
                    {leadingBlockType === 'icon' && (
                      <Skeleton circle height={32} width={32} style={{ marginRight: '0.75rem' }} />
                    )}

                    <div style={{ flex: 1 }}>
                      <Skeleton height={16} width='60%' />
                      <Skeleton height={12} width='10%' style={{ marginTop: '4px' }} />
                    </div>

                    {trailingBlockType === 'badge' && (
                      <Skeleton height={20} width={40} borderRadius={12} style={{ marginLeft: '0.75rem' }} />
                    )}
                  </div>

                  {/* Divider after each item except the last */}
                  {itemIndex < itemsPerBlock - 1 && <div className={styles.divider} />}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
