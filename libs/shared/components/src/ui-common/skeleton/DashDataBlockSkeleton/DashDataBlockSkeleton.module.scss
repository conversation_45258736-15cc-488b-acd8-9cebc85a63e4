.container {
  position: relative;
  overflow-x: auto;
  white-space: nowrap;
  padding: 12px;
}

.listContainer {
  display: flex;
  flex-direction: column;
}

.contentContainer {
  display: inline-flex;
  align-items: center;
}

.listItem {
  margin-right: 50px;
  white-space: nowrap;
  flex-shrink: 0;
}

.gridContainer {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.gridItem {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.divider {
  height: 1px;
  background-color: #ccc;
  width: 90%;
  margin: 0.5rem 0;
  background-clip: content-box;
}
