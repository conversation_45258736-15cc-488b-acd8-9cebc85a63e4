import 'react-loading-skeleton/dist/skeleton.css'

import Skeleton from 'react-loading-skeleton'

import defaultStyles from './TypoCaptionSkeleton.module.scss'

export interface TypoCaptionSkeletonProps {
  className?: Record<string, string>
}

export const TypoCaptionSkeleton = ({ className }: TypoCaptionSkeletonProps) => {
  const styles = className ?? defaultStyles
  return <Skeleton width={160} height={20} className={styles.caption} />
}
