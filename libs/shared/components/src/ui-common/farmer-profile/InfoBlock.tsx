import { TypoBody, TypoCaption, TypoDisplay } from '@element/react-typography'
import { useIsMobile, useScreenRes } from '@gc/hooks'
import { getTypoDisplayLevel } from '@gc/utils'
import { useCallback } from 'react'

import { Badge, BadgeProps } from '../badge/Badge'
import styles from './InfoBlock.module.scss'

export interface InfoBlockProps {
  badgeProps?: BadgeProps
  mainText: string
  secondaryTexts?: string[]
}

export function InfoBlock({ mainText, secondaryTexts = [], badgeProps }: Readonly<InfoBlockProps>) {
  const res = useScreenRes()
  const typoDisplayLevel = getTypoDisplayLevel(res)
  const isMobile = useIsMobile()

  const mobileContent = (secondaryText: string) => {
    return isMobile ? (
      <TypoBody level={2}>{secondaryText && <TypoCaption> {secondaryText}</TypoCaption>}</TypoBody>
    ) : null
  }

  const desktopContent = (secondaryText: string) => {
    return <TypoBody level={2}>{secondaryText}</TypoBody>
  }

  const renderSecondarytext = useCallback(
    (secondaryText: string | undefined) => {
      if (!secondaryText) return null
      if (isMobile) {
        return mobileContent(secondaryText)
      }

      return desktopContent(secondaryText)
    },
    [isMobile, mobileContent, desktopContent]
  )

  return (
    <div className={styles.block}>
      {badgeProps?.labelText && <Badge {...badgeProps} />}
      <TypoDisplay level={typoDisplayLevel}>{mainText}</TypoDisplay>
      {secondaryTexts.map((text) => renderSecondarytext(text))}
    </div>
  )
}

export default InfoBlock
