import { Grid, GridCol, GridRow } from '@element/react-grid'
import { List } from '@element/react-list'
import React from 'react'

import styles from './DashDataBlock.module.scss'

interface DashDataBlockProps {
  data: Array<
    [
      {
        primaryText: string
        secondaryText?: string | number
        trailingBlock?: React.ReactNode | string
        leadingBlock?: React.ReactNode | string
      }
    ]
  >
  trailingBlockType?: string
  leadingBlockType?: string
}

export const DashDataBlock = ({ data, trailingBlockType, leadingBlockType }: DashDataBlockProps) => {
  return (
    <Grid columnGap={30}>
      <GridRow>
        {data.map((item, index) => (
          <GridCol desktopCol={3} key={index}>
            <List
              style={{ width: '100%' }}
              showDivider
              nonInteractive
              dense
              dividerVariant='padded'
              className={styles.listContainer}
              trailingBlockType={trailingBlockType}
              leadingBlockType={leadingBlockType}
              items={item}
            />
          </GridCol>
        ))}
      </GridRow>
    </Grid>
  )
}

export default DashDataBlock
