/* eslint-disable no-case-declarations */
import { Button } from '@element/react-button'
import { Group } from '@element/react-group'
import { Icon } from '@element/react-icon'
import { IconButton } from '@element/react-icon-button'

import MenuButton from '../button/MenuButton'

type ActionBlockProps = {
  config: Array<BaseProps & (RegularButtonProps | MenuButtonProps | IconButtonProps)>
  direction?: 'horizontal' | 'vertical'
}

type BaseProps = {
  disabled?: boolean
  hide?: boolean
  data: object
  type?: 'regular' | 'menu' | 'icon'
  onClick?: (a?: object) => void
  label?: string
}

type RegularButtonProps = {
  variant?: string
}

type MenuButtonProps = {
  listItems: { value: string; label: string; onClick: (a?: object) => void }[]
  leadingIcon?: string
  trailingIcon?: string
  variant?: string
}

type IconButtonProps = {
  icon: string
}

type ButtonComponentProps = BaseProps & (RegularButtonProps | MenuButtonProps | IconButtonProps)

function DesktopButton(props: ButtonComponentProps) {
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  const { data, type = 'regular', disabled, onClick = () => {}, label = '', hide } = props

  const renderButton = () => {
    if (hide) return null
    switch (type) {
      case 'regular':
        const { variant: regularVariant = 'filled' } = props as RegularButtonProps
        return (
          <Button disabled={disabled} variant={regularVariant} onClick={() => onClick(data)}>
            {label}
          </Button>
        )
      case 'menu':
        const { listItems, leadingIcon = '', trailingIcon = '', variant: menuVariant } = props as MenuButtonProps
        return (
          <MenuButton
            buttonLabel={label}
            data={data}
            listItems={listItems}
            leadingIcon={leadingIcon}
            trailingIcon={trailingIcon}
            disabled={disabled}
            variant={menuVariant}
          />
        )
      case 'icon':
        const { icon } = props as IconButtonProps
        return (
          <IconButton disabled={disabled} onClick={() => onClick(data)}>
            <Icon icon={icon} />
          </IconButton>
        )
      default:
        return null
    }
  }

  return renderButton()
}

export function ActionBlock(props: ActionBlockProps) {
  const { config, direction = 'horizontal' } = props

  return (
    <Group gap='dense' direction={direction} secondaryAlign='center'>
      {config.map((item, index) => (
        <DesktopButton key={index} {...item} />
      ))}
    </Group>
  )
}

export default ActionBlock
