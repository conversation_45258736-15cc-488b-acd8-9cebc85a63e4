import { Tab, TabBar } from '@element/react-tabs'
import React, { useMemo, useState } from 'react'

import styles from './TabBlock.module.scss'

interface TabConfig {
  label: string
  component: React.ReactNode
}

interface TabsProps {
  tabs: TabConfig[]
  defaultTabIndex?: number
  onTabChange?: (index: number) => void
}

export function TabBlock({ tabs, defaultTabIndex = 0, onTabChange }: Readonly<TabsProps>) {
  const [currentTab, setCurrentTab] = useState(defaultTabIndex)

  const handleTabActivated = (index: number) => {
    setCurrentTab(index)
    if (onTabChange) onTabChange(index)
  }

  const TabLabels = useMemo(() => {
    return tabs.map((tabConfig, index) => (
      <Tab key={index} clustered={false} indicatorSize='full' indicatorTransition='slide'>
        {tabConfig.label.toUpperCase()}
      </Tab>
    ))
  }, [tabs])

  const TabContent = useMemo(() => {
    return tabs[currentTab]?.component
  }, [currentTab, tabs])

  return (
    <div>
      <TabBar
        elevated={false}
        variant='surface'
        activeTabIndex={currentTab}
        stacked={false}
        onTabActivated={handleTabActivated}
      >
        {TabLabels}
      </TabBar>

      <div className={styles.container}>{TabContent}</div>
    </div>
  )
}

export default TabBlock
