import { Icon<PERSON>utton } from '@element/react-icon-button'
import { Textfield } from '@element/react-textfield'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import Loading from '../../loading/Loading'
import styles from './FileUploadField.module.scss'

// File validation constants
const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB in bytes
const ALLOWED_EXTENSIONS = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']

// MIME type mapping for enhanced validation
const MIME_TYPE_MAP: Record<string, string[]> = {
  '.pdf': ['application/pdf'],
  '.jpg': ['image/jpeg'],
  '.jpeg': ['image/jpeg'],
  '.png': ['image/png'],
  '.doc': ['application/msword'],
  '.docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document']
}

interface FileValidationResult {
  isValid: boolean
  errorMessage?: string
}

// Utility function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export interface FileUploadFieldProps {
  /** The name of the form field */
  name?: string
  /** The selected file */
  value?: File | null
  /** Callback fired when file selection changes */
  onChange?: (file?: File) => void
  /** Callback fired when field loses focus */
  onBlur?: () => void
  /** External error message to display */
  error?: string
  /** Placeholder text for the input field */
  placeholder?: string
  /** Helper text to display below the field */
  helperText?: string
  /** Whether the field is required */
  required?: boolean
  /** Whether the field is disabled */
  disabled?: boolean
  /** Additional CSS class name */
  className?: string
  /** ARIA label for accessibility */
  ariaLabel?: string
  /** ARIA description for additional context */
  ariaDescription?: string
  /** Maximum file size in bytes (default: 5MB) */
  maxFileSize?: number
  /** Allowed file extensions (default: .pdf, .jpg, .jpeg, .png, .doc, .docx) */
  allowedExtensions?: string[]
  /** Test ID for the file upload field */
  dataTestId?: string
  /** Callback fired when file upload status changes */
  onStatusChange?: (status: 'idle' | 'loading' | 'success' | 'error', error?: string) => void
}

export function FileUploadField({
  name,
  value,
  onChange,
  onBlur,
  error,
  ariaDescription,
  className = '',
  required = false,
  disabled = false,
  helperText = 'Required',
  ariaLabel = 'Upload file',
  placeholder = 'Select file',
  maxFileSize = MAX_FILE_SIZE,
  allowedExtensions = ALLOWED_EXTENSIONS,
  onStatusChange,
  dataTestId
}: FileUploadFieldProps) {
  // Enhanced state for file upload status with validation
  const [fileUploadStatus, setFileUploadStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [validationError, setValidationError] = useState<string>('')
  const fileInputRef = useRef<HTMLInputElement>(null)
  const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const focusTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Status change callback effect
  useEffect(() => {
    onStatusChange?.(fileUploadStatus, validationError || undefined)
  }, [fileUploadStatus, validationError, onStatusChange])

  // Enhanced validation method for file upload
  const validateFile = useCallback(
    (file: File): FileValidationResult => {
      // Check for empty file
      if (file.size === 0) {
        return {
          isValid: false,
          errorMessage: 'File is empty. Please select a valid file.'
        }
      }

      // Check file extension
      const fileName = file.name.toLowerCase()
      const fileExtension = fileName.substring(fileName.lastIndexOf('.'))

      if (!allowedExtensions.includes(fileExtension)) {
        return {
          isValid: false,
          errorMessage: `Invalid file type. Allowed formats: ${allowedExtensions.join(', ')}`
        }
      }

      // Enhanced MIME type validation (optional - only validate if MIME type is available)
      const expectedMimeTypes = MIME_TYPE_MAP[fileExtension]
      if (expectedMimeTypes && file.type && !expectedMimeTypes.includes(file.type)) {
        return {
          isValid: false,
          errorMessage: `File type mismatch. Expected ${expectedMimeTypes.join(' or ')} but got ${file.type}.`
        }
      }

      // Check file size with better error message
      if (file.size > maxFileSize) {
        return {
          isValid: false,
          errorMessage: `File size (${formatFileSize(file.size)}) exceeds the ${formatFileSize(maxFileSize)} limit.`
        }
      }

      return { isValid: true }
    },
    [allowedExtensions, maxFileSize]
  )

  // Helper function to reset status if loading
  const resetStatusIfLoading = useCallback(() => {
    setFileUploadStatus((prevStatus) => (prevStatus === 'loading' ? 'idle' : prevStatus))
  }, [])

  // Reset file upload status when the file is removed and cleanup timeouts
  // But preserve error state to show validation feedback
  useEffect(() => {
    if (!value) {
      setFileUploadStatus((prevStatus) => {
        // Only reset to idle if not in error state - preserve error state for user feedback
        return prevStatus === 'error' ? prevStatus : 'idle'
      })
      // Don't clear validation error when field is cleared due to validation failure
      // The error should persist to show user what went wrong
    }
  }, [value])

  // Cleanup timeouts on unmount
  useEffect(() => {
    const validationTimeout = validationTimeoutRef.current
    const focusTimeout = focusTimeoutRef.current

    return () => {
      if (validationTimeout) {
        clearTimeout(validationTimeout)
      }
      if (focusTimeout) {
        clearTimeout(focusTimeout)
      }
    }
  }, [])

  // Optimized file upload handler with better timeout management
  const handleFileUpload = useCallback(() => {
    // Clear the file input value to ensure onChange fires even with same filename
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }

    const onFocus = () => {
      // Remove the event listener immediately
      window.removeEventListener('focus', onFocus)

      // Clear any existing focus timeout
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current)
      }

      // Small delay to allow file change event to fire first if a file was selected
      focusTimeoutRef.current = setTimeout(resetStatusIfLoading, 500) // Increased timeout to allow validation to complete
    }

    const detectCancel = () => {
      // Small delay to ensure the dialog has actually opened
      setTimeout(() => {
        window.addEventListener('focus', onFocus)
      }, 100)
    }

    detectCancel()
    fileInputRef.current?.click()
  }, [resetStatusIfLoading])

  // Handle file selection with enhanced validation and debouncing
  const handleFileChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      // Clear any existing validation timeout
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current)
      }

      setFileUploadStatus('loading')
      setValidationError('')

      const files = event.target.files
      if (files && files.length > 0) {
        const selectedFile = files[0]

        // Debounced validation with proper cleanup
        validationTimeoutRef.current = setTimeout(() => {
          try {
            const validationResult = validateFile(selectedFile)

            if (validationResult.isValid) {
              onChange?.(selectedFile)
              setFileUploadStatus('success')
            } else {
              setFileUploadStatus('error')
              setValidationError(validationResult.errorMessage || 'File validation failed')
              // Clear the file input and form value
              if (fileInputRef.current) {
                fileInputRef.current.value = ''
              }
              onChange?.(undefined)
            }
          } catch (error) {
            // Handle unexpected validation errors gracefully
            // This should never happen, but we handle it gracefully
            setFileUploadStatus('error')
            setValidationError('An error occurred while validating the file. Please try again.')
            if (fileInputRef.current) {
              fileInputRef.current.value = ''
            }
            onChange?.(undefined)
            console.error('File validation error:', error)
          }
        }, 300) // Reduced timeout for better UX
      } else {
        // This handles the case where the file input is cleared
        setFileUploadStatus('idle')
        setValidationError('')
        onChange?.(undefined)
      }
    },
    [onChange, validateFile]
  )

  const TrailingIcon = useMemo(() => {
    if (fileUploadStatus === 'loading') {
      return (
        <span>
          <Loading type='circular' size='sm' />
        </span>
      )
    } else if (fileUploadStatus === 'success') {
      return <IconButton icon='check_circle' onClick={handleFileUpload} style={{ color: 'green' }} />
    } else if (fileUploadStatus === 'error') {
      return <IconButton icon='close' onClick={handleFileUpload} style={{ color: 'red' }} />
    }

    return <IconButton icon='file_upload' onClick={handleFileUpload} />
  }, [fileUploadStatus, handleFileUpload])

  // Generate unique IDs for accessibility
  const inputId = name ? `${name}-file-input` : 'file-input'
  const descriptionId = ariaDescription ? `${inputId}-description` : undefined

  // Determine the display value and error state
  const displayValue = value?.name || ''
  const hasError = !!validationError || !!error
  const errorMessage = validationError || error || helperText

  return (
    <>
      <input
        id={inputId}
        tabIndex={-1}
        type='file'
        ref={fileInputRef}
        onChange={handleFileChange}
        onBlur={() => {
          handleFileUpload()
          onBlur?.()
        }}
        style={{ display: 'none' }}
        accept={allowedExtensions.join(',')}
        aria-label={ariaLabel}
        aria-describedby={descriptionId}
        aria-invalid={hasError}
        disabled={disabled}
      />

      {ariaDescription && (
        <div id={descriptionId} className='sr-only'>
          {ariaDescription}
        </div>
      )}

      <div className={`${styles.fileUploadField} ${className}`}>
        <Textfield
          name={name}
          fullWidth
          readOnly
          required={required}
          disabled={disabled}
          helperTextPersistent
          variant='outlined'
          helperText={errorMessage}
          helperTextValidation={hasError}
          placeholder={placeholder}
          data-testid={dataTestId || (name ? `${name}-input` : 'file-input')}
          value={displayValue}
          valid={!hasError && fileUploadStatus !== 'error'}
          trailingIcon={TrailingIcon}
          aria-describedby={`${inputId}-helper-text`}
          role='button'
          aria-label={`${ariaLabel}. ${displayValue ? 'Selected file: ' + displayValue : 'No file selected'}`}
        />
      </div>
    </>
  )
}
