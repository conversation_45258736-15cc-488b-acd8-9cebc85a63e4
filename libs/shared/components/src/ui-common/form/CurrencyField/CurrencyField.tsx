/* eslint-disable @nx/enforce-module-boundaries */
import { Textfield } from '@element/react-textfield'
import { useLocale } from '@gc/hooks'
import { Locale } from '@gc/types'
import { getCurrencyFormat } from '@gc/utils'
import { isUndefined } from 'es-toolkit'
import { useCallback, useMemo, useState } from 'react'

// Helper function to format currency
const formatCurrency = (value: string, locale: Locale): string => {
  // Remove any non-numeric characters except decimal point
  const numericValue = value.replace(/[^0-9.]/g, '')

  // Handle empty or invalid input
  if (!numericValue || numericValue === '.') return ''

  // Parse the numeric value
  const parsed = parseFloat(numericValue)
  if (isNaN(parsed)) return ''

  return getCurrencyFormat('USD', parsed, locale)
}

export interface CurrencyFieldProps {
  name: string
  value: string
  onChange: (value: string) => void
  onBlur?: (value: string) => void
  error?: string
  label?: string
  placeholder?: string
  helperText?: string
  required?: boolean
  dataTestId?: string
}
export function CurrencyField({
  name,
  value,
  onChange,
  onBlur,
  error,
  label,
  placeholder,
  helperText,
  required = false,
  dataTestId = 'amount'
}: Readonly<CurrencyFieldProps>) {
  const locale = useLocale()

  // Local state for display values and focus state
  const [displayAmount, setDisplayAmount] = useState('')
  const [isAmountFocused, setIsAmountFocused] = useState(false)

  const handleAmountFocus = useCallback(() => {
    setIsAmountFocused(true)

    // Convert formatted currency back to plain number for editing
    const currentValue = value || ''
    setDisplayAmount(currentValue)
  }, [value])

  const handleAmountChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = event.target.value
      // When focused, allow only numeric input with decimal
      const numericValue = inputValue.replace(/[^0-9.]/g, '')
      setDisplayAmount(numericValue)
      onChange(numericValue)
    },
    [onChange]
  )

  // Special handler for amount field with currency formatting
  const handleAmountBlur = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setIsAmountFocused(false)
      const inputValue = event.target.value
      const numericValue = inputValue.replace(/[^0-9.]/g, '')

      if (numericValue) {
        const formattedValue = formatCurrency(numericValue, locale)
        setDisplayAmount(formattedValue)
        onChange(numericValue)
      } else {
        setDisplayAmount('')
        onChange('')
      }

      // Call optional onBlur callback
      if (onBlur) {
        onBlur(numericValue)
      }
    },
    [locale, onChange, onBlur]
  )

  const amountValue = useMemo(() => {
    if (isAmountFocused) {
      return displayAmount
    }
    if (displayAmount) {
      return displayAmount
    }
    if (value) {
      return formatCurrency(value, locale)
    }
    return ''
  }, [isAmountFocused, displayAmount, value, locale])

  const helperTextValue = useMemo(() => {
    if (error) {
      return error
    }

    return helperText
  }, [error, helperText])

  return (
    <Textfield
      name={name}
      fullWidth
      helperTextPersistent
      required={required}
      label={label}
      value={amountValue}
      variant='outlined'
      data-testid={dataTestId}
      placeholder={placeholder}
      helperText={helperTextValue}
      onChange={handleAmountChange}
      onFocus={handleAmountFocus}
      onBlur={handleAmountBlur}
      valid={isUndefined(error)}
    />
  )
}
