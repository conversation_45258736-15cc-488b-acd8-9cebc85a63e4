import { Button, ButtonProps } from '@element/react-button'
import { Group } from '@element/react-group'
import { TypoBody, TypoCaption, TypoDisplay } from '@element/react-typography'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { useIsMobile, useIsSmallMobile, useScreenRes } from '@gc/hooks'
import { getTypoDisplayLevel, mergeClassNames } from '@gc/utils'
import { useCallback, useMemo } from 'react'
import MediaQuery from 'react-responsive'

import { Badge, BadgeProps } from '../badge/Badge'
import MenuButton, { MenuButtonProps } from '../button/MenuButton'
import styles from './Header.module.scss'

interface HeaderTitleProps {
  title: string
  secText1?: string
  secText2?: string
  typoDisplayLevel: number
  overlineBadgeProps?: BadgeProps
  inEditMode?: boolean
  trailingIcon?: React.ReactNode
}

const HeaderTitle: React.FC<HeaderTitleProps> = ({
  overlineBadgeProps,
  title,
  typoDisplayLevel,
  secText1,
  secText2,
  inEditMode,
  trailingIcon
}) => {
  const isMobile = useIsMobile()

  const getCaption = useCallback(() => {
    return (secText1 ?? '') + (secText1 && secText2 ? '  •  ' : '') + (secText2 ?? '')
  }, [secText1, secText2])

  const mobileHeader = useMemo(() => {
    return isMobile ? (
      <>
        <TypoBody level={2}>{secText1 && <TypoCaption> {secText1}</TypoCaption>}</TypoBody>
        <TypoBody level={2}>{secText2 && <TypoCaption>{secText2}</TypoCaption>}</TypoBody>
      </>
    ) : null
  }, [isMobile, secText1, secText2])

  const desktopHeader = useMemo(() => {
    return !inEditMode && <TypoBody level={2}>{getCaption()}</TypoBody>
  }, [getCaption, inEditMode])

  const displayHeader = useCallback(() => {
    if (isMobile) {
      return mobileHeader
    }

    return desktopHeader
  }, [isMobile, mobileHeader, desktopHeader])

  return trailingIcon ? (
    <div className={styles.header_main_row}>
      <div className={styles.main_leading}>
        {overlineBadgeProps?.labelText && <Badge {...overlineBadgeProps} />}
        <TypoDisplay level={typoDisplayLevel}>{title}</TypoDisplay>
        {displayHeader()}
      </div>
      <div className={styles.main_trailing}>{trailingIcon}</div>
    </div>
  ) : (
    <div className={styles.header_main}>
      {overlineBadgeProps?.labelText && <Badge {...overlineBadgeProps} />}
      <TypoDisplay level={typoDisplayLevel}>{title}</TypoDisplay>
      {displayHeader()}
    </div>
  )
}

export interface HeaderProps {
  overlineBadgeProps?: BadgeProps
  title: string
  secText1?: string
  secText2?: string
  buttonProps?: ButtonProps[]
  inEditMode?: boolean
  moreActions?: MenuButtonProps & { isPrimary?: boolean }
  trailingIcon?: React.ReactNode
  trailingSlot?: React.ReactNode
  classNames?: {
    title?: string
    container?: string
    buttonGroup?: string
    desktopButtonGroup?: string
  }
}

export function Header({
  secText1,
  secText2,
  moreActions,
  overlineBadgeProps,
  buttonProps,
  title,
  inEditMode,
  trailingIcon,
  trailingSlot,
  classNames
}: Readonly<HeaderProps>) {
  const res = useScreenRes()
  const isSmallMobile = useIsSmallMobile()
  const typoDisplayLevel = getTypoDisplayLevel(res)

  const containerClassNames = mergeClassNames(styles.container, classNames?.container)
  const buttonGroupClassNames = mergeClassNames(styles.buttonGroup, classNames?.buttonGroup)
  const desktopButtonGroupClassNames = mergeClassNames(styles.desktopButtonGroup, classNames?.desktopButtonGroup)

  const buttons = useMemo(
    () =>
      buttonProps?.map((buttonProps) => (
        <Button {...buttonProps} key={buttonProps.label} fullWidth={isSmallMobile} type='button' />
      )),
    [buttonProps, isSmallMobile]
  )

  const MobileBody = useMemo(() => {
    if (!buttons?.length) return null

    if (isSmallMobile) {
      return (
        <Group className={buttonGroupClassNames} gap='dense' direction='vertical'>
          {buttons}
        </Group>
      )
    }

    return (
      <Group className={buttonGroupClassNames} gap='dense' direction='horizontal'>
        {buttons}
      </Group>
    )
  }, [isSmallMobile, buttons, buttonGroupClassNames])

  const HeaderBody = useMemo(() => {
    const displayBody = !inEditMode || trailingSlot
    if (!displayBody) return null

    return (
      <div className={desktopButtonGroupClassNames}>
        {!inEditMode && (
          <Group gap='dense' direction='horizontal'>
            {moreActions && !moreActions?.isPrimary && <MenuButton {...moreActions} />}
            {buttons}
            {moreActions?.isPrimary && <MenuButton variant='filled' {...moreActions} />}
          </Group>
        )}

        {!!trailingSlot && <div className={desktopButtonGroupClassNames}>{trailingSlot}</div>}
      </div>
    )
  }, [inEditMode, trailingSlot, moreActions, buttons, desktopButtonGroupClassNames])

  return (
    <div className={containerClassNames}>
      <MediaQuery maxWidth={IS_MOBILE}>
        <HeaderTitle
          title={title}
          secText1={secText1}
          secText2={secText2}
          typoDisplayLevel={typoDisplayLevel}
          overlineBadgeProps={overlineBadgeProps}
          trailingIcon={trailingIcon}
        />
        {MobileBody}
      </MediaQuery>

      <MediaQuery minWidth={IS_DESKTOP}>
        <HeaderTitle
          title={title}
          secText1={secText1}
          secText2={secText2}
          typoDisplayLevel={typoDisplayLevel}
          overlineBadgeProps={overlineBadgeProps}
        />
        {HeaderBody}

        {trailingIcon}
      </MediaQuery>
    </div>
  )
}

export default Header
