/* eslint-disable @nx/enforce-module-boundaries */
import { UniqueIdentifier } from '@dnd-kit/core'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Checkbox } from '@element/react-checkbox'
import { IconButton } from '@element/react-icon-button'
import { space } from '@gc/constants'
import { Filter, ReportFilterOption, SelectedFilter } from '@gc/types'
import { noop } from 'es-toolkit'
import { memo, useCallback, useMemo } from 'react'

import FilterChip from '../filter/FilterChip'
import { ListItem } from './ListItem'
import styles from './SortableListItem.module.scss'

export interface SortableListItemProps {
  id: UniqueIdentifier
  name: string
  checked: boolean
  onToggle: (id: UniqueIdentifier) => void
  disableDrag?: boolean
  isFilterable?: boolean
  filterOptions?: ReportFilterOption[]
  onFilterUpdate?: (fieldId: string, filterSelections: SelectedFilter) => void
  appliedFilters?: SelectedFilter
  filterId?: string
}

export const SortableListItem = memo(function SortableListItem({
  id,
  onToggle,
  name,
  checked,
  filterOptions,
  disableDrag = false,
  isFilterable = false,
  onFilterUpdate,
  appliedFilters,
  filterId
}: Readonly<SortableListItemProps>) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id })

  // Memoize the event handler to prevent recreation on each render
  const handleToggle = useCallback(() => {
    onToggle(id)
  }, [onToggle, id])

  const activeListeners = useMemo(() => {
    if (disableDrag) {
      return {}
    }
    return listeners
  }, [disableDrag, listeners])

  // Memoize the blocks to prevent recreation on each render
  const leadingBlock = useMemo(
    () => <Checkbox id={`checkbox-${id}`} label={space} themeColor='primary' checked={checked} />,
    [checked, id]
  )

  const availableFilters = useMemo(() => {
    const filtersMap = new Map<string, Filter>()

    if (isFilterable && filterOptions && filterId) {
      filterOptions.forEach((option) => {
        if (!filtersMap.has(filterId)) {
          filtersMap.set(filterId, {
            title: name,
            options: [],
            category: filterId,
            selectedOptions: appliedFilters?.[filterId] || []
          } as Filter)
        }
        filtersMap.get(filterId)?.options.push({
          label: option.displayValue,
          value: option.optionId
        })
      })
    }

    return Array.from(filtersMap.values())
  }, [isFilterable, filterOptions, filterId, name, appliedFilters])

  const trailingBlock = useMemo(
    () => (
      <div className={styles.filter_chip_container}>
        {availableFilters.length > 0 && (
          <FilterChip
            chipLabel={'Add Filter'}
            menuTitle={name}
            isAllFilters={false}
            filterList={availableFilters}
            trailingIcon='arrow_drop_down'
            applyFilters={onFilterUpdate ? (filters) => onFilterUpdate(id.toString(), filters) : noop}
          />
        )}
        {!disableDrag && <IconButton {...activeListeners} icon='drag_indicator' className='h-5 w-5 cursor-grab' />}
      </div>
    ),
    [activeListeners, availableFilters, disableDrag, id, name, onFilterUpdate]
  )

  // Memoize the style object to prevent unnecessary recalculations
  const style = useMemo(
    () => ({
      transition,
      transform: CSS.Transform.toString(transform)
    }),
    [transition, transform]
  )

  return (
    <div ref={setNodeRef} style={style} id={`list-item-${id}`}>
      <ListItem
        {...attributes}
        noPadding
        showDivider
        className={styles.sortable_list_item}
        disableHighlight={true}
        trailingBlockType='icon'
        leadingBlockType='checkbox'
        onClick={handleToggle}
        trailingBlock={trailingBlock}
        leadingBlock={leadingBlock}
        isCustomTrailingBlock={true}
      >
        {name}
      </ListItem>
    </div>
  )
})
