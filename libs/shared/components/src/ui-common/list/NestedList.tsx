/* eslint-disable @nx/enforce-module-boundaries */
import { Card, CardContent } from '@element/react-card'
import { useAppSession } from '@gc/hooks'
import { Filter, FilterOption, SearchFn, SelectedFilter, UnknownObject } from '@gc/types'
import { getFilteredData, getFilterList, searchDataPartial } from '@gc/utils'
import { get, isEmpty, map, uniqBy } from 'es-toolkit/compat'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import FilterBar, { FilterBarProps } from '../filter/FilterBar'
import List, { ItemProps, ListProps } from './List'
import styles from './NestedList.module.scss'

// Base search types
type NestedSearchSubItemsProps<T extends UnknownObject> = {
  subItemsKeys?: Array<keyof T>
  searchSubItemsKeys?: Array<string>
  customSearchFn?: SearchFn<T>
  uniqAccessorKey: string
}

type NestedSearchProps<T extends UnknownObject> = {
  searchTerm?: string
  searchKeys?: Array<string>
  customSearchFn?: SearchFn<T>
  subItemsProps?: NestedSearchSubItemsProps<T>
}

// Core list props
type BaseNestedListProps<T extends UnknownObject, P extends UnknownObject = T> = {
  data?: T[]
  hashKey?: string
  fasteStoreKey?: string
  filterProps?: {
    filters: FilterOption[]
    additionalFilters?: FilterOption[]
  }
  listKey: string
  listProps: Omit<ListProps<P>, 'data' | 'items'> & {
    getHeader?: (item: T) => React.ReactNode
    getStyledListItems?: (data: T) => ItemProps<P>[]
  }
  noMatchingDataContingency?: React.ReactNode
  searchProps?: NestedSearchProps<T>
}

// Filter bar variants
export type NestedListProps<T extends UnknownObject, P extends UnknownObject = T> = BaseNestedListProps<T, P> &
  (
    | { filterBar: React.ReactNode; filterBarProps?: never }
    | { filterBar?: never; filterBarProps?: Omit<FilterBarProps, 'filterList' | 'applyFilters'> }
  )

export function NestedList<T extends UnknownObject, P extends UnknownObject = T>({
  data = [],
  fasteStoreKey,
  filterBar,
  filterBarProps,
  filterProps,
  listKey,
  listProps,
  noMatchingDataContingency,
  searchProps,
  hashKey
}: NestedListProps<T, P>) {
  const { t } = useTranslation()
  const [appSessionData, upsertAppSessionData] = useAppSession()
  const hashKeyRef = useRef(hashKey)

  const existingFilters = useMemo(
    () => (fasteStoreKey ? (get(appSessionData, `${fasteStoreKey}.filters`) as SelectedFilter) : undefined),
    [appSessionData, fasteStoreKey]
  )
  const _getFilterList = useCallback(
    () => getFilterList(data, filterProps?.filters ?? [], existingFilters),
    [data, filterProps?.filters, existingFilters]
  )
  const [filterList, setFilterList] = useState<Array<Filter>>(_getFilterList())

  // Memoize searchFilteredData dependencies
  const searchConfig = useMemo(
    () => ({
      searchTerm: searchProps?.searchTerm ?? '',
      searchKeys: searchProps?.searchKeys ?? [],
      customSearchFn: searchProps?.customSearchFn,
      subItemsProps: searchProps?.subItemsProps ?? ({} as NestedSearchSubItemsProps<T>)
    }),
    [searchProps]
  )

  const searchFilteredData = useCallback(
    (data: T[]) => {
      const { searchTerm, searchKeys, customSearchFn, subItemsProps } = searchConfig
      const {
        subItemsKeys = [],
        searchSubItemsKeys = [],
        customSearchFn: subItemsCustomSearchFn,
        uniqAccessorKey
      } = subItemsProps

      const shouldForceUpdate = hashKeyRef.current !== hashKey
      if (shouldForceUpdate) {
        hashKeyRef.current = hashKey
      }

      // Search the data based on the search keys and custom search function
      const topLevelResults = searchDataPartial(searchTerm, data, searchKeys, customSearchFn, {
        forceUpdate: shouldForceUpdate
      })

      if (isEmpty(subItemsKeys)) {
        return topLevelResults
      }

      // Search through sub-items and collect matching parent items with filtered sub-items
      const subItemResults = data
        .map((item) => {
          const matchingSubItems = subItemsKeys.reduce((acc, key) => {
            const subItems = get(item, key) as T[]
            if (!Array.isArray(subItems)) return acc

            const filteredSubItems = searchDataPartial(searchTerm, subItems, searchSubItemsKeys, subItemsCustomSearchFn)
            return filteredSubItems.length > 0 ? { ...acc, [key]: filteredSubItems } : acc
          }, {})

          return Object.keys(matchingSubItems).length > 0 ? { ...item, ...matchingSubItems } : null
        })
        .filter((item): item is T => item !== null)

      // Combine results and remove duplicates
      return uniqBy([...subItemResults, ...topLevelResults], uniqAccessorKey)
    },
    [hashKey, searchConfig]
  )

  const filteredAndSearchedData = useMemo(() => {
    // Filter the data based on the filter lists
    const filteredData = getFilteredData(filterList, data)
    if (isEmpty(searchConfig.searchTerm)) return filteredData

    // Search the filtered data
    return searchFilteredData(filteredData)
  }, [filterList, data, searchConfig.searchTerm, searchFilteredData])

  const renderList = useMemo(() => {
    if (isEmpty(filteredAndSearchedData)) return noMatchingDataContingency

    const { getHeader, getStyledListItems, ...restListProps } = listProps ?? {}
    return map(filteredAndSearchedData, (dataItem, index) => {
      // Try to create a more stable key using unique properties of the data item
      const itemKey =
        (dataItem as UnknownObject)?.orderId ||
        (dataItem as UnknownObject)?.id ||
        (dataItem as UnknownObject)?.code ||
        index
      const uniqueKey = `${listKey}-${itemKey}-list-container`

      return (
        <div key={uniqueKey} data-testid={`${listKey}-list-${index}-container`}>
          {getHeader ? <div data-testid={`${listKey}-header-${index}`}>{getHeader?.(dataItem)}</div> : null}
          <List<P> {...restListProps} items={getStyledListItems?.(dataItem)} data-testid={`${listKey}-list-${index}`} />
        </div>
      )
    })
  }, [filteredAndSearchedData, listKey, listProps, noMatchingDataContingency])

  const handleFilterChange = useCallback(
    (selectedFilters: SelectedFilter) => {
      setFilterList((filterList) =>
        filterList.map((f) => ({ ...f, selectedOptions: selectedFilters[f.category] || f.selectedOptions }))
      )

      if (fasteStoreKey) {
        upsertAppSessionData(fasteStoreKey, {
          filters: {
            ...existingFilters,
            ...selectedFilters
          }
        })
      }
    },
    [existingFilters, fasteStoreKey, upsertAppSessionData]
  )

  const getFilterBar = useCallback(() => {
    const filterLabel = filterBarProps?.allFilterLabel ?? t('common.filters.label')

    return (
      filterBar ?? (
        <FilterBar
          {...filterBarProps}
          allFilterLabel={filterLabel}
          applyFilters={handleFilterChange}
          filterList={filterList}
        />
      )
    )
  }, [filterBar, filterBarProps, filterList, handleFilterChange, t])

  useEffect(() => {
    const _filterList = _getFilterList()
    if (_filterList.length > 0) setFilterList(_filterList)
  }, [_getFilterList])

  return (
    <div className={styles.nested_list_container} data-testid={`${listKey}-nested-list`}>
      <Card variant='flat' className={styles.card}>
        {getFilterBar()}
        <CardContent>{renderList}</CardContent>
      </Card>
    </div>
  )
}

export default NestedList
