{"misc": [{"config": {"shipmentsTableTitle": "inventory.shipments.your_shipments.label", "ShipmentsDetailsTitle": "deliveries.view_delivery.label"}, "lobs": ["seed", "cp", "lic", "wb"]}], "shipmentsColumns": [{"config": [{"defaultSortOrder": 1, "accessor": "deliveryId", "header": "Delivery ID", "id": "deliveryId", "defaultSort": "asc", "searchable": true, "sortable": true}, {"accessor": "shipmentId", "header": "Shipment ID", "searchable": true}, {"accessor": "poNumber", "header": "PO Number", "searchable": true}, {"accessor": "type", "header": "Type", "searchable": true, "sortable": true, "filterable": true}, {"accessor": "shipToName", "header": "Ship To", "searchable": true, "filterable": true}, {"accessor": "plannedShipDate", "header": "Planned Ship Date", "searchable": true}, {"accessor": "deliveryStatus", "header": "Delivery Status", "searchable": true, "sortable": true, "filterable": true}], "lobs": ["seed", "cp", "lic", "wb"]}], "shipmentsLoadingContingencyConfig": [{"config": {"errorHeader": "common.data_load_failed.label", "errorDescription": "common.refresh_page.label", "loadingMessage": "inventory.shipments.loading_shipments.label", "noDataHeader": "inventory.shipments.no_data_header.msg", "noDataDescription": "inventory.shipments.no_data_description.msg", "noDataButtonProps": {"label": "orders.farmer.order.label", "modalToOpen": ""}, "noDataSecondaryButtonProps": {"label": "orders.dealer.order.label", "modalToOpen": ""}}, "lobs": ["seed", "cp", "lic", "wb"]}], "isShipmentsListExpandableColumn": [{"config": {"expandable": false}, "lobs": ["seed", "lic", "wb"]}, {"config": {"expandable": true, "value": "expandableRowTemplate"}, "lobs": ["cp"]}], "shipmentsListActions": [{"config": [{"label": "Print BOL", "value": "printBOL"}, {"label": "Add Issue", "value": "addIssue"}], "lobs": ["seed", "lic", "wb", "cp"]}], "shipmentsListExpandableColumns": [{"config": [{"accessor": "productDesc", "header": "Product Name", "filterable": true}, {"accessor": "uom", "header": "UOM", "filterable": true}, {"accessor": "quantity", "header": "Quantity", "filterable": true}], "lobs": ["seed", "lic", "wb", "cp"]}], "inventoryTabsConfig": [{"config": {"tabs": [{"label": "inventory.shipments.shipments.label", "value": "shipments"}]}, "lobs": ["seed", "cp", "lic", "wb"]}], "shipmentsListMobileConfig": [{"config": {"filters": [{"accessor": "type", "title": "Type"}, {"title": "Ship To", "accessor": "shipToName"}, {"title": "Delivery Status", "accessor": "deliveryStatus"}], "options": [], "listItemParams": {"orderHeader": "shipmentId", "overlineText": "deliveryStatus", "primaryText": "deliveryId", "secondaryText": "shipToName", "trailingBlock": ""}}, "lobs": ["seed", "sg", "cp", "lic", "wb"]}]}