# Shared Assets - CSS Standardization System

This library provides standardized CSS utilities, design tokens, and themes for consistent styling across the entire monorepo.

## Overview

The shared assets system provides:

- **Standardized Breakpoints**: Consistent responsive design breakpoints
- **Spacing Utilities**: 8px grid-based spacing system
- **Typography Utilities**: Standardized font sizes, weights, and text styles
- **Animation Utilities**: Common transitions and animations
- **Theme System**: Global CSS custom properties and theme variables

## Quick Start

### Import the Global Theme

```scss
@use '@gc/shared/assets/gc-theme.scss' as *;

.my-component {
  @include md-max {
    @include padding('lg');
    @include heading-medium;
  }
}
```

### Available Utilities

#### Breakpoints

- **Values**: `xs: 599px`, `sm: 600px`, `md: 720px`, `lg: 840px`, `xl: 1024px`, `xxl: 1440px`
- **Min-width mixins**: `xs-min`, `sm-min`, `md-min`, `lg-min`, `xl-min`, `xxl-min`
- **Max-width mixins**: `xs-max`, `sm-max`, `md-max`, `lg-max`, `xl-max`, `xxl-max`

```scss
// Responsive design
@include lg-min {
  // Styles for large screens and up
}

@include md-max {
  // Styles for medium screens and down
}
```

#### Spacing (8px Grid System)

- **Values**: `xs: 4px`, `sm: 8px`, `md: 16px`, `lg: 24px`, `xl: 32px`, `xxl: 48px`, `xxxl: 64px`
- **Mixins**: `margin()`, `padding()`, `gap()`, with directional variants

```scss
.card {
  @include padding('lg'); // 24px all sides
  @include margin-y('md'); // 16px top/bottom
  @include gap('sm'); // 8px gap
}
```

#### Typography

- **Font Weights**: `light: 300`, `regular: 400`, `medium: 500`, `semibold: 600`, `bold: 700`
- **Font Sizes**: `xs: 12px`, `sm: 14px`, `md: 16px`, `lg: 18px`, `xl: 20px`, `xxl: 24px`, `xxxl: 32px`
- **Preset Styles**: `heading-large`, `heading-medium`, `body-large`, etc.

```scss
.title {
  @include heading-large; // 32px, bold, tight line-height
}

.description {
  @include body-medium; // 16px, regular, normal line-height
}
```

#### Animations

- **Durations**: `fast: 150ms`, `normal: 250ms`, `slow: 350ms`, `slower: 500ms`
- **Easings**: `linear`, `ease`, `smooth`, `bounce`, etc.
- **Presets**: `hover-lift`, `hover-scale`, `fade-in`

```scss
.button {
  @include hover-lift; // Hover animation with lift effect
}

.modal {
  @include transition-opacity('normal');
}
```

## File Structure

```text
libs/shared/assets/
├── README.md                  # This file
├── MIGRATION_GUIDE.md        # Migration from hardcoded values
├── gc-theme.scss             # Main theme file (import this)
├── lmnt-theme-overrides.scss # Element theme customizations
├── _breakpoints.scss         # Responsive breakpoint system
├── _spacing.scss             # Spacing utilities (margins, padding, gap)
├── _typography.scss          # Font utilities and text styles
└── _animations.scss          # Transitions and animation utilities
```

## Usage Examples

### Component with Responsive Design and Spacing

```scss
@use '@gc/shared/assets/gc-theme.scss' as *;

.product-card {
  @include padding('lg');
  @include margin-bottom('md');
  @include transition-colors();

  // Stack on mobile, row on tablet+
  @include sm-max {
    flex-direction: column;
    @include gap('sm');
  }

  @include md-min {
    flex-direction: row;
    @include gap('lg');
  }

  .title {
    @include heading-medium;
  }

  .description {
    @include body-small;
    @include margin-y('xs');
  }

  &:hover {
    background-color: var(--lmnt-theme-surface-variant);
  }
}
```

### Modal Component with Animation

```scss
@use '@gc/shared/assets/gc-theme.scss' as *;

.modal-overlay {
  @include fade-in;
  @include padding('xl');

  @include sm-max {
    @include padding('md');
  }
}

.modal-content {
  @include transition-transform();
  transform: scale(0.95);

  &.visible {
    transform: scale(1);
  }
}
```

## Integration with Webpack

The webpack configuration automatically includes `libs/shared/assets` in the SCSS include paths, allowing you to import files without relative paths:

```scss
// ✅ Correct - works from anywhere in the monorepo
@use '@gc/shared/assets/gc-theme.scss' as *;

// ❌ Avoid - relative paths are fragile
@use '../../../libs/shared/assets/gc-theme.scss' as *;
```

## Migration from Hardcoded Values

See `MIGRATION_GUIDE.md` for detailed instructions on migrating from hardcoded breakpoints and values to the standardized system.

### Common Migrations

```scss
// Before
@media (max-width: 1023px) {
  margin: 16px;
  font-size: 24px;
}

// After
@include xl-max {
  @include margin('md');
  @include font-size('xxl');
}
```

## Best Practices

1. **Always use the standardized utilities** instead of hardcoded values
2. **Import the global theme** (`gc-theme.scss`) rather than individual utility files
3. **Follow the 8px grid system** for consistent spacing
4. **Use semantic breakpoint names** rather than pixel values in your mental model
5. **Combine utilities** to create complex responsive designs
6. **Test at all breakpoints** to ensure responsive behavior works correctly

## Contributing

When adding new utilities:

1. Follow the existing patterns and naming conventions
2. Add the utility to the appropriate `_utility.scss` file
3. Forward the utility in `gc-theme.scss`
4. Document the new utility in this README
5. Add examples to the migration guide if relevant

## Troubleshooting

### Import Issues

If imports aren't working, ensure:

- You're using `@use '@gc/shared/assets/gc-theme.scss' as *;`
- The webpack configuration includes the SCSS include paths
- You're not mixing `@import` and `@use` syntax

### Breakpoint Issues

If breakpoints aren't working as expected:

- Check that you're using the correct mixin names (`md-max`, not `md-down`)
- Verify breakpoint values match your design requirements
- Test in browser dev tools at the exact pixel values

### Spacing Issues

If spacing appears incorrect:

- Ensure you're using the spacing scale consistently
- Check for CSS specificity issues
- Verify that the 8px grid system aligns with your design
