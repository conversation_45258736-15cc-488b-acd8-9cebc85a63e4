@use 'sass:map';

// Animation durations
$durations: (
  'fast': 150ms,
  'normal': 250ms,
  'slow': 350ms,
  'slower': 500ms
);

// Easing functions
$easings: (
  'linear': linear,
  'ease': ease,
  'ease-in': ease-in,
  'ease-out': ease-out,
  'ease-in-out': ease-in-out,
  'bounce': cubic-bezier(0.68, -0.55, 0.265, 1.55),
  'smooth': cubic-bezier(0.4, 0, 0.2, 1)
);

// Animation utility mixins
@mixin duration($speed) {
  transition-duration: map.get($durations, $speed);
}

@mixin easing($ease) {
  transition-timing-function: map.get($easings, $ease);
}

@mixin transition($property, $speed: 'normal', $ease: 'smooth') {
  transition: $property map.get($durations, $speed) map.get($easings, $ease);
}

// Common transition patterns
@mixin transition-colors($speed: 'normal') {
  @include transition(color background-color border-color, $speed);
}

@mixin transition-transform($speed: 'normal') {
  @include transition(transform, $speed);
}

@mixin transition-opacity($speed: 'normal') {
  @include transition(opacity, $speed);
}

@mixin transition-all($speed: 'normal') {
  @include transition(all, $speed);
}

// Common animation states
@mixin hover-lift {
  @include transition-transform('fast');

  &:hover {
    transform: translateY(-2px);
  }
}

@mixin hover-scale {
  @include transition-transform('fast');

  &:hover {
    transform: scale(1.05);
  }
}

@mixin fade-in {
  opacity: 0;
  @include transition-opacity();

  &.visible {
    opacity: 1;
  }
}
