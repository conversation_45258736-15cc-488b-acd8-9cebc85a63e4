.gc-theme-bayer,
.gc-theme-bayer2_5,
.gc-theme-velocity {
  input::placeholder {
    font-size: 14px;
    letter-spacing: 0.25;
    color: var(--lmnt-theme-on-surface-inactive);
  }
  .lmnt-text-bubble__theme-color--blue {
    background: var(--lmnt-theme-secondary-100);
  }
}

.gc-theme-bayer,
.gc-theme-velocity {
  a {
    color: var(--lmnt-theme-primary) !important;
    font-weight: 700 !important;
  }
  .mdc-typography--headline3 {
    font-weight: 500 !important;
    font-style: normal !important;
  }
  .lmnt-table__header-cell {
    font-weight: 700 !important;
  }
  .mdc-button {
    text-transform: uppercase;
  }
  .lmnt,
  & {
    --color-primary: #006f9b;
    --color-primary-variant: #10384f;
    --lmnt-theme-primary: var(--color-primary) !important;
    --lmnt-theme-primary-variant: var(--color-primary-variant) !important;
    --lmnt-theme-primary-on-surface: var(--color-primary) !important;
    --mdc-theme-primary: var(--color-primary) !important;
    --mdc-theme-secondary: var(--color-primary) !important;
    --mdc-typography-caption-font-weight: 400 !important;
    --mdc-typography-headline5-font-weight: 500 !important;
    --mdc-typography-overline-font-weight: 500 !important;
  }
}

.gc-theme-bayer2_5 {
  .lmnt,
  & {
    /* Temporary fix for button color showing as purple instead of blue when Element 4 is mixed with Element 5. Ticket submitted. */
    --mdc-theme-primary: #006f9b !important;
    /* Temporary fix for font color showing as black instead of dark blue. Ticket submitted. */
    --lmnt-theme-on-surface: #10384f !important;
  }
}

.gc-theme-channel {
  a {
    color: var(--lmnt-theme-primary) !important;
    font-weight: 500 !important;
  }
}

.gc-theme-dad {
  a {
    color: var(--lmnt-theme-primary) !important;
    font-weight: 500 !important;
    text-decoration: underline !important;
  }
}
