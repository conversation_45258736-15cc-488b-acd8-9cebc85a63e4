@use './lmnt-theme-overrides.scss';

.gc-icon-info {
  background-color: var(--gc-theme-info-200);
  color: var(--gc-theme-info-on-surface);
}
.gc-icon-info-secondary {
  background-color: var(--lmnt-theme-secondary-100);
  color: var(--lmnt-theme-secondary-on-surface);
}
.gc-icon-action {
  background-color: var(--gc-theme-action-200);
  color: var(--gc-theme-action-on-surface);
}
.gc-icon-warning {
  background-color: var(--gc-theme-warning-200);
  color: var(--gc-theme-warning-on-surface);
}
.gc-icon-danger {
  background-color: var(--gc-theme-danger-200);
  color: var(--gc-theme-danger-on-surface);
}
.gc-icon-danger-variant {
  background-color: var(--gc-theme-danger-200);
  color: var(--gc-theme-danger-variant-on-surface);
}
.gc-icon-success {
  background-color: var(--lmnt-theme-success-200);
  color: var(--lmnt-theme-success-700);
}

label.gc-input-danger {
  border: 1px solid var(--lmnt-theme-danger-on-surface) !important;
  color: var(--lmnt-theme-danger-on-surface) !important;
  i {
    padding: 0px 8px;
    margin-top: 5px;
  }
}

:root,
.lmnt {
  --gc-theme-info-200: #c0caff;
  --gc-theme-info-on-surface: #091a75;
  --gc-theme-action-200: #a4e7ff;
  --gc-theme-action-on-surface: #6e760b;
  --gc-theme-warning-200: #ffe494;
  --gc-theme-warning-on-surface: #754a00;
  --gc-theme-danger-200: #ffc18d;
  --gc-theme-danger-on-surface: #b3190d;
  --gc-theme-danger-variant-on-surface: #652d01;
}
