@use 'sass:map';

// Font weights
$font-weights: (
  'light': 300,
  'regular': 400,
  'medium': 500,
  'semibold': 600,
  'bold': 700
);

// Font sizes (maintaining Material Design scale)
$font-sizes: (
  'xs': 12px,
  'sm': 14px,
  'md': 16px,
  'lg': 18px,
  'xl': 20px,
  'xxl': 24px,
  'xxxl': 32px
);

// Line heights
$line-heights: (
  'tight': 1.2,
  'normal': 1.4,
  'relaxed': 1.6,
  'loose': 1.8
);

// Letter spacing
$letter-spacing: (
  'tight': -0.025em,
  'normal': 0,
  'wide': 0.025em,
  'wider': 0.05em,
  'widest': 0.1em
);

// Typography utility mixins
@mixin font-weight($weight) {
  font-weight: map.get($font-weights, $weight);
}

@mixin font-size($size) {
  font-size: map.get($font-sizes, $size);
}

@mixin line-height($height) {
  line-height: map.get($line-heights, $height);
}

@mixin letter-spacing($spacing) {
  letter-spacing: map.get($letter-spacing, $spacing);
}

// Combined typography mixins
@mixin text-style($size, $weight: 'regular', $height: 'normal', $spacing: 'normal') {
  @include font-size($size);
  @include font-weight($weight);
  @include line-height($height);
  @include letter-spacing($spacing);
}

// Common text patterns
@mixin heading-large {
  @include text-style('xxxl', 'bold', 'tight');
}

@mixin heading-medium {
  @include text-style('xxl', 'semibold', 'tight');
}

@mixin heading-small {
  @include text-style('xl', 'semibold', 'normal');
}

@mixin body-large {
  @include text-style('lg', 'regular', 'normal');
}

@mixin body-medium {
  @include text-style('md', 'regular', 'normal');
}

@mixin body-small {
  @include text-style('sm', 'regular', 'normal');
}

@mixin caption {
  @include text-style('xs', 'regular', 'normal', 'wide');
}
