@use 'sass:map';

$breakpoints: (
  'xs': 599px,
  'sm': 600px,
  'md': 720px,
  'lg': 840px,
  'xl': 1024px,
  'xxl': 1440px
);

@mixin xs-min {
  @media (min-width: map.get($breakpoints, 'xs')) {
    @content;
  }
}
@mixin sm-min {
  @media (min-width: map.get($breakpoints, 'sm')) {
    @content;
  }
}
@mixin md-min {
  @media (min-width: map.get($breakpoints, 'md')) {
    @content;
  }
}
@mixin lg-min {
  @media (min-width: map.get($breakpoints, 'lg')) {
    @content;
  }
}
@mixin xl-min {
  @media (min-width: map.get($breakpoints, 'xl')) {
    @content;
  }
}
@mixin xxl-min {
  @media (min-width: map.get($breakpoints, 'xxl')) {
    @content;
  }
}

@mixin xs-max {
  @media (max-width: map.get($breakpoints, 'xs')) {
    @content;
  }
}

@mixin sm-max {
  @media (max-width: map.get($breakpoints, 'sm')) {
    @content;
  }
}

@mixin md-max {
  @media (max-width: map.get($breakpoints, 'md')) {
    @content;
  }
}

@mixin lg-max {
  @media (max-width: map.get($breakpoints, 'lg')) {
    @content;
  }
}

@mixin xl-max {
  @media (max-width: map.get($breakpoints, 'xl')) {
    @content;
  }
}

@mixin xxl-max {
  @media (max-width: map.get($breakpoints, 'xxl')) {
    @content;
  }
}

@mixin breakpoint($bp: 0) {
  @media (max-width: $bp) {
    @content;
  }
}
