# CSS Standardization Migration Guide

## Overview

This guide documents the migration from hardcoded CSS values to standardized utilities in the `@libs/shared/assets/` directory.

## Breakpoint Migration

### Current Hardcoded Values → Standardized Breakpoints

| Hardcoded Value              | Standardized Breakpoint | Mixin to Use       |
| ---------------------------- | ----------------------- | ------------------ |
| `@media (max-width: 1023px)` | `xl: 1024px`            | `@include xl-max`  |
| `@media (max-width: 719px)`  | `md: 720px`             | `@include md-max`  |
| `@media (min-width: 720px)`  | `md: 720px`             | `@include md-min`  |
| `@media (min-width: 1024px)` | `xl: 1024px`            | `@include xl-min`  |
| `@media (max-width: 599px)`  | `xs: 599px`             | `@include xs-max`  |
| `@media (min-width: 600px)`  | `sm: 600px`             | `@include sm-min`  |
| `@media (max-width: 839px)`  | `lg: 840px`             | `@include lg-max`  |
| `@media (min-width: 840px)`  | `lg: 840px`             | `@include lg-min`  |
| `@media (min-width: 1440px)` | `xxl: 1440px`           | `@include xxl-min` |

### Files Requiring Migration (103 files identified)

See the complete list of files with hardcoded breakpoints that need to be updated.

## How to Migrate Components

### Step 1: Import Standardized Utilities

Replace individual utility imports with the global theme:

```scss
// Before
@import 'some/relative/path/to/styles';

// After
@use '@gc/shared/assets/gc-theme.scss' as *;
```

### Step 2: Replace Hardcoded Breakpoints

```scss
// Before
@media (max-width: 1023px) {
  .my-component {
    flex-direction: column;
  }
}

// After
@include xl-max {
  .my-component {
    flex-direction: column;
  }
}
```

### Step 3: Use Standardized Spacing

```scss
// Before
.my-component {
  margin: 16px;
  padding: 24px 32px;
  gap: 8px;
}

// After
.my-component {
  @include margin('md');
  @include padding-y('lg');
  @include padding-x('xl');
  @include gap('sm');
}
```

### Step 4: Use Standardized Typography

```scss
// Before
.my-heading {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.2;
}

// After
.my-heading {
  @include heading-medium;
}
```

### Step 5: Use Standardized Animations

```scss
// Before
.my-button {
  transition: all 0.25s ease-in-out;
}

.my-button:hover {
  transform: translateY(-2px);
}

// After
.my-button {
  @include hover-lift;
}
```

## Available Utilities

### Breakpoints

- `xs-min`, `sm-min`, `md-min`, `lg-min`, `xl-min`, `xxl-min`
- `xs-max`, `sm-max`, `md-max`, `lg-max`, `xl-max`, `xxl-max`
- `breakpoint($bp)` for custom values

### Spacing

- `margin($size)`, `margin-x($size)`, `margin-y($size)`, `margin-top($size)`, `margin-bottom($size)`, `margin-left($size)`, `margin-right($size)`
- `padding($size)`, `padding-x($size)`, `padding-y($size)`, `padding-top($size)`, `padding-bottom($size)`, `padding-left($size)`, `padding-right($size)`
- `gap($size)`
- Sizes: `xs` (4px), `sm` (8px), `md` (16px), `lg` (24px), `xl` (32px), `xxl` (48px), `xxxl` (64px)

### Typography

- `font-size($size)`, `font-weight($weight)`, `line-height($height)`, `letter-spacing($spacing)`
- `text-style($size, $weight, $height, $spacing)` - Combined typography utility
- `heading-large` (32px, bold, tight), `heading-medium` (24px, semibold, tight), `heading-small` (20px, semibold, normal)
- `body-large` (18px), `body-medium` (16px), `body-small` (14px), `caption` (12px)
- Font sizes: `xs` (12px), `sm` (14px), `md` (16px), `lg` (18px), `xl` (20px), `xxl` (24px), `xxxl` (32px)
- Font weights: `light` (300), `regular` (400), `medium` (500), `semibold` (600), `bold` (700)
- Line heights: `tight` (1.2), `normal` (1.4), `relaxed` (1.6), `loose` (1.8)
- Letter spacing: `tight` (-0.025em), `normal` (0), `wide` (0.025em), `wider` (0.05em), `widest` (0.1em)

### Animations

- `transition($property, $speed, $ease)` - Generic transition utility
- `duration($speed)`, `easing($ease)` - Individual timing controls
- `transition-colors($speed)`, `transition-transform($speed)`, `transition-opacity($speed)`, `transition-all($speed)`
- `hover-lift`, `hover-scale`, `fade-in` - Pre-built animation states
- Durations: `fast` (150ms), `normal` (250ms), `slow` (350ms), `slower` (500ms)
- Easings: `linear`, `ease`, `ease-in`, `ease-out`, `ease-in-out`, `bounce`, `smooth`

### CSS Custom Properties (Variables)

The theme system includes custom properties for consistent theming:

**GC Theme Variables:**

- `--gc-theme-info-200`, `--gc-theme-info-on-surface`
- `--gc-theme-action-200`, `--gc-theme-action-on-surface`
- `--gc-theme-warning-200`, `--gc-theme-warning-on-surface`
- `--gc-theme-danger-200`, `--gc-theme-danger-on-surface`, `--gc-theme-danger-variant-on-surface`

**Icon Utility Classes:**

- `.gc-icon-info`, `.gc-icon-info-secondary`
- `.gc-icon-action`, `.gc-icon-warning`, `.gc-icon-danger`, `.gc-icon-danger-variant`, `.gc-icon-success`

**Input Utility Classes:**

- `.gc-input-danger` - For error state inputs

## Migration Priority

### High Priority (Immediate)

1. App-level stylesheets (app.module.scss files)
2. Commonly used UI components
3. Modal containers and major layout components

### Medium Priority

1. Feature-specific components
2. Page-level components

### Low Priority

1. Specialized one-off components
2. Legacy components with minimal usage

## Benefits After Migration

1. **Consistency**: All components use the same breakpoint values
2. **Maintainability**: Single source of truth for design tokens
3. **Performance**: Reduced CSS bundle size through reuse
4. **Developer Experience**: Semantic naming and autocomplete support
5. **Design System Alignment**: Easier to maintain design consistency

## Testing After Migration

1. Test responsive behavior at all breakpoints
2. Verify no visual regressions
3. Check that animations and transitions work correctly
4. Validate that spacing is consistent across components
