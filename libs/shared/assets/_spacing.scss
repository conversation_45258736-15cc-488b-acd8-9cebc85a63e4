@use 'sass:map';

// Standardized spacing scale (based on 8px grid system)
$spacing: (
  'xs': 4px,
  'sm': 8px,
  'md': 16px,
  'lg': 24px,
  'xl': 32px,
  'xxl': 48px,
  'xxxl': 64px
);

// Spacing utility mixins
@mixin margin($size) {
  margin: map.get($spacing, $size);
}

@mixin margin-x($size) {
  margin-left: map.get($spacing, $size);
  margin-right: map.get($spacing, $size);
}

@mixin margin-y($size) {
  margin-top: map.get($spacing, $size);
  margin-bottom: map.get($spacing, $size);
}

@mixin margin-top($size) {
  margin-top: map.get($spacing, $size);
}

@mixin margin-bottom($size) {
  margin-bottom: map.get($spacing, $size);
}

@mixin margin-left($size) {
  margin-left: map.get($spacing, $size);
}

@mixin margin-right($size) {
  margin-right: map.get($spacing, $size);
}

@mixin padding($size) {
  padding: map.get($spacing, $size);
}

@mixin padding-x($size) {
  padding-left: map.get($spacing, $size);
  padding-right: map.get($spacing, $size);
}

@mixin padding-y($size) {
  padding-top: map.get($spacing, $size);
  padding-bottom: map.get($spacing, $size);
}

@mixin padding-top($size) {
  padding-top: map.get($spacing, $size);
}

@mixin padding-bottom($size) {
  padding-bottom: map.get($spacing, $size);
}

@mixin padding-left($size) {
  padding-left: map.get($spacing, $size);
}

@mixin padding-right($size) {
  padding-right: map.get($spacing, $size);
}

@mixin gap($size) {
  gap: map.get($spacing, $size);
}
