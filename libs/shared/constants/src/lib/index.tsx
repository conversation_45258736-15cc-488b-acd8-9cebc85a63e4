export const resolutions = {
  M599: 0,
  M719: 1,
  M839: 2,
  M1023: 3,
  D1439: 4,
  D1440PLUS: 5
}

export const interpunct = '  •  '
export const space = ' '
export const PREPAY = 'Prepay'

export const IS_SMALL_MOBILE = 599 // Max width for Viewport to identify as Mobile
export const IS_MOBILE = 1023 // Max width for viewport to identify as Mobile/Tablet.

export const IS_TABLET_MIN = 600 // Min width for Viewport to identify as Tablet
export const IS_TABLET_MAX = 1023 // Max width for Viewport to identify as Tablet

export const IS_DESKTOP = 1024 // Min width for viewport to identify as Desktop.

export const ccFieldsChannel = {
  MOBILE: 'ONEDCE_CBUS_MOBILE',
  DESKTOP: 'ONEDCE_CBUS_DESKTOP'
}
export const ccFieldsChannelInventory = {
  MOBILE: 'ONEDCE_CBUS',
  DESKTOP: 'ONEDCE_CBUS'
}
export const ccFieldsMyCrop = {
  MOBILE: 'ONEDCE_NBUS',
  DESKTOP: 'ONEDCE_NBUS'
}
export const ccFieldsChannelAU = {
  MOBILE: 'ONEDCE_AU_MOBILE',
  DESKTOP: 'ONEDCE_AU_DESKTOP'
}

export const consignmentTypes = {
  FARMER: 'FARMER',
  SHIPMENT: 'SHIPMENT',
  RETURN: 'RETURN',
  TRANSFER: 'TRANSFER',
  SEED_GROWTH: 'SEED_GROWTH',
  AGENTRETURN: 'AGENTRETURN',
  FARMERRETURN: 'FARMERRETURN'
} as const

export const orderDocumentTypes = {
  SEED_GROWTH: ['ZUOR', 'ZUOB'],
  STOCK: ['ZU3L'],
  RETURN: ['ZU3R'],
  SHIPMENT_AU: ['ZAKB']
}

export const AuDocumentTypes = {
  ZAKB1: 'ZAKB1'
}

export const ConsignmentDocTypes = {
  ZAKB: 'ZAKB',
  ZAKE: 'ZAKE'
}

export const HeaderHookValues = {
  FILL: 'fill',
  ISSUE: 'issue',
  DEFAULT: 'default'
} as const

export const GLOBAL_APP = 'GLOBAL_APP' as const

export const SALES_UOM = 'SUM' as const

export const entitlements = {
  FARMER_ORDER: 'orders',
  STOCK_ORDER: 'stock-orders',
  INVENTORY_OVERVIEW: 'inventory-overview',
  FARMER_ORDER_DELIVERY: 'farmer-order-delivery',
  FARMER: 'farmers'
} as const

export const entitlementAccessType = {
  READ: 'read',
  WRITE: 'write',
  DELETE: 'delete'
} as const

export const USA_STATES = [
  { text: 'Alabama', value: 'AL' },
  { text: 'Alaska', value: 'AK' },
  { text: 'Arizona', value: 'AZ' },
  { text: 'Arkansas', value: 'AR' },
  { text: 'California', value: 'CA' },
  { text: 'Colorado', value: 'CO' },
  { text: 'Connecticut', value: 'CT' },
  { text: 'Delaware', value: 'DE' },
  { text: 'Florida', value: 'FL' },
  { text: 'Georgia', value: 'GA' },
  { text: 'Hawaii', value: 'HI' },
  { text: 'Idaho', value: 'ID' },
  { text: 'Illinois', value: 'IL' },
  { text: 'Indiana', value: 'IN' },
  { text: 'Iowa', value: 'IA' },
  { text: 'Kansas', value: 'KS' },
  { text: 'Kentucky', value: 'KY' },
  { text: 'Louisiana', value: 'LA' },
  { text: 'Maine', value: 'ME' },
  { text: 'Maryland', value: 'MD' },
  { text: 'Massachusetts', value: 'MA' },
  { text: 'Michigan', value: 'MI' },
  { text: 'Minnesota', value: 'MN' },
  { text: 'Mississippi', value: 'MS' },
  { text: 'Missouri', value: 'MO' },
  { text: 'Montana', value: 'MT' },
  { text: 'Nebraska', value: 'NE' },
  { text: 'Nevada', value: 'NV' },
  { text: 'New Hampshire', value: 'NH' },
  { text: 'New Jersey', value: 'NJ' },
  { text: 'New Mexico', value: 'NM' },
  { text: 'New York', value: 'NY' },
  { text: 'North Carolina', value: 'NC' },
  { text: 'North Dakota', value: 'ND' },
  { text: 'Ohio', value: 'OH' },
  { text: 'Oklahoma', value: 'OK' },
  { text: 'Oregon', value: 'OR' },
  { text: 'Pennsylvania', value: 'PA' },
  { text: 'Rhode Island', value: 'RI' },
  { text: 'South Carolina', value: 'SC' },
  { text: 'South Dakota', value: 'SD' },
  { text: 'Tennessee', value: 'TN' },
  { text: 'Texas', value: 'TX' },
  { text: 'Utah', value: 'UT' },
  { text: 'Vermont', value: 'VT' },
  { text: 'Virginia', value: 'VA' },
  { text: 'Washington', value: 'WA' },
  { text: 'West Virginia', value: 'WV' },
  { text: 'Wisconsin', value: 'WI' },
  { text: 'Wyoming', value: 'WY' },
  { text: 'District of Columbia', value: 'DC' }
]

export const TRANSFER_OPERATION = {
  transfer: 'TRANSFER',
  reconfirm: 'CONFIRM',
  transferToNew: 'TRANSFER_TO_NEW_ORDER'
}

export const CHANGE_TYPES = {
  reconfirm: 'RECONFIRM',
  processed: 'PROCESSED',
  transfer: 'SFDCTRGT'
}

export const AUD = 'AUD'

export enum OrderStatus {
  Confirmed = 'Confirmed',
  Picking = 'Picking',
  partiallyDispatched = 'Partially Dispatched',
  fullyDispatched = 'Fully Dispatched',
  Cancelled = 'Cancelled',
  Failed = 'Failed',
  Error = 'Error',
  backOrderFailed = 'Back Order Failed',
  Processing = 'Processing',
  Scheduled = 'Scheduled',
  Dispatched = 'Dispatched',
  Submitted = 'Submitted',
  Open = 'Open',
  Delivered = 'Delivered',
  Deleted = 'Deleted'
}
