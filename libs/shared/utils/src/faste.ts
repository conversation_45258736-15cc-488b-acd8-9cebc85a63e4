import { FasteStore } from '@monsantoit/faste-lite-react'
import { get } from 'es-toolkit/compat'

let fasteStore: FasteStore

export function setFasteStore(store: FasteStore) {
  fasteStore = store
}

export function getFasteStore() {
  return fasteStore
}

export function fetchStore(name: string) {
  return fasteStore?.fetch(name)?.value
}

export function updateStore(key: string, value: object) {
  fasteStore.update(key, { ...fetchStore(key), ...value })
}

export function subscribeStore(key: string, value: () => void) {
  fasteStore.subscribe(key, value)
}

export function fasteRoute(route: string, data?: object) {
  const historyState = window.history.state ?? {}
  get(window, 'faste.route', (_route: string, _data?: object) => {
    // noop
  })(route, {
    data: {
      ...historyState,
      ...data
    }
  })
}

export const getFasteStoreKey = (app: string, page: string) => {
  return `${app}_${page}`.toUpperCase()
}

export const goToOrderDetails = (code: string) => fasteRoute(`/orders/${code}`, { code })
export const goToPrefixCode = (code: string, prefix?: string) =>
  fasteRoute(prefix ? `/${prefix}/${code}` : `/${code}`, { code })
