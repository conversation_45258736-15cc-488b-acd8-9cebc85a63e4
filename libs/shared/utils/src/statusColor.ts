import { OrderStatus } from '@gc/constants'

export const statusColorMap: Record<OrderStatus, { default: string; alternate?: string }> = {
  [OrderStatus.Confirmed]: { default: '#DCDCDC', alternate: 'green' },
  [OrderStatus.Picking]: { default: 'yellow' },
  [OrderStatus.partiallyDispatched]: { default: 'green' },
  [OrderStatus.fullyDispatched]: { default: 'green' },
  [OrderStatus.Cancelled]: { default: 'red' },
  [OrderStatus.Failed]: { default: 'red' },
  [OrderStatus.Error]: { default: 'red' },
  [OrderStatus.backOrderFailed]: { default: 'red' },
  [OrderStatus.Processing]: { default: '#DCDCDC' },
  [OrderStatus.Scheduled]: { default: 'green' },
  [OrderStatus.Dispatched]: { default: 'green' },
  [OrderStatus.Submitted]: { default: 'yellow' },
  [OrderStatus.Open]: { default: 'yellow' },
  [OrderStatus.Delivered]: { default: 'green' },
  [OrderStatus.Deleted]: { default: 'red' }
}

export const getThemeColor = (status: OrderStatus, preferAlternate = false): string => {
  const colorConfig = statusColorMap[status]
  if (!colorConfig) return '#DCDCDC' // fallback grey

  return preferAlternate && colorConfig.alternate ? colorConfig.alternate : colorConfig.default
}
