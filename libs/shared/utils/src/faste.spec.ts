import { FasteStore } from '@monsantoit/faste-lite-react'

import { fetchStore, getFasteStore, setFasteStore, subscribeStore, updateStore } from './faste'

describe('module.ts', () => {
  let mockFasteStore: FasteStore

  beforeEach(() => {
    mockFasteStore = {
      fetch: jest.fn(),
      update: jest.fn(),
      subscribe: jest.fn(),
      id: 'mockId',
      stores: [],
      list: jest.fn(),
      create: jest.fn()
    }
    setFasteStore(mockFasteStore)
  })

  it('should set and get FasteStore', () => {
    expect(getFasteStore()).toBe(mockFasteStore)
  })

  it('should fetch a value from the store', () => {
    // eslint-disable-next-line prettier/prettier
    (mockFasteStore.fetch as jest.Mock).mockReturnValue({ value: 'testValue' })
    expect(fetchStore('testKey')).toBe('testValue')
    expect(mockFasteStore.fetch).toHaveBeenCalledWith('testKey')
  })

  it('should update a value in the store', () => {
    // eslint-disable-next-line prettier/prettier
    (mockFasteStore.fetch as jest.Mock).mockReturnValue({ value: { existingKey: 'existingValue' } }) // Add missing semicolon
    updateStore('testKey', { newKey: 'newValue' })
    expect(mockFasteStore.update).toHaveBeenCalledWith('testKey', {
      existingKey: 'existingValue',
      newKey: 'newValue'
    })
  })

  it('should subscribe to a store key', () => {
    const callback = jest.fn()
    subscribeStore('testKey', callback)
    expect(mockFasteStore.subscribe).toHaveBeenCalledWith('testKey', callback)
  })
})
