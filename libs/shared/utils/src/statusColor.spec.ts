import { OrderStatus } from '@gc/constants'

import { getThemeColor } from './statusColor'

describe('statusColor.ts', () => {
  it('should return theme color', () => {
    expect(getThemeColor(OrderStatus.Picking)).toBe('yellow')
    expect(getThemeColor(OrderStatus.Confirmed, true)).toBe('green')
  })

  it('should return default theme color', () => {
    expect(getThemeColor(null)).toBe('#DCDCDC')
  })
})
