schema-version: v1
rulesets:
  - javascript-best-practices
  - javascript-browser-security
  - javascript-code-style
  - javascript-common-security
  - javascript-express
  - javascript-inclusive
  - javascript-node-security
  - jsx-react
  - github-actions
  - typescript-best-practices
  - typescript-code-style
  - typescript-common-security
  - typescript-browser-security
  - typescript-node-security
  - typescript-express
  - typescript-inclusive
