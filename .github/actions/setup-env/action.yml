name: Set Environment
description: Sets the staging environment with optimized caching

inputs:
  VAULT_ROLE_ID:
    description: Use secrets.VAULT_ROLE_ID
    required: true
  VAULT_SECRET_ID:
    description: Use secrets.VAULT_SECRET_ID
    required: true
  VAULT_PATH:
    description: Use env.VAULT_NON_PROD_PATH
    required: false
    default: kv/data/gc-agency/npm
  AUTH_TOKEN_KEY:
    description: Vault Key for the Artifactory Token
    required: false
    default: ARTIFACTORY_ACCESS_TOKEN
  is_prod:
    description: Set to true if workflow is for production
    required: true
    default: 'false'
  is_np:
    description: Set to true if workflow needs to override to np
    required: false
    default: 'true'
  node_version:
    description: Version of node to run workflow
    default: '20'
    required: false
  base_branch:
    description: The base branch of your repository (the branch which you target with PRs).
    default: 'development'
    required: false
  last-successful-event:
    description: The last successful event of the workflow
    default: 'pull_request'
    required: false
  cache_dependency_path:
    description: Used to specify the path to a dependency file - package-lock.json, yarn.lock, etc.
    required: false
    default: '**/package-lock.json'

runs:
  using: 'composite'
  steps:
    - name: Set Environment Variables
      shell: bash
      run: |
        if [[ "${{ inputs.is_prod }}" == "true" ]]; then
          echo 'ENV=prod' >> $GITHUB_ENV
          echo 'STAGE=prod' >> $GITHUB_ENV
          echo 'CACHE_PREFIX=prod' >> $GITHUB_ENV
        elif [[ "${{ github.ref_name }}" == "np" || "${{ inputs.is_np }}" == "true" ]]; then
          echo 'ENV=non-prod' >> $GITHUB_ENV
          echo 'STAGE=non-prod' >> $GITHUB_ENV
          echo 'CACHE_PREFIX=np' >> $GITHUB_ENV
        else
          echo 'ENV=dev' >> $GITHUB_ENV
          echo 'STAGE=dev' >> $GITHUB_ENV
          echo 'CACHE_PREFIX=dev' >> $GITHUB_ENV
        fi

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node_version }}
        cache: 'npm'
        cache-dependency-path: ${{ inputs.cache_dependency_path }}

    - name: Setup NPM Configuration
      uses: ./.github/actions/setup-npm
      with:
        VAULT_PATH: ${{ inputs.VAULT_PATH }}
        VAULT_ROLE_ID: ${{ inputs.VAULT_ROLE_ID }}
        VAULT_SECRET_ID: ${{ inputs.VAULT_SECRET_ID }}
        AUTH_TOKEN_KEY: ${{ inputs.AUTH_TOKEN_KEY }}

    # Enhanced caching strategy for node_modules
    - name: Cache node_modules
      id: cache-node-modules
      uses: actions/cache@v4
      with:
        path: |
          node_modules
          ~/.npm
          **/node_modules
        key: ${{ runner.os }}-${{ env.CACHE_PREFIX }}-node-modules-${{ hashFiles(inputs.cache_dependency_path) }}-${{ hashFiles('**/package.json') }}
        restore-keys: |
          ${{ runner.os }}-${{ env.CACHE_PREFIX }}-node-modules-${{ hashFiles(inputs.cache_dependency_path) }}-
          ${{ runner.os }}-${{ env.CACHE_PREFIX }}-node-modules-
          ${{ runner.os }}-node-modules-

    # Skip installation if cache hit and node_modules exists
    - name: Verify node_modules integrity
      id: verify-modules
      if: steps.cache-node-modules.outputs.cache-hit == 'true'
      shell: bash
      run: |
        if [[ -d "node_modules" && -f "node_modules/.package-lock.json" ]]; then
          echo "modules-valid=true" >> $GITHUB_OUTPUT
          echo "✅ node_modules cache is valid"
        else
          echo "modules-valid=false" >> $GITHUB_OUTPUT
          echo "⚠️ node_modules cache exists but is incomplete"
        fi

    - name: Install dependencies
      if: steps.cache-node-modules.outputs.cache-hit != 'true' || steps.verify-modules.outputs.modules-valid != 'true'
      shell: bash
      run: |
        echo "📦 Installing dependencies..."
        npm ci --prefer-offline --no-audit --include=optional --timing

    - name: Validate installation
      if: steps.cache-node-modules.outputs.cache-hit != 'true' || steps.verify-modules.outputs.modules-valid != 'true'
      shell: bash
      run: |
        echo "🔍 Validating installation..."
        if ! npm ls --depth=0 > /dev/null 2>&1; then
          echo "❌ Installation validation failed"
          exit 1
        fi
        echo "✅ Installation validated successfully"

    # Cache NX computation cache for faster builds
    # IF THINGS BREAK, REMOVE THIS CACHE
    - name: Cache NX
      uses: actions/cache@v4
      with:
        path: |
          .nx/cache
          node_modules/.cache/nx
        key: ${{ runner.os }}-${{ env.CACHE_PREFIX }}-nx-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-${{ env.CACHE_PREFIX }}-nx-
          ${{ runner.os }}-nx-

    - name: Set NX SHAs
      uses: nrwl/nx-set-shas@v4
      with:
        main-branch-name: ${{ inputs.base_branch }}
        last-successful-event: ${{ inputs.last-successful-event }}

    # Output cache statistics for debugging
    - name: Cache Statistics
      shell: bash
      run: |
        echo "📊 Cache Statistics:"
        echo "Node modules cache hit: ${{ steps.cache-node-modules.outputs.cache-hit }}"
        echo "Modules validation: ${{ steps.verify-modules.outputs.modules-valid }}"
        if [[ -d "node_modules" ]]; then
          echo "node_modules size: $(du -sh node_modules | cut -f1)"
        fi
        if [[ -d ".nx/cache" ]]; then
          echo "NX cache size: $(du -sh .nx/cache | cut -f1 2>/dev/null || echo 'N/A')"
        fi
