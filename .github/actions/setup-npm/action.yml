name: Configure Package Manager
description: Configures Package Manager for NPM repo usage.

inputs:
  VAULT_ROLE_ID:
    description: Use secrets.VAULT_ROLE_ID
    required: true
  VAULT_SECRET_ID:
    description: Use secrets.VAULT_SECRET_ID
    required: true
  VAULT_PATH:
    description: Use env.VAULT_NON_PROD_PATH
    required: false
    default: kv/data/gc-agency/npm
  AUTH_TOKEN_KEY:
    description: Vault Key for the Artifactory Token
    required: false
    default: ARTIFACTORY_ACCESS_TOKEN

runs:
  using: 'composite'
  steps:
    - name: Import NPM Secrets
      uses: hashicorp/vault-action@v3.1.0
      with:
        url: https://vault.agro.services
        method: approle
        roleId: ${{ inputs.VAULT_ROLE_ID }}
        secretId: ${{ inputs.VAULT_SECRET_ID }}
        secrets: |
          ${{ inputs.VAULT_PATH }} ${{ inputs.AUTH_TOKEN_KEY }} | AUTH_TOKEN;

    - name: Configure NPM
      shell: bash
      run: |
        npm config set //artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/:_authToken ${{ env.AUTH_TOKEN }}
        npm config set @bayerit:registry=https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
        npm config set @c7:registry=https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
        npm config set @element:registry https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
        npm config set @faste-lite:registry=https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
        npm config set @global-commerce:registry https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
        npm config set @monsantoit:registry https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
        npm config set @supernova:registry https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
