# CODEOWNERS - Assign reviewers for different parts of the repository

# ──────────── Global fallback ────────────
*                          @jaco<PERSON>-le<PERSON><PERSON> @schok-d @maheshprasadext @joeflatt

# ──────────── Applications ────────────
apps/channel/*             @bayer-int/endeavor
apps/australia/*           @bayer-int/anomaly-team
apps/mycrop/*              @bayer-int/lodestar
apps/mycrop-v2/*           @bayer-int/lodestar
apps/seedsman/*            @bayer-int/lodestar

# ──────────── Libraries ───────────────
libs/api/**                @bayer-int/endeavor @bayer-int/lodestar
libs/hooks/**              @bayer-int/endeavor @bayer-int/lodestar
libs/redux-store/**        @bayer-int/endeavor @bayer-int/lodestar
libs/features/common/**    @bayer-int/endeavor @bayer-int/lodestar
libs/features/nb/**        @bayer-int/lodestar
libs/shared/**             @bayer-int/endeavor @bayer-int/lodestar
libs/types/**              @bayer-int/endeavor @bayer-int/lodestar

# ──────────── Tooling & CI ────────────
.github/**                 @jacob-lecoq @schok-d @maheshprasadext @joeflatt
scripts/**                 @jacob-lecoq @schok-d @maheshprasadext @joeflatt

# ──────────── Documentation ───────────
docs/**                    @jacob-lecoq @schok-d @maheshprasadext @joeflatt

# Keep CODEOWNERS tightly reviewed
/.github/CODEOWNERS        @jacob-lecoq @schok-d @catchmeramesh @maheshprasadext @joeflatt
