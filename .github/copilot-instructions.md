# GitHub Copilot Instructions

Welcome to the `gc-agency-ui-monorepo`! This guide outlines our conventions and best practices for using GitHub Copilot effectively in this NX-powered TypeScript React monorepo.

---

## 1. Codebase Overview

- **Framework:** NX 21.3.9 Monorepo
- **Main Language:** TypeScript 5.8.3
- **Frontend Framework:** React 18.3.1
- **Styling:** SCSS
- **State Management:** Redux Toolkit with RTK Query
- **Testing:** Jest with React Testing Library
- **Bundling:** Webpack 5
- **Linting:** ESLint with TypeScript, React, and SonarJS rules
- **Purpose:** Multi-brand UI applications (Australia, Channel, MyCrop, Seedsman) with shared feature libraries

### Architecture

- **Applications:** Brand-specific apps in `/apps` (australia, channel, mycrop, seedsman)
- **Libraries:** Shared functionality in `/libs`
  - `features/common/*`: Cross-brand feature libraries
  - `features/nb/*`: NB-specific features
  - `shared/*`: Utilities, components, assets
  - `types`, `hooks`, `redux-store`, `api`: Core infrastructure

---

## 2. NX Development Guidelines

### Library Generation

**Use NX generators for consistency:**

```bash
# Generate React library
nx g @nx/react:library my-feature --directory=libs/features/common --style=scss

# Generate React component
nx g @nx/react:component MyComponent --project=my-feature --style=scss
```

### Module Boundaries

- **Respect dependency constraints:** Libraries can only depend on other libraries with compatible tags
- **Use barrel exports:** Always export through `index.ts` files
- **Path mapping:** Use `@gc/*` aliases defined in `tsconfig.base.json`

### NX Commands

```bash
# Run affected projects only
nx affected:build
nx affected:test
nx affected:lint

# Visualize dependency graph
nx graph

# Run specific project
nx run project-name:target
```

---

## 3. React Development Patterns

### Component Structure

```typescript
// Preferred component pattern
export interface MyComponentProps {
  title: string;
  onAction?: () => void;
}

export const MyComponent: React.FC<MyComponentProps> = ({ title, onAction }) => {
  return (
    <div className="my-component">
      <h2>{title}</h2>
      {onAction && <button onClick={onAction}>Action</button>}
    </div>
  );
};
```

### Custom Hooks

- **Location:** Place in `libs/hooks/src/`
- **Naming:** Use `use` prefix
- **Testing:** Include unit tests for complex logic

### State Management

- **Redux Toolkit:** Use for global state
- **RTK Query:** For API calls and caching
- **Local State:** Use `useState` for component-local state
- **Form State:** Use `react-hook-form` with `zod` validation

---

## 4. TypeScript Best Practices

### Type Definitions

- **Shared types:** Define in `libs/types/src/`
- **Feature-specific types:** Co-locate with components
- **API types:** Auto-generate from OpenAPI specs when possible

### Import Organization

```typescript
// External libraries
import React from 'react'
import { useDispatch } from 'react-redux'

// Internal libraries (alphabetical)
import { MyComponent } from '@gc/components'
import { useApi } from '@gc/hooks'
import { ApiClient } from '@gc/api/client'

// Relative imports
import './MyComponent.scss'
```

### Interface Naming

- **Props interfaces:** `ComponentNameProps`
- **API responses:** `ApiResponseName`
- **Generic interfaces:** Descriptive names without prefixes

---

## 5. Testing Guidelines

### Unit Testing

```typescript
// Component testing pattern
import { render, screen } from '@testing-library/react';
import { MyComponent } from './MyComponent';

describe('MyComponent', () => {
  it('should render title', () => {
    render(<MyComponent title="Test Title" />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });
});
```

### Testing Utilities

- **Test setup:** Use utilities from `@gc/shared/test`
- **Mocking:** Use MSW for API mocking
- **Coverage:** Maintain >80% coverage for new code

---

## 6. Styling Guidelines

### SCSS Structure

```scss
// Component styles
.my-component {
  display: flex;
  padding: 1rem;

  &__title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  &--variant {
    background-color: var(--color-primary);
  }
}
```

### CSS Modules

- **Usage:** Prefer CSS modules for component styles
- **Naming:** Use kebab-case for class names
- **Variables:** Use CSS custom properties for theming

---

## 7. Commit Message Guidelines

**Format:**
`country/type/taskNumber(scope): subject`

**Example:**
`us/feat/123456(quotes): Added new button`

**Fields Explained:**

- `country`: Country code (e.g., `us`, `de`)
- `type`: Commit type (`feat`, `fix`, `chore`, `refactor`, etc.)
- `taskNumber`: ID of the related task or ticket
- `scope`: (Optional, in parentheses) Affected module or area (e.g., `quotes`, `orders`)
- `subject`: Short description of the change

**Tips for Copilot:**

- Always start with the country code.
- Use the correct commit type.
- Include the task number from the relevant ticket.
- Add a scope if appropriate.
- Write a concise, meaningful subject.

---

## 8. Branch Naming Convention

**Format:**
`taskNumber-appName-optionalDescription`

**Example:**
`123456-quotes-add-new-button`

**Fields Explained:**

- `taskNumber`: ID of the related task or ticket
- `appName`: The module or application being changed (e.g., `quotes`, `orders`)
- `optionalDescription`: (Optional) Brief description of the work

**Tips for Copilot:**

- Always begin with the task number.
- Use lowercase and hyphens as separators.
- Be descriptive but concise.

---

## 9. Advanced Copilot Usage

### Code Generation Preferences

- **Components:** Generate functional components with TypeScript interfaces
- **Hooks:** Include proper dependency arrays and cleanup
- **Tests:** Generate comprehensive test suites with happy/error paths
- **Types:** Prefer interfaces over types for object shapes

### NX-Specific Suggestions

- **Library dependencies:** Suggest appropriate `@gc/*` imports
- **Generator usage:** Recommend NX generators for consistency
- **Build optimization:** Consider affected builds in suggestions
- **Module boundaries:** Respect architectural constraints

### React Patterns

- **Performance:** Suggest `React.memo`, `useMemo`, `useCallback` when appropriate
- **Accessibility:** Include ARIA attributes and semantic HTML
- **Error handling:** Use `react-error-boundary` for component error boundaries
- **Loading states:** Include loading and error states in async components

### Code Quality

- **ESLint compliance:** Follow configured ESLint rules
- **Type safety:** Prefer strict TypeScript patterns
- **Performance:** Suggest optimizations for bundle size and runtime
- **Maintainability:** Favor readable, self-documenting code

---

## 10. Project-Specific Context

### Available Scripts

```bash
# Development
npm run channel:quotes:faste    # Start quotes app
npm run mycrop:v2:farmers:faste # Start MyCrop farmers app

# Building
npm run build:affected          # Build affected projects
npm run build:storybook        # Build Storybook

# Testing
npm run test:affected          # Test affected projects
npm run test:coverage          # Run with coverage

# Quality
npm run lint:affected          # Lint affected projects
```

### Key Dependencies

- **UI Components:** `@element/react-components` for design system
- **State Management:** `@reduxjs/toolkit` with RTK Query
- **Forms:** `react-hook-form` with `@hookform/resolvers`
- **Validation:** `zod` for runtime type checking
- **Styling:** `sass` for SCSS compilation
- **Testing:** `@testing-library/react` with `jest`

### Module Federation

- **Faste Integration:** Uses `@monsantoit/faste-lite-react` for module federation
- **Module Loader:** `@supernova/faste-module-loader` for dynamic loading

---

Happy coding!
