name: CI Pipeline

on:
  pull_request:
    branches: [development]
  push:
    branches: [development]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  actions: write

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      affected-apps: ${{ steps.affected.outputs.apps }}
      affected-libs: ${{ steps.affected.outputs.libs }}
      has-affected: ${{ steps.affected.outputs.has-affected }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          filter: tree:0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}

      - name: Get affected projects
        id: affected
        run: |
          AFFECTED=$(npx nx show projects --affected --json)
          echo "apps=$(echo $AFFECTED | jq -r 'map(select(startswith("apps/"))) | @json')" >> $GITHUB_OUTPUT
          echo "libs=$(echo $AFFECTED | jq -r 'map(select(startswith("libs/"))) | @json')" >> $GITHUB_OUTPUT
          echo "has-affected=$(echo $AFFECTED | jq -r 'length > 0')" >> $GITHUB_OUTPUT

  lint:
    needs: setup
    if: needs.setup.outputs.has-affected == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}
          last-successful-event: ${{ github.event_name }}

      - name: Run lint
        run: npx nx affected -t lint --parallel=4 --exclude 'storybook-host'

  test:
    needs: setup
    if: needs.setup.outputs.has-affected == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}
          last-successful-event: ${{ github.event_name }}
      - name: Run tests
        run: npx nx affected -t test --exclude 'components,storybook-host' -- --coverage --maxWorkers=50% --silent
        env:
          NODE_ENV: test
          CI: true

      - name: Upload coverage
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage
          path: coverage/
          retention-days: 7

  build:
    needs: setup
    if: needs.setup.outputs.has-affected == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}
          last-successful-event: ${{ github.event_name }}

      - name: Run build
        run: npx nx affected -t build --configuration=development --parallel=4 --exclude '*seedsman*' --exclude '*mycrop*'
        env:
          NODE_ENV: production
          CI: true

      - name: Cache build artifacts
        uses: actions/cache@v4
        with:
          path: dist
          key: ${{ runner.os }}-build-${{ github.sha }}
