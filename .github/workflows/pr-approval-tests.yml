name: 'PR Approval System Tests'

on:
  push:
    branches: [development, main]
    paths:
      - 'scripts/pr-approval/**'
      - '.github/workflows/pr-approval.yml'
      - '.github/workflows/pr-approval-tests.yml'
  pull_request:
    branches: [development, main]
    paths:
      - 'scripts/pr-approval/**'
      - '.github/workflows/pr-approval.yml'
      - '.github/workflows/pr-approval-tests.yml'
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of tests to run'
        required: false
        default: 'all'
        type: choice
        options:
          - 'all'
          - 'unit'
          - 'integration'
      verbose:
        description: 'Enable verbose output'
        required: false
        default: false
        type: boolean

concurrency:
  group: pr-approval-tests-${{ github.ref }}
  cancel-in-progress: true

jobs:
  validate-scripts:
    name: 'Validate Scripts'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Validate shell scripts
        run: |
          echo "🔍 Validating shell scripts..."
          
          # Find all shell scripts in pr-approval directory
          find scripts/pr-approval -name "*.sh" -type f | while read -r script; do
            echo "Checking: $script"
            
            # Check if script is executable
            if [[ ! -x "$script" ]]; then
              echo "⚠️ Script is not executable: $script"
              chmod +x "$script"
            fi
            
            # Basic syntax check
            if ! bash -n "$script"; then
              echo "❌ Syntax error in: $script"
              exit 1
            fi
            
            echo "✅ $script is valid"
          done
          
          echo "✅ All scripts validated successfully"
      
      - name: Validate mock data
        run: |
          echo "🔍 Validating mock data..."
          scripts/pr-approval/tests/run-tests.sh --validate-mocks

  unit-tests:
    name: 'Unit Tests'
    runs-on: ubuntu-latest
    needs: validate-scripts
    strategy:
      matrix:
        test-module:
          - 'test-utils.sh'
          - 'test-pr-data.sh'
          - 'test-approval-logic.sh'
          - 'test-comment-manager.sh'
      fail-fast: false
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Set up test environment
        run: |
          echo "🔧 Setting up test environment..."
          
          # Install required tools
          sudo apt-get update
          sudo apt-get install -y jq
          
          # Install GitHub CLI
          curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
          echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
          sudo apt-get update
          sudo apt-get install -y gh
          
          # Verify installations
          jq --version
          gh --version
          
          echo "✅ Test environment ready"
      
      - name: Run unit tests
        env:
          VERBOSE_TESTS: ${{ github.event.inputs.verbose || 'false' }}
        run: |
          echo "🧪 Running unit tests for ${{ matrix.test-module }}..."
          
          # Set verbose flag if requested
          VERBOSE_FLAG=""
          if [[ "$VERBOSE_TESTS" == "true" ]]; then
            VERBOSE_FLAG="--verbose"
          fi
          
          # Run specific test module
          scripts/pr-approval/tests/run-tests.sh $VERBOSE_FLAG --test "${{ matrix.test-module }}"
      
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: unit-test-results-${{ matrix.test-module }}
          path: scripts/pr-approval/tests/output/
          retention-days: 7

  integration-tests:
    name: 'Integration Tests'
    runs-on: ubuntu-latest
    needs: validate-scripts
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Set up test environment
        run: |
          echo "🔧 Setting up test environment..."
          
          # Install required tools
          sudo apt-get update
          sudo apt-get install -y jq
          
          # Install GitHub CLI
          curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
          echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
          sudo apt-get update
          sudo apt-get install -y gh
          
          # Verify installations
          jq --version
          gh --version
          
          echo "✅ Test environment ready"
      
      - name: Run integration tests
        env:
          VERBOSE_TESTS: ${{ github.event.inputs.verbose || 'false' }}
        run: |
          echo "🧪 Running integration tests..."
          
          # Set verbose flag if requested
          VERBOSE_FLAG=""
          if [[ "$VERBOSE_TESTS" == "true" ]]; then
            VERBOSE_FLAG="--verbose"
          fi
          
          # Run integration tests
          scripts/pr-approval/tests/run-tests.sh $VERBOSE_FLAG --integration-only
      
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: integration-test-results
          path: scripts/pr-approval/tests/output/
          retention-days: 7

  all-tests:
    name: 'All Tests'
    runs-on: ubuntu-latest
    needs: validate-scripts
    if: github.event.inputs.test_type == 'all' || github.event.inputs.test_type == ''
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Set up test environment
        run: |
          echo "🔧 Setting up test environment..."
          
          # Install required tools
          sudo apt-get update
          sudo apt-get install -y jq
          
          # Install GitHub CLI
          curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
          echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
          sudo apt-get update
          sudo apt-get install -y gh
          
          # Verify installations
          jq --version
          gh --version
          
          echo "✅ Test environment ready"
      
      - name: Run all tests
        env:
          VERBOSE_TESTS: ${{ github.event.inputs.verbose || 'false' }}
        run: |
          echo "🧪 Running all tests..."
          
          # Set verbose flag if requested
          VERBOSE_FLAG=""
          if [[ "$VERBOSE_TESTS" == "true" ]]; then
            VERBOSE_FLAG="--verbose"
          fi
          
          # Run all tests
          scripts/pr-approval/tests/run-tests.sh $VERBOSE_FLAG
      
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: all-test-results
          path: scripts/pr-approval/tests/output/
          retention-days: 7

  test-summary:
    name: 'Test Summary'
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: always()
    
    steps:
      - name: Download all test results
        uses: actions/download-artifact@v4
        with:
          path: test-results/
      
      - name: Generate test summary
        run: |
          echo "📊 Test Summary" >> $GITHUB_STEP_SUMMARY
          echo "==============" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Count test results
          total_tests=0
          passed_tests=0
          failed_tests=0
          
          # Process all test result files
          find test-results/ -name "*.log" -type f | while read -r log_file; do
            if [[ -f "$log_file" ]]; then
              passed=$(grep -c "✅ PASSED" "$log_file" 2>/dev/null || echo "0")
              failed=$(grep -c "❌ FAILED" "$log_file" 2>/dev/null || echo "0")
              
              total_tests=$((total_tests + passed + failed))
              passed_tests=$((passed_tests + passed))
              failed_tests=$((failed_tests + failed))
            fi
          done
          
          echo "- **Total Tests:** $total_tests" >> $GITHUB_STEP_SUMMARY
          echo "- **Passed:** $passed_tests" >> $GITHUB_STEP_SUMMARY
          echo "- **Failed:** $failed_tests" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [[ $failed_tests -eq 0 ]]; then
            echo "🎉 **All tests passed!**" >> $GITHUB_STEP_SUMMARY
          else
            echo "💥 **Some tests failed!**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "Check the individual test artifacts for details." >> $GITHUB_STEP_SUMMARY
          fi
      
      - name: Set job status
        run: |
          # Check if any tests failed
          failed_count=0
          find test-results/ -name "*.log" -type f | while read -r log_file; do
            if [[ -f "$log_file" ]]; then
              failed=$(grep -c "❌ FAILED" "$log_file" 2>/dev/null || echo "0")
              failed_count=$((failed_count + failed))
            fi
          done
          
          if [[ $failed_count -gt 0 ]]; then
            echo "Tests failed!"
            exit 1
          else
            echo "All tests passed!"
            exit 0
          fi
