name: Deploy Australia inventory to Non Prod

on:
  push:
    branches:
      - Au-development-branch
    paths:
      - '.github/workflows/australia-inventory-dev.yml'
      - 'apps/australia/inventory/**'
      - 'libs/**'
  workflow_dispatch:
    inputs:
      commitHash:
        description: 'ًSource Commit Hash'
        required: false

env:
  # secrets.ARTIFACTORY_NPM_TOKEN comes from the "globalcommerce" org
  TRANSIENT_TOKEN: ${{ secrets.TRANSIENT_TOKEN }}
  # secrets.AZURE_* and secrets.API_KEY come from the "globalcommerce" org
  AZURE_CLIENT_ID_NP: ${{ secrets.AZURE_CLIENT_ID_NP }}
  AZURE_CLIENT_SECRET_NP: ${{ secrets.AZURE_CLIENT_SECRET_NP }}
  FASTE_TEAM_API_KEY: ${{ secrets.FASTE_TEAM_API_KEY_ANOMALY }}
  # set INPUT_TOKEN='' workaround for action bug
  INPUT_TOKEN: ''
  MODULE_NAME: australia-inventory

jobs:
  build_and_deploy_australia_inventory_dev:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Configure NPM
        run: |
          npm config set //artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/:_auth $TRANSIENT_TOKEN
          npm config set registry https://registry.npmjs.org
          npm config set @bayerit:registry=https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
          npm config set @c7:registry=https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
          npm config set @finance-acs2:registry https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
          npm config set @monsantoit:registry https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
          npm config set @element:registry https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
          npm config set @supernova:registry https://artifactory.bayer.com/artifactory/api/npm/npm-platforms-engineering/
      - run: npm ci
      - name: Build Australia inventory Module
        run: |
          npm run australia:inventory:build:np
      - name: Publish australia  Module to NP using Spectrum
        run: npx @bayerit/spectrum-cli faste publish --config apps/australia/inventory/spectrum.config.json --module-name $MODULE_NAME --module-version stable --ci --api-key $FASTE_TEAM_API_KEY --client-id $AZURE_CLIENT_ID_NP --client-secret $AZURE_CLIENT_SECRET_NP
      - uses: actions/checkout@v4
