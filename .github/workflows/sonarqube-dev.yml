name: SonarQube Scan on Development

on:
  push:
    branches:
      - development
  workflow_dispatch:

concurrency:
  cancel-in-progress: true
  group: ${{ github.workflow }}-dev-${{ github.ref }}

jobs:
  sonarqube-scan:
    runs-on: ubuntu-latest
    permissions: read-all
    steps:
      - name: Checkout Git
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones might not be enough for SonarQube

      - name: Cache Node Modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: <PERSON><PERSON> Nx
        uses: actions/cache@v4
        with:
          path: .nx/cache
          key: ${{ runner.os }}-nx-${{ hashFiles('nx.json', 'package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-nx-

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}

      - name: Install Dependencies
        run: npm ci

      - name: Run Tests to Generate Coverage
        run: npm run test:coverage

      - name: Upload Coverage Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-reports
          path: coverage/
          retention-days: 7

      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@v5.2.0
        if: always()
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
        with:
          args: >
            -Dsonar.branch.name=development
