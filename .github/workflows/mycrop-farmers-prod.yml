name: MyCrop Farmers Module Prod Deploy

on:
  workflow_dispatch:
    inputs:
      commitHash:
        description: 'Source Commit Hash'
        required: false

run-name: Deploy Farmers Module to MyCrop prod by @${{ github.actor }}

env:
  MODULE_NAME: mycrop-farmers-ui
  COMMIT_HASH: ${{ github.event.inputs.commitHash || github.sha }}

jobs:
  deploy:
    name: build-deploy
    runs-on: ubuntu-latest
    environment: nb
    steps:
      - name: Import Secrets
        uses: hashicorp/vault-action@v3.1.0
        with:
          url: https://vault.agro.services
          method: approle
          roleId: ${{ secrets.VAULT_ROLE_ID }}
          secretId: ${{ secrets.VAULT_SECRET_ID }}
          secrets: |
            secret/lepsi/prod/faste-lite lodestar_api_key | API_KEY ;
            secret/lepsi/prod/azureAD/MYCROP-PROD-SVC client_id | CLIENT_ID ;
            secret/lepsi/prod/azureAD/MYCROP-PROD-SVC client_secret | CLIENT_SECRET ;

      - name: Checkout Source Code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.COMMIT_HASH }}
          show-progress: false

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}
          VAULT_PATH: secret/lepsi/common/artifactory
          AUTH_TOKEN_KEY: access_token
          is_prod: true

      - name: Build Farmers Module
        run: |
          npm run mycrop:farmers:build

      - name: Get Approved Commit
        run: |
          npx @bayerit/spectrum-cli deploytool list commit --client-id ${{ env.CLIENT_ID }} --client-secret ${{ env.CLIENT_SECRET }} > approved-commit.txt
          echo '::set-output name=COMMIT_HASH::$(cat approved-commit.txt)'
        id: approved-commit

      - name: Compare Commit Hash
        run: |
          echo Approved Hash: ${{ steps.approved-commit.outputs.COMMIT_HASH }}
          echo Commit Hash: ${{ env.COMMIT_HASH }}

      - name: Spectrum Deploy
        run: |
          npx @bayerit/spectrum-cli faste publish --config apps/mycrop/farmers/spectrum.config.json --module-name ${{ env.MODULE_NAME }} --module-version stable --cache --prod --ci --api-key ${{ env.API_KEY }} --client-id ${{ env.CLIENT_ID }} --client-secret ${{ env.CLIENT_SECRET }}
        id: spectrum-deploy
