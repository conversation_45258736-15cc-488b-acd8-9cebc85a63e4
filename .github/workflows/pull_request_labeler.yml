name: GC Agency Pull Request Labeler

on:
  merge_group:
    types: [checks_requested]
  pull_request_target:
    types: [opened, synchronize, reopened, ready_for_review]

concurrency:
  cancel-in-progress: true
  group: ${{ github.workflow }}-${{ github.ref }}

jobs:
  Label-PR:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      pull-requests: write

    steps:
      - name: Checkout Git
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - id: label-the-PR
        uses: actions/labeler@v5
