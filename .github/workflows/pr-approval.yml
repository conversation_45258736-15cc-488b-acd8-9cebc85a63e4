name: 'PR Approval Checks'

on:
  pull_request:
    branches: [development]
    types: [opened, synchronize, labeled, unlabeled, edited, ready_for_review]

concurrency:
  group: pr-${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  pr-approval:
    name: 'PR Approval and Merge'
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
      contents: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          # Fetch full history for better context
          fetch-depth: 0

      - name: Run PR approval workflow
        env:
          # GitHub context
          REPO: ${{ github.repository }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

          # Configuration (can be overridden here if needed)
          LOG_LEVEL: 'INFO'

          # All other configuration is loaded from scripts/pr-approval/config/defaults.env
          # You can override specific values here if needed:
          # BUG_FIX_APPROVALS: '1'
          # FEATURE_APPROVALS: '2'
          # MERGE_METHOD: 'squash'
          # ALLOWED_BASE_BRANCHES: 'development'
          # READY_FOR_MERGE_LABEL: 'ready-for-merge'

        run: |
          # Make script executable (in case it's not already)
          chmod +x scripts/pr-approval/main.sh

          # Run the PR approval workflow
          scripts/pr-approval/main.sh
