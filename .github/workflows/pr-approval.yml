name: 'PR Approval Checks (gh)'

on:
  pull_request:
    branches: [development]
    types: [opened, synchronize, labeled, unlabeled, edited, ready_for_review]

concurrency:
  group: pr-${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  check-pr:
    permissions:
      issues: write
      pull-requests: write
    runs-on: ubuntu-latest
    env:
      BUG_FIX_LABEL: 'bug-fix'
      BUG_FIX_APPROVALS: '1'
      FEATURE_APPROVALS: '2'
      MERGE_METHOD: 'squash' # squash | merge | rebase
      ALLOWED_BASE_BRANCHES: 'development'
      READY_FOR_MERGE_LABEL: 'ready-for-merge'
      STATUS_COMMENT_TAG: '<!-- pr-approval-checks-gh -->'
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Set up repo/PR context
        id: ctx
        run: |
          set -euo pipefail

          # Basic context
          REPO="${{ github.repository }}"
          PR_NUMBER="${{ github.event.pull_request.number }}"
          echo "repo=$REPO" >> "$GITHUB_OUTPUT"
          echo "pr=$PR_NUMBER" >> "$GITHUB_OUTPUT"

      - name: Quick guard checks (draft, base branch)
        run: |
          set -euo pipefail
          REPO="${{ steps.ctx.outputs.repo }}"
          PR="${{ steps.ctx.outputs.pr }}"

          # Fetch PR JSON
          PR_JSON="$(gh pr view "$PR" --repo "$REPO" --json number,state,isDraft,baseRefName,labels,title,mergeable,mergeStateStatus)"
          IS_DRAFT="$(echo "$PR_JSON" | jq -r '.isDraft')"
          BASE_REF="$(echo "$PR_JSON" | jq -r '.baseRefName')"
          LABELS="$(echo "$PR_JSON" | jq -r '[.labels[].name] | join(",")')"

          # Draft PRs are skipped
          if [ "$IS_DRAFT" = "true" ]; then
            echo "Draft PR; skipping checks."
            exit 0
          fi

          # Base branch filter
          IFS=',' read -ra ALLOWED <<< "${ALLOWED_BASE_BRANCHES}"
          allowed_ok="false"
          for b in "${ALLOWED[@]}"; do
            b_trim="$(echo "$b" | xargs)"
            if [ "$b_trim" = "$BASE_REF" ]; then
              allowed_ok="true"
              break
            fi
          done

          if [ "$allowed_ok" != "true" ]; then
            echo "Base branch '$BASE_REF' not in [${ALLOWED_BASE_BRANCHES}]; skipping."
            exit 0
          fi

      - name: Compute approvals (latest review per reviewer)
        id: approvals
        run: |
          set -euo pipefail
          REPO="${{ steps.ctx.outputs.repo }}"
          PR="${{ steps.ctx.outputs.pr }}"

          # List all reviews; gh paginates automatically with --limit
          REVIEWS_JSON="$(gh api \
            repos/$REPO/pulls/$PR/reviews \
            --paginate --jq '.')"

          # Map latest review per user, then count APPROVED
          # jq script:
          # group by user.login, pick last by submitted_at/created_at, count state=="APPROVED"
          COUNT="$(echo "$REVIEWS_JSON" | jq -r '
            group_by(.user.login) |
            map(
              sort_by(.submitted_at // .created_at) | last
            ) |
            map(select(.state == "APPROVED")) |
            length
          ')"

          echo "approvals=$COUNT" >> "$GITHUB_OUTPUT"

      - name: Determine PR type and required approvals
        id: req
        run: |
          set -euo pipefail
          REPO="${{ steps.ctx.outputs.repo }}"
          PR="${{ steps.ctx.outputs.pr }}"
          BUG_LABEL="${BUG_FIX_LABEL}"

          PR_JSON="$(gh pr view "$PR" --repo "$REPO" --json labels)"
          HAS_BUG="$(echo "$PR_JSON" | jq -r --arg L "$BUG_LABEL" '[.labels[].name] | index($L) | if . == null then "false" else "true" end')"

          BUG_REQ="${BUG_FIX_APPROVALS:-1}"
          FEAT_REQ="${FEATURE_APPROVALS:-2}"
          if [ "$HAS_BUG" = "true" ]; then
            REQ="$BUG_REQ"
            TYPE="Bug-fix"
          else
            REQ="$FEAT_REQ"
            TYPE="Feature"
          fi

          echo "type=$TYPE" >> "$GITHUB_OUTPUT"
          echo "required=$REQ" >> "$GITHUB_OUTPUT"
          echo "is_bug=$HAS_BUG" >> "$GITHUB_OUTPUT"

      - name: Upsert status comment (helper)
        id: comment_helper
        shell: bash
        run: |
          cat > upsert_comment.sh << 'EOF'
          #!/usr/bin/env bash
          set -euo pipefail
          REPO="$1"
          PR="$2"
          BODY="$3"
          TAG="$4"

          # Build tagged body safely
          TAGGED_BODY="$(printf "%s\n\n%s\n" "$BODY" "$TAG")"

          # Fetch comments and find an existing tagged one
          COMMENTS_JSON="$(gh api "repos/$REPO/issues/$PR/comments")"

          EXISTING_ID="$(printf "%s" "$COMMENTS_JSON" \
            | jq -r --arg TAG "$TAG" '
                [ .[] | select(.body != null and (.body | contains($TAG))) ][0].id // empty
              ')"

          if [ -n "${EXISTING_ID:-}" ]; then
            gh api \
              --method PATCH \
              "repos/$REPO/issues/comments/$EXISTING_ID" \
              -f body="$TAGGED_BODY" >/dev/null
          else
            gh api \
              --method POST \
              "repos/$REPO/issues/$PR/comments" \
              -f body="$TAGGED_BODY" >/dev/null
          fi
          EOF
          chmod +x upsert_comment.sh

      - name: Gate on approval count; post/update comment if insufficient
        id: gate
        run: |
          set -euo pipefail
          REPO="${{ steps.ctx.outputs.repo }}"
          PR="${{ steps.ctx.outputs.pr }}"
          TYPE="${{ steps.req.outputs.type }}"
          REQUIRED="${{ steps.req.outputs.required }}"
          APPROVALS="${{ steps.approvals.outputs.approvals }}"
          TAG="${STATUS_COMMENT_TAG}"

          if [ "$APPROVALS" -lt "$REQUIRED" ]; then
            ./upsert_comment.sh "$REPO" "$PR" \
              "${TYPE} PR requires at least ${REQUIRED} approval(s); currently has ${APPROVALS}." \
              "$TAG"
            echo "sufficient=false" >> "$GITHUB_OUTPUT"
            exit 0
          else
            ./upsert_comment.sh "$REPO" "$PR" \
              "${TYPE} PR has sufficient approvals (${APPROVALS}/${REQUIRED})." \
              "$TAG"
            echo "sufficient=true" >> "$GITHUB_OUTPUT"
          fi

      - name: Optionally add ready-for-merge label for bug-fix
        if: steps.gate.outputs.sufficient == 'true' && steps.req.outputs.is_bug == 'true'
        run: |
          set -euo pipefail
          REPO="${{ steps.ctx.outputs.repo }}"
          PR="${{ steps.ctx.outputs.pr }}"
          LABEL="${READY_FOR_MERGE_LABEL}"

          if [ -n "$LABEL" ]; then
            # Add label if not present
            LABELS="$(gh pr view "$PR" --repo "$REPO" --json labels --jq '[.labels[].name]')"
            HAS_LABEL="$(echo "$LABELS" | jq -r --arg L "$LABEL" 'index($L) | if . == null then "false" else "true" end')"
            if [ "$HAS_LABEL" = "false" ]; then
              gh issue edit "$PR" --repo "$REPO" --add-label "$LABEL"
            fi
          fi

      - name: Merge if mergeable and approvals sufficient
        if: steps.gate.outputs.sufficient == 'true'
        run: |
          set -euo pipefail
          REPO="${{ steps.ctx.outputs.repo }}"
          PR="${{ steps.ctx.outputs.pr }}"
          REQUIRED="${{ steps.req.outputs.required }}"
          APPROVALS="${{ steps.approvals.outputs.approvals }}"
          TAG="${STATUS_COMMENT_TAG}"
          METHOD="${MERGE_METHOD}"

          # Poll mergeable up to 5 times
          attempts=0
          MERGEABLE=""
          MERGE_STATE=""
          STATE="open"
          while [ "$attempts" -lt 5 ]; do
            PR_JSON="$(gh pr view "$PR" --repo "$REPO" --json state,mergeable,mergeStateStatus)"
            STATE="$(echo "$PR_JSON" | jq -r '.state')"
            MERGEABLE="$(echo "$PR_JSON" | jq -r '.mergeable')"
            MERGE_STATE="$(echo "$PR_JSON" | jq -r '.mergeStateStatus')"

            if [ "$MERGEABLE" != "UNKNOWN" ]; then
              break
            fi

            attempts=$((attempts + 1))
            sleep 2
          done

          if [ "$STATE" != "OPEN" ]; then
            ./upsert_comment.sh "$REPO" "$PR" \
              "Approvals met (${APPROVALS}/${REQUIRED}) but PR is not open." \
              "$TAG"
            exit 0
          fi

          if [ "$MERGEABLE" != "MERGEABLE" ]; then
            ./upsert_comment.sh "$REPO" "$PR" \
              "Approvals met (${APPROVALS}/${REQUIRED}) but PR is not mergeable (state: ${MERGE_STATE})." \
              "$TAG"
            exit 0
          fi

          # Attempt merge via gh
          set +e
          gh pr merge "$PR" --repo "$REPO" --"$METHOD" --delete-branch --auto
          rc=$?
          set -e

          if [ $rc -ne 0 ]; then
            ./upsert_comment.sh "$REPO" "$PR" \
              "Approvals met (${APPROVALS}/${REQUIRED}) but merge failed. Please check required checks/branch status." \
              "$TAG"
            exit 0
          else
            echo "Merged PR #$PR with ${APPROVALS} approval(s) via ${METHOD}."
          fi
