name: 'Chromatic'

on:
  push:
    branches: [development]

  workflow_dispatch:
    inputs:
      commitHash:
        description: 'Source Commit Hash'
        required: false

run-name: Build Storybook then Deploy to Chromatic by @${{ github.actor }}

env:
  COMMIT_HASH: ${{ github.event.inputs.commitHash || github.sha }}

jobs:
  chromatic-deployment:
    runs-on: ubuntu-latest
    permissions: read-all
    steps:
      - name: Checkout Source Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ env.COMMIT_HASH }}
          show-progress: false

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}

      - name: Publish to Chromatic
        uses: chromaui/action@latest
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          buildScriptName: build:storybook
          zip: true
          onlyChanged: true
          exitOnceUploaded: true
          skipUpdateCheck: true
          skip: '@(renovate/**|dependabot/**)'
          exitZeroOnChanges: true
          autoAcceptChanges: 'development'
