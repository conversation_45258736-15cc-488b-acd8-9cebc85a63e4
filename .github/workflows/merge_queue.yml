name: GC Agency Merge Queue

on:
  merge_group:

jobs:
  lint:
    runs-on: ubuntu-latest
    permissions: read-all
    steps:
      - name: Checkout Git
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}

      - name: Get Affected Projects
        run: npx nx show projects --affected --json | npx json

      - name: Run Lint
        run: npm run lint:ci
        env:
          NODE_ENV: production
          CI: true

  build:
    runs-on: ubuntu-latest
    permissions: read-all
    steps:
      - name: Checkout Git
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}

      - name: Get Affected Projects
        run: npx nx show projects --affected --json | npx json

      - name: Run Build
        run: npm run build:ci
        env:
          NODE_ENV: production
          CI: true

  test:
    runs-on: ubuntu-latest
    permissions: read-all
    steps:
      - name: Checkout Git
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}

      - name: Get Affected Projects
        run: npx nx show projects --affected --json | npx json

      # TODO: Run e2e tests
      - name: Run e2e tests
        run: echo "Running e2e tests"
