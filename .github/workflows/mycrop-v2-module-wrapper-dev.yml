name: MyCrop-V2 Module Wrapper Module NP Deploy

on:
  push:
    branches: development
    paths:
      - '.github/workflows/mycrop-v2-module-wrapper-dev.yml'
      - 'apps/mycrop-v2/module-wrapper/**'
      - 'libs/**'

  workflow_dispatch:
    inputs:
      commitHash:
        description: 'Source Commit Hash'
        required: false

run-name: Deploy Module Wrapper Module to MyCrop-V2 non-prod by @${{ github.actor }}

env:
  MODULE_NAME: mycrop-v2-module-wrapper
  COMMIT_HASH: ${{ github.event.inputs.commitHash || github.sha }}

jobs:
  deploy:
    name: build-deploy
    runs-on: ubuntu-latest
    environment: nb
    steps:
      - name: Import Secrets
        uses: hashicorp/vault-action@v3.1.0
        with:
          url: https://vault.agro.services
          method: approle
          roleId: ${{ secrets.VAULT_ROLE_ID }}
          secretId: ${{ secrets.VAULT_SECRET_ID }}
          secrets: |
            secret/lepsi/non-prod/faste-lite lodestar_api_key | API_KEY ;
            secret/lepsi/non-prod/azureAD/MYCROP-NP-SVC client_id | CLIENT_ID ;
            secret/lepsi/non-prod/azureAD/MYCROP-NP-SVC client_secret | CLIENT_SECRET ;

      - name: Checkout Source Code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.COMMIT_HASH }}
          show-progress: false

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}
          VAULT_PATH: secret/lepsi/common/artifactory
          AUTH_TOKEN_KEY: access_token

      - name: Build Module Wrapper Module
        run: |
          npm run mycrop:v2:module-wrapper:build:np

      - name: Spectrum Deploy
        run: |
          npx @bayerit/spectrum-cli faste publish --config apps/mycrop-v2/module-wrapper/spectrum.config.json --module-name ${{ env.MODULE_NAME }} --module-version stable --cache --ci --api-key ${{ env.API_KEY }} --client-id ${{ env.CLIENT_ID }} --client-secret ${{ env.CLIENT_SECRET }}
        id: spectrum-deploy
