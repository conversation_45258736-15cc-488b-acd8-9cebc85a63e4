name: GC Agency Pull Request Label Check

on:
  pull_request:
    types: [labeled, unlabeled, synchronize]
    branches: [development]

concurrency:
  cancel-in-progress: true
  group: ${{ github.workflow }}-${{ github.ref }}

jobs:
  changed_files:
    runs-on: ubuntu-latest
    if: ${{ contains(github.event.pull_request.labels.*.name, 'chromatic') }}
    permissions:
      actions: read
      contents: read
      pull-requests: read
    outputs:
      all_changed_files: ${{ steps.changed-files.outputs.all_changed_files }}
      any_changed: ${{ steps.changed-files.outputs.any_changed }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          show-progress: false

      - name: Get changed files
        id: changed-files
        uses: tj-actions/changed-files@2f7c5bfce28377bc069a65ba478de0a74aa0ca32 # 46.0.1
        with:
          files: libs/**

  chromatic:
    runs-on: ubuntu-latest
    needs: [changed_files]
    steps:
      - name: Checkout Git
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          show-progress: false

      - name: Setup Environment
        uses: ./.github/actions/setup-env
        with:
          VAULT_ROLE_ID: ${{ secrets.VAULT_ROLE_ID }}
          VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}

      - name: Publish to Chromatic
        uses: chromaui/action@latest
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          buildScriptName: build:storybook
          zip: true
          onlyChanged: true
          exitOnceUploaded: true
          skipUpdateCheck: true
          skip: ${{ needs.changed_files.outputs.any_changed == 'false' }}
          exitZeroOnChanges: ${{ needs.changed_files.outputs.any_changed == 'false' }}
