name: Cleanup GitHub runner caches on closed pull requests
on:
  pull_request:
    types:
      - closed
  workflow_dispatch:
    inputs:
      action:
        description: 'Action to perform'
        required: true
        default: 'cleanup-dev'
        type: choice
        options:
          - cleanup-dev
          - wipe-cache

jobs:
  cleanup-pr:
    runs-on: ubuntu-latest
    if: github.event_name != 'workflow_dispatch'
    permissions:
      actions: write
    steps:
      - name: Cleanup PR caches
        run: |
          echo "Fetching list of cache keys for PR ${{ github.event.pull_request.number }}"
          cacheKeysForPR=$(gh cache list --ref $BRANCH --limit 100 --json id --jq '.[].id')

          ## Setting this to not fail the workflow while deleting cache keys.
          set +e
          echo "Deleting caches..."
          for cacheKey in $cacheKeysForPR
          do
              gh cache delete $cacheKey
          done
          echo "Done"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_REPO: ${{ github.repository }}
          BRANCH: refs/pull/${{ github.event.pull_request.number }}/merge

  cleanup-dev:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'cleanup-dev'
    permissions:
      actions: write
    steps:
      - name: Cleanup development branch caches
        run: |
          echo "Fetching list of cache keys for development branch"
          cacheKeysForDev=$(gh cache list --ref refs/heads/development --limit 100 --json id --jq '.[].id')

          ## Setting this to not fail the workflow while deleting cache keys.
          set +e
          echo "Deleting caches..."
          for cacheKey in $cacheKeysForDev
          do
              gh cache delete $cacheKey
          done
          echo "Done"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_REPO: ${{ github.repository }}

  wipe-cache:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'wipe-cache'
    permissions:
      actions: write
    steps:
      - name: Wipe all caches
        run: |

          ## Setting this to not fail the workflow while deleting cache keys.
          set +e
          echo "Deleting all caches..."
          gh cache delete --all
          echo "Done"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_REPO: ${{ github.repository }}
