# Test Coverage Report and Weak Areas – GC Agency UI Monorepo

## Testing & Coverage Overview

- **Testing setup:** The monorepo uses <PERSON>st for unit testing across all apps and libraries, with Nx orchestrating test runs. There is SonarQube integration for code quality and coverage reporting (see `docs/ci-cd.md`).
- **Test scripts:** Each library/app README documents how to run its tests (e.g., `nx test [project]`). There is a top-level script for running all tests: `npm run test`.
- **Observed tests:** There are meaningful unit/integration tests for utility libraries (e.g., `libs/shared/utils/src/format.test.ts`, `libs/shared/utils/src/search/searchUtils.spec.ts`) and for some React components (e.g., `libs/shared/components/src/ui-common/list/List.spec.tsx`).

---

## Pros

- **Libraries and features** have explicit test scripts and some coverage (utilities, shared components).
- **Jest and SonarQube** integration ensures test execution and code quality gates in CI.
- **PR review best practices** recommend checking for test coverage before merging.

---

## Weak Areas & Potential Gaps

1. **Superficial README test instructions:**

   - Many library READMEs only state how to run tests but do not specify coverage expectations or actual coverage status.
   - No explicit coverage reports or badges are visible in the documentation.

2. **Spotty or Missing Specs:**

   - Some core libraries (e.g., `libs/features/common/quotes`, `libs/features/common/farmers`, `libs/features/common/location-picker`, and others) only mention how to run tests, but do not show or reference actual test files or coverage stats.
   - There is no evidence of tests for higher-level business logic, API client libraries, or E2E flows.

3. **Component Coverage:**

   - Shared UI components (e.g., `libs/shared/components`) have some tests (e.g., `List.spec.tsx`) but coverage for all components is unclear.
   - Storybook is mentioned as a future improvement for component documentation and testing, but appears incomplete.

4. **Flaky/Commented Out Tests:**

   - The changelog documents instances where flaky tests (e.g., table interaction tests) were commented out or fixed, which could indicate ongoing reliability issues.

5. **Lack of E2E/Integration Tests:**

   - No clear mention or evidence of Cypress, Playwright, or other E2E frameworks for full application flows.
   - No explicit integration test coverage for how micro-frontends interact.

6. **Coverage Enforcement:**

   - While SonarQube is enabled, there is no documentation of enforced minimum coverage thresholds at the PR or branch level.

7. **Documentation Gaps:**
   - No coverage summary or badge in the main README or feature docs.
   - No documented standard for what constitutes "adequate" test coverage for features or components.

---

## Recommendations

1. **Generate and Publish Coverage Reports**

   - Ensure all Jest runs output coverage (e.g., `coverage/lcov-report/index.html`).
   - Add scripts and CI steps to generate and upload coverage summaries.

2. **Surface Coverage in Docs**

   - Add a coverage badge to the main `README.md`.
   - Include a summary table of coverage per app/library in documentation.

3. **Identify and Fill Gaps**

   - Audit each app and library for missing or superficial tests, especially for:
     - Business logic functions
     - API client interactions
     - UI components (visual and interaction tests)
     - Edge cases and error handling

4. **Expand E2E Coverage**

   - Integrate/expand E2E tests (Cypress/Playwright) for user journeys and micro-frontend integration scenarios.

5. **Automate Threshold Enforcement**

   - Set minimum coverage thresholds in Jest and SonarQube.
   - Make PRs fail if thresholds are not met.

6. **Document Coverage Standards**
   - Update development docs to specify coverage requirements (e.g., "80%+ branch and statement coverage for all new code").

---

## Example: Test Coverage Audit Table

| Library/App                     | Has Tests? | Coverage Reported? | Gaps Noted                    |
| ------------------------------- | :--------: | :----------------: | ----------------------------- |
| shared/utils                    |     ✅     | ❓ (not surfaced)  | No coverage badge/summary     |
| shared/components               |     ✅     |         ❓         | Incomplete component coverage |
| features/common/quotes          |     ❓     |         ❌         | Only README mentions tests    |
| features/common/farmers         |     ❓     |         ❌         | Only README mentions tests    |
| features/common/location-picker |     ❓     |         ❌         | Only README mentions tests    |
| features/nb/dashboard           |     ❓     |         ❌         | Only README mentions tests    |
| API libs (client, rtk)          |     ❓     |         ❌         | No evidence of tests          |
| E2E flows                       |     ❌     |         ❌         | No mention or evidence        |

Legend: ✅ = Present, ❌ = Absent, ❓ = Unclear

---

## Next Steps

1. Run `npm run test:coverage` and review coverage output.
2. Audit each app/lib for missing or superficial test files.
3. Add missing unit, integration, and E2E tests as needed.
4. Update documentation to communicate current and target coverage levels.

---

**If you want a deep-dive or sample test implementation for a specific area, let me know!**
