# Test Coverage Report & Recommendations

## 1. Current State

- **Overall coverage is very low:**
  - Statements: **14.12%**
  - Branches: **6.53%**
  - Functions: **11.32%**
  - Lines: **14.55%**
- **Test Suites:** 23/23 passed, but only 106 tests in total for a large codebase.
- **Many files, especially in `libs/features/common` and `libs/shared/components`, have 0% coverage.**
- **Most coverage comes from app entry points and a few utility files.**

---

## 2. Where to Focus First

### A. Critical Gaps (0% Coverage)

- **UI/Feature Components:**
  - `libs/features/common/inventory/src/components/seed-product/seed-product-details/SeedProductDetailsCards.tsx` (and related files)
  - `libs/features/common/inventory/src/components/returns/`
  - `libs/features/common/orders/src/components/`
  - `libs/features/common/quotes/src/components/`
  - `libs/shared/components/src/features/`
  - `libs/shared/components/src/ui-common/`
- **Hooks:**
  - Many custom hooks in `libs/features/common/inventory/src/hooks/`, `libs/features/common/orders/src/hooks/`, and `libs/hooks/src/` have 0% or near-0% coverage.
- **Store/State:**
  - Some slices and selectors have partial or no coverage.

### B. High-Impact, High-Complexity Areas

- **Business Logic in API/RTK Query files:**
  - `libs/api/rtk-queries/src/` (most files <25% coverage)
- **Utility Functions:**
  - `libs/shared/utils/src/` (most files <30% coverage)

---

## 3. Recommended Prioritization

### Step 1: Cover Core Business Logic

- **Start with files in `libs/api/rtk-queries/src/` and `libs/shared/utils/src/`**
  - These files are likely to contain logic that affects many features.
  - Add unit tests for each function, especially those with conditionals/branches.

### Step 2: Cover Feature Components

- **Focus on key UI components in `libs/features/common/inventory/src/components/` and `libs/shared/components/src/features/`**
  - Add basic render tests (using React Testing Library).
  - Add interaction tests for user actions and state changes.

### Step 3: Cover Custom Hooks

- **Test hooks in `libs/features/common/inventory/src/hooks/` and `libs/hooks/src/`**
  - Use `@testing-library/react-hooks` or similar.
  - Focus on hooks with side effects or complex logic.

### Step 4: Cover Redux Slices/Selectors

- **Test reducers and selectors in `libs/redux-store/src/slices/` and `libs/redux-store/src/selectors/`**
  - Add tests for all state transitions and selector outputs.

---

## 4. Quick Wins

- **Files with some coverage but many uncovered lines:**
  - `libs/api/client/src/axiosBaseQuery.ts` (69% statements, but some lines missed)
  - `libs/features/common/farmers/src/hooks/useColumns.ts` (86% statements, but some branches missed)
  - **Finish coverage for these files for fast % gains.**

---

## 5. General Coverage Improvement Tips

- **Write tests for all new code/features.**
- **Add at least one test for every exported function/component.**
- **Use coverage reports to identify and target uncovered lines/branches.**
- **Refactor untestable code (e.g., tightly coupled logic/UI) to improve testability.**

---

## 6. Summary Table: Where to Focus

| Area/Folder                                      | Coverage | Priority | Why?                        |
| ------------------------------------------------ | -------- | -------- | --------------------------- |
| `libs/api/rtk-queries/src/`                      | <25%     | High     | Core business logic         |
| `libs/shared/utils/src/`                         | <30%     | High     | Used by many features       |
| `libs/features/common/inventory/src/components/` | 0-10%    | High     | User-facing, critical flows |
| `libs/features/common/orders/src/components/`    | 0%       | High     | User-facing, critical flows |
| `libs/features/common/quotes/src/components/`    | 0%       | High     | User-facing, critical flows |
| `libs/shared/components/src/features/`           | 0-10%    | High     | Shared UI, many consumers   |
| `libs/features/common/inventory/src/hooks/`      | 0-5%     | Medium   | Custom logic, state         |
| `libs/hooks/src/`                                | 0-35%    | Medium   | Custom logic, state         |
| `libs/redux-store/src/slices/`                   | 0-45%    | Medium   | State management            |

---

## 7. Next Steps

- **Pick one high-priority area (e.g., `libs/api/rtk-queries/src/`) and add tests for uncovered files/functions.**
- **After each round, re-run coverage and re-prioritize.**
- **If you want a more detailed file-by-file plan, specify the area to start with.**

---

**Would you like a detailed plan for a specific folder or file next?**
Or should I suggest a concrete test file to add as a first step?
