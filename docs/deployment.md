# Deployment Guide

This document outlines the procedures and best practices for deploying applications from the GC Agency UI Monorepo to various environments.

## Deployment Architecture

Our applications are deployed using the following architecture:

1. **Development Environment**:

   - Automatic deployments from feature branches
   - Used for development and testing
   - Accessible to internal team members

2. **Non-Production Environment**:

   - Deployment from main/master branch
   - Used for QA, UAT, and demonstration
   - Accessible to stakeholders and testing teams

3. **Production Environment**:
   - Deployment from release tags
   - Used by end-users
   - Subject to strict approval process

## Deployment Workflow

### Development Deployment

1. **Create a PR**:

   - Push your feature branch
   - Create a Pull Request to the main branch
   - CI/CD will automatically build and deploy to the development environment

2. **Review Deployment**:
   - Access the deployed application at the development URL
   - Verify that your changes work as expected
   - Share the URL with reviewers if needed

### Non-Production Deployment

1. **Merge to Main**:

   - After PR approval, merge your changes to the main branch
   - CI/CD will automatically deploy to the non-production environment

2. **Validation**:
   - Access the non-production environment to validate changes
   - Perform testing according to the testing strategy
   - Get stakeholder approval if required

### Production Deployment

1. **Create a Release**:

   - Tag the validated commit with a version number
   - Follow semver (major.minor.patch) versioning
   - Use special suffixes for specific channels (mycrop, seedsman, etc.)

2. **Approval Process**:

   - Submit release documentation
   - Get approval from product owner
   - Verify all tests are passing

3. **Deploy to Production**:
   - CI/CD will deploy the tagged version to production
   - Monitor the deployment process
   - Verify the production deployment

## Deployment Commands

### Manual Build Commands (if needed)

```bash
# Build all applications
npm run build:np

# Build a specific application
nx build [app-name] --configuration=production
```

### Spectrum FASTE Deployment

Our deployment uses Spectrum FASTE. Key commands:

```bash
# Deploy to non-production
spectrum deploy --env=np --app=[app-name]

# Deploy to production
spectrum deploy --env=prod --app=[app-name]
```

## Environment Configuration

Each environment has specific configurations managed through:

1. **Environment Files**:

   - `environment.ts` - Local development
   - `environment.np.ts` - Non-production
   - `environment.prod.ts` - Production

2. **Environment Variables**:
   - Sensitive information is managed through HashiCorp Vault
   - CI/CD pipelines inject environment-specific variables

## Rollback Procedure

If issues are detected after deployment:

1. **Identify the Issue**:

   - Confirm the issue and its severity
   - Document the problem

2. **Rollback Decision**:

   - For critical issues, proceed with rollback
   - For minor issues, consider a fix-forward approach

3. **Perform Rollback**:

   - Deploy the previous stable version
   - Verify the rollback resolved the issue
   - Communicate the rollback to stakeholders

4. **Post-Rollback Actions**:
   - Investigate the root cause
   - Fix the issue in a new branch
   - Follow the regular deployment process for the fix

## Monitoring Deployments

After deployment, monitor:

1. **Application Health**:

   - Check logs for errors
   - Monitor performance metrics
   - Verify critical flows are working

2. **User Feedback**:
   - Monitor support channels
   - Check for user-reported issues
   - Address critical feedback promptly

## Deployment Schedule

- **Regular Deployments**: Scheduled bi-weekly
- **Hotfixes**: As needed, following expedited approval process
- **Major Releases**: Scheduled quarterly, with extended testing period
