# GC Agency UI Monorepo Documentation

Welcome to the comprehensive documentation for the GC Agency UI Monorepo. This overview provides a guide to all available documentation resources in the project.

## Documentation Structure

Our documentation is organized into several key sections:

| Document                                    | Purpose                                                                                |
| ------------------------------------------- | -------------------------------------------------------------------------------------- |
| [Architecture Overview](architecture.md)    | Describes the monorepo structure, application organization, and technical architecture |
| [Development Guide](development.md)         | Setup instructions, local development workflow, and best practices                     |
| [Deployment Guide](deployment.md)           | Procedures for deploying applications across environments                              |
| [Contributing Guidelines](contributing.md)  | How to contribute to the project, including PR process                                 |
| [CI/CD Workflows](ci-cd.md)                 | Information about our continuous integration and deployment processes                  |
| [General Guidelines](general-guidelines.md) | Team workflows, PR reviews, and communication standards                                |

## Key Concepts

The GC Agency UI Monorepo is built on several key concepts:

1. **Monorepo Architecture**: All applications and shared libraries are managed in a single repository using Nx.
2. **Micro-frontends**: Independent applications for different business domains (Home, Farmers, Quotes, Orders, etc.).
3. **Shared Libraries**: Common components, utilities, and assets reused across applications.
4. **Channel-specific Applications**: Applications customized for different channels and brands.

## Quick References

- **Application Ports**:

  - Home: 3001
  - Farmers: 3002
  - Quote: 3003
  - Orders: 3004
  - Advance Application: 3007

- **Common Commands**:

  ```bash
  # Build all applications
  npm run build:np

  # Run tests
  npm run test

  # Lint the codebase
  npm run lint
  ```

## Getting Help

If you need further assistance:

- Review the specific documentation linked above
- Contact the development team
- Check the Teams channels for discussions and parking lot items
