# Development Guide

This guide covers the setup process, local development workflow, and best practices for developing applications in the GC Agency UI Monorepo.

## Prerequisites

Before starting development, ensure you have the following installed:

- **Node.js v20**: The required Node.js version for this project
- **Git**: For version control
- **Local HTTPS certificates**: Required for local development (see setup below)

## Initial Setup

1. **Clone the Repository**:

   ```bash
   git clone [repository-url]
   cd gc-agency-ui-monorepo
   ```

2. **Install Dependencies**:

   ```bash
   npm install
   ```

3. **Set Up Local HTTPS Certificates**:

   We use HTTPS for local development. Follow these steps to set up certificates:

   a. Install mkcert:

   ```bash
   # On macOS
   brew install mkcert

   # On Windows (with chocolatey)
   choco install mkcert
   ```

   b. Generate and install certificates:

   ```bash
   mkcert -install
   mkcert localhost
   ```

   c. Place the generated certificates in the appropriate location (defined in your environment configuration)

## Running Applications Locally

Start individual applications using npm:

```bash
# Start Farmers Portal
npm run channel:farmers:faste

# Start Quote Portal
npm run channel:quotes:faste

# Start Orders Portal
npm run channel:orders:faste

# Start Inventory Portal
npm run channel:inventory:faste

# Start Reporting Portal
npm run channel:reporting:faste
```

Run all applications at once:

```bash
npm run channel:all:faste
```

## Connect to application within dev environment

```bash
sessionStorage.setItem("channel-farmers", "https://localhost:3002");
sessionStorage.setItem("channel-quotes", "https://localhost:3003");
sessionStorage.setItem("channel-orders", "https://localhost:3004");
sessionStorage.setItem("channel-inventory", "https://localhost:3005");
sessionStorage.setItem("channel-reporting", "https://localhost:3006");
sessionStorage.setItem("channel-advance-application", "https://localhost:3007");
```

## Remove connection within dev environment

```bash
sessionStorage.removeItem("channel-<portal-name>");
```

## Development Workflow

1. **Create a Branch**:

   ```bash
   git checkout -b [taskNumber]-[appName]-[optionalDescription]
   ```

   Example: `GC-123-channel-home-login-fix`

2. **Development Best Practices**:

   - Follow the TypeScript coding standards
   - Use shared components from the libraries whenever possible
   - Write unit tests for all new features
   - Keep components small and focused on a single responsibility

3. **Testing Your Changes**:

   ```bash
   # Run unit tests
   nx test [app-name]

   # Run linting
   nx lint [app-name]

   # Run e2e tests (if available)
   nx e2e [app-name]
   ```

4. **Commit Changes**:

   ```bash
   git add .
   git commit -m "[type]: [description]"
   ```

   Use semantic commit types (feat, fix, docs, style, refactor, test, chore)

## Working with Shared Libraries

When developing shared components:

1. **Creating Components**:

   ```bash
   nx g @nrwl/react:component MyComponent --project=shared-components
   ```

2. **Testing Components**:

   ```bash
   nx test shared-components
   ```

3. **Best Practices**:
   - Keep components agnostic of specific applications
   - Document props and usage examples
   - Consider backwards compatibility
   - When developing a new shared component, notify other teams

## Troubleshooting

Common issues and their solutions:

### Certificate Issues

If you encounter HTTPS certificate errors:

- Verify certificates are correctly installed
- Check that the certificate paths in configuration are correct
- Regenerate certificates if they've expired

### Build Errors

- Clear the Nx cache: `nx clear-cache`
- Remove node_modules and reinstall: `rm -rf node_modules && npm install`

### Port Conflicts

If you encounter port conflicts:

- Check for other running applications on the same port
- Modify the port in the application's project.json file

## Additional Development Resources

- [Nx Documentation](https://nx.dev)
- [React Best Practices](https://reactjs.org/docs/thinking-in-react.html)
- [TypeScript Guidelines](https://www.typescriptlang.org/docs/handbook/intro.html)
