# GC Agency UI Monorepo Architecture

## Overview

This repository is a monorepo built using Nx for managing multiple frontend applications and shared libraries. It contains various channel-specific modules including Farmers, Quotes, Orders, and Inventory portals for different brands (MyCrop, Seedsman, Channel).

## Project Structure

### Applications

The monorepo contains several micro-frontend applications under the `apps/` directory:

1. Channel Applications
   - Farmers (`apps/channel/farmers`)
   - Quotes (`apps/channel/quotes`)
   - Orders (`apps/channel/orders`)
   - Inventory (`apps/channel/inventory`)
   - Home (`apps/channel/home`) - Port: 3001

2. MyCrop Applications
   - Farmers (`apps/mycrop/farmers`)
   - MyCrop V2 Farmers (`apps/mycrop-v2/farmers`)

3. Seedsman Applications
   - Farmers (`apps/seedsman/farmers`)

### Libraries (`libs/`)

1. Shared Libraries
   - Components (`libs/shared/components`) - Reusable UI components
   - Assets (`libs/shared/assets`) - Shared static assets
   - Environment (`libs/shared/env`) - Environment configurations

2. API Libraries
   - Client (`libs/api/client`) - API client implementations
   - RTK Queries (`libs/api/rtk-queries`) - Redux Toolkit query definitions

## Technology Stack

1. Core Technologies
   - React (Frontend Framework)
   - Nx (Build System and Monorepo Tool)
   - Node.js v20

2. State Management & API
   - Redux Toolkit (RTK)
   - RTK Query for API calls

3. Build & Deployment
   - Spectrum CLI for module deployment
   - GitHub Actions for CI/CD
   - HashiCorp Vault for secrets management

## Deployment Architecture

### Development Pipeline

1. Pull Request Workflow
   - Automated builds
   - Unit tests
   - SonarQube analysis
   - Merge queue validation

2. Development Deployment
   - Separate workflows for each module
   - Automated deployment to non-production environment
   - Integration with Spectrum FASTE

### Production Pipeline

1. Release Process
   - Version tagging with specific suffixes (mycrop, seedsman, orders)
   - Release approval documentation
   - Commit validation

2. Production Deployment
   - Production-specific build configurations
   - Spectrum FASTE deployment with validation
   - Automated testing triggers

## Security & Configuration

1. Secret Management
   - HashiCorp Vault integration
   - Environment-specific secrets
   - Role-based access control

2. Environment Configuration
   - Support for local development with HTTPS
   - Environment-specific variables
   - Local development utilities (mkcert support)

## Development Workflow

1. Local Development
   - Individual module development servers
   - HTTPS support with local certificates
   - Environment-specific configurations

2. Code Quality
   - Pre-commit hooks (lefthook)
   - Commit message validation
   - Automated formatting and linting
   - Unit testing requirements

3. Build Process
   - Parallel builds for multiple projects
   - Affected-based building for optimization
   - Separate configurations for development and production

## Monitoring & Testing

1. Testing Strategy
   - Unit tests with Jest
   - Integration with test automation repository
   - Automated test triggers on deployment

2. Quality Assurance
   - SonarQube integration for code quality
   - Automated PR validation
   - Build and test status checks

## Future Improvements

(Based on general-guideline.md)

1. Build Optimization
   - Implement dynamic builds based on affected modules
   - Optimize NX configuration for change detection

2. Component Documentation
   - Storybook integration for all components
   - Enhanced documentation for shared components

3. Workflow Improvements
   - Automated reviewer tagging
   - Custom approval requirements
   - Resolution for simultaneous release conflicts

## Module Ports

- Home: 3001
- Farmers: 3002
- Quote: 3003
- Orders: 3004

## Notes

- The system uses a micro-frontend architecture with multiple independent modules
- Shared components and libraries ensure consistency across applications
- Deployment is managed through Spectrum FASTE with environment-specific configurations
- Strong emphasis on automated testing and quality assurance
