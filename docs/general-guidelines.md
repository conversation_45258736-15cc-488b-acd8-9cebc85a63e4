# General Guidelines

This document outlines the general guidelines for collaboration, code review, and communication within the GC Agency UI Monorepo project.

## PR Reviews

### Review Assignment

- **Based on Impact**:

  - For minor changes: Tag your team members only
  - For significant changes: Tag both teams
  - For shared component changes: Always tag both teams

- **Required Approvals**:

  - At least one approval from your team
  - One approval from the other team for shared code changes

- **Review Timeline**:

  - Start by tagging team members with a link to the PR in the common chat
  - PR authors should wait at least 24 hours before following up
  - Reviewers should respond within 24 hours (approval or feedback)

- **Review Responsibility**:
  - Approvers must manually test all screens/features impacted by the changes

### Review Best Practices

- Provide constructive feedback
- Review code, not the author
- Consider both implementation and design
- Check for test coverage
- Verify documentation updates

## Branching Model

### Branch Naming Convention

Format: `taskNumber-appName-optionalDescription`

Examples:

- `GC-123-channel-home-login-page`
- `GC-456-shared-button-component`
- `GC-789-mycrop-farmers-fix-crash`

### Branch Management

- Create branches from the latest main branch
- Keep branches focused on a single task or feature
- Regularly rebase with main to avoid merge conflicts
- Delete branches after merging

## Team Communication

### Communication Channels

- **Teams Channel**: A dedicated channel for both teams with a Parking Lot section
- **PR Comments**: For code-specific discussions
- **Meetings**: Regular sync meetings and sprint demos

### Communication Best Practices

- Put calendar reminders for sprint demos
- When developing a new shared component, give a heads-up to the other team
- Document decisions made in meetings in the appropriate issue/PR
- Use the Parking Lot section for discussions that require more time

## Collaborative Development

### Pairing Guidelines

- Consider pairing for significant changes to common code (atoms, molecules, UI features)
- If pairing is not possible, do a quick review about changes before starting work
- Document pairing outcomes in the PR description

### Knowledge Sharing

- Schedule regular knowledge sharing sessions
- Document technical decisions and patterns
- Create and maintain component documentation

## Action Items

The following items have been identified for improvement:

1. **Build Optimization**:

   - Configure Nx to determine which apps/modules a change is affecting
   - Make automatic builds dynamic based on which app/module was changed

2. **Component Documentation**:

   - Configure Storybook for all components under ui-common and features
   - Ensure documentation is updated when components change

3. **Process Improvements** (Lower Priority):
   - Explore options to automatically tag reviewers based on changes in a PR
   - Check if GitHub allows custom number of approvers based on change identification
   - Address commit message conflicts if all teams try to release to production simultaneously

## Conflict Resolution

When conflicts arise:

1. **Technical Conflicts**:

   - Discuss in the Teams channel
   - If needed, schedule a quick meeting with key stakeholders
   - Document the decision and rationale

2. **Priority Conflicts**:

   - Escalate to team leads or product owners
   - Find a compromise that balances the needs of both teams
   - Document the decision in the relevant issue

3. **Release Conflicts**:
   - Coordinate release schedules between teams
   - Consider using feature flags for independent deployment
   - Develop a solution for simultaneous release handling
