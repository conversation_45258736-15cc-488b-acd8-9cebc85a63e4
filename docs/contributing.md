# Contributing Guidelines

This document outlines the process and best practices for contributing to the GC Agency UI Monorepo.

## Getting Started

1. **Familiarize Yourself with the Codebase**:
   - Review the [Architecture Overview](architecture.md)
   - Understand the application structure and shared libraries

2. **Set Up Your Development Environment**:
   - Follow the instructions in the [Development Guide](development.md)
   - Ensure you can run applications locally

## Contribution Workflow

### 1. Create an Issue (if one doesn't exist)

Before starting work, ensure there's an issue (task, bug, feature) in the issue tracking system.

### 2. Branch Creation

Create a new branch from the main branch:

```bash
git checkout main
git pull
git checkout -b [taskNumber]-[appName]-[optionalDescription]
```

Example: `GC-123-channel-home-login-fix`

### 3. Development

- Make your changes following the coding standards
- Write or update tests as needed
- Commit regularly with clear commit messages

### 4. Commit Message Format

Follow the conventional commit format:

## Commit Message Format

We follow a modified version of [conventional commits](https://www.conventionalcommits.org/en/v1.0.0/).

Format: `country/type/taskNumber(scope): subject` (e.g., `us/feat/123456: Added new button`)

Examples:

- `us/fix/123456: Added new button`
- `us/feat/78912(channel-quotes): Added new button`

### Commit Types

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code changes that neither fix bugs nor add features
- `perf`: Performance improvements
- `test`: Adding or updating tests
- `chore`: Other changes that don't modify src or test files

## Branching Model

- Branch names should follow the format: `taskNumber-appName-optionalDescription`
- Example: `1234-channel-farmers-add-button`

## Code Review Process

- Tag appropriate team members based on change impact
- Required approvers:
  - Any number from your team
  - 1 Approver from other team
- Wait 24 hours before follow-up actions
- Both teams must review new reusable components

## Communication Guidelines

- Use Teams channels for project communication
- Create calendar reminders for sprint demos
- Notify other teams when developing shared components

## Pairing Guidelines

- Pair programming recommended for significant changes to common code
- If pairing isn't possible, conduct quick review before implementation
