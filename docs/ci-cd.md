# CI/CD Workflows

This document details the Continuous Integration and Continuous Deployment workflows used in the GC Agency UI Monorepo.

## CI/CD Architecture

Our CI/CD pipeline is built using GitHub Actions and integrates with Spectrum FASTE for deployment. The workflow includes:

1. **Build and Test**: Compilation, linting, and testing of code
2. **Quality Analysis**: SonarQube analysis for code quality
3. **Artifact Creation**: Building deployment-ready artifacts
4. **Deployment**: Automated deployment to appropriate environments

## CI Workflows

### Pull Request Workflow

Triggered on every pull request to the main branch:

1. **Build Check**:

   - Builds all affected applications and libraries
   - Ensures code compiles successfully

2. **Lint Check**:

   - Runs linters on affected code
   - Ensures code style consistency

3. **Test Execution**:

   - Runs unit tests for affected projects
   - Reports test coverage

4. **SonarQube Analysis**:

   - Analyzes code quality
   - Detects potential issues
   - Enforces quality gates

5. **PR Status**:
   - Updates PR with build, test, and analysis results
   - Blocks merging if checks fail

### Main Branch Workflow

Triggered on merge to main branch:

1. **Full Build**:

   - Builds all applications and libraries
   - Creates deployment artifacts

2. **Comprehensive Testing**:

   - Runs all unit tests
   - Generates coverage reports

3. **Non-Production Deployment**:
   - Deploys to non-production environment
   - Runs smoke tests after deployment

## CD Workflows

### Development Deployment

Automated deployment to the development environment:

1. **Trigger**:

   - Successful PR builds
   - Manual trigger for specific branches

2. **Process**:
   - Deploys to development environment
   - Tagged with PR number and branch name
   - Available for testing and review

### Non-Production Deployment

Deployment to the non-production (staging) environment:

1. **Trigger**:

   - Successful merge to main
   - Scheduled deployments

2. **Process**:
   - Deploys to non-production environment
   - Tagged with commit hash
   - Available for QA and stakeholder review

### Production Deployment

Deployment to the production environment:

1. **Trigger**:

   - Release tag creation
   - Manual approval

2. **Process**:
   - Validation checks
   - Deployment to production
   - Post-deployment verification

## Versioning Strategy

We use semantic versioning for releases:

- **Major Version** (x.0.0): Breaking changes
- **Minor Version** (0.x.0): New features, non-breaking
- **Patch Version** (0.0.x): Bug fixes and small changes

Special suffixes are used for channel-specific versions:

- `-mycrop`: MyCrop specific releases
- `-seedsman`: Seedsman specific releases
- `-channel`: Channel specific releases

## Common CI/CD Commands

### Build Commands

```bash
# Build all applications
npm run build:np

# Build specific application
nx build [app-name] --configuration=production

# Run linting
npm run lint

# Run tests
npm run test
```

### Deployment Commands

```bash
# Spectrum FASTE deployment to NP
spectrum deploy --env=np --app=[app-name]

# Spectrum FASTE deployment to Production
spectrum deploy --env=prod --app=[app-name]
```

## Environment Configuration

Each environment has specialized configurations:

1. **Development**:

   - Development API endpoints
   - Debug tools enabled
   - Verbose logging

2. **Non-Production**:

   - Staging API endpoints
   - Limited debug information
   - Production-like settings

3. **Production**:
   - Production API endpoints
   - No debug information
   - Optimized for performance

## Secret Management

Secrets are managed through HashiCorp Vault:

1. **Secret Injection**:

   - CI/CD workflows fetch secrets from Vault
   - Secrets are injected as environment variables

2. **Environment-Specific Secrets**:
   - Different secrets for dev, non-prod, and production
   - Role-based access control for secret management

## Monitoring and Notifications

The CI/CD process includes monitoring and notifications:

1. **Build Notifications**:

   - Teams notifications for build failures
   - Email notifications for deployments

2. **Deployment Status**:
   - Dashboard for deployment status
   - Historical deployment logs

## Troubleshooting CI/CD Issues

Common issues and solutions:

### Build Failures

- Check for compilation errors in the logs
- Verify dependencies are correctly installed
- Check for environment-specific issues

### Deployment Failures

- Check Spectrum FASTE logs
- Verify environment configurations
- Check for resource constraints

### Test Failures

- Review test logs for specific failures
- Check for flaky tests
- Verify test environment configuration
