# AI Tools: Copilot & Cursor IDE

## Overview

This doc explains how to use AI tools like GitHub Copilot and Cursor IDE in our workspace. These tools help you write code, fix bugs, and boost productivity.

## GitHub Copilot

- **What:** AI code assistant by GitHub.
- **How to use:**
  1. Install the GitHub Copilot extension in VS Code.
  2. Sign in with your GitHub account.
  3. Start typing code—<PERSON><PERSON><PERSON> will suggest completions.
- **Tips:**
  - Use comments to guide <PERSON><PERSON><PERSON>.
  - Review suggestions for accuracy.

## Cursor IDE

- **What:** An AI-powered code editor with built-in Copilot support.
- **How to use:**
  1. Download Cursor IDE from [cursor.so](https://www.cursor.so/).
  2. Open your project folder.
  3. Use the built-in AI chat or code completion features.
- **Tips:**
  - Use the chat for code explanations or refactoring help.
  - Integrates with Copilot for suggestions.

## Best Practices

- Always review AI-generated code.
- Don’t commit sensitive info suggested by AI.
- Use AI as a helper, not a replacement for code review.

---

For more info, see the official docs for [Copilot](https://docs.github.com/en/copilot) and [Cursor IDE](https://docs.cursor.so/).

## MCP Servers for AI Agents

Our workspace uses Model Context Protocol (MCP) servers to power different AI agents. These servers let you interact with AI tools in a more flexible way. Here are the main MCP servers provided:

- **Copilot MCP**: Lets you use Copilot suggestions and chat via the MCP protocol.
- **Cursor IDE MCP**: Connects Cursor IDE’s AI features to the workspace using MCP.
- **Perplexity MCP**: Connects to Perplexity’s AI for advanced Q&A (needs API key).
- **Memory MCP**: Stores and retrieves context for AI agents.
- **Sequential Thinking MCP**: Supports step-by-step reasoning for complex tasks.
- **Context7 MCP**: Custom context server for advanced workflows.

You can find the config for these in `.vscode/mcp.json`. Each server can be started and managed from VS Code or your terminal. For more info, check the comments in that file.
