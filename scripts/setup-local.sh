#!/bin/bash

echo "Welcome to the GC Agency UI Monorepo setup script!"

# Install dependencies
echo "Installing dependencies..."
npm install

# Install mkcert if not already installed
if ! command -v mkcert &>/dev/null; then
  echo "Installing mkcert..."
  brew install mkcert
fi

echo "Installing mkcert..."
mkcert -install

echo "Generating certificates..."
mkcert localhost

echo "Running nx reset..."
npm run clean
