#!/usr/bin/env bash
# =============================================================================
# Comment Management System
# =============================================================================
# This file contains functions for managing PR comments, including creating,
# updating, and finding status comments with proper tagging.

set -euo pipefail

# Source utilities and PR data functions
# shellcheck source=./utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"
# shellcheck source=./pr-data.sh
source "$(dirname "${BASH_SOURCE[0]}")/pr-data.sh"

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

# Comment management state
declare -g EXISTING_COMMENT_ID=""
declare -g LAST_COMMENT_BODY=""

# =============================================================================
# COMMENT FETCHING FUNCTIONS
# =============================================================================

# Find existing status comment by tag
find_existing_status_comment() {
    local repo="$1"
    local pr_number="$2"
    local tag="${3:-$STATUS_COMMENT_TAG}"
    
    log_debug "Looking for existing status comment with tag: $tag"
    
    # Get PR comments
    local comments_data
    comments_data=$(get_pr_comments "$repo" "$pr_number")
    
    # Find comment with the specified tag
    local comment_id
    comment_id=$(echo "$comments_data" | jq -r --arg TAG "$tag" '
        [.[] | select(.body != null and (.body | contains($TAG)))] |
        if length > 0 then .[0].id else empty end
    ')
    
    if [[ -n "$comment_id" ]]; then
        log_debug "Found existing status comment with ID: $comment_id"
        EXISTING_COMMENT_ID="$comment_id"
        
        # Get the comment body for reference
        LAST_COMMENT_BODY=$(echo "$comments_data" | jq -r --arg ID "$comment_id" '
            .[] | select(.id == ($ID | tonumber)) | .body
        ')
        
        return 0
    else
        log_debug "No existing status comment found"
        EXISTING_COMMENT_ID=""
        LAST_COMMENT_BODY=""
        return 1
    fi
}

# Get all comments for a PR
get_all_pr_comments() {
    local repo="$1"
    local pr_number="$2"
    
    log_debug "Fetching all comments for PR $repo#$pr_number"
    
    get_pr_comments "$repo" "$pr_number"
}

# =============================================================================
# COMMENT CREATION AND UPDATING
# =============================================================================

# Create a new status comment
create_status_comment() {
    local repo="$1"
    local pr_number="$2"
    local message="$3"
    local tag="${4:-$STATUS_COMMENT_TAG}"
    
    log_debug "Creating new status comment"
    
    # Build tagged comment body
    local tagged_body
    tagged_body=$(printf "%s\n\n%s" "$message" "$tag")
    
    # Create the comment
    local comment_response
    if comment_response=$(gh api \
        --method POST \
        "repos/$repo/issues/$pr_number/comments" \
        -f body="$tagged_body" 2>/dev/null); then
        
        local comment_id
        comment_id=$(echo "$comment_response" | jq -r '.id')
        
        log_info "Created status comment with ID: $comment_id"
        EXISTING_COMMENT_ID="$comment_id"
        LAST_COMMENT_BODY="$tagged_body"
        
        return 0
    else
        log_error "Failed to create status comment"
        return $EXIT_API_ERROR
    fi
}

# Update existing status comment
update_status_comment() {
    local repo="$1"
    local pr_number="$2"
    local message="$3"
    local tag="${4:-$STATUS_COMMENT_TAG}"
    local comment_id="${5:-$EXISTING_COMMENT_ID}"
    
    if [[ -z "$comment_id" ]]; then
        log_error "No comment ID provided for update"
        return $EXIT_INVALID_INPUT
    fi
    
    log_debug "Updating status comment with ID: $comment_id"
    
    # Build tagged comment body
    local tagged_body
    tagged_body=$(printf "%s\n\n%s" "$message" "$tag")
    
    # Update the comment
    if gh api \
        --method PATCH \
        "repos/$repo/issues/comments/$comment_id" \
        -f body="$tagged_body" >/dev/null 2>&1; then
        
        log_info "Updated status comment with ID: $comment_id"
        LAST_COMMENT_BODY="$tagged_body"
        
        return 0
    else
        log_error "Failed to update status comment with ID: $comment_id"
        return $EXIT_API_ERROR
    fi
}

# =============================================================================
# COMMENT UPSERT FUNCTIONALITY
# =============================================================================

# Upsert (create or update) status comment
upsert_status_comment() {
    local repo="$1"
    local pr_number="$2"
    local message="$3"
    local tag="${4:-$STATUS_COMMENT_TAG}"
    
    log_debug "Upserting status comment for PR $repo#$pr_number"
    
    # Validate inputs
    validate_repo_format "$repo"
    validate_pr_number "$pr_number"
    
    if [[ -z "$message" ]]; then
        log_error "Comment message cannot be empty"
        return $EXIT_INVALID_INPUT
    fi
    
    # Check if we need to avoid duplicate comments
    if should_skip_comment_update "$message" "$tag"; then
        log_info "Skipping comment update - message unchanged"
        return 0
    fi
    
    # Try to find existing comment
    if find_existing_status_comment "$repo" "$pr_number" "$tag"; then
        # Update existing comment
        update_status_comment "$repo" "$pr_number" "$message" "$tag"
    else
        # Create new comment
        create_status_comment "$repo" "$pr_number" "$message" "$tag"
    fi
}

# Check if comment update should be skipped (to avoid spam)
should_skip_comment_update() {
    local new_message="$1"
    local tag="${2:-$STATUS_COMMENT_TAG}"
    
    # If we don't have a previous comment body, don't skip
    if [[ -z "$LAST_COMMENT_BODY" ]]; then
        return 1
    fi
    
    # Build what the new tagged body would be
    local new_tagged_body
    new_tagged_body=$(printf "%s\n\n%s" "$new_message" "$tag")
    
    # Compare with last comment body
    if [[ "$new_tagged_body" == "$LAST_COMMENT_BODY" ]]; then
        log_debug "Comment content unchanged, skipping update"
        return 0
    else
        log_debug "Comment content changed, proceeding with update"
        return 1
    fi
}

# =============================================================================
# SPECIALIZED COMMENT FUNCTIONS
# =============================================================================

# Post approval status comment
post_approval_status_comment() {
    local repo="$1"
    local pr_number="$2"
    local status="$3"  # "sufficient" or "insufficient"
    local approval_count="$4"
    local required_approvals="$5"
    local pr_type="$6"
    
    local message
    if [[ "$status" == "sufficient" ]]; then
        message="${pr_type} PR has sufficient approvals (${approval_count}/${required_approvals})."
        
        # Add additional context for sufficient approvals
        if [[ "$approval_count" -gt "$required_approvals" ]]; then
            message="${message} ✅ Ready for merge."
        else
            message="${message} ✅"
        fi
    else
        message="${pr_type} PR requires at least ${required_approvals} approval(s); currently has ${approval_count}."
        message="${message} ⏳ Waiting for additional approvals."
    fi
    
    upsert_status_comment "$repo" "$pr_number" "$message"
}

# Post merge status comment
post_merge_status_comment() {
    local repo="$1"
    local pr_number="$2"
    local status="$3"  # "success", "failed", "not_mergeable", "not_open"
    local approval_count="$4"
    local required_approvals="$5"
    local additional_info="${6:-}"
    
    local message
    case "$status" in
        "success")
            message="✅ PR merged successfully with ${approval_count} approval(s)."
            ;;
        "failed")
            message="❌ Approvals met (${approval_count}/${required_approvals}) but merge failed."
            if [[ -n "$additional_info" ]]; then
                message="${message} ${additional_info}"
            else
                message="${message} Please check required checks/branch status."
            fi
            ;;
        "not_mergeable")
            message="⚠️ Approvals met (${approval_count}/${required_approvals}) but PR is not mergeable."
            if [[ -n "$additional_info" ]]; then
                message="${message} State: ${additional_info}."
            fi
            ;;
        "not_open")
            message="⚠️ Approvals met (${approval_count}/${required_approvals}) but PR is not open."
            ;;
        *)
            message="❓ Unknown merge status: $status"
            ;;
    esac
    
    upsert_status_comment "$repo" "$pr_number" "$message"
}

# =============================================================================
# COMMENT CLEANUP FUNCTIONS
# =============================================================================

# Delete status comment
delete_status_comment() {
    local repo="$1"
    local pr_number="$2"
    local tag="${3:-$STATUS_COMMENT_TAG}"
    
    log_debug "Attempting to delete status comment"
    
    # Find existing comment
    if find_existing_status_comment "$repo" "$pr_number" "$tag"; then
        if gh api \
            --method DELETE \
            "repos/$repo/issues/comments/$EXISTING_COMMENT_ID" >/dev/null 2>&1; then
            
            log_info "Deleted status comment with ID: $EXISTING_COMMENT_ID"
            EXISTING_COMMENT_ID=""
            LAST_COMMENT_BODY=""
            return 0
        else
            log_error "Failed to delete status comment with ID: $EXISTING_COMMENT_ID"
            return $EXIT_API_ERROR
        fi
    else
        log_debug "No status comment found to delete"
        return 0
    fi
}

# Clean up old status comments (keep only the latest)
cleanup_old_status_comments() {
    local repo="$1"
    local pr_number="$2"
    local tag="${3:-$STATUS_COMMENT_TAG}"
    
    log_debug "Cleaning up old status comments"
    
    # Get all comments with the tag
    local comments_data
    comments_data=$(get_pr_comments "$repo" "$pr_number")
    
    # Get all comment IDs with the tag, sorted by creation date (oldest first)
    local comment_ids
    comment_ids=$(echo "$comments_data" | jq -r --arg TAG "$tag" '
        [.[] | select(.body != null and (.body | contains($TAG)))] |
        sort_by(.created_at) |
        .[:-1] |  # All except the last (newest) one
        .[].id
    ')
    
    # Delete old comments
    local deleted_count=0
    while IFS= read -r comment_id; do
        if [[ -n "$comment_id" ]]; then
            if gh api \
                --method DELETE \
                "repos/$repo/issues/comments/$comment_id" >/dev/null 2>&1; then
                log_debug "Deleted old status comment with ID: $comment_id"
                ((deleted_count++))
            else
                log_warn "Failed to delete old status comment with ID: $comment_id"
            fi
        fi
    done <<< "$comment_ids"
    
    if [[ $deleted_count -gt 0 ]]; then
        log_info "Cleaned up $deleted_count old status comments"
    else
        log_debug "No old status comments to clean up"
    fi
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Get comment statistics
get_comment_stats() {
    local repo="$1"
    local pr_number="$2"
    local tag="${3:-$STATUS_COMMENT_TAG}"
    
    local comments_data
    comments_data=$(get_pr_comments "$repo" "$pr_number")
    
    local total_comments status_comments
    total_comments=$(echo "$comments_data" | jq '. | length')
    status_comments=$(echo "$comments_data" | jq --arg TAG "$tag" '
        [.[] | select(.body != null and (.body | contains($TAG)))] | length
    ')
    
    log_info "Comment Statistics:"
    log_info "  Total Comments: $total_comments"
    log_info "  Status Comments: $status_comments"
}

# =============================================================================
# EXPORT FUNCTIONS
# =============================================================================

# Export functions for use in other scripts
export -f upsert_status_comment
export -f post_approval_status_comment
export -f post_merge_status_comment
export -f find_existing_status_comment
export -f delete_status_comment
export -f cleanup_old_status_comments
export -f get_comment_stats
