#!/usr/bin/env bash
# =============================================================================
# Merge Handling Operations
# =============================================================================
# This file contains functions for handling PR merges, including mergeable
# state checking, automatic merging, and merge status reporting.

set -euo pipefail

# Source utilities and other required modules
# shellcheck source=./utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"
# shellcheck source=./pr-data.sh
source "$(dirname "${BASH_SOURCE[0]}")/pr-data.sh"
# shellcheck source=./comment-manager.sh
source "$(dirname "${BASH_SOURCE[0]}")/comment-manager.sh"

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

# Merge state tracking
declare -g MERGE_ATTEMPT_COUNT=0
declare -g LAST_MERGE_ERROR=""
declare -g MERGE_SUCCESS=false

# =============================================================================
# MERGEABLE STATE CHECKING
# =============================================================================

# Check if PR is in a mergeable state
check_mergeable_state() {
    local repo="$1"
    local pr_number="$2"
    local max_attempts="${3:-$MAX_MERGEABLE_ATTEMPTS}"
    local delay="${4:-$MERGEABLE_CHECK_DELAY}"
    
    log_debug "Checking mergeable state for PR $repo#$pr_number"
    
    local attempts=0
    local mergeable=""
    local merge_state=""
    local pr_state=""
    
    while [[ $attempts -lt $max_attempts ]]; do
        log_debug "Mergeable check attempt $((attempts + 1))/$max_attempts"
        
        # Refresh PR data to get latest mergeable status
        refresh_pr_data "$repo" "$pr_number"
        
        pr_state="${PR_STATE:-}"
        mergeable="${PR_MERGEABLE:-}"
        merge_state="${PR_MERGE_STATE_STATUS:-}"
        
        log_debug "PR state: $pr_state, Mergeable: $mergeable, Merge state: $merge_state"
        
        # If mergeable status is no longer UNKNOWN, we can proceed
        if [[ "$mergeable" != "UNKNOWN" ]]; then
            break
        fi
        
        attempts=$((attempts + 1))
        if [[ $attempts -lt $max_attempts ]]; then
            log_debug "Mergeable status is UNKNOWN, waiting ${delay}s before retry..."
            sleep "$delay"
        fi
    done
    
    # Export current state for use by other functions
    export PR_STATE="$pr_state"
    export PR_MERGEABLE="$mergeable"
    export PR_MERGE_STATE_STATUS="$merge_state"
    
    log_debug "Final mergeable check result - State: $pr_state, Mergeable: $mergeable, Merge state: $merge_state"
    
    # Return appropriate exit code based on state
    if [[ "$pr_state" != "OPEN" ]]; then
        return $EXIT_INVALID_BASE
    elif [[ "$mergeable" == "MERGEABLE" ]]; then
        return $EXIT_SUCCESS
    else
        return $EXIT_NOT_MERGEABLE
    fi
}

# Get detailed mergeable status information
get_mergeable_status_details() {
    local repo="$1"
    local pr_number="$2"
    
    log_debug "Getting detailed mergeable status for PR $repo#$pr_number"
    
    # Get fresh PR data
    refresh_pr_data "$repo" "$pr_number"
    
    local status_info
    status_info=$(cat <<EOF
{
    "state": "${PR_STATE:-unknown}",
    "mergeable": "${PR_MERGEABLE:-unknown}",
    "merge_state_status": "${PR_MERGE_STATE_STATUS:-unknown}",
    "is_mergeable": $(if [[ "${PR_MERGEABLE:-}" == "MERGEABLE" ]]; then echo "true"; else echo "false"; fi)
}
EOF
)
    
    echo "$status_info"
}

# =============================================================================
# LABEL MANAGEMENT
# =============================================================================

# Add ready-for-merge label (for bug-fix PRs)
add_ready_for_merge_label() {
    local repo="$1"
    local pr_number="$2"
    local label="${3:-$READY_FOR_MERGE_LABEL}"
    
    if [[ -z "$label" ]]; then
        log_debug "No ready-for-merge label configured, skipping"
        return 0
    fi
    
    log_debug "Adding ready-for-merge label: $label"
    
    # Check if PR already has the label
    if pr_has_label "$label"; then
        log_debug "PR already has ready-for-merge label"
        return 0
    fi
    
    # Add the label
    if gh issue edit "$pr_number" --repo "$repo" --add-label "$label" 2>/dev/null; then
        log_info "Added ready-for-merge label: $label"
        return 0
    else
        log_warn "Failed to add ready-for-merge label: $label"
        return 1
    fi
}

# Remove ready-for-merge label
remove_ready_for_merge_label() {
    local repo="$1"
    local pr_number="$2"
    local label="${3:-$READY_FOR_MERGE_LABEL}"
    
    if [[ -z "$label" ]]; then
        log_debug "No ready-for-merge label configured, skipping"
        return 0
    fi
    
    log_debug "Removing ready-for-merge label: $label"
    
    # Check if PR has the label
    if ! pr_has_label "$label"; then
        log_debug "PR does not have ready-for-merge label"
        return 0
    fi
    
    # Remove the label
    if gh issue edit "$pr_number" --repo "$repo" --remove-label "$label" 2>/dev/null; then
        log_info "Removed ready-for-merge label: $label"
        return 0
    else
        log_warn "Failed to remove ready-for-merge label: $label"
        return 1
    fi
}

# =============================================================================
# MERGE OPERATIONS
# =============================================================================

# Attempt to merge PR
attempt_merge() {
    local repo="$1"
    local pr_number="$2"
    local merge_method="${3:-$MERGE_METHOD}"
    local delete_branch="${4:-$DELETE_BRANCH_AFTER_MERGE}"
    local use_auto_merge="${5:-$USE_AUTO_MERGE}"
    
    log_info "Attempting to merge PR $repo#$pr_number using method: $merge_method"
    
    # Validate merge method
    validate_merge_method "$merge_method"
    
    # Reset merge state
    MERGE_ATTEMPT_COUNT=$((MERGE_ATTEMPT_COUNT + 1))
    LAST_MERGE_ERROR=""
    MERGE_SUCCESS=false
    
    # Build merge command
    local merge_cmd="gh pr merge $pr_number --repo $repo --$merge_method"
    
    if [[ "$delete_branch" == "true" ]]; then
        merge_cmd="$merge_cmd --delete-branch"
    fi
    
    if [[ "$use_auto_merge" == "true" ]]; then
        merge_cmd="$merge_cmd --auto"
    fi
    
    log_debug "Merge command: $merge_cmd"
    
    # Attempt the merge
    local merge_output=""
    local merge_exit_code=0
    
    if merge_output=$($merge_cmd 2>&1); then
        log_info "✅ PR merged successfully"
        log_debug "Merge output: $merge_output"
        MERGE_SUCCESS=true
        return $EXIT_SUCCESS
    else
        merge_exit_code=$?
        log_error "❌ Merge failed with exit code: $merge_exit_code"
        log_error "Merge output: $merge_output"
        LAST_MERGE_ERROR="$merge_output"
        MERGE_SUCCESS=false
        return $EXIT_MERGE_FAILED
    fi
}

# Handle merge with full workflow
handle_merge_workflow() {
    local repo="$1"
    local pr_number="$2"
    local approval_count="$3"
    local required_approvals="$4"
    local is_bug_fix="${5:-false}"
    
    log_info "Starting merge workflow for PR $repo#$pr_number"
    
    # Check mergeable state
    local mergeable_exit_code=0
    if ! check_mergeable_state "$repo" "$pr_number"; then
        mergeable_exit_code=$?
        
        case $mergeable_exit_code in
            $EXIT_INVALID_BASE)
                log_warn "PR is not open, cannot merge"
                post_merge_status_comment "$repo" "$pr_number" "not_open" "$approval_count" "$required_approvals"
                return $EXIT_INVALID_BASE
                ;;
            $EXIT_NOT_MERGEABLE)
                log_warn "PR is not mergeable (state: ${PR_MERGE_STATE_STATUS:-unknown})"
                post_merge_status_comment "$repo" "$pr_number" "not_mergeable" "$approval_count" "$required_approvals" "${PR_MERGE_STATE_STATUS:-unknown}"
                return $EXIT_NOT_MERGEABLE
                ;;
            *)
                log_error "Unexpected error checking mergeable state: $mergeable_exit_code"
                return $mergeable_exit_code
                ;;
        esac
    fi
    
    # Add ready-for-merge label for bug-fix PRs
    if [[ "$is_bug_fix" == "true" ]]; then
        add_ready_for_merge_label "$repo" "$pr_number"
    fi
    
    # Attempt the merge
    if attempt_merge "$repo" "$pr_number"; then
        log_info "🎉 PR #$pr_number merged successfully with $approval_count approval(s)"
        post_merge_status_comment "$repo" "$pr_number" "success" "$approval_count" "$required_approvals"
        return $EXIT_SUCCESS
    else
        log_error "Failed to merge PR #$pr_number"
        local error_info="$LAST_MERGE_ERROR"
        post_merge_status_comment "$repo" "$pr_number" "failed" "$approval_count" "$required_approvals" "$error_info"
        return $EXIT_MERGE_FAILED
    fi
}

# =============================================================================
# MERGE STATUS REPORTING
# =============================================================================

# Get merge attempt statistics
get_merge_stats() {
    log_info "Merge Statistics:"
    log_info "  Attempts: $MERGE_ATTEMPT_COUNT"
    log_info "  Success: $MERGE_SUCCESS"
    if [[ -n "$LAST_MERGE_ERROR" ]]; then
        log_info "  Last Error: $LAST_MERGE_ERROR"
    fi
}

# Check if merge is possible
is_merge_possible() {
    local repo="$1"
    local pr_number="$2"
    
    # Check mergeable state without retries for quick check
    check_mergeable_state "$repo" "$pr_number" 1 0
}

# =============================================================================
# MERGE VALIDATION
# =============================================================================

# Validate merge prerequisites
validate_merge_prerequisites() {
    local repo="$1"
    local pr_number="$2"
    local approval_count="$3"
    local required_approvals="$4"
    
    log_debug "Validating merge prerequisites"
    
    # Check if approvals are sufficient
    if [[ "$approval_count" -lt "$required_approvals" ]]; then
        log_error "Insufficient approvals for merge: $approval_count/$required_approvals"
        return $EXIT_INSUFFICIENT_APPROVALS
    fi
    
    # Check if PR is eligible for merge
    if ! check_mergeable_state "$repo" "$pr_number" 1 0; then
        local exit_code=$?
        log_error "PR is not eligible for merge (exit code: $exit_code)"
        return $exit_code
    fi
    
    log_debug "Merge prerequisites validated successfully"
    return $EXIT_SUCCESS
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Reset merge state (useful for testing)
reset_merge_state() {
    MERGE_ATTEMPT_COUNT=0
    LAST_MERGE_ERROR=""
    MERGE_SUCCESS=false
    
    log_debug "Merge state reset"
}

# Get merge method from configuration
get_configured_merge_method() {
    echo "${MERGE_METHOD:-squash}"
}

# Check if auto-merge is enabled
is_auto_merge_enabled() {
    [[ "${USE_AUTO_MERGE:-true}" == "true" ]]
}

# Check if branch deletion is enabled
is_branch_deletion_enabled() {
    [[ "${DELETE_BRANCH_AFTER_MERGE:-true}" == "true" ]]
}

# =============================================================================
# EXPORT FUNCTIONS
# =============================================================================

# Export functions for use in other scripts
export -f check_mergeable_state
export -f get_mergeable_status_details
export -f add_ready_for_merge_label
export -f remove_ready_for_merge_label
export -f attempt_merge
export -f handle_merge_workflow
export -f validate_merge_prerequisites
export -f is_merge_possible
export -f get_merge_stats
