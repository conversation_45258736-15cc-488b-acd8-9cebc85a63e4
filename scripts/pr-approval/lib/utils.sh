#!/usr/bin/env bash
# =============================================================================
# PR Approval Utilities and Logging Framework
# =============================================================================
# This file contains common utilities, logging functions, error handling,
# and input validation used throughout the PR approval system.

set -euo pipefail

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

# Script directory paths
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
readonly LIB_DIR="${SCRIPT_DIR}/lib"
readonly CONFIG_DIR="${SCRIPT_DIR}/config"

# Load configuration
if [[ -f "${CONFIG_DIR}/defaults.env" ]]; then
    # shellcheck source=../config/defaults.env
    source "${CONFIG_DIR}/defaults.env"
fi

# Cache directory setup
CACHE_DIR="${CACHE_DIR:-/tmp/pr-approval-cache}"
mkdir -p "$CACHE_DIR"

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

# Get current timestamp for logging
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# Log with specified level
log_with_level() {
    local level="$1"
    shift
    local message="$*"
    
    local prefix=""
    if [[ "${LOG_TIMESTAMPS:-true}" == "true" ]]; then
        prefix="$(get_timestamp) "
    fi
    
    if [[ "${LOG_LEVELS:-true}" == "true" ]]; then
        prefix="${prefix}[${level}] "
    fi
    
    echo "${prefix}${message}" >&2
}

# Debug logging (only shown if LOG_LEVEL is DEBUG)
log_debug() {
    if [[ "${LOG_LEVEL:-INFO}" == "DEBUG" ]]; then
        log_with_level "DEBUG" "$@"
    fi
}

# Info logging
log_info() {
    if [[ "${LOG_LEVEL:-INFO}" =~ ^(DEBUG|INFO)$ ]]; then
        log_with_level "INFO" "$@"
    fi
}

# Warning logging
log_warn() {
    if [[ "${LOG_LEVEL:-INFO}" =~ ^(DEBUG|INFO|WARN)$ ]]; then
        log_with_level "WARN" "$@"
    fi
}

# Error logging
log_error() {
    log_with_level "ERROR" "$@"
}

# =============================================================================
# ERROR HANDLING
# =============================================================================

# Exit codes
readonly EXIT_SUCCESS=0
readonly EXIT_DRAFT_PR=1
readonly EXIT_INVALID_BASE=2
readonly EXIT_INSUFFICIENT_APPROVALS=3
readonly EXIT_NOT_MERGEABLE=4
readonly EXIT_MERGE_FAILED=5
readonly EXIT_INVALID_INPUT=6
readonly EXIT_API_ERROR=7
readonly EXIT_CACHE_ERROR=8

# Error handler function
handle_error() {
    local exit_code=$1
    local line_number=$2
    local command="$3"
    
    log_error "Script failed with exit code $exit_code at line $line_number"
    log_error "Failed command: $command"
    
    # Cleanup on error
    cleanup_on_exit
    
    exit "$exit_code"
}

# Set up error trap
trap 'handle_error $? $LINENO "$BASH_COMMAND"' ERR

# Cleanup function
cleanup_on_exit() {
    log_debug "Performing cleanup..."
    # Remove temporary files if they exist
    if [[ -n "${TEMP_FILES:-}" ]]; then
        # shellcheck disable=SC2086
        rm -f $TEMP_FILES 2>/dev/null || true
    fi
}

# Set up exit trap
trap cleanup_on_exit EXIT

# =============================================================================
# INPUT VALIDATION
# =============================================================================

# Validate required environment variables
validate_required_env() {
    local required_vars=("$@")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "Missing required environment variables: ${missing_vars[*]}"
        exit $EXIT_INVALID_INPUT
    fi
}

# Validate GitHub repository format
validate_repo_format() {
    local repo="$1"
    if [[ ! "$repo" =~ ^[a-zA-Z0-9_.-]+/[a-zA-Z0-9_.-]+$ ]]; then
        log_error "Invalid repository format: $repo (expected: owner/repo)"
        exit $EXIT_INVALID_INPUT
    fi
}

# Validate PR number
validate_pr_number() {
    local pr_number="$1"
    if [[ ! "$pr_number" =~ ^[0-9]+$ ]]; then
        log_error "Invalid PR number: $pr_number (expected: positive integer)"
        exit $EXIT_INVALID_INPUT
    fi
}

# Validate merge method
validate_merge_method() {
    local method="$1"
    if [[ ! "$method" =~ ^(squash|merge|rebase)$ ]]; then
        log_error "Invalid merge method: $method (expected: squash, merge, or rebase)"
        exit $EXIT_INVALID_INPUT
    fi
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Ensure required commands are available
ensure_commands() {
    local required_commands=("gh" "jq" "curl")
    local missing_commands=()
    
    for cmd in "${required_commands[@]}"; do
        if ! command_exists "$cmd"; then
            missing_commands+=("$cmd")
        fi
    done
    
    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_error "Missing required commands: ${missing_commands[*]}"
        log_error "Please install the missing commands and try again"
        exit $EXIT_INVALID_INPUT
    fi
}

# Trim whitespace from string
trim() {
    local var="$*"
    # Remove leading whitespace
    var="${var#"${var%%[![:space:]]*}"}"
    # Remove trailing whitespace
    var="${var%"${var##*[![:space:]]}"}"
    echo "$var"
}

# Check if array contains element
array_contains() {
    local element="$1"
    shift
    local array=("$@")
    
    for item in "${array[@]}"; do
        if [[ "$item" == "$element" ]]; then
            return 0
        fi
    done
    return 1
}

# Split string by delimiter into array
split_string() {
    local string="$1"
    local delimiter="$2"
    local -n result_array=$3
    
    IFS="$delimiter" read -ra result_array <<< "$string"
}

# =============================================================================
# CACHE FUNCTIONS
# =============================================================================

# Generate cache key
generate_cache_key() {
    local key_parts=("$@")
    local key
    key=$(printf "%s" "${key_parts[@]}" | sha256sum | cut -d' ' -f1)
    echo "$key"
}

# Check if cache entry is valid
is_cache_valid() {
    local cache_file="$1"
    local ttl="${2:-${CACHE_TTL:-300}}"
    
    if [[ ! -f "$cache_file" ]]; then
        return 1
    fi
    
    local file_age
    file_age=$(($(date +%s) - $(stat -c %Y "$cache_file" 2>/dev/null || stat -f %m "$cache_file" 2>/dev/null || echo 0)))
    
    if [[ $file_age -gt $ttl ]]; then
        log_debug "Cache entry expired: $cache_file (age: ${file_age}s, ttl: ${ttl}s)"
        return 1
    fi
    
    return 0
}

# Get from cache
cache_get() {
    local cache_key="$1"
    local cache_file="${CACHE_DIR}/${cache_key}"
    
    if [[ "${ENABLE_CACHE:-true}" != "true" ]]; then
        return 1
    fi
    
    if is_cache_valid "$cache_file"; then
        log_debug "Cache hit: $cache_key"
        cat "$cache_file"
        return 0
    else
        log_debug "Cache miss: $cache_key"
        return 1
    fi
}

# Set cache entry
cache_set() {
    local cache_key="$1"
    local data="$2"
    local cache_file="${CACHE_DIR}/${cache_key}"
    
    if [[ "${ENABLE_CACHE:-true}" != "true" ]]; then
        return 0
    fi
    
    echo "$data" > "$cache_file"
    log_debug "Cache set: $cache_key"
}

# Clear cache
cache_clear() {
    if [[ -d "$CACHE_DIR" ]]; then
        rm -rf "${CACHE_DIR:?}"/*
        log_debug "Cache cleared"
    fi
}

# =============================================================================
# INITIALIZATION
# =============================================================================

# Initialize utilities
init_utils() {
    log_debug "Initializing PR approval utilities..."
    
    # Ensure required commands are available
    ensure_commands
    
    # Create cache directory
    mkdir -p "$CACHE_DIR"
    
    log_debug "Utilities initialized successfully"
}

# Auto-initialize when sourced
if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
    init_utils
fi
