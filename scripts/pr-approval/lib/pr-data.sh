#!/usr/bin/env bash
# =============================================================================
# PR Data Fetching and Caching System
# =============================================================================
# This file contains functions for fetching PR data from GitHub API,
# caching responses, and parsing PR information efficiently.

set -euo pipefail

# Source utilities
# shellcheck source=./utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

# Cache keys for different data types
declare -g PR_DATA_CACHE_KEY=""
declare -g PR_REVIEWS_CACHE_KEY=""
declare -g PR_COMMENTS_CACHE_KEY=""

# Cached data variables
declare -g PR_DATA=""
declare -g PR_REVIEWS=""
declare -g PR_COMMENTS=""

# =============================================================================
# CACHE KEY GENERATION
# =============================================================================

# Generate cache key for PR data
generate_pr_data_cache_key() {
    local repo="$1"
    local pr_number="$2"
    generate_cache_key "pr-data" "$repo" "$pr_number"
}

# Generate cache key for PR reviews
generate_pr_reviews_cache_key() {
    local repo="$1"
    local pr_number="$2"
    generate_cache_key "pr-reviews" "$repo" "$pr_number"
}

# Generate cache key for PR comments
generate_pr_comments_cache_key() {
    local repo="$1"
    local pr_number="$2"
    generate_cache_key "pr-comments" "$repo" "$pr_number"
}

# =============================================================================
# GITHUB API FUNCTIONS
# =============================================================================

# Fetch PR data from GitHub API
fetch_pr_data_from_api() {
    local repo="$1"
    local pr_number="$2"
    
    log_debug "Fetching PR data from API: $repo#$pr_number"
    
    local pr_data
    if ! pr_data=$(gh pr view "$pr_number" --repo "$repo" --json \
        number,state,isDraft,baseRefName,labels,title,mergeable,mergeStateStatus,url,author 2>/dev/null); then
        log_error "Failed to fetch PR data for $repo#$pr_number"
        exit $EXIT_API_ERROR
    fi
    
    log_debug "Successfully fetched PR data"
    echo "$pr_data"
}

# Fetch PR reviews from GitHub API
fetch_pr_reviews_from_api() {
    local repo="$1"
    local pr_number="$2"
    
    log_debug "Fetching PR reviews from API: $repo#$pr_number"
    
    local reviews_data
    if ! reviews_data=$(gh api "repos/$repo/pulls/$pr_number/reviews" \
        --paginate --jq '.' 2>/dev/null); then
        log_error "Failed to fetch PR reviews for $repo#$pr_number"
        exit $EXIT_API_ERROR
    fi
    
    log_debug "Successfully fetched PR reviews"
    echo "$reviews_data"
}

# Fetch PR comments from GitHub API
fetch_pr_comments_from_api() {
    local repo="$1"
    local pr_number="$2"
    
    log_debug "Fetching PR comments from API: $repo#$pr_number"
    
    local comments_data
    if ! comments_data=$(gh api "repos/$repo/issues/$pr_number/comments" \
        --paginate --jq '.' 2>/dev/null); then
        log_error "Failed to fetch PR comments for $repo#$pr_number"
        exit $EXIT_API_ERROR
    fi
    
    log_debug "Successfully fetched PR comments"
    echo "$comments_data"
}

# =============================================================================
# CACHING FUNCTIONS
# =============================================================================

# Get PR data with caching
get_pr_data() {
    local repo="$1"
    local pr_number="$2"
    
    # Generate cache key if not already set
    if [[ -z "$PR_DATA_CACHE_KEY" ]]; then
        PR_DATA_CACHE_KEY=$(generate_pr_data_cache_key "$repo" "$pr_number")
    fi
    
    # Try to get from cache first
    if PR_DATA=$(cache_get "$PR_DATA_CACHE_KEY"); then
        log_debug "Using cached PR data"
    else
        # Fetch from API and cache
        PR_DATA=$(fetch_pr_data_from_api "$repo" "$pr_number")
        cache_set "$PR_DATA_CACHE_KEY" "$PR_DATA"
    fi
    
    echo "$PR_DATA"
}

# Get PR reviews with caching
get_pr_reviews() {
    local repo="$1"
    local pr_number="$2"
    
    # Generate cache key if not already set
    if [[ -z "$PR_REVIEWS_CACHE_KEY" ]]; then
        PR_REVIEWS_CACHE_KEY=$(generate_pr_reviews_cache_key "$repo" "$pr_number")
    fi
    
    # Try to get from cache first
    if PR_REVIEWS=$(cache_get "$PR_REVIEWS_CACHE_KEY"); then
        log_debug "Using cached PR reviews"
    else
        # Fetch from API and cache
        PR_REVIEWS=$(fetch_pr_reviews_from_api "$repo" "$pr_number")
        cache_set "$PR_REVIEWS_CACHE_KEY" "$PR_REVIEWS"
    fi
    
    echo "$PR_REVIEWS"
}

# Get PR comments with caching
get_pr_comments() {
    local repo="$1"
    local pr_number="$2"
    
    # Generate cache key if not already set
    if [[ -z "$PR_COMMENTS_CACHE_KEY" ]]; then
        PR_COMMENTS_CACHE_KEY=$(generate_pr_comments_cache_key "$repo" "$pr_number")
    fi
    
    # Try to get from cache first
    if PR_COMMENTS=$(cache_get "$PR_COMMENTS_CACHE_KEY"); then
        log_debug "Using cached PR comments"
    else
        # Fetch from API and cache
        PR_COMMENTS=$(fetch_pr_comments_from_api "$repo" "$pr_number")
        cache_set "$PR_COMMENTS_CACHE_KEY" "$PR_COMMENTS"
    fi
    
    echo "$PR_COMMENTS"
}

# =============================================================================
# DATA PARSING FUNCTIONS
# =============================================================================

# Parse PR basic information
parse_pr_info() {
    local pr_data="$1"
    
    local number state is_draft base_ref title url author
    number=$(echo "$pr_data" | jq -r '.number')
    state=$(echo "$pr_data" | jq -r '.state')
    is_draft=$(echo "$pr_data" | jq -r '.isDraft')
    base_ref=$(echo "$pr_data" | jq -r '.baseRefName')
    title=$(echo "$pr_data" | jq -r '.title')
    url=$(echo "$pr_data" | jq -r '.url')
    author=$(echo "$pr_data" | jq -r '.author.login')
    
    log_debug "Parsed PR info - Number: $number, State: $state, Draft: $is_draft, Base: $base_ref"
    
    # Export as global variables for easy access
    export PR_NUMBER="$number"
    export PR_STATE="$state"
    export PR_IS_DRAFT="$is_draft"
    export PR_BASE_REF="$base_ref"
    export PR_TITLE="$title"
    export PR_URL="$url"
    export PR_AUTHOR="$author"
}

# Parse PR labels
parse_pr_labels() {
    local pr_data="$1"
    
    local labels_json labels_csv
    labels_json=$(echo "$pr_data" | jq -r '.labels')
    labels_csv=$(echo "$labels_json" | jq -r '[.[].name] | join(",")')
    
    log_debug "Parsed PR labels: $labels_csv"
    
    # Export as global variables
    export PR_LABELS_JSON="$labels_json"
    export PR_LABELS_CSV="$labels_csv"
}

# Parse PR mergeable status
parse_pr_mergeable_status() {
    local pr_data="$1"
    
    local mergeable merge_state_status
    mergeable=$(echo "$pr_data" | jq -r '.mergeable')
    merge_state_status=$(echo "$pr_data" | jq -r '.mergeStateStatus')
    
    log_debug "Parsed mergeable status - Mergeable: $mergeable, State: $merge_state_status"
    
    # Export as global variables
    export PR_MERGEABLE="$mergeable"
    export PR_MERGE_STATE_STATUS="$merge_state_status"
}

# Check if PR has specific label
pr_has_label() {
    local label="$1"
    local pr_data="${2:-$PR_DATA}"
    
    if [[ -z "$pr_data" ]]; then
        log_error "No PR data available for label check"
        return 1
    fi
    
    local has_label
    has_label=$(echo "$pr_data" | jq -r --arg L "$label" \
        '[.labels[].name] | index($L) | if . == null then "false" else "true" end')
    
    [[ "$has_label" == "true" ]]
}

# =============================================================================
# MAIN DATA FETCHING FUNCTION
# =============================================================================

# Fetch and cache all PR data
fetch_all_pr_data() {
    local repo="$1"
    local pr_number="$2"
    
    log_info "Fetching all PR data for $repo#$pr_number"
    
    # Validate inputs
    validate_repo_format "$repo"
    validate_pr_number "$pr_number"
    
    # Initialize cache keys
    PR_DATA_CACHE_KEY=$(generate_pr_data_cache_key "$repo" "$pr_number")
    PR_REVIEWS_CACHE_KEY=$(generate_pr_reviews_cache_key "$repo" "$pr_number")
    PR_COMMENTS_CACHE_KEY=$(generate_pr_comments_cache_key "$repo" "$pr_number")
    
    # Fetch PR data
    PR_DATA=$(get_pr_data "$repo" "$pr_number")
    
    # Parse PR information
    parse_pr_info "$PR_DATA"
    parse_pr_labels "$PR_DATA"
    parse_pr_mergeable_status "$PR_DATA"
    
    log_info "Successfully fetched and parsed PR data"
    
    # Return success
    return 0
}

# Refresh PR data (bypass cache)
refresh_pr_data() {
    local repo="$1"
    local pr_number="$2"
    
    log_info "Refreshing PR data for $repo#$pr_number"
    
    # Clear relevant cache entries
    if [[ -n "$PR_DATA_CACHE_KEY" ]]; then
        rm -f "${CACHE_DIR}/${PR_DATA_CACHE_KEY}" 2>/dev/null || true
    fi
    
    # Fetch fresh data
    fetch_all_pr_data "$repo" "$pr_number"
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Print PR summary
print_pr_summary() {
    if [[ -z "${PR_DATA:-}" ]]; then
        log_error "No PR data available for summary"
        return 1
    fi
    
    log_info "PR Summary:"
    log_info "  Number: ${PR_NUMBER:-N/A}"
    log_info "  Title: ${PR_TITLE:-N/A}"
    log_info "  Author: ${PR_AUTHOR:-N/A}"
    log_info "  State: ${PR_STATE:-N/A}"
    log_info "  Draft: ${PR_IS_DRAFT:-N/A}"
    log_info "  Base Branch: ${PR_BASE_REF:-N/A}"
    log_info "  Labels: ${PR_LABELS_CSV:-none}"
    log_info "  Mergeable: ${PR_MERGEABLE:-N/A}"
    log_info "  URL: ${PR_URL:-N/A}"
}

# Export functions for use in other scripts
export -f fetch_all_pr_data
export -f refresh_pr_data
export -f get_pr_data
export -f get_pr_reviews
export -f get_pr_comments
export -f pr_has_label
export -f print_pr_summary
