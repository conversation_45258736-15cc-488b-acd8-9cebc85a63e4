#!/usr/bin/env bash
# =============================================================================
# Approval Counting and Validation Logic
# =============================================================================
# This file contains functions for counting PR approvals, determining PR types,
# and validating approval requirements.

set -euo pipefail

# Source utilities and PR data functions
# shellcheck source=./utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"
# shellcheck source=./pr-data.sh
source "$(dirname "${BASH_SOURCE[0]}")/pr-data.sh"

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

# Approval counting results
declare -g APPROVAL_COUNT=0
declare -g REQUIRED_APPROVALS=0
declare -g PR_TYPE=""
declare -g IS_BUG_FIX=false

# =============================================================================
# PR ELIGIBILITY CHECKS
# =============================================================================

# Check if PR is eligible for approval processing
is_pr_eligible() {
    local repo="$1"
    local pr_number="$2"
    
    log_debug "Checking PR eligibility for $repo#$pr_number"
    
    # Ensure we have PR data
    if [[ -z "${PR_DATA:-}" ]]; then
        fetch_all_pr_data "$repo" "$pr_number"
    fi
    
    # Check if PR is draft
    if [[ "${PR_IS_DRAFT:-}" == "true" ]]; then
        log_info "PR #$pr_number is a draft - skipping approval checks"
        return $EXIT_DRAFT_PR
    fi
    
    # Check base branch
    if ! is_base_branch_allowed "${PR_BASE_REF:-}"; then
        log_info "PR #$pr_number targets branch '${PR_BASE_REF:-}' which is not in allowed list [${ALLOWED_BASE_BRANCHES}] - skipping"
        return $EXIT_INVALID_BASE
    fi
    
    # Check if PR is open
    if [[ "${PR_STATE:-}" != "OPEN" ]]; then
        log_info "PR #$pr_number is not open (state: ${PR_STATE:-}) - skipping"
        return $EXIT_INVALID_BASE
    fi
    
    log_debug "PR is eligible for approval processing"
    return $EXIT_SUCCESS
}

# Check if base branch is allowed
is_base_branch_allowed() {
    local base_branch="$1"
    local allowed_branches_array=()
    
    # Split allowed branches by comma
    split_string "$ALLOWED_BASE_BRANCHES" "," allowed_branches_array
    
    # Trim whitespace from each branch and check
    for branch in "${allowed_branches_array[@]}"; do
        local trimmed_branch
        trimmed_branch=$(trim "$branch")
        if [[ "$trimmed_branch" == "$base_branch" ]]; then
            return 0
        fi
    done
    
    return 1
}

# =============================================================================
# PR TYPE DETECTION
# =============================================================================

# Determine PR type (bug-fix or feature)
determine_pr_type() {
    local repo="$1"
    local pr_number="$2"
    
    log_debug "Determining PR type for $repo#$pr_number"
    
    # Ensure we have PR data
    if [[ -z "${PR_DATA:-}" ]]; then
        fetch_all_pr_data "$repo" "$pr_number"
    fi
    
    # Check if PR has bug-fix label
    if pr_has_label "$BUG_FIX_LABEL"; then
        PR_TYPE="Bug-fix"
        IS_BUG_FIX=true
        REQUIRED_APPROVALS="$BUG_FIX_APPROVALS"
        log_debug "PR identified as bug-fix (requires $REQUIRED_APPROVALS approvals)"
    else
        PR_TYPE="Feature"
        IS_BUG_FIX=false
        REQUIRED_APPROVALS="$FEATURE_APPROVALS"
        log_debug "PR identified as feature (requires $REQUIRED_APPROVALS approvals)"
    fi
    
    # Export variables for use in other scripts
    export PR_TYPE
    export IS_BUG_FIX
    export REQUIRED_APPROVALS
    
    log_info "PR type: $PR_TYPE (requires $REQUIRED_APPROVALS approvals)"
}

# =============================================================================
# APPROVAL COUNTING
# =============================================================================

# Count approved reviews (latest review per reviewer)
count_approvals() {
    local repo="$1"
    local pr_number="$2"
    
    log_debug "Counting approvals for $repo#$pr_number"
    
    # Get PR reviews
    local reviews_data
    reviews_data=$(get_pr_reviews "$repo" "$pr_number")
    
    # Count approvals using jq to group by user and get latest review
    APPROVAL_COUNT=$(echo "$reviews_data" | jq -r '
        group_by(.user.login) |
        map(
            sort_by(.submitted_at // .created_at) | last
        ) |
        map(select(.state == "APPROVED")) |
        length
    ')
    
    log_debug "Found $APPROVAL_COUNT approvals"
    
    # Export for use in other scripts
    export APPROVAL_COUNT
    
    return 0
}

# Get detailed approval information
get_approval_details() {
    local repo="$1"
    local pr_number="$2"
    
    log_debug "Getting detailed approval information for $repo#$pr_number"
    
    # Get PR reviews
    local reviews_data
    reviews_data=$(get_pr_reviews "$repo" "$pr_number")
    
    # Get latest review per user with details
    local approval_details
    approval_details=$(echo "$reviews_data" | jq -r '
        group_by(.user.login) |
        map(
            sort_by(.submitted_at // .created_at) | last
        ) |
        map(select(.state == "APPROVED")) |
        map({
            user: .user.login,
            state: .state,
            submitted_at: .submitted_at,
            body: .body
        })
    ')
    
    log_debug "Approval details: $approval_details"
    echo "$approval_details"
}

# =============================================================================
# APPROVAL VALIDATION
# =============================================================================

# Check if PR has sufficient approvals
has_sufficient_approvals() {
    local repo="$1"
    local pr_number="$2"
    
    log_debug "Checking if PR has sufficient approvals"
    
    # Ensure we have approval count and requirements
    if [[ -z "${APPROVAL_COUNT:-}" ]] || [[ -z "${REQUIRED_APPROVALS:-}" ]]; then
        count_approvals "$repo" "$pr_number"
        determine_pr_type "$repo" "$pr_number"
    fi
    
    if [[ "$APPROVAL_COUNT" -ge "$REQUIRED_APPROVALS" ]]; then
        log_info "PR has sufficient approvals ($APPROVAL_COUNT/$REQUIRED_APPROVALS)"
        return 0
    else
        log_info "PR has insufficient approvals ($APPROVAL_COUNT/$REQUIRED_APPROVALS)"
        return 1
    fi
}

# Validate all approval requirements
validate_approval_requirements() {
    local repo="$1"
    local pr_number="$2"
    
    log_info "Validating approval requirements for $repo#$pr_number"
    
    # Check PR eligibility
    if ! is_pr_eligible "$repo" "$pr_number"; then
        local exit_code=$?
        log_debug "PR is not eligible for approval processing (exit code: $exit_code)"
        return $exit_code
    fi
    
    # Determine PR type and required approvals
    determine_pr_type "$repo" "$pr_number"
    
    # Count current approvals
    count_approvals "$repo" "$pr_number"
    
    # Check if approvals are sufficient
    if has_sufficient_approvals "$repo" "$pr_number"; then
        log_info "✅ Approval requirements satisfied"
        return $EXIT_SUCCESS
    else
        log_info "❌ Approval requirements not satisfied"
        return $EXIT_INSUFFICIENT_APPROVALS
    fi
}

# =============================================================================
# APPROVAL STATUS REPORTING
# =============================================================================

# Generate approval status message
generate_approval_status_message() {
    local status="$1"  # "sufficient" or "insufficient"
    
    local message=""
    
    if [[ "$status" == "sufficient" ]]; then
        message="${PR_TYPE} PR has sufficient approvals (${APPROVAL_COUNT}/${REQUIRED_APPROVALS})."
    else
        message="${PR_TYPE} PR requires at least ${REQUIRED_APPROVALS} approval(s); currently has ${APPROVAL_COUNT}."
    fi
    
    echo "$message"
}

# Print approval summary
print_approval_summary() {
    log_info "Approval Summary:"
    log_info "  PR Type: ${PR_TYPE:-N/A}"
    log_info "  Required Approvals: ${REQUIRED_APPROVALS:-N/A}"
    log_info "  Current Approvals: ${APPROVAL_COUNT:-N/A}"
    log_info "  Status: $(if [[ "${APPROVAL_COUNT:-0}" -ge "${REQUIRED_APPROVALS:-999}" ]]; then echo "✅ Sufficient"; else echo "❌ Insufficient"; fi)"
    
    # Show approval details if available
    if [[ "${APPROVAL_COUNT:-0}" -gt 0 ]]; then
        log_info "  Approved by:"
        local approval_details
        approval_details=$(get_approval_details "${REPO:-}" "${PR_NUMBER:-}")
        echo "$approval_details" | jq -r '.[] | "    - " + .user + " (" + .submitted_at + ")"' 2>/dev/null || true
    fi
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Reset approval state (useful for testing)
reset_approval_state() {
    APPROVAL_COUNT=0
    REQUIRED_APPROVALS=0
    PR_TYPE=""
    IS_BUG_FIX=false
    
    log_debug "Approval state reset"
}

# Check if PR author can approve their own PR
is_self_approval_allowed() {
    # This is typically not allowed in most workflows
    # Can be configured if needed
    return 1
}

# Get list of users who approved the PR
get_approving_users() {
    local repo="$1"
    local pr_number="$2"
    
    local reviews_data
    reviews_data=$(get_pr_reviews "$repo" "$pr_number")
    
    # Get list of users who approved
    local approving_users
    approving_users=$(echo "$reviews_data" | jq -r '
        group_by(.user.login) |
        map(
            sort_by(.submitted_at // .created_at) | last
        ) |
        map(select(.state == "APPROVED")) |
        map(.user.login) |
        join(", ")
    ')
    
    echo "$approving_users"
}

# =============================================================================
# EXPORT FUNCTIONS
# =============================================================================

# Export functions for use in other scripts
export -f is_pr_eligible
export -f determine_pr_type
export -f count_approvals
export -f has_sufficient_approvals
export -f validate_approval_requirements
export -f generate_approval_status_message
export -f print_approval_summary
export -f get_approving_users
