#!/usr/bin/env bash
# =============================================================================
# PR Approval Main Orchestrator
# =============================================================================
# This is the main entry point for the PR approval workflow. It coordinates
# all components and implements the complete approval and merge logic.

set -euo pipefail

# =============================================================================
# SCRIPT INITIALIZATION
# =============================================================================

# Get script directory
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source all required modules
# shellcheck source=./lib/utils.sh
source "${SCRIPT_DIR}/lib/utils.sh"
# shellcheck source=./lib/pr-data.sh
source "${SCRIPT_DIR}/lib/pr-data.sh"
# shellcheck source=./lib/approval-logic.sh
source "${SCRIPT_DIR}/lib/approval-logic.sh"
# shellcheck source=./lib/comment-manager.sh
source "${SCRIPT_DIR}/lib/comment-manager.sh"
# shellcheck source=./lib/merge-handler.sh
source "${SCRIPT_DIR}/lib/merge-handler.sh"

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

# Workflow state
declare -g WORKFLOW_START_TIME=""
declare -g WORKFLOW_SUCCESS=false

# =============================================================================
# MAIN WORKFLOW FUNCTIONS
# =============================================================================

# Initialize the workflow
init_workflow() {
    WORKFLOW_START_TIME=$(date +%s)

    log_info "🚀 Starting PR Approval Workflow"
    log_info "Repository: ${REPO:-N/A}"
    log_info "PR Number: ${PR_NUMBER:-N/A}"
    log_info "Timestamp: $(date)"

    # Validate required environment variables
    validate_required_env "REPO" "PR_NUMBER" "GH_TOKEN"

    # Validate input formats
    validate_repo_format "$REPO"
    validate_pr_number "$PR_NUMBER"

    log_debug "Workflow initialization completed"
}

# Perform initial PR checks
perform_initial_checks() {
    log_info "📋 Performing initial PR checks..."

    # Fetch all PR data
    fetch_all_pr_data "$REPO" "$PR_NUMBER"

    # Print PR summary for debugging
    if [[ "${LOG_LEVEL:-INFO}" == "DEBUG" ]]; then
        print_pr_summary
    fi

    # Check if PR is eligible for processing
    local eligibility_exit_code=0
    if ! is_pr_eligible "$REPO" "$PR_NUMBER"; then
        eligibility_exit_code=$?

        case $eligibility_exit_code in
        $EXIT_DRAFT_PR)
            log_info "✋ PR is a draft - workflow completed"
            exit "$EXIT_SUCCESS"
            ;;
        $EXIT_INVALID_BASE)
            log_info "✋ PR targets invalid base branch - workflow completed"
            exit "$EXIT_SUCCESS"
            ;;
        *)
            log_error "❌ PR eligibility check failed with exit code: $eligibility_exit_code"
            exit $eligibility_exit_code
            ;;
        esac
    fi

    log_info "✅ Initial checks passed"
}

# Process approval requirements
process_approval_requirements() {
    log_info "🔍 Processing approval requirements..."

    # Validate approval requirements
    local validation_exit_code=0
    if ! validate_approval_requirements "$REPO" "$PR_NUMBER"; then
        validation_exit_code=$?

        if [[ $validation_exit_code == $EXIT_INSUFFICIENT_APPROVALS ]]; then
            log_info "⏳ Insufficient approvals - posting status comment"

            # Post insufficient approvals comment
            post_approval_status_comment "$REPO" "$PR_NUMBER" "insufficient" \
                "$APPROVAL_COUNT" "$REQUIRED_APPROVALS" "$PR_TYPE"

            log_info "📝 Status comment posted - workflow completed"
            exit "$EXIT_SUCCESS"
        else
            log_error "❌ Approval validation failed with exit code: $validation_exit_code"
            exit $validation_exit_code
        fi
    fi

    log_info "✅ Approval requirements satisfied"

    # Print approval summary for debugging
    if [[ "${LOG_LEVEL:-INFO}" == "DEBUG" ]]; then
        print_approval_summary
    fi

    # Post sufficient approvals comment
    post_approval_status_comment "$REPO" "$PR_NUMBER" "sufficient" \
        "$APPROVAL_COUNT" "$REQUIRED_APPROVALS" "$PR_TYPE"
}

# Handle merge workflow
handle_merge_process() {
    log_info "🔄 Handling merge process..."

    # Handle the complete merge workflow
    local merge_exit_code=0
    if ! handle_merge_workflow "$REPO" "$PR_NUMBER" "$APPROVAL_COUNT" "$REQUIRED_APPROVALS" "$IS_BUG_FIX"; then
        merge_exit_code=$?

        case $merge_exit_code in
        $EXIT_INVALID_BASE)
            log_info "⚠️ PR is not open - cannot merge"
            ;;
        $EXIT_NOT_MERGEABLE)
            log_info "⚠️ PR is not mergeable - waiting for checks"
            ;;
        $EXIT_MERGE_FAILED)
            log_error "❌ Merge failed - see comments for details"
            ;;
        *)
            log_error "❌ Merge workflow failed with exit code: $merge_exit_code"
            ;;
        esac

        # Don't exit with error for expected merge failures
        if [[ $merge_exit_code == $EXIT_INVALID_BASE ]] || [[ $merge_exit_code == $EXIT_NOT_MERGEABLE ]]; then
            log_info "📝 Status comment posted - workflow completed"
            exit "$EXIT_SUCCESS"
        else
            exit $merge_exit_code
        fi
    fi

    log_info "🎉 Merge completed successfully"
    WORKFLOW_SUCCESS=true
}

# =============================================================================
# WORKFLOW REPORTING
# =============================================================================

# Generate workflow summary
generate_workflow_summary() {
    local end_time
    end_time=$(date +%s)
    local duration=$((end_time - WORKFLOW_START_TIME))

    log_info "📊 Workflow Summary:"
    log_info "  Repository: $REPO"
    log_info "  PR Number: #$PR_NUMBER"
    log_info "  PR Type: ${PR_TYPE:-N/A}"
    log_info "  Approvals: ${APPROVAL_COUNT:-0}/${REQUIRED_APPROVALS:-0}"
    log_info "  Success: $WORKFLOW_SUCCESS"
    log_info "  Duration: ${duration}s"

    if [[ "$WORKFLOW_SUCCESS" == "true" ]]; then
        log_info "  Result: ✅ PR merged successfully"
    else
        log_info "  Result: ⏳ Workflow completed (no merge)"
    fi
}

# =============================================================================
# ERROR HANDLING
# =============================================================================

# Workflow error handler
handle_workflow_error() {
    local exit_code=$1
    local line_number=$2
    local command="$3"

    log_error "🚨 Workflow failed at line $line_number with exit code $exit_code"
    log_error "Failed command: $command"

    # Try to post error comment if we have enough context
    if [[ -n "${REPO:-}" ]] && [[ -n "${PR_NUMBER:-}" ]]; then
        local error_message="❌ PR approval workflow encountered an error. Please check the workflow logs for details."
        upsert_status_comment "$REPO" "$PR_NUMBER" "$error_message" 2>/dev/null || true
    fi

    # Generate summary even on error
    if [[ -n "$WORKFLOW_START_TIME" ]]; then
        generate_workflow_summary
    fi

    # Cleanup and exit
    cleanup_on_exit
    exit "$exit_code"
}

# Override the default error handler
trap 'handle_workflow_error $? $LINENO "$BASH_COMMAND"' ERR

# =============================================================================
# MAIN WORKFLOW EXECUTION
# =============================================================================

# Main workflow function
main() {
    # Initialize workflow
    init_workflow

    # Perform initial checks
    perform_initial_checks

    # Process approval requirements
    process_approval_requirements

    # Handle merge process
    handle_merge_process

    # Generate final summary
    generate_workflow_summary

    log_info "🏁 PR Approval Workflow completed successfully"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Help function
show_help() {
    cat <<EOF
PR Approval Workflow Script

USAGE:
    $0 [OPTIONS]

DESCRIPTION:
    This script orchestrates the complete PR approval workflow, including:
    - Fetching and validating PR data
    - Counting approvals and determining requirements
    - Posting status comments
    - Handling automatic merging

REQUIRED ENVIRONMENT VARIABLES:
    REPO        - GitHub repository in format 'owner/repo'
    PR_NUMBER   - Pull request number
    GH_TOKEN    - GitHub token for API access

OPTIONAL ENVIRONMENT VARIABLES:
    LOG_LEVEL   - Logging level (DEBUG, INFO, WARN, ERROR)

    See config/defaults.env for all available configuration options.

OPTIONS:
    -h, --help      Show this help message
    -v, --version   Show version information
    -d, --debug     Enable debug logging

EXAMPLES:
    # Basic usage (requires environment variables)
    export REPO="owner/repo"
    export PR_NUMBER="123"
    export GH_TOKEN="ghp_..."
    $0

    # With debug logging
    $0 --debug

EXIT CODES:
    0   - Success
    1   - Draft PR (skipped)
    2   - Invalid base branch (skipped)
    3   - Insufficient approvals
    4   - Not mergeable
    5   - Merge failed
    6   - Invalid input
    7   - API error
    8   - Cache error

EOF
}

# Version function
show_version() {
    echo "PR Approval Workflow Script v1.0.0"
    echo "Part of the GC Agency UI Monorepo"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
        -h | --help)
            show_help
            exit 0
            ;;
        -v | --version)
            show_version
            exit 0
            ;;
        -d | --debug)
            export LOG_LEVEL="DEBUG"
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit "$EXIT_INVALID_INPUT"
            ;;
        esac
    done
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    parse_args "$@"

    # Run main workflow
    main
fi
