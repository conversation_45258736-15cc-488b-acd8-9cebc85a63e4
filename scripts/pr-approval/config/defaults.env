# PR Approval Workflow Configuration
# This file contains all default configuration values for the PR approval system

# =============================================================================
# APPROVAL REQUIREMENTS
# =============================================================================

# Number of approvals required for bug-fix PRs
BUG_FIX_APPROVALS=1

# Number of approvals required for feature PRs
FEATURE_APPROVALS=2

# Label that identifies a PR as a bug-fix
BUG_FIX_LABEL="bug-fix"

# =============================================================================
# MERGE CONFIGURATION
# =============================================================================

# Merge method to use: squash, merge, or rebase
MERGE_METHOD="squash"

# Label to add when PR is ready for merge (bug-fix PRs only)
READY_FOR_MERGE_LABEL="ready-for-merge"

# Whether to delete the source branch after merge
DELETE_BRANCH_AFTER_MERGE=true

# Whether to use auto-merge (waits for required checks)
USE_AUTO_MERGE=true

# =============================================================================
# BRANCH CONFIGURATION
# =============================================================================

# Comma-separated list of allowed base branches
ALLOWED_BASE_BRANCHES="development"

# =============================================================================
# COMMENT CONFIGURATION
# =============================================================================

# HTML comment tag used to identify status comments
STATUS_COMMENT_TAG="<!-- pr-approval-checks-gh -->"

# =============================================================================
# POLLING CONFIGURATION
# =============================================================================

# Maximum number of attempts to check mergeable status
MAX_MERGEABLE_ATTEMPTS=5

# Delay between mergeable status checks (seconds)
MERGEABLE_CHECK_DELAY=2

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: DEBUG, INFO, WARN, ERROR
LOG_LEVEL="INFO"

# Whether to include timestamps in log output
LOG_TIMESTAMPS=true

# Whether to include log levels in output
LOG_LEVELS=true

# =============================================================================
# GITHUB API CONFIGURATION
# =============================================================================

# GitHub API pagination limit for reviews
REVIEWS_PAGE_LIMIT=100

# GitHub API pagination limit for comments
COMMENTS_PAGE_LIMIT=100

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Directory for temporary cache files
CACHE_DIR="/tmp/pr-approval-cache"

# Cache TTL in seconds (5 minutes)
CACHE_TTL=300

# Whether to enable caching
ENABLE_CACHE=true
