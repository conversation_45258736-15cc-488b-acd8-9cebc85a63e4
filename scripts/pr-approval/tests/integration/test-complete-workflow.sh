#!/usr/bin/env bash
# =============================================================================
# Integration Tests for Complete PR Approval Workflow
# =============================================================================
# This file contains end-to-end integration tests that test the complete
# PR approval workflow with mocked GitHub API responses.

set -euo pipefail

# Source test framework and configuration
# shellcheck source=../test-utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/../test-utils.sh"
# shellcheck source=../test-config.sh
source "$(dirname "${BASH_SOURCE[0]}")/../test-config.sh"

# Source the main script
# shellcheck source=../../main.sh
source "$(dirname "${BASH_SOURCE[0]}")/../../main.sh"

# =============================================================================
# COMPLETE WORKFLOW TESTS
# =============================================================================

test_complete_workflow_bugfix_success() {
    enable_mocking
    setup_test_environment

    # Set up complete mock workflow for successful bug-fix PR
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_bugfix_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_bugfix_reviews)" 0
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    add_mock_response "gh issue edit $TEST_PR_NUMBER --repo $TEST_REPO --add-label" "" 0
    add_mock_response "gh pr merge $TEST_PR_NUMBER --repo $TEST_REPO --squash --delete-branch --auto" "Pull request #$TEST_PR_NUMBER merged" 0

    # Run the complete workflow
    local exit_code=0
    if main 2>/dev/null; then
        exit_code=$?
    else
        exit_code=$?
    fi

    # Verify successful completion
    assert_equals "$EXIT_SUCCESS" $exit_code "Bug-fix workflow should complete successfully"

    # Verify that merge was attempted
    local merge_calls
    merge_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh pr merge" || echo "0")
    assert_equals "1" "$merge_calls" "Should attempt merge once"

    # Verify that label was added
    local label_calls
    label_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh issue edit.*add-label" || echo "0")
    assert_equals "1" "$label_calls" "Should add ready-for-merge label"

    cleanup_test_environment
    disable_mocking
}

test_complete_workflow_feature_insufficient() {
    enable_mocking
    setup_test_environment

    # Set up complete mock workflow for feature PR with insufficient approvals
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_insufficient_feature_reviews)" 0
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0

    # Run the complete workflow
    local exit_code=0
    if main 2>/dev/null; then
        exit_code=$?
    else
        exit_code=$?
    fi

    # Verify successful completion (workflow succeeds but doesn't merge)
    assert_equals "$EXIT_SUCCESS" $exit_code "Feature workflow with insufficient approvals should complete successfully"

    # Verify that merge was NOT attempted
    local merge_calls
    merge_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh pr merge" || echo "0")
    assert_equals "0" "$merge_calls" "Should not attempt merge with insufficient approvals"

    # Verify that comment was posted
    local comment_calls
    comment_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh api.*POST.*comments" || echo "0")
    assert_equals "1" "$comment_calls" "Should post status comment"

    cleanup_test_environment
    disable_mocking
}

test_complete_workflow_draft_pr() {
    enable_mocking
    setup_test_environment

    # Set up mock workflow for draft PR
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_draft_pr_data)" 0

    # Run the complete workflow
    local exit_code=0
    if main 2>/dev/null; then
        exit_code=$?
    else
        exit_code=$?
    fi

    # Verify successful completion (draft PRs are skipped)
    assert_equals "$EXIT_SUCCESS" $exit_code "Draft PR workflow should complete successfully"

    # Verify that no merge was attempted
    local merge_calls
    merge_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh pr merge" || echo "0")
    assert_equals "0" "$merge_calls" "Should not attempt merge for draft PR"

    # Verify that no comments were posted
    local comment_calls
    comment_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh api.*POST.*comments" || echo "0")
    assert_equals "0" "$comment_calls" "Should not post comments for draft PR"

    cleanup_test_environment
    disable_mocking
}

test_complete_workflow_invalid_base() {
    enable_mocking
    setup_test_environment

    # Set up mock workflow for PR with invalid base branch
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_invalid_base_pr_data)" 0

    # Run the complete workflow
    local exit_code=0
    if main 2>/dev/null; then
        exit_code=$?
    else
        exit_code=$?
    fi

    # Verify successful completion (invalid base PRs are skipped)
    assert_equals "$EXIT_SUCCESS" $exit_code "Invalid base PR workflow should complete successfully"

    # Verify that no merge was attempted
    local merge_calls
    merge_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh pr merge" || echo "0")
    assert_equals "0" "$merge_calls" "Should not attempt merge for invalid base PR"

    cleanup_test_environment
    disable_mocking
}

test_complete_workflow_feature_sufficient() {
    enable_mocking
    setup_test_environment

    # Set up complete mock workflow for feature PR with sufficient approvals
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_feature_reviews)" 0
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    add_mock_response "gh pr merge $TEST_PR_NUMBER --repo $TEST_REPO --squash --delete-branch --auto" "Pull request #$TEST_PR_NUMBER merged" 0

    # Run the complete workflow
    local exit_code=0
    if main 2>/dev/null; then
        exit_code=$?
    else
        exit_code=$?
    fi

    # Verify successful completion
    assert_equals "$EXIT_SUCCESS" $exit_code "Feature workflow with sufficient approvals should complete successfully"

    # Verify that merge was attempted
    local merge_calls
    merge_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh pr merge" || echo "0")
    assert_equals "1" "$merge_calls" "Should attempt merge with sufficient approvals"

    # Verify that ready-for-merge label was NOT added (only for bug-fix PRs)
    local label_calls
    label_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh issue edit.*add-label" || echo "0")
    assert_equals "0" "$label_calls" "Should not add ready-for-merge label for feature PR"

    cleanup_test_environment
    disable_mocking
}

test_complete_workflow_merge_failure() {
    enable_mocking
    setup_test_environment

    # Set up mock workflow with merge failure
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_bugfix_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_bugfix_reviews)" 0
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    add_mock_response "gh issue edit $TEST_PR_NUMBER --repo $TEST_REPO --add-label" "" 0
    add_mock_response "gh pr merge $TEST_PR_NUMBER --repo $TEST_REPO --squash --delete-branch --auto" "Merge failed: checks required" 1

    # Run the complete workflow
    local exit_code=0
    if main 2>/dev/null; then
        exit_code=$?
    else
        exit_code=$?
    fi

    # Verify successful completion (workflow handles merge failure gracefully)
    assert_equals "$EXIT_SUCCESS" $exit_code "Workflow should complete successfully even with merge failure"

    # Verify that merge was attempted
    local merge_calls
    merge_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh pr merge" || echo "0")
    assert_equals "1" "$merge_calls" "Should attempt merge"

    # Verify that failure comment was posted
    local comment_calls
    comment_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh api.*POST.*comments" || echo "0")
    assert_true "$comment_calls -ge 1" "Should post status comments including failure message"

    cleanup_test_environment
    disable_mocking
}

test_complete_workflow_api_error() {
    enable_mocking
    setup_test_environment

    # Set up mock workflow with API error
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "" 1

    # Run the complete workflow
    local exit_code=0
    if main 2>/dev/null; then
        exit_code=$?
    else
        exit_code=$?
    fi

    # Verify that workflow fails with API error
    assert_equals "$EXIT_API_ERROR" $exit_code "Workflow should fail with API error"

    cleanup_test_environment
    disable_mocking
}

# =============================================================================
# WORKFLOW STATE TESTS
# =============================================================================

test_workflow_state_management() {
    enable_mocking
    setup_test_environment

    # Set up mock workflow
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_feature_reviews)" 0
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    add_mock_response "gh pr merge $TEST_PR_NUMBER --repo $TEST_REPO --squash --delete-branch --auto" "Pull request #$TEST_PR_NUMBER merged" 0

    # Run workflow and capture output
    local workflow_output
    workflow_output=$(create_temp_file "workflow_output")
    main 2>"$workflow_output" || true

    # Verify workflow state is properly managed
    assert_contains "$(cat "$workflow_output")" "Starting PR Approval Workflow" "Should show workflow start"
    assert_contains "$(cat "$workflow_output")" "Workflow Summary" "Should show workflow summary"

    cleanup_test_environment
    disable_mocking
}

# =============================================================================
# CONFIGURATION TESTS
# =============================================================================

test_workflow_with_custom_config() {
    enable_mocking
    setup_test_environment

    # Override configuration
    export BUG_FIX_APPROVALS=2
    export FEATURE_APPROVALS=3
    export MERGE_METHOD="merge"

    # Set up mock workflow for bug-fix PR (now needs 2 approvals)
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_bugfix_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_feature_reviews)" 0 # 2 approvals
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    add_mock_response "gh issue edit $TEST_PR_NUMBER --repo $TEST_REPO --add-label" "" 0
    add_mock_response "gh pr merge $TEST_PR_NUMBER --repo $TEST_REPO --merge --delete-branch --auto" "Pull request #$TEST_PR_NUMBER merged" 0

    # Run the complete workflow
    local exit_code=0
    if main 2>/dev/null; then
        exit_code=$?
    else
        exit_code=$?
    fi

    # Verify successful completion with custom config
    assert_equals "$EXIT_SUCCESS" $exit_code "Workflow should work with custom configuration"

    # Verify that merge method was used correctly
    local merge_calls
    merge_calls=$(printf '%s\n' "${MOCK_CALLS[@]}" | grep -c "gh pr merge.*--merge" || echo "0")
    assert_equals "1" "$merge_calls" "Should use custom merge method"

    cleanup_test_environment
    disable_mocking
}

# =============================================================================
# ERROR HANDLING TESTS
# =============================================================================

test_workflow_error_recovery() {
    enable_mocking
    setup_test_environment

    # Set up mock workflow with intermittent failures
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_insufficient_feature_reviews)" 0
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0

    # Run workflow
    local exit_code=0
    if main 2>/dev/null; then
        exit_code=$?
    else
        exit_code=$?
    fi

    # Verify that workflow completes successfully even with insufficient approvals
    assert_equals "$EXIT_SUCCESS" $exit_code "Workflow should handle insufficient approvals gracefully"

    cleanup_test_environment
    disable_mocking
}

# =============================================================================
# TEST RUNNER
# =============================================================================

main() {
    init_test_framework "complete-workflow-tests"
    set_verbose

    echo "🧪 Running Complete Workflow Integration Tests"
    echo "=============================================="

    # Run complete workflow tests
    echo "🔄 Testing Complete Workflows..."
    run_test "test_complete_workflow_bugfix_success" "Bug-fix PR successful workflow"
    run_test "test_complete_workflow_feature_insufficient" "Feature PR insufficient approvals workflow"
    run_test "test_complete_workflow_draft_pr" "Draft PR workflow"
    run_test "test_complete_workflow_invalid_base" "Invalid base branch workflow"
    run_test "test_complete_workflow_feature_sufficient" "Feature PR sufficient approvals workflow"
    run_test "test_complete_workflow_merge_failure" "Merge failure handling workflow"
    run_test "test_complete_workflow_api_error" "API error handling workflow"

    # Run workflow state tests
    echo "📊 Testing Workflow State Management..."
    run_test "test_workflow_state_management" "Workflow state management"

    # Run configuration tests
    echo "⚙️ Testing Configuration..."
    run_test "test_workflow_with_custom_config" "Workflow with custom configuration"

    # Run error handling tests
    echo "⚠️ Testing Error Handling..."
    run_test "test_workflow_error_recovery" "Workflow error recovery"

    # Print summary and exit
    print_test_summary
}

# Run tests if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
