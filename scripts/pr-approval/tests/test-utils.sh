#!/usr/bin/env bash
# =============================================================================
# Testing Framework Utilities
# =============================================================================
# This file contains utilities for testing the PR approval system, including
# test runners, assertion functions, mocking utilities, and test reporting.

set -euo pipefail

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

# Test framework configuration
readonly TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SCRIPT_DIR="$(cd "${TEST_DIR}/.." && pwd)"
readonly PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"

# Test state
declare -g TEST_COUNT=0
declare -g PASSED_COUNT=0
declare -g FAILED_COUNT=0
declare -g SKIPPED_COUNT=0
declare -g CURRENT_TEST=""
declare -g TEST_START_TIME=""

# Test output
declare -g TEST_OUTPUT_FILE=""
declare -g VERBOSE_OUTPUT=false

# Mock state
declare -g MOCK_ENABLED=false
declare -g MOCK_RESPONSES=()
declare -g MOCK_CALLS=()

# =============================================================================
# TEST FRAMEWORK FUNCTIONS
# =============================================================================

# Initialize test framework
init_test_framework() {
    local test_name="${1:-test}"
    
    TEST_COUNT=0
    PASSED_COUNT=0
    FAILED_COUNT=0
    SKIPPED_COUNT=0
    
    # Create test output directory
    mkdir -p "${TEST_DIR}/output"
    TEST_OUTPUT_FILE="${TEST_DIR}/output/${test_name}-$(date +%Y%m%d-%H%M%S).log"
    
    # Set up test environment
    export TEST_MODE=true
    export LOG_LEVEL=ERROR  # Reduce noise during tests
    export CACHE_DIR="${TEST_DIR}/cache"
    export ENABLE_CACHE=false  # Disable caching during tests by default
    
    # Clean up any previous test artifacts
    rm -rf "${TEST_DIR}/cache" 2>/dev/null || true
    mkdir -p "${TEST_DIR}/cache"
    
    echo "🧪 Test Framework Initialized: $test_name"
    echo "📁 Output: $TEST_OUTPUT_FILE"
}

# Start a test case
start_test() {
    local test_name="$1"
    local description="${2:-}"
    
    CURRENT_TEST="$test_name"
    TEST_START_TIME=$(date +%s)
    TEST_COUNT=$((TEST_COUNT + 1))
    
    if [[ "$VERBOSE_OUTPUT" == "true" ]]; then
        echo -n "  🔍 $test_name"
        if [[ -n "$description" ]]; then
            echo " - $description"
        else
            echo
        fi
    fi
    
    # Log test start
    echo "=== TEST START: $test_name ===" >> "$TEST_OUTPUT_FILE"
    if [[ -n "$description" ]]; then
        echo "Description: $description" >> "$TEST_OUTPUT_FILE"
    fi
    echo "Started: $(date)" >> "$TEST_OUTPUT_FILE"
}

# End a test case
end_test() {
    local status="$1"  # PASS, FAIL, SKIP
    local message="${2:-}"
    
    local end_time
    end_time=$(date +%s)
    local duration=$((end_time - TEST_START_TIME))
    
    case "$status" in
        "PASS")
            PASSED_COUNT=$((PASSED_COUNT + 1))
            if [[ "$VERBOSE_OUTPUT" == "true" ]]; then
                echo "    ✅ PASSED (${duration}s)"
            fi
            ;;
        "FAIL")
            FAILED_COUNT=$((FAILED_COUNT + 1))
            if [[ "$VERBOSE_OUTPUT" == "true" ]]; then
                echo "    ❌ FAILED (${duration}s)"
                if [[ -n "$message" ]]; then
                    echo "       $message"
                fi
            fi
            ;;
        "SKIP")
            SKIPPED_COUNT=$((SKIPPED_COUNT + 1))
            if [[ "$VERBOSE_OUTPUT" == "true" ]]; then
                echo "    ⏭️  SKIPPED"
                if [[ -n "$message" ]]; then
                    echo "       $message"
                fi
            fi
            ;;
    esac
    
    # Log test end
    echo "Status: $status" >> "$TEST_OUTPUT_FILE"
    echo "Duration: ${duration}s" >> "$TEST_OUTPUT_FILE"
    if [[ -n "$message" ]]; then
        echo "Message: $message" >> "$TEST_OUTPUT_FILE"
    fi
    echo "=== TEST END: $CURRENT_TEST ===" >> "$TEST_OUTPUT_FILE"
    echo >> "$TEST_OUTPUT_FILE"
}

# =============================================================================
# ASSERTION FUNCTIONS
# =============================================================================

# Assert that two values are equal
assert_equals() {
    local expected="$1"
    local actual="$2"
    local message="${3:-Values should be equal}"
    
    if [[ "$expected" == "$actual" ]]; then
        return 0
    else
        echo "ASSERTION FAILED: $message" >> "$TEST_OUTPUT_FILE"
        echo "  Expected: '$expected'" >> "$TEST_OUTPUT_FILE"
        echo "  Actual:   '$actual'" >> "$TEST_OUTPUT_FILE"
        return 1
    fi
}

# Assert that two values are not equal
assert_not_equals() {
    local expected="$1"
    local actual="$2"
    local message="${3:-Values should not be equal}"
    
    if [[ "$expected" != "$actual" ]]; then
        return 0
    else
        echo "ASSERTION FAILED: $message" >> "$TEST_OUTPUT_FILE"
        echo "  Both values: '$expected'" >> "$TEST_OUTPUT_FILE"
        return 1
    fi
}

# Assert that a value is true
assert_true() {
    local value="$1"
    local message="${2:-Value should be true}"
    
    if [[ "$value" == "true" ]] || [[ "$value" == "0" ]]; then
        return 0
    else
        echo "ASSERTION FAILED: $message" >> "$TEST_OUTPUT_FILE"
        echo "  Value: '$value'" >> "$TEST_OUTPUT_FILE"
        return 1
    fi
}

# Assert that a value is false
assert_false() {
    local value="$1"
    local message="${2:-Value should be false}"
    
    if [[ "$value" == "false" ]] || [[ "$value" != "0" ]]; then
        return 0
    else
        echo "ASSERTION FAILED: $message" >> "$TEST_OUTPUT_FILE"
        echo "  Value: '$value'" >> "$TEST_OUTPUT_FILE"
        return 1
    fi
}

# Assert that a string contains a substring
assert_contains() {
    local haystack="$1"
    local needle="$2"
    local message="${3:-String should contain substring}"
    
    if [[ "$haystack" == *"$needle"* ]]; then
        return 0
    else
        echo "ASSERTION FAILED: $message" >> "$TEST_OUTPUT_FILE"
        echo "  Haystack: '$haystack'" >> "$TEST_OUTPUT_FILE"
        echo "  Needle:   '$needle'" >> "$TEST_OUTPUT_FILE"
        return 1
    fi
}

# Assert that a file exists
assert_file_exists() {
    local file_path="$1"
    local message="${2:-File should exist}"
    
    if [[ -f "$file_path" ]]; then
        return 0
    else
        echo "ASSERTION FAILED: $message" >> "$TEST_OUTPUT_FILE"
        echo "  File: '$file_path'" >> "$TEST_OUTPUT_FILE"
        return 1
    fi
}

# Assert that a command succeeds
assert_success() {
    local command="$1"
    local message="${2:-Command should succeed}"
    
    if eval "$command" &>> "$TEST_OUTPUT_FILE"; then
        return 0
    else
        echo "ASSERTION FAILED: $message" >> "$TEST_OUTPUT_FILE"
        echo "  Command: '$command'" >> "$TEST_OUTPUT_FILE"
        return 1
    fi
}

# Assert that a command fails
assert_failure() {
    local command="$1"
    local message="${2:-Command should fail}"
    
    if ! eval "$command" &>> "$TEST_OUTPUT_FILE"; then
        return 0
    else
        echo "ASSERTION FAILED: $message" >> "$TEST_OUTPUT_FILE"
        echo "  Command: '$command'" >> "$TEST_OUTPUT_FILE"
        return 1
    fi
}

# =============================================================================
# MOCKING FUNCTIONS
# =============================================================================

# Enable mocking
enable_mocking() {
    MOCK_ENABLED=true
    MOCK_RESPONSES=()
    MOCK_CALLS=()
    
    # Create mock functions
    create_mock_functions
}

# Disable mocking
disable_mocking() {
    MOCK_ENABLED=false
    MOCK_RESPONSES=()
    MOCK_CALLS=()
    
    # Remove mock functions
    remove_mock_functions
}

# Add a mock response
add_mock_response() {
    local command="$1"
    local response="$2"
    local exit_code="${3:-0}"
    
    MOCK_RESPONSES+=("$command|$response|$exit_code")
}

# Get mock response
get_mock_response() {
    local command="$1"
    
    for mock in "${MOCK_RESPONSES[@]}"; do
        local mock_command="${mock%%|*}"
        local rest="${mock#*|}"
        local mock_response="${rest%%|*}"
        local mock_exit_code="${rest##*|}"
        
        if [[ "$command" == *"$mock_command"* ]]; then
            echo "$mock_response"
            return "$mock_exit_code"
        fi
    done
    
    # No mock found, return empty response
    echo ""
    return 1
}

# Record mock call
record_mock_call() {
    local command="$1"
    MOCK_CALLS+=("$command")
}

# Create mock functions
create_mock_functions() {
    # Mock gh command
    gh() {
        local full_command="gh $*"
        record_mock_call "$full_command"
        
        if [[ "$MOCK_ENABLED" == "true" ]]; then
            get_mock_response "$full_command"
        else
            command gh "$@"
        fi
    }
    export -f gh
    
    # Mock jq command (usually we want real jq for parsing)
    # jq() {
    #     local full_command="jq $*"
    #     record_mock_call "$full_command"
    #     command jq "$@"
    # }
    # export -f jq
}

# Remove mock functions
remove_mock_functions() {
    unset -f gh 2>/dev/null || true
    # unset -f jq 2>/dev/null || true
}

# =============================================================================
# TEST REPORTING
# =============================================================================

# Print test summary
print_test_summary() {
    local total_tests=$((PASSED_COUNT + FAILED_COUNT + SKIPPED_COUNT))
    
    echo
    echo "🧪 Test Summary"
    echo "==============="
    echo "Total Tests:  $total_tests"
    echo "Passed:       $PASSED_COUNT"
    echo "Failed:       $FAILED_COUNT"
    echo "Skipped:      $SKIPPED_COUNT"
    echo
    
    if [[ $FAILED_COUNT -eq 0 ]]; then
        echo "✅ All tests passed!"
        return 0
    else
        echo "❌ Some tests failed!"
        echo "📄 Check log file: $TEST_OUTPUT_FILE"
        return 1
    fi
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Set verbose output
set_verbose() {
    VERBOSE_OUTPUT=true
}

# Create temporary file
create_temp_file() {
    local prefix="${1:-test}"
    mktemp "${TEST_DIR}/cache/${prefix}.XXXXXX"
}

# Clean up test artifacts
cleanup_test_artifacts() {
    rm -rf "${TEST_DIR}/cache" 2>/dev/null || true
    disable_mocking
}

# Source a script for testing
source_script() {
    local script_path="$1"
    
    if [[ -f "$script_path" ]]; then
        # shellcheck source=/dev/null
        source "$script_path"
    else
        echo "ERROR: Script not found: $script_path" >> "$TEST_OUTPUT_FILE"
        return 1
    fi
}

# =============================================================================
# TEST RUNNER FUNCTIONS
# =============================================================================

# Run a single test function
run_test() {
    local test_function="$1"
    local test_description="${2:-}"
    
    start_test "$test_function" "$test_description"
    
    # Run the test function
    if "$test_function" &>> "$TEST_OUTPUT_FILE"; then
        end_test "PASS"
    else
        end_test "FAIL" "Test function failed"
    fi
}

# Run all test functions in current scope
run_all_tests() {
    local test_prefix="${1:-test_}"
    
    # Get all functions that start with the test prefix
    local test_functions
    test_functions=$(declare -F | grep -E "declare -f ${test_prefix}" | awk '{print $3}' || true)
    
    if [[ -z "$test_functions" ]]; then
        echo "No test functions found with prefix: $test_prefix"
        return 1
    fi
    
    # Run each test function
    while IFS= read -r test_function; do
        if [[ -n "$test_function" ]]; then
            run_test "$test_function"
        fi
    done <<< "$test_functions"
}

# =============================================================================
# INITIALIZATION
# =============================================================================

# Auto-initialize when sourced
if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
    # Set up cleanup trap
    trap cleanup_test_artifacts EXIT
fi
