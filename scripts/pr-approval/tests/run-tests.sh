#!/usr/bin/env bash
# =============================================================================
# Test Runner for PR Approval System
# =============================================================================
# This script runs all unit and integration tests for the PR approval system.

set -euo pipefail

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"

# Test configuration
VERBOSE=false
UNIT_TESTS_ONLY=false
INTEGRATION_TESTS_ONLY=false
SPECIFIC_TEST=""
PARALLEL=false

# Test results
declare -g TOTAL_TESTS=0
declare -g TOTAL_PASSED=0
declare -g TOTAL_FAILED=0
declare -g FAILED_TESTS=()

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Print usage information
show_usage() {
    cat << EOF
PR Approval Test Runner

USAGE:
    $0 [OPTIONS]

DESCRIPTION:
    Runs unit and integration tests for the PR approval system.

OPTIONS:
    -h, --help              Show this help message
    -v, --verbose           Enable verbose output
    -u, --unit-only         Run only unit tests
    -i, --integration-only  Run only integration tests
    -t, --test TEST_NAME    Run specific test file
    -p, --parallel          Run tests in parallel (experimental)
    --validate-mocks        Validate mock data before running tests

EXAMPLES:
    # Run all tests
    $0

    # Run with verbose output
    $0 --verbose

    # Run only unit tests
    $0 --unit-only

    # Run specific test
    $0 --test test-utils.sh

    # Run tests in parallel
    $0 --parallel

EXIT CODES:
    0   - All tests passed
    1   - Some tests failed
    2   - Invalid arguments
    3   - Test setup failed

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -u|--unit-only)
                UNIT_TESTS_ONLY=true
                shift
                ;;
            -i|--integration-only)
                INTEGRATION_TESTS_ONLY=true
                shift
                ;;
            -t|--test)
                SPECIFIC_TEST="$2"
                shift 2
                ;;
            -p|--parallel)
                PARALLEL=true
                shift
                ;;
            --validate-mocks)
                validate_mock_data
                exit $?
                ;;
            *)
                echo "Unknown option: $1" >&2
                show_usage
                exit 2
                ;;
        esac
    done
}

# Validate test environment
validate_environment() {
    echo "🔍 Validating test environment..."
    
    # Check required commands
    local required_commands=("bash" "jq" "gh")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            echo "❌ Required command not found: $cmd" >&2
            return 3
        fi
    done
    
    # Check test framework files
    local required_files=(
        "${SCRIPT_DIR}/test-utils.sh"
        "${SCRIPT_DIR}/test-config.sh"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            echo "❌ Required test file not found: $file" >&2
            return 3
        fi
    done
    
    echo "✅ Test environment validated"
    return 0
}

# Validate mock data
validate_mock_data() {
    echo "🔍 Validating mock data..."
    
    # Source test configuration
    # shellcheck source=./test-config.sh
    source "${SCRIPT_DIR}/test-config.sh"
    
    # Validate mock data
    if validate_mock_data; then
        echo "✅ Mock data validation passed"
        return 0
    else
        echo "❌ Mock data validation failed" >&2
        return 1
    fi
}

# =============================================================================
# TEST EXECUTION FUNCTIONS
# =============================================================================

# Run a single test file
run_test_file() {
    local test_file="$1"
    local test_name
    test_name=$(basename "$test_file" .sh)
    
    echo "🧪 Running $test_name..."
    
    local start_time end_time duration
    start_time=$(date +%s)
    
    local output_file="${SCRIPT_DIR}/output/${test_name}-$(date +%Y%m%d-%H%M%S).log"
    mkdir -p "$(dirname "$output_file")"
    
    # Run the test
    local exit_code=0
    if [[ "$VERBOSE" == "true" ]]; then
        if "$test_file" 2>&1 | tee "$output_file"; then
            exit_code=0
        else
            exit_code=$?
        fi
    else
        if "$test_file" > "$output_file" 2>&1; then
            exit_code=0
        else
            exit_code=$?
        fi
    fi
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    # Parse test results from output
    local passed failed
    passed=$(grep -c "✅ PASSED" "$output_file" 2>/dev/null || echo "0")
    failed=$(grep -c "❌ FAILED" "$output_file" 2>/dev/null || echo "0")
    
    TOTAL_TESTS=$((TOTAL_TESTS + passed + failed))
    TOTAL_PASSED=$((TOTAL_PASSED + passed))
    TOTAL_FAILED=$((TOTAL_FAILED + failed))
    
    if [[ $exit_code -eq 0 ]]; then
        echo "  ✅ $test_name completed successfully (${duration}s)"
        if [[ "$VERBOSE" == "false" ]]; then
            echo "     Passed: $passed, Failed: $failed"
        fi
    else
        echo "  ❌ $test_name failed (${duration}s)"
        FAILED_TESTS+=("$test_name")
        if [[ "$VERBOSE" == "false" ]]; then
            echo "     Check log: $output_file"
        fi
    fi
    
    return $exit_code
}

# Run tests in parallel
run_tests_parallel() {
    local test_files=("$@")
    local pids=()
    local results=()
    
    echo "🚀 Running ${#test_files[@]} tests in parallel..."
    
    # Start all tests
    for test_file in "${test_files[@]}"; do
        run_test_file "$test_file" &
        pids+=($!)
    done
    
    # Wait for all tests to complete
    local overall_result=0
    for i in "${!pids[@]}"; do
        local pid=${pids[$i]}
        if wait "$pid"; then
            results+=("PASS")
        else
            results+=("FAIL")
            overall_result=1
        fi
    done
    
    return $overall_result
}

# Run tests sequentially
run_tests_sequential() {
    local test_files=("$@")
    local overall_result=0
    
    echo "🔄 Running ${#test_files[@]} tests sequentially..."
    
    for test_file in "${test_files[@]}"; do
        if ! run_test_file "$test_file"; then
            overall_result=1
        fi
    done
    
    return $overall_result
}

# =============================================================================
# TEST DISCOVERY
# =============================================================================

# Find unit test files
find_unit_tests() {
    find "${SCRIPT_DIR}/unit" -name "test-*.sh" -type f -executable 2>/dev/null | sort
}

# Find integration test files
find_integration_tests() {
    find "${SCRIPT_DIR}/integration" -name "test-*.sh" -type f -executable 2>/dev/null | sort
}

# Find all test files
find_all_tests() {
    {
        find_unit_tests
        find_integration_tests
    } | sort
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

# Print test summary
print_summary() {
    echo
    echo "📊 Test Summary"
    echo "==============="
    echo "Total Tests:  $TOTAL_TESTS"
    echo "Passed:       $TOTAL_PASSED"
    echo "Failed:       $TOTAL_FAILED"
    
    if [[ ${#FAILED_TESTS[@]} -gt 0 ]]; then
        echo
        echo "❌ Failed Tests:"
        for test in "${FAILED_TESTS[@]}"; do
            echo "  - $test"
        done
    fi
    
    echo
    if [[ $TOTAL_FAILED -eq 0 ]]; then
        echo "🎉 All tests passed!"
        return 0
    else
        echo "💥 Some tests failed!"
        return 1
    fi
}

# Main function
main() {
    echo "🧪 PR Approval System Test Runner"
    echo "================================="
    
    # Validate environment
    if ! validate_environment; then
        exit 3
    fi
    
    # Validate mock data
    if ! validate_mock_data; then
        echo "⚠️ Mock data validation failed, but continuing with tests..."
    fi
    
    # Determine which tests to run
    local test_files=()
    
    if [[ -n "$SPECIFIC_TEST" ]]; then
        # Run specific test
        local test_path
        if [[ -f "${SCRIPT_DIR}/unit/${SPECIFIC_TEST}" ]]; then
            test_path="${SCRIPT_DIR}/unit/${SPECIFIC_TEST}"
        elif [[ -f "${SCRIPT_DIR}/integration/${SPECIFIC_TEST}" ]]; then
            test_path="${SCRIPT_DIR}/integration/${SPECIFIC_TEST}"
        elif [[ -f "$SPECIFIC_TEST" ]]; then
            test_path="$SPECIFIC_TEST"
        else
            echo "❌ Test file not found: $SPECIFIC_TEST" >&2
            exit 2
        fi
        test_files=("$test_path")
    elif [[ "$UNIT_TESTS_ONLY" == "true" ]]; then
        # Run only unit tests
        readarray -t test_files < <(find_unit_tests)
    elif [[ "$INTEGRATION_TESTS_ONLY" == "true" ]]; then
        # Run only integration tests
        readarray -t test_files < <(find_integration_tests)
    else
        # Run all tests
        readarray -t test_files < <(find_all_tests)
    fi
    
    if [[ ${#test_files[@]} -eq 0 ]]; then
        echo "❌ No test files found!" >&2
        exit 2
    fi
    
    echo "Found ${#test_files[@]} test files to run"
    
    # Create output directory
    mkdir -p "${SCRIPT_DIR}/output"
    
    # Run tests
    local test_result=0
    if [[ "$PARALLEL" == "true" ]]; then
        if ! run_tests_parallel "${test_files[@]}"; then
            test_result=1
        fi
    else
        if ! run_tests_sequential "${test_files[@]}"; then
            test_result=1
        fi
    fi
    
    # Print summary
    if ! print_summary; then
        test_result=1
    fi
    
    exit $test_result
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    parse_args "$@"
    main
fi
