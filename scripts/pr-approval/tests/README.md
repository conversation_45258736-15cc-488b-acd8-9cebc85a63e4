# PR Approval System Testing Framework

This directory contains a comprehensive testing framework for the PR approval system, including unit tests, integration tests, and testing utilities.

## 📁 Directory Structure

```bash
tests/
├── README.md                    # This file
├── run-tests.sh                 # Main test runner script
├── test-utils.sh                # Testing framework utilities
├── test-config.sh               # Test configuration and mock data
├── unit/                        # Unit tests
│   ├── test-utils.sh           # Tests for lib/utils.sh
│   ├── test-pr-data.sh         # Tests for lib/pr-data.sh
│   ├── test-approval-logic.sh  # Tests for lib/approval-logic.sh
│   └── test-comment-manager.sh # Tests for lib/comment-manager.sh
├── integration/                 # Integration tests
│   └── test-complete-workflow.sh # End-to-end workflow tests
└── output/                      # Test output and logs (created during runs)
```

## 🚀 Quick Start

### Run All Tests

```bash
# Run all tests with default settings
./scripts/pr-approval/tests/run-tests.sh

# Run all tests with verbose output
./scripts/pr-approval/tests/run-tests.sh --verbose
```

### Run Specific Test Types

```bash
# Run only unit tests
./scripts/pr-approval/tests/run-tests.sh --unit-only

# Run only integration tests
./scripts/pr-approval/tests/run-tests.sh --integration-only

# Run a specific test file
./scripts/pr-approval/tests/run-tests.sh --test test-utils.sh
```

### Validate Mock Data

```bash
# Validate all mock data before running tests
./scripts/pr-approval/tests/run-tests.sh --validate-mocks
```

## 🧪 Test Framework Features

### Assertion Functions

The testing framework provides comprehensive assertion functions:

- `assert_equals expected actual [message]` - Assert two values are equal
- `assert_not_equals expected actual [message]` - Assert two values are not equal
- `assert_true value [message]` - Assert value is true
- `assert_false value [message]` - Assert value is false
- `assert_contains haystack needle [message]` - Assert string contains substring
- `assert_file_exists path [message]` - Assert file exists
- `assert_success command [message]` - Assert command succeeds
- `assert_failure command [message]` - Assert command fails

### Mocking System

The framework includes a powerful mocking system for GitHub API calls:

```bash
# Enable mocking
enable_mocking

# Add mock responses
add_mock_response "gh pr view 123" '{"number": 123, "state": "OPEN"}' 0

# Mock responses are automatically matched and returned
# Disable mocking when done
disable_mocking
```

### Test Structure

Each test follows a consistent structure:

```bash
test_function_name() {
    # Setup
    enable_mocking
    setup_test_environment

    # Add mock responses
    add_mock_response "command" "response" exit_code

    # Execute test
    local result
    result=$(function_under_test "args")

    # Assertions
    assert_equals "expected" "$result" "Description"

    # Cleanup
    cleanup_test_environment
    disable_mocking
}
```

## 📊 Test Categories

### Unit Tests

#### `test-utils.sh`

Tests for the core utilities module (`lib/utils.sh`):

- Logging functions with different levels
- Input validation (repo format, PR numbers, merge methods)
- Utility functions (trim, array operations, string splitting)
- Cache operations (key generation, set/get, TTL validation)
- Error handling and exit codes

#### `test-pr-data.sh`

Tests for the PR data module (`lib/pr-data.sh`):

- Cache key generation for different data types
- Data parsing (PR info, labels, mergeable status)
- API mocking and response handling
- Caching behavior and cache invalidation
- Error handling for invalid inputs and API failures

#### `test-approval-logic.sh`

Tests for the approval logic module (`lib/approval-logic.sh`):

- PR eligibility checks (draft, base branch validation)
- PR type detection (feature vs bug-fix)
- Approval counting with latest review per user
- Approval validation and requirement checking
- Status message generation

#### `test-comment-manager.sh`

Tests for the comment management module (`lib/comment-manager.sh`):

- Finding existing status comments
- Creating and updating comments
- Comment upsert functionality (create or update)
- Specialized comment types (approval status, merge status)
- Comment cleanup and deletion

### Integration Tests

#### `test-complete-workflow.sh`

End-to-end tests for the complete PR approval workflow:

- Bug-fix PR successful workflow
- Feature PR with insufficient/sufficient approvals
- Draft PR and invalid base branch handling
- Merge failure scenarios
- API error handling
- Custom configuration testing
- Workflow state management

## 🔧 Configuration

### Test Environment Variables

The testing framework uses these environment variables:

```bash
# Test repository and PR (set automatically)
TEST_REPO="test-org/test-repo"
TEST_PR_NUMBER="123"
TEST_GH_TOKEN="ghp_test_token_123456789"

# Test configuration (overrides defaults)
BUG_FIX_APPROVALS=1
FEATURE_APPROVALS=2
MERGE_METHOD="squash"
ALLOWED_BASE_BRANCHES="development"

# Test-specific settings
LOG_LEVEL="ERROR"          # Reduce noise during tests
ENABLE_CACHE=false         # Disable caching by default
TEST_MODE=true             # Enable test mode
```

### Mock Data

The framework includes comprehensive mock data for different scenarios:

- **PR Data**: Feature PRs, bug-fix PRs, draft PRs, invalid base branch PRs
- **Review Data**: Sufficient/insufficient approvals, changes requested then approved
- **Comment Data**: Existing status comments, multiple comments, no comments

## 🎯 Writing New Tests

### Adding a Unit Test

1. Create a new test function in the appropriate test file:

```bash
test_new_functionality() {
    # Setup
    enable_mocking
    setup_test_environment

    # Test implementation
    # ...

    # Cleanup
    cleanup_test_environment
    disable_mocking
}
```

2. The test runner will automatically discover and run functions prefixed with `test_`

### Adding Mock Data

1. Add new mock data functions to `test-config.sh`:

```bash
get_mock_new_scenario() {
    cat << 'EOF'
{
  "key": "value"
}
EOF
}
```

2. Export the function:

```bash
export -f get_mock_new_scenario
```

### Adding Integration Tests

1. Create a new test file in the `integration/` directory
2. Follow the same structure as unit tests but test complete workflows
3. Make the file executable: `chmod +x integration/test-new-integration.sh`

## 🔍 Debugging Tests

### Verbose Output

Run tests with verbose output to see detailed execution:

```bash
./scripts/pr-approval/tests/run-tests.sh --verbose
```

### Test Logs

All test output is saved to log files in the `output/` directory:

```bash
# View latest test logs
ls -la scripts/pr-approval/tests/output/

# View specific test log
cat scripts/pr-approval/tests/output/test-utils-20231201-120000.log
```

### Mock Call Inspection

The mocking system records all mock calls for debugging:

```bash
# In your test, inspect mock calls
echo "Mock calls made:"
printf '%s\n' "${MOCK_CALLS[@]}"
```

## 🚀 CI/CD Integration

### GitHub Actions

Tests are automatically run in GitHub Actions:

- On pushes to `development` and `main` branches
- On pull requests affecting PR approval system files
- Manual workflow dispatch with configurable options

### Test Matrix

The CI runs tests in parallel across different modules for faster feedback.

### Artifacts

Test results and logs are uploaded as artifacts for debugging failed runs.

## 📈 Test Coverage

The testing framework provides comprehensive coverage:

- **Unit Tests**: Test individual functions and modules in isolation
- **Integration Tests**: Test complete workflows end-to-end
- **Error Scenarios**: Test error handling and edge cases
- **Configuration**: Test different configuration combinations
- **API Mocking**: Test without making real GitHub API calls

## 🛠️ Maintenance

### Updating Mock Data

When the GitHub API changes or new scenarios are needed:

1. Update mock data functions in `test-config.sh`
2. Validate mock data: `./run-tests.sh --validate-mocks`
3. Update tests that depend on the changed data

### Adding New Modules

When adding new modules to the PR approval system:

1. Create corresponding unit tests in `unit/`
2. Add integration tests if the module affects the workflow
3. Update this README with new test descriptions

### Performance

The test framework is designed for speed:

- Mocking eliminates network calls
- Parallel test execution (experimental)
- Efficient cache management
- Minimal test setup/teardown

## 📚 Best Practices

1. **Test Naming**: Use descriptive test function names that explain what is being tested
2. **Assertions**: Include meaningful assertion messages for easier debugging
3. **Cleanup**: Always clean up test artifacts and disable mocking
4. **Independence**: Tests should not depend on each other
5. **Coverage**: Test both success and failure scenarios
6. **Documentation**: Document complex test scenarios and mock setups

## 🤝 Contributing

When contributing to the testing framework:

1. Follow the existing test structure and naming conventions
2. Add tests for any new functionality
3. Ensure all tests pass before submitting changes
4. Update documentation for new test categories or features
5. Consider adding integration tests for workflow changes
