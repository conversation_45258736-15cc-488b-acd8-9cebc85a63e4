#!/usr/bin/env bash
# =============================================================================
# Test Configuration and Mock Data
# =============================================================================
# This file contains test configuration, mock data, and test fixtures for
# the PR approval system tests.

set -euo pipefail

# =============================================================================
# TEST CONFIGURATION
# =============================================================================

# Test repository and PR data
readonly TEST_REPO="test-org/test-repo"
readonly TEST_PR_NUMBER="123"
readonly TEST_AUTHOR="test-user"

# Test GitHub token (fake for testing)
readonly TEST_GH_TOKEN="ghp_test_token_123456789"

# Test labels
readonly TEST_BUG_LABEL="bug-fix"
readonly TEST_FEATURE_LABEL="feature"
readonly TEST_READY_LABEL="ready-for-merge"

# =============================================================================
# MOCK PR DATA
# =============================================================================

# Mock PR data for a feature PR
get_mock_feature_pr_data() {
    cat << 'EOF'
{
  "number": 123,
  "state": "OPEN",
  "isDraft": false,
  "baseRefName": "development",
  "title": "Add new feature",
  "url": "https://github.com/test-org/test-repo/pull/123",
  "author": {
    "login": "test-user"
  },
  "labels": [
    {
      "name": "feature"
    }
  ],
  "mergeable": "MERGEABLE",
  "mergeStateStatus": "CLEAN"
}
EOF
}

# Mock PR data for a bug-fix PR
get_mock_bugfix_pr_data() {
    cat << 'EOF'
{
  "number": 123,
  "state": "OPEN",
  "isDraft": false,
  "baseRefName": "development",
  "title": "Fix critical bug",
  "url": "https://github.com/test-org/test-repo/pull/123",
  "author": {
    "login": "test-user"
  },
  "labels": [
    {
      "name": "bug-fix"
    }
  ],
  "mergeable": "MERGEABLE",
  "mergeStateStatus": "CLEAN"
}
EOF
}

# Mock PR data for a draft PR
get_mock_draft_pr_data() {
    cat << 'EOF'
{
  "number": 123,
  "state": "OPEN",
  "isDraft": true,
  "baseRefName": "development",
  "title": "Work in progress",
  "url": "https://github.com/test-org/test-repo/pull/123",
  "author": {
    "login": "test-user"
  },
  "labels": [],
  "mergeable": "UNKNOWN",
  "mergeStateStatus": "DRAFT"
}
EOF
}

# Mock PR data for invalid base branch
get_mock_invalid_base_pr_data() {
    cat << 'EOF'
{
  "number": 123,
  "state": "OPEN",
  "isDraft": false,
  "baseRefName": "main",
  "title": "PR to wrong branch",
  "url": "https://github.com/test-org/test-repo/pull/123",
  "author": {
    "login": "test-user"
  },
  "labels": [],
  "mergeable": "MERGEABLE",
  "mergeStateStatus": "CLEAN"
}
EOF
}

# =============================================================================
# MOCK REVIEW DATA
# =============================================================================

# Mock reviews with sufficient approvals for feature (2 approvals)
get_mock_sufficient_feature_reviews() {
    cat << 'EOF'
[
  {
    "id": 1,
    "user": {
      "login": "reviewer1"
    },
    "state": "APPROVED",
    "submitted_at": "2023-01-01T10:00:00Z",
    "body": "LGTM!"
  },
  {
    "id": 2,
    "user": {
      "login": "reviewer2"
    },
    "state": "APPROVED",
    "submitted_at": "2023-01-01T11:00:00Z",
    "body": "Looks good"
  }
]
EOF
}

# Mock reviews with insufficient approvals for feature (1 approval)
get_mock_insufficient_feature_reviews() {
    cat << 'EOF'
[
  {
    "id": 1,
    "user": {
      "login": "reviewer1"
    },
    "state": "APPROVED",
    "submitted_at": "2023-01-01T10:00:00Z",
    "body": "LGTM!"
  }
]
EOF
}

# Mock reviews with sufficient approvals for bug-fix (1 approval)
get_mock_sufficient_bugfix_reviews() {
    cat << 'EOF'
[
  {
    "id": 1,
    "user": {
      "login": "reviewer1"
    },
    "state": "APPROVED",
    "submitted_at": "2023-01-01T10:00:00Z",
    "body": "Bug fix looks good"
  }
]
EOF
}

# Mock reviews with changes requested
get_mock_changes_requested_reviews() {
    cat << 'EOF'
[
  {
    "id": 1,
    "user": {
      "login": "reviewer1"
    },
    "state": "CHANGES_REQUESTED",
    "submitted_at": "2023-01-01T10:00:00Z",
    "body": "Please fix the formatting"
  },
  {
    "id": 2,
    "user": {
      "login": "reviewer1"
    },
    "state": "APPROVED",
    "submitted_at": "2023-01-01T11:00:00Z",
    "body": "Thanks for the fixes!"
  }
]
EOF
}

# Mock empty reviews
get_mock_no_reviews() {
    echo "[]"
}

# =============================================================================
# MOCK COMMENT DATA
# =============================================================================

# Mock existing status comment
get_mock_existing_status_comment() {
    cat << 'EOF'
[
  {
    "id": 456,
    "body": "Feature PR requires at least 2 approval(s); currently has 1.\n\n<!-- pr-approval-checks-gh -->",
    "created_at": "2023-01-01T09:00:00Z"
  }
]
EOF
}

# Mock no existing comments
get_mock_no_comments() {
    echo "[]"
}

# Mock multiple comments with status comment
get_mock_multiple_comments() {
    cat << 'EOF'
[
  {
    "id": 455,
    "body": "This is a regular comment",
    "created_at": "2023-01-01T08:00:00Z"
  },
  {
    "id": 456,
    "body": "Feature PR requires at least 2 approval(s); currently has 1.\n\n<!-- pr-approval-checks-gh -->",
    "created_at": "2023-01-01T09:00:00Z"
  },
  {
    "id": 457,
    "body": "Another regular comment",
    "created_at": "2023-01-01T10:00:00Z"
  }
]
EOF
}

# =============================================================================
# MOCK API RESPONSES
# =============================================================================

# Set up standard mock responses for a feature PR workflow
setup_feature_pr_mocks() {
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_insufficient_feature_reviews)" 0
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
}

# Set up standard mock responses for a bug-fix PR workflow
setup_bugfix_pr_mocks() {
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_bugfix_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_bugfix_reviews)" 0
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    add_mock_response "gh issue edit $TEST_PR_NUMBER --repo $TEST_REPO --add-label" "" 0
    add_mock_response "gh pr merge $TEST_PR_NUMBER --repo $TEST_REPO" "Pull request #$TEST_PR_NUMBER merged" 0
}

# Set up mock responses for draft PR
setup_draft_pr_mocks() {
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_draft_pr_data)" 0
}

# Set up mock responses for invalid base branch
setup_invalid_base_pr_mocks() {
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_invalid_base_pr_data)" 0
}

# =============================================================================
# TEST ENVIRONMENT SETUP
# =============================================================================

# Set up test environment variables
setup_test_environment() {
    export REPO="$TEST_REPO"
    export PR_NUMBER="$TEST_PR_NUMBER"
    export GH_TOKEN="$TEST_GH_TOKEN"
    
    # Override configuration for testing
    export BUG_FIX_APPROVALS=1
    export FEATURE_APPROVALS=2
    export BUG_FIX_LABEL="$TEST_BUG_LABEL"
    export MERGE_METHOD="squash"
    export ALLOWED_BASE_BRANCHES="development"
    export READY_FOR_MERGE_LABEL="$TEST_READY_LABEL"
    export STATUS_COMMENT_TAG="<!-- pr-approval-checks-gh -->"
    
    # Test-specific settings
    export LOG_LEVEL="ERROR"
    export ENABLE_CACHE=false
    export TEST_MODE=true
}

# Clean up test environment
cleanup_test_environment() {
    unset REPO PR_NUMBER GH_TOKEN
    unset BUG_FIX_APPROVALS FEATURE_APPROVALS BUG_FIX_LABEL
    unset MERGE_METHOD ALLOWED_BASE_BRANCHES READY_FOR_MERGE_LABEL
    unset STATUS_COMMENT_TAG LOG_LEVEL ENABLE_CACHE TEST_MODE
}

# =============================================================================
# TEST DATA VALIDATION
# =============================================================================

# Validate that mock data is valid JSON
validate_mock_data() {
    local data_functions=(
        "get_mock_feature_pr_data"
        "get_mock_bugfix_pr_data"
        "get_mock_draft_pr_data"
        "get_mock_invalid_base_pr_data"
        "get_mock_sufficient_feature_reviews"
        "get_mock_insufficient_feature_reviews"
        "get_mock_sufficient_bugfix_reviews"
        "get_mock_changes_requested_reviews"
        "get_mock_no_reviews"
        "get_mock_existing_status_comment"
        "get_mock_no_comments"
        "get_mock_multiple_comments"
    )
    
    for func in "${data_functions[@]}"; do
        if ! "$func" | jq . >/dev/null 2>&1; then
            echo "ERROR: Invalid JSON in $func"
            return 1
        fi
    done
    
    echo "✅ All mock data validated successfully"
    return 0
}

# =============================================================================
# EXPORT FUNCTIONS
# =============================================================================

# Export all mock data functions
export -f get_mock_feature_pr_data
export -f get_mock_bugfix_pr_data
export -f get_mock_draft_pr_data
export -f get_mock_invalid_base_pr_data
export -f get_mock_sufficient_feature_reviews
export -f get_mock_insufficient_feature_reviews
export -f get_mock_sufficient_bugfix_reviews
export -f get_mock_changes_requested_reviews
export -f get_mock_no_reviews
export -f get_mock_existing_status_comment
export -f get_mock_no_comments
export -f get_mock_multiple_comments

# Export setup functions
export -f setup_feature_pr_mocks
export -f setup_bugfix_pr_mocks
export -f setup_draft_pr_mocks
export -f setup_invalid_base_pr_mocks
export -f setup_test_environment
export -f cleanup_test_environment
export -f validate_mock_data
