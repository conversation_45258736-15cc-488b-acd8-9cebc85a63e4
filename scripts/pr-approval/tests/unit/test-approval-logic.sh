#!/usr/bin/env bash
# =============================================================================
# Unit Tests for Approval Logic Module
# =============================================================================
# This file contains comprehensive unit tests for the lib/approval-logic.sh module,
# including approval counting, PR type detection, and validation logic.

set -euo pipefail

# Source test framework and configuration
# shellcheck source=../test-utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/../test-utils.sh"
# shellcheck source=../test-config.sh
source "$(dirname "${BASH_SOURCE[0]}")/../test-config.sh"

# Source the modules under test
# shellcheck source=../../lib/utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/../../lib/utils.sh"
# shellcheck source=../../lib/pr-data.sh
source "$(dirname "${BASH_SOURCE[0]}")/../../lib/pr-data.sh"
# shellcheck source=../../lib/approval-logic.sh
source "$(dirname "${BASH_SOURCE[0]}")/../../lib/approval-logic.sh"

# =============================================================================
# PR ELIGIBILITY TESTS
# =============================================================================

test_is_pr_eligible_draft() {
    enable_mocking
    
    # Set up mock for draft PR
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_draft_pr_data)" 0
    
    # Test draft PR eligibility
    local exit_code=0
    if ! is_pr_eligible "$TEST_REPO" "$TEST_PR_NUMBER"; then
        exit_code=$?
    fi
    
    assert_equals $EXIT_DRAFT_PR $exit_code "Draft PR should return EXIT_DRAFT_PR"
    
    disable_mocking
}

test_is_pr_eligible_invalid_base() {
    enable_mocking
    
    # Set up mock for PR with invalid base branch
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_invalid_base_pr_data)" 0
    
    # Test invalid base branch eligibility
    local exit_code=0
    if ! is_pr_eligible "$TEST_REPO" "$TEST_PR_NUMBER"; then
        exit_code=$?
    fi
    
    assert_equals $EXIT_INVALID_BASE $exit_code "PR with invalid base should return EXIT_INVALID_BASE"
    
    disable_mocking
}

test_is_pr_eligible_valid() {
    enable_mocking
    
    # Set up mock for valid PR
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    
    # Test valid PR eligibility
    local exit_code=0
    if is_pr_eligible "$TEST_REPO" "$TEST_PR_NUMBER"; then
        exit_code=$?
    else
        exit_code=$?
    fi
    
    assert_equals $EXIT_SUCCESS $exit_code "Valid PR should return EXIT_SUCCESS"
    
    disable_mocking
}

test_is_base_branch_allowed() {
    # Test allowed base branch
    if is_base_branch_allowed "development"; then
        assert_true true "development should be allowed base branch"
    else
        assert_true false "development should be allowed base branch"
    fi
    
    # Test disallowed base branch
    if is_base_branch_allowed "main"; then
        assert_true false "main should not be allowed base branch"
    else
        assert_true true "main should not be allowed base branch"
    fi
    
    # Test with whitespace (should be trimmed)
    if is_base_branch_allowed " development "; then
        assert_true true "development with whitespace should be allowed"
    else
        assert_true false "development with whitespace should be allowed"
    fi
}

# =============================================================================
# PR TYPE DETECTION TESTS
# =============================================================================

test_determine_pr_type_feature() {
    enable_mocking
    
    # Set up mock for feature PR
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    
    # Test PR type determination
    determine_pr_type "$TEST_REPO" "$TEST_PR_NUMBER"
    
    assert_equals "Feature" "$PR_TYPE" "Feature PR should be identified as Feature"
    assert_equals "false" "$IS_BUG_FIX" "Feature PR should not be identified as bug fix"
    assert_equals "2" "$REQUIRED_APPROVALS" "Feature PR should require 2 approvals"
    
    disable_mocking
}

test_determine_pr_type_bugfix() {
    enable_mocking
    
    # Set up mock for bug-fix PR
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_bugfix_pr_data)" 0
    
    # Test PR type determination
    determine_pr_type "$TEST_REPO" "$TEST_PR_NUMBER"
    
    assert_equals "Bug-fix" "$PR_TYPE" "Bug-fix PR should be identified as Bug-fix"
    assert_equals "true" "$IS_BUG_FIX" "Bug-fix PR should be identified as bug fix"
    assert_equals "1" "$REQUIRED_APPROVALS" "Bug-fix PR should require 1 approval"
    
    disable_mocking
}

# =============================================================================
# APPROVAL COUNTING TESTS
# =============================================================================

test_count_approvals_sufficient_feature() {
    enable_mocking
    
    # Set up mocks for feature PR with sufficient approvals
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_feature_reviews)" 0
    
    # Test approval counting
    count_approvals "$TEST_REPO" "$TEST_PR_NUMBER"
    
    assert_equals "2" "$APPROVAL_COUNT" "Should count 2 approvals for sufficient feature reviews"
    
    disable_mocking
}

test_count_approvals_insufficient_feature() {
    enable_mocking
    
    # Set up mocks for feature PR with insufficient approvals
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_insufficient_feature_reviews)" 0
    
    # Test approval counting
    count_approvals "$TEST_REPO" "$TEST_PR_NUMBER"
    
    assert_equals "1" "$APPROVAL_COUNT" "Should count 1 approval for insufficient feature reviews"
    
    disable_mocking
}

test_count_approvals_changes_requested() {
    enable_mocking
    
    # Set up mocks for reviews with changes requested then approved
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_changes_requested_reviews)" 0
    
    # Test approval counting (should use latest review per user)
    count_approvals "$TEST_REPO" "$TEST_PR_NUMBER"
    
    assert_equals "1" "$APPROVAL_COUNT" "Should count 1 approval (latest review per user)"
    
    disable_mocking
}

test_count_approvals_no_reviews() {
    enable_mocking
    
    # Set up mocks for no reviews
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_no_reviews)" 0
    
    # Test approval counting
    count_approvals "$TEST_REPO" "$TEST_PR_NUMBER"
    
    assert_equals "0" "$APPROVAL_COUNT" "Should count 0 approvals when no reviews exist"
    
    disable_mocking
}

# =============================================================================
# APPROVAL VALIDATION TESTS
# =============================================================================

test_has_sufficient_approvals_feature_sufficient() {
    # Set up state for feature PR with sufficient approvals
    APPROVAL_COUNT=2
    REQUIRED_APPROVALS=2
    
    if has_sufficient_approvals "$TEST_REPO" "$TEST_PR_NUMBER"; then
        assert_true true "Feature PR with 2/2 approvals should be sufficient"
    else
        assert_true false "Feature PR with 2/2 approvals should be sufficient"
    fi
}

test_has_sufficient_approvals_feature_insufficient() {
    # Set up state for feature PR with insufficient approvals
    APPROVAL_COUNT=1
    REQUIRED_APPROVALS=2
    
    if has_sufficient_approvals "$TEST_REPO" "$TEST_PR_NUMBER"; then
        assert_true false "Feature PR with 1/2 approvals should be insufficient"
    else
        assert_true true "Feature PR with 1/2 approvals should be insufficient"
    fi
}

test_has_sufficient_approvals_bugfix_sufficient() {
    # Set up state for bug-fix PR with sufficient approvals
    APPROVAL_COUNT=1
    REQUIRED_APPROVALS=1
    
    if has_sufficient_approvals "$TEST_REPO" "$TEST_PR_NUMBER"; then
        assert_true true "Bug-fix PR with 1/1 approvals should be sufficient"
    else
        assert_true false "Bug-fix PR with 1/1 approvals should be sufficient"
    fi
}

test_validate_approval_requirements_sufficient() {
    enable_mocking
    
    # Set up mocks for bug-fix PR with sufficient approvals
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_bugfix_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_bugfix_reviews)" 0
    
    # Test validation
    local exit_code=0
    if validate_approval_requirements "$TEST_REPO" "$TEST_PR_NUMBER"; then
        exit_code=$?
    else
        exit_code=$?
    fi
    
    assert_equals $EXIT_SUCCESS $exit_code "Bug-fix PR with sufficient approvals should pass validation"
    
    disable_mocking
}

test_validate_approval_requirements_insufficient() {
    enable_mocking
    
    # Set up mocks for feature PR with insufficient approvals
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_insufficient_feature_reviews)" 0
    
    # Test validation
    local exit_code=0
    if ! validate_approval_requirements "$TEST_REPO" "$TEST_PR_NUMBER"; then
        exit_code=$?
    fi
    
    assert_equals $EXIT_INSUFFICIENT_APPROVALS $exit_code "Feature PR with insufficient approvals should fail validation"
    
    disable_mocking
}

# =============================================================================
# APPROVAL STATUS REPORTING TESTS
# =============================================================================

test_generate_approval_status_message_sufficient() {
    # Set up state
    PR_TYPE="Feature"
    APPROVAL_COUNT=2
    REQUIRED_APPROVALS=2
    
    local message
    message=$(generate_approval_status_message "sufficient")
    
    assert_contains "$message" "Feature PR has sufficient approvals (2/2)" "Sufficient status message should be correct"
}

test_generate_approval_status_message_insufficient() {
    # Set up state
    PR_TYPE="Bug-fix"
    APPROVAL_COUNT=0
    REQUIRED_APPROVALS=1
    
    local message
    message=$(generate_approval_status_message "insufficient")
    
    assert_contains "$message" "Bug-fix PR requires at least 1 approval(s); currently has 0" "Insufficient status message should be correct"
}

test_get_approval_details() {
    enable_mocking
    
    # Set up mocks for reviews
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_feature_reviews)" 0
    
    # Test getting approval details
    local details
    details=$(get_approval_details "$TEST_REPO" "$TEST_PR_NUMBER")
    
    assert_contains "$details" "reviewer1" "Approval details should contain reviewer1"
    assert_contains "$details" "reviewer2" "Approval details should contain reviewer2"
    assert_contains "$details" "APPROVED" "Approval details should contain APPROVED status"
    
    disable_mocking
}

test_get_approving_users() {
    enable_mocking
    
    # Set up mocks for reviews
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_feature_reviews)" 0
    
    # Test getting approving users
    local users
    users=$(get_approving_users "$TEST_REPO" "$TEST_PR_NUMBER")
    
    assert_contains "$users" "reviewer1" "Approving users should contain reviewer1"
    assert_contains "$users" "reviewer2" "Approving users should contain reviewer2"
    
    disable_mocking
}

# =============================================================================
# UTILITY FUNCTION TESTS
# =============================================================================

test_reset_approval_state() {
    # Set up some state
    APPROVAL_COUNT=5
    REQUIRED_APPROVALS=3
    PR_TYPE="Test"
    IS_BUG_FIX=true
    
    # Reset state
    reset_approval_state
    
    # Verify state is reset
    assert_equals "0" "$APPROVAL_COUNT" "APPROVAL_COUNT should be reset to 0"
    assert_equals "0" "$REQUIRED_APPROVALS" "REQUIRED_APPROVALS should be reset to 0"
    assert_equals "" "$PR_TYPE" "PR_TYPE should be reset to empty"
    assert_equals "false" "$IS_BUG_FIX" "IS_BUG_FIX should be reset to false"
}

test_is_self_approval_allowed() {
    # Test self-approval policy (should be false by default)
    if is_self_approval_allowed; then
        assert_true false "Self-approval should not be allowed by default"
    else
        assert_true true "Self-approval correctly not allowed by default"
    fi
}

test_print_approval_summary() {
    # Set up state
    PR_TYPE="Feature"
    APPROVAL_COUNT=2
    REQUIRED_APPROVALS=2
    
    # Test print summary (should not fail)
    local summary_output
    summary_output=$(create_temp_file "approval_summary")
    print_approval_summary 2>"$summary_output"
    
    # Verify summary contains expected information
    assert_contains "$(cat "$summary_output")" "Approval Summary" "Summary should contain header"
    assert_contains "$(cat "$summary_output")" "Feature" "Summary should contain PR type"
    assert_contains "$(cat "$summary_output")" "2" "Summary should contain approval counts"
    assert_contains "$(cat "$summary_output")" "Sufficient" "Summary should show sufficient status"
}

# =============================================================================
# INTEGRATION TESTS
# =============================================================================

test_full_approval_workflow_bugfix() {
    enable_mocking
    
    # Set up complete mock workflow for bug-fix PR
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_bugfix_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_bugfix_reviews)" 0
    
    # Run complete validation
    local exit_code=0
    if validate_approval_requirements "$TEST_REPO" "$TEST_PR_NUMBER"; then
        exit_code=$?
    else
        exit_code=$?
    fi
    
    # Verify results
    assert_equals $EXIT_SUCCESS $exit_code "Full bug-fix workflow should succeed"
    assert_equals "Bug-fix" "$PR_TYPE" "PR type should be Bug-fix"
    assert_equals "1" "$APPROVAL_COUNT" "Should have 1 approval"
    assert_equals "1" "$REQUIRED_APPROVALS" "Should require 1 approval"
    assert_equals "true" "$IS_BUG_FIX" "Should be identified as bug fix"
    
    disable_mocking
}

test_full_approval_workflow_feature_insufficient() {
    enable_mocking
    
    # Set up complete mock workflow for feature PR with insufficient approvals
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_insufficient_feature_reviews)" 0
    
    # Run complete validation
    local exit_code=0
    if ! validate_approval_requirements "$TEST_REPO" "$TEST_PR_NUMBER"; then
        exit_code=$?
    fi
    
    # Verify results
    assert_equals $EXIT_INSUFFICIENT_APPROVALS $exit_code "Feature workflow with insufficient approvals should fail"
    assert_equals "Feature" "$PR_TYPE" "PR type should be Feature"
    assert_equals "1" "$APPROVAL_COUNT" "Should have 1 approval"
    assert_equals "2" "$REQUIRED_APPROVALS" "Should require 2 approvals"
    assert_equals "false" "$IS_BUG_FIX" "Should not be identified as bug fix"
    
    disable_mocking
}

# =============================================================================
# TEST RUNNER
# =============================================================================

main() {
    init_test_framework "approval-logic-tests"
    set_verbose
    
    echo "🧪 Running Approval Logic Module Unit Tests"
    echo "==========================================="
    
    # Set up test environment
    setup_test_environment
    
    # Run PR eligibility tests
    echo "✅ Testing PR Eligibility..."
    run_test "test_is_pr_eligible_draft" "Draft PR eligibility check"
    run_test "test_is_pr_eligible_invalid_base" "Invalid base branch eligibility check"
    run_test "test_is_pr_eligible_valid" "Valid PR eligibility check"
    run_test "test_is_base_branch_allowed" "Base branch validation"
    
    # Run PR type detection tests
    echo "🏷️ Testing PR Type Detection..."
    run_test "test_determine_pr_type_feature" "Feature PR type detection"
    run_test "test_determine_pr_type_bugfix" "Bug-fix PR type detection"
    
    # Run approval counting tests
    echo "🔢 Testing Approval Counting..."
    run_test "test_count_approvals_sufficient_feature" "Sufficient feature approvals counting"
    run_test "test_count_approvals_insufficient_feature" "Insufficient feature approvals counting"
    run_test "test_count_approvals_changes_requested" "Changes requested then approved counting"
    run_test "test_count_approvals_no_reviews" "No reviews counting"
    
    # Run approval validation tests
    echo "✔️ Testing Approval Validation..."
    run_test "test_has_sufficient_approvals_feature_sufficient" "Feature sufficient approvals validation"
    run_test "test_has_sufficient_approvals_feature_insufficient" "Feature insufficient approvals validation"
    run_test "test_has_sufficient_approvals_bugfix_sufficient" "Bug-fix sufficient approvals validation"
    run_test "test_validate_approval_requirements_sufficient" "Complete validation with sufficient approvals"
    run_test "test_validate_approval_requirements_insufficient" "Complete validation with insufficient approvals"
    
    # Run approval status reporting tests
    echo "📊 Testing Approval Status Reporting..."
    run_test "test_generate_approval_status_message_sufficient" "Sufficient approval status message"
    run_test "test_generate_approval_status_message_insufficient" "Insufficient approval status message"
    run_test "test_get_approval_details" "Approval details retrieval"
    run_test "test_get_approving_users" "Approving users retrieval"
    
    # Run utility function tests
    echo "🔧 Testing Utility Functions..."
    run_test "test_reset_approval_state" "Approval state reset"
    run_test "test_is_self_approval_allowed" "Self-approval policy check"
    run_test "test_print_approval_summary" "Approval summary printing"
    
    # Run integration tests
    echo "🔗 Testing Integration Workflows..."
    run_test "test_full_approval_workflow_bugfix" "Complete bug-fix approval workflow"
    run_test "test_full_approval_workflow_feature_insufficient" "Complete feature approval workflow (insufficient)"
    
    # Clean up test environment
    cleanup_test_environment
    
    # Print summary and exit
    print_test_summary
}

# Run tests if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
