#!/usr/bin/env bash
# =============================================================================
# Unit Tests for PR Data Module
# =============================================================================
# This file contains comprehensive unit tests for the lib/pr-data.sh module,
# including API mocking, caching behavior, and data parsing functions.

set -euo pipefail

# Source test framework and configuration
# shellcheck source=../test-utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/../test-utils.sh"
# shellcheck source=../test-config.sh
source "$(dirname "${BASH_SOURCE[0]}")/../test-config.sh"

# Source the modules under test
# shellcheck source=../../lib/utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/../../lib/utils.sh"
# shellcheck source=../../lib/pr-data.sh
source "$(dirname "${BASH_SOURCE[0]}")/../../lib/pr-data.sh"

# =============================================================================
# CACHE KEY GENERATION TESTS
# =============================================================================

test_generate_pr_data_cache_key() {
    local key1 key2
    key1=$(generate_pr_data_cache_key "$TEST_REPO" "$TEST_PR_NUMBER")
    key2=$(generate_pr_data_cache_key "$TEST_REPO" "$TEST_PR_NUMBER")
    
    assert_equals "$key1" "$key2" "Same input should generate same PR data cache key"
    
    local key3
    key3=$(generate_pr_data_cache_key "different/repo" "$TEST_PR_NUMBER")
    assert_not_equals "$key1" "$key3" "Different repo should generate different cache key"
    
    # Check that key is a valid hash
    assert_equals 64 "${#key1}" "PR data cache key should be 64 characters long"
}

test_generate_pr_reviews_cache_key() {
    local key1 key2
    key1=$(generate_pr_reviews_cache_key "$TEST_REPO" "$TEST_PR_NUMBER")
    key2=$(generate_pr_reviews_cache_key "$TEST_REPO" "$TEST_PR_NUMBER")
    
    assert_equals "$key1" "$key2" "Same input should generate same PR reviews cache key"
    
    local key3
    key3=$(generate_pr_reviews_cache_key "$TEST_REPO" "456")
    assert_not_equals "$key1" "$key3" "Different PR number should generate different cache key"
}

test_generate_pr_comments_cache_key() {
    local key1 key2
    key1=$(generate_pr_comments_cache_key "$TEST_REPO" "$TEST_PR_NUMBER")
    key2=$(generate_pr_comments_cache_key "$TEST_REPO" "$TEST_PR_NUMBER")
    
    assert_equals "$key1" "$key2" "Same input should generate same PR comments cache key"
    
    # Ensure different from other cache keys
    local pr_data_key
    pr_data_key=$(generate_pr_data_cache_key "$TEST_REPO" "$TEST_PR_NUMBER")
    assert_not_equals "$key1" "$pr_data_key" "PR comments cache key should differ from PR data cache key"
}

# =============================================================================
# DATA PARSING TESTS
# =============================================================================

test_parse_pr_info() {
    local pr_data
    pr_data=$(get_mock_feature_pr_data)
    
    # Parse the PR info
    parse_pr_info "$pr_data"
    
    # Check that global variables are set correctly
    assert_equals "$TEST_PR_NUMBER" "$PR_NUMBER" "PR_NUMBER should be parsed correctly"
    assert_equals "OPEN" "$PR_STATE" "PR_STATE should be parsed correctly"
    assert_equals "false" "$PR_IS_DRAFT" "PR_IS_DRAFT should be parsed correctly"
    assert_equals "development" "$PR_BASE_REF" "PR_BASE_REF should be parsed correctly"
    assert_equals "Add new feature" "$PR_TITLE" "PR_TITLE should be parsed correctly"
    assert_equals "$TEST_AUTHOR" "$PR_AUTHOR" "PR_AUTHOR should be parsed correctly"
    assert_contains "$PR_URL" "github.com" "PR_URL should contain github.com"
}

test_parse_pr_labels() {
    local pr_data
    pr_data=$(get_mock_feature_pr_data)
    
    # Parse the PR labels
    parse_pr_labels "$pr_data"
    
    # Check that labels are parsed correctly
    assert_equals "feature" "$PR_LABELS_CSV" "PR_LABELS_CSV should contain feature label"
    assert_contains "$PR_LABELS_JSON" "feature" "PR_LABELS_JSON should contain feature label"
}

test_parse_pr_mergeable_status() {
    local pr_data
    pr_data=$(get_mock_feature_pr_data)
    
    # Parse the mergeable status
    parse_pr_mergeable_status "$pr_data"
    
    # Check that mergeable status is parsed correctly
    assert_equals "MERGEABLE" "$PR_MERGEABLE" "PR_MERGEABLE should be parsed correctly"
    assert_equals "CLEAN" "$PR_MERGE_STATE_STATUS" "PR_MERGE_STATE_STATUS should be parsed correctly"
}

test_pr_has_label() {
    local pr_data
    pr_data=$(get_mock_feature_pr_data)
    
    # Test with existing label
    if pr_has_label "feature" "$pr_data"; then
        assert_true true "Should find existing feature label"
    else
        assert_true false "Should find existing feature label"
    fi
    
    # Test with non-existing label
    if pr_has_label "bug-fix" "$pr_data"; then
        assert_true false "Should not find non-existing bug-fix label"
    else
        assert_true true "Should not find non-existing bug-fix label"
    fi
}

# =============================================================================
# MOCKED API TESTS
# =============================================================================

test_get_pr_data_with_mocking() {
    enable_mocking
    
    # Set up mock response
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    
    # Test getting PR data
    local pr_data
    pr_data=$(get_pr_data "$TEST_REPO" "$TEST_PR_NUMBER")
    
    # Verify the data
    assert_contains "$pr_data" "Add new feature" "PR data should contain the title"
    assert_contains "$pr_data" "feature" "PR data should contain the feature label"
    
    disable_mocking
}

test_get_pr_reviews_with_mocking() {
    enable_mocking
    
    # Set up mock response
    add_mock_response "gh api repos/$TEST_REPO/pulls/$TEST_PR_NUMBER/reviews" "$(get_mock_sufficient_feature_reviews)" 0
    
    # Test getting PR reviews
    local reviews_data
    reviews_data=$(get_pr_reviews "$TEST_REPO" "$TEST_PR_NUMBER")
    
    # Verify the data
    assert_contains "$reviews_data" "reviewer1" "Reviews data should contain reviewer1"
    assert_contains "$reviews_data" "APPROVED" "Reviews data should contain APPROVED status"
    
    disable_mocking
}

test_get_pr_comments_with_mocking() {
    enable_mocking
    
    # Set up mock response
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_existing_status_comment)" 0
    
    # Test getting PR comments
    local comments_data
    comments_data=$(get_pr_comments "$TEST_REPO" "$TEST_PR_NUMBER")
    
    # Verify the data
    assert_contains "$comments_data" "pr-approval-checks-gh" "Comments data should contain status comment tag"
    
    disable_mocking
}

# =============================================================================
# CACHING BEHAVIOR TESTS
# =============================================================================

test_pr_data_caching() {
    enable_mocking
    ENABLE_CACHE=true
    
    # Set up mock response
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    
    # First call should hit the API
    local pr_data1
    pr_data1=$(get_pr_data "$TEST_REPO" "$TEST_PR_NUMBER")
    
    # Second call should use cache (mock should not be called again)
    local pr_data2
    pr_data2=$(get_pr_data "$TEST_REPO" "$TEST_PR_NUMBER")
    
    # Data should be identical
    assert_equals "$pr_data1" "$pr_data2" "Cached PR data should match original data"
    
    # Verify cache file exists
    local cache_key
    cache_key=$(generate_pr_data_cache_key "$TEST_REPO" "$TEST_PR_NUMBER")
    assert_file_exists "${CACHE_DIR}/${cache_key}" "Cache file should exist after first call"
    
    disable_mocking
    ENABLE_CACHE=false
}

test_fetch_all_pr_data() {
    enable_mocking
    
    # Set up mock responses
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    
    # Test fetching all PR data
    fetch_all_pr_data "$TEST_REPO" "$TEST_PR_NUMBER"
    
    # Verify that global variables are set
    assert_equals "$TEST_PR_NUMBER" "$PR_NUMBER" "PR_NUMBER should be set by fetch_all_pr_data"
    assert_equals "OPEN" "$PR_STATE" "PR_STATE should be set by fetch_all_pr_data"
    assert_equals "false" "$PR_IS_DRAFT" "PR_IS_DRAFT should be set by fetch_all_pr_data"
    assert_equals "development" "$PR_BASE_REF" "PR_BASE_REF should be set by fetch_all_pr_data"
    assert_equals "feature" "$PR_LABELS_CSV" "PR_LABELS_CSV should be set by fetch_all_pr_data"
    assert_equals "MERGEABLE" "$PR_MERGEABLE" "PR_MERGEABLE should be set by fetch_all_pr_data"
    
    disable_mocking
}

test_refresh_pr_data() {
    enable_mocking
    ENABLE_CACHE=true
    
    # Set up initial mock response
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    
    # Fetch data initially (should cache)
    fetch_all_pr_data "$TEST_REPO" "$TEST_PR_NUMBER"
    local initial_title="$PR_TITLE"
    
    # Update mock response to simulate changed data
    local updated_pr_data
    updated_pr_data=$(get_mock_feature_pr_data | jq '.title = "Updated feature title"')
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$updated_pr_data" 0
    
    # Refresh data (should bypass cache)
    refresh_pr_data "$TEST_REPO" "$TEST_PR_NUMBER"
    
    # Title should be updated
    assert_not_equals "$initial_title" "$PR_TITLE" "PR title should be updated after refresh"
    assert_equals "Updated feature title" "$PR_TITLE" "PR title should match updated data"
    
    disable_mocking
    ENABLE_CACHE=false
}

# =============================================================================
# ERROR HANDLING TESTS
# =============================================================================

test_invalid_repo_format() {
    # Test with invalid repository format
    if fetch_all_pr_data "invalid-repo" "$TEST_PR_NUMBER" 2>/dev/null; then
        assert_true false "Should fail with invalid repository format"
    else
        assert_true true "Correctly failed with invalid repository format"
    fi
}

test_invalid_pr_number() {
    # Test with invalid PR number
    if fetch_all_pr_data "$TEST_REPO" "invalid-pr" 2>/dev/null; then
        assert_true false "Should fail with invalid PR number"
    else
        assert_true true "Correctly failed with invalid PR number"
    fi
}

test_api_error_handling() {
    enable_mocking
    
    # Set up mock to return error
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "" 1
    
    # Test that API error is handled
    if fetch_all_pr_data "$TEST_REPO" "$TEST_PR_NUMBER" 2>/dev/null; then
        assert_true false "Should fail when API returns error"
    else
        assert_true true "Correctly handled API error"
    fi
    
    disable_mocking
}

# =============================================================================
# UTILITY FUNCTION TESTS
# =============================================================================

test_print_pr_summary() {
    enable_mocking
    
    # Set up mock and fetch data
    add_mock_response "gh pr view $TEST_PR_NUMBER --repo $TEST_REPO --json" "$(get_mock_feature_pr_data)" 0
    fetch_all_pr_data "$TEST_REPO" "$TEST_PR_NUMBER"
    
    # Test print summary (should not fail)
    local summary_output
    summary_output=$(create_temp_file "pr_summary")
    print_pr_summary 2>"$summary_output"
    
    # Verify summary contains expected information
    assert_contains "$(cat "$summary_output")" "PR Summary" "Summary should contain header"
    assert_contains "$(cat "$summary_output")" "$TEST_PR_NUMBER" "Summary should contain PR number"
    assert_contains "$(cat "$summary_output")" "Add new feature" "Summary should contain PR title"
    
    disable_mocking
}

# =============================================================================
# TEST RUNNER
# =============================================================================

main() {
    init_test_framework "pr-data-tests"
    set_verbose
    
    echo "🧪 Running PR Data Module Unit Tests"
    echo "===================================="
    
    # Set up test environment
    setup_test_environment
    
    # Run cache key generation tests
    echo "🔑 Testing Cache Key Generation..."
    run_test "test_generate_pr_data_cache_key" "PR data cache key generation"
    run_test "test_generate_pr_reviews_cache_key" "PR reviews cache key generation"
    run_test "test_generate_pr_comments_cache_key" "PR comments cache key generation"
    
    # Run data parsing tests
    echo "📊 Testing Data Parsing..."
    run_test "test_parse_pr_info" "PR info parsing"
    run_test "test_parse_pr_labels" "PR labels parsing"
    run_test "test_parse_pr_mergeable_status" "PR mergeable status parsing"
    run_test "test_pr_has_label" "PR label checking"
    
    # Run mocked API tests
    echo "🌐 Testing API Functions with Mocking..."
    run_test "test_get_pr_data_with_mocking" "PR data fetching with mocks"
    run_test "test_get_pr_reviews_with_mocking" "PR reviews fetching with mocks"
    run_test "test_get_pr_comments_with_mocking" "PR comments fetching with mocks"
    
    # Run caching behavior tests
    echo "💾 Testing Caching Behavior..."
    run_test "test_pr_data_caching" "PR data caching functionality"
    run_test "test_fetch_all_pr_data" "Complete PR data fetching"
    run_test "test_refresh_pr_data" "PR data refresh functionality"
    
    # Run error handling tests
    echo "⚠️ Testing Error Handling..."
    run_test "test_invalid_repo_format" "Invalid repository format handling"
    run_test "test_invalid_pr_number" "Invalid PR number handling"
    run_test "test_api_error_handling" "API error handling"
    
    # Run utility function tests
    echo "🔧 Testing Utility Functions..."
    run_test "test_print_pr_summary" "PR summary printing"
    
    # Clean up test environment
    cleanup_test_environment
    
    # Print summary and exit
    print_test_summary
}

# Run tests if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
