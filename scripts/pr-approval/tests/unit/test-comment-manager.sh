#!/usr/bin/env bash
# =============================================================================
# Unit Tests for Comment Manager Module
# =============================================================================
# This file contains comprehensive unit tests for the lib/comment-manager.sh module,
# including comment upsert, status tracking, and cleanup functions.

set -euo pipefail

# Source test framework and configuration
# shellcheck source=../test-utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/../test-utils.sh"
# shellcheck source=../test-config.sh
source "$(dirname "${BASH_SOURCE[0]}")/../test-config.sh"

# Source the modules under test
# shellcheck source=../../lib/utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/../../lib/utils.sh"
# shellcheck source=../../lib/pr-data.sh
source "$(dirname "${BASH_SOURCE[0]}")/../../lib/pr-data.sh"
# shellcheck source=../../lib/comment-manager.sh
source "$(dirname "${BASH_SOURCE[0]}")/../../lib/comment-manager.sh"

# =============================================================================
# COMMENT FETCHING TESTS
# =============================================================================

test_find_existing_status_comment_found() {
    enable_mocking
    
    # Set up mock for existing status comment
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_existing_status_comment)" 0
    
    # Test finding existing comment
    if find_existing_status_comment "$TEST_REPO" "$TEST_PR_NUMBER"; then
        assert_true true "Should find existing status comment"
        assert_equals "456" "$EXISTING_COMMENT_ID" "Should set correct comment ID"
        assert_contains "$LAST_COMMENT_BODY" "pr-approval-checks-gh" "Should set correct comment body"
    else
        assert_true false "Should find existing status comment"
    fi
    
    disable_mocking
}

test_find_existing_status_comment_not_found() {
    enable_mocking
    
    # Set up mock for no comments
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    
    # Test finding non-existing comment
    if find_existing_status_comment "$TEST_REPO" "$TEST_PR_NUMBER"; then
        assert_true false "Should not find status comment when none exists"
    else
        assert_true true "Should not find status comment when none exists"
        assert_equals "" "$EXISTING_COMMENT_ID" "Comment ID should be empty"
        assert_equals "" "$LAST_COMMENT_BODY" "Comment body should be empty"
    fi
    
    disable_mocking
}

test_find_existing_status_comment_multiple() {
    enable_mocking
    
    # Set up mock for multiple comments with one status comment
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_multiple_comments)" 0
    
    # Test finding status comment among multiple comments
    if find_existing_status_comment "$TEST_REPO" "$TEST_PR_NUMBER"; then
        assert_true true "Should find status comment among multiple comments"
        assert_equals "456" "$EXISTING_COMMENT_ID" "Should find correct status comment ID"
    else
        assert_true false "Should find status comment among multiple comments"
    fi
    
    disable_mocking
}

test_get_all_pr_comments() {
    enable_mocking
    
    # Set up mock for comments
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_multiple_comments)" 0
    
    # Test getting all comments
    local comments
    comments=$(get_all_pr_comments "$TEST_REPO" "$TEST_PR_NUMBER")
    
    assert_contains "$comments" "This is a regular comment" "Should contain regular comments"
    assert_contains "$comments" "pr-approval-checks-gh" "Should contain status comment"
    
    disable_mocking
}

# =============================================================================
# COMMENT CREATION TESTS
# =============================================================================

test_create_status_comment() {
    enable_mocking
    
    # Set up mock for comment creation
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    
    # Test creating status comment
    local message="Test status message"
    if create_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "$message"; then
        assert_true true "Should successfully create status comment"
        assert_equals "789" "$EXISTING_COMMENT_ID" "Should set correct comment ID"
        assert_contains "$LAST_COMMENT_BODY" "$message" "Should set correct comment body"
        assert_contains "$LAST_COMMENT_BODY" "pr-approval-checks-gh" "Should include status tag"
    else
        assert_true false "Should successfully create status comment"
    fi
    
    disable_mocking
}

test_create_status_comment_api_error() {
    enable_mocking
    
    # Set up mock for API error
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "" 1
    
    # Test creating status comment with API error
    local message="Test status message"
    if create_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "$message" 2>/dev/null; then
        assert_true false "Should fail when API returns error"
    else
        assert_true true "Should fail when API returns error"
    fi
    
    disable_mocking
}

# =============================================================================
# COMMENT UPDATE TESTS
# =============================================================================

test_update_status_comment() {
    enable_mocking
    
    # Set up existing comment ID
    EXISTING_COMMENT_ID="456"
    
    # Set up mock for comment update
    add_mock_response "gh api --method PATCH repos/$TEST_REPO/issues/comments/456" "" 0
    
    # Test updating status comment
    local message="Updated status message"
    if update_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "$message"; then
        assert_true true "Should successfully update status comment"
        assert_contains "$LAST_COMMENT_BODY" "$message" "Should update comment body"
        assert_contains "$LAST_COMMENT_BODY" "pr-approval-checks-gh" "Should include status tag"
    else
        assert_true false "Should successfully update status comment"
    fi
    
    disable_mocking
}

test_update_status_comment_no_id() {
    # Test updating without comment ID
    EXISTING_COMMENT_ID=""
    
    local message="Test message"
    if update_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "$message" 2>/dev/null; then
        assert_true false "Should fail when no comment ID provided"
    else
        assert_true true "Should fail when no comment ID provided"
    fi
}

test_update_status_comment_api_error() {
    enable_mocking
    
    # Set up existing comment ID
    EXISTING_COMMENT_ID="456"
    
    # Set up mock for API error
    add_mock_response "gh api --method PATCH repos/$TEST_REPO/issues/comments/456" "" 1
    
    # Test updating status comment with API error
    local message="Test message"
    if update_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "$message" 2>/dev/null; then
        assert_true false "Should fail when API returns error"
    else
        assert_true true "Should fail when API returns error"
    fi
    
    disable_mocking
}

# =============================================================================
# COMMENT UPSERT TESTS
# =============================================================================

test_upsert_status_comment_create() {
    enable_mocking
    
    # Set up mocks for no existing comment and successful creation
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    
    # Test upserting (should create)
    local message="New status message"
    if upsert_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "$message"; then
        assert_true true "Should successfully upsert (create) status comment"
        assert_equals "789" "$EXISTING_COMMENT_ID" "Should set new comment ID"
    else
        assert_true false "Should successfully upsert (create) status comment"
    fi
    
    disable_mocking
}

test_upsert_status_comment_update() {
    enable_mocking
    
    # Set up mocks for existing comment and successful update
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_existing_status_comment)" 0
    add_mock_response "gh api --method PATCH repos/$TEST_REPO/issues/comments/456" "" 0
    
    # Test upserting (should update)
    local message="Updated status message"
    if upsert_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "$message"; then
        assert_true true "Should successfully upsert (update) status comment"
        assert_equals "456" "$EXISTING_COMMENT_ID" "Should keep existing comment ID"
    else
        assert_true false "Should successfully upsert (update) status comment"
    fi
    
    disable_mocking
}

test_upsert_status_comment_invalid_input() {
    # Test with empty message
    if upsert_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "" 2>/dev/null; then
        assert_true false "Should fail with empty message"
    else
        assert_true true "Should fail with empty message"
    fi
    
    # Test with invalid repo format
    if upsert_status_comment "invalid-repo" "$TEST_PR_NUMBER" "message" 2>/dev/null; then
        assert_true false "Should fail with invalid repo format"
    else
        assert_true true "Should fail with invalid repo format"
    fi
    
    # Test with invalid PR number
    if upsert_status_comment "$TEST_REPO" "invalid-pr" "message" 2>/dev/null; then
        assert_true false "Should fail with invalid PR number"
    else
        assert_true true "Should fail with invalid PR number"
    fi
}

# =============================================================================
# COMMENT SKIP LOGIC TESTS
# =============================================================================

test_should_skip_comment_update_same() {
    # Set up previous comment body
    LAST_COMMENT_BODY="Test message

<!-- pr-approval-checks-gh -->"
    
    # Test with same message
    if should_skip_comment_update "Test message"; then
        assert_true true "Should skip update when message is unchanged"
    else
        assert_true false "Should skip update when message is unchanged"
    fi
}

test_should_skip_comment_update_different() {
    # Set up previous comment body
    LAST_COMMENT_BODY="Old message

<!-- pr-approval-checks-gh -->"
    
    # Test with different message
    if should_skip_comment_update "New message"; then
        assert_true false "Should not skip update when message is different"
    else
        assert_true true "Should not skip update when message is different"
    fi
}

test_should_skip_comment_update_no_previous() {
    # Clear previous comment body
    LAST_COMMENT_BODY=""
    
    # Test with no previous comment
    if should_skip_comment_update "Test message"; then
        assert_true false "Should not skip update when no previous comment"
    else
        assert_true true "Should not skip update when no previous comment"
    fi
}

# =============================================================================
# SPECIALIZED COMMENT TESTS
# =============================================================================

test_post_approval_status_comment_sufficient() {
    enable_mocking
    
    # Set up mocks
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    
    # Test posting sufficient approval status
    if post_approval_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "sufficient" "2" "2" "Feature"; then
        assert_true true "Should successfully post sufficient approval status"
        assert_contains "$LAST_COMMENT_BODY" "sufficient approvals (2/2)" "Should contain correct approval message"
        assert_contains "$LAST_COMMENT_BODY" "✅" "Should contain success indicator"
    else
        assert_true false "Should successfully post sufficient approval status"
    fi
    
    disable_mocking
}

test_post_approval_status_comment_insufficient() {
    enable_mocking
    
    # Set up mocks
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    
    # Test posting insufficient approval status
    if post_approval_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "insufficient" "1" "2" "Feature"; then
        assert_true true "Should successfully post insufficient approval status"
        assert_contains "$LAST_COMMENT_BODY" "requires at least 2 approval(s); currently has 1" "Should contain correct approval message"
        assert_contains "$LAST_COMMENT_BODY" "⏳" "Should contain waiting indicator"
    else
        assert_true false "Should successfully post insufficient approval status"
    fi
    
    disable_mocking
}

test_post_merge_status_comment_success() {
    enable_mocking
    
    # Set up mocks
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    
    # Test posting successful merge status
    if post_merge_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "success" "2" "2"; then
        assert_true true "Should successfully post merge success status"
        assert_contains "$LAST_COMMENT_BODY" "PR merged successfully" "Should contain success message"
        assert_contains "$LAST_COMMENT_BODY" "✅" "Should contain success indicator"
    else
        assert_true false "Should successfully post merge success status"
    fi
    
    disable_mocking
}

test_post_merge_status_comment_failed() {
    enable_mocking
    
    # Set up mocks
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    add_mock_response "gh api --method POST repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" '{"id": 789}' 0
    
    # Test posting failed merge status
    if post_merge_status_comment "$TEST_REPO" "$TEST_PR_NUMBER" "failed" "2" "2" "Check failed"; then
        assert_true true "Should successfully post merge failed status"
        assert_contains "$LAST_COMMENT_BODY" "merge failed" "Should contain failure message"
        assert_contains "$LAST_COMMENT_BODY" "Check failed" "Should contain additional info"
        assert_contains "$LAST_COMMENT_BODY" "❌" "Should contain failure indicator"
    else
        assert_true false "Should successfully post merge failed status"
    fi
    
    disable_mocking
}

# =============================================================================
# COMMENT CLEANUP TESTS
# =============================================================================

test_delete_status_comment() {
    enable_mocking
    
    # Set up mocks for finding and deleting comment
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_existing_status_comment)" 0
    add_mock_response "gh api --method DELETE repos/$TEST_REPO/issues/comments/456" "" 0
    
    # Test deleting status comment
    if delete_status_comment "$TEST_REPO" "$TEST_PR_NUMBER"; then
        assert_true true "Should successfully delete status comment"
        assert_equals "" "$EXISTING_COMMENT_ID" "Should clear comment ID after deletion"
        assert_equals "" "$LAST_COMMENT_BODY" "Should clear comment body after deletion"
    else
        assert_true false "Should successfully delete status comment"
    fi
    
    disable_mocking
}

test_delete_status_comment_not_found() {
    enable_mocking
    
    # Set up mock for no existing comment
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_no_comments)" 0
    
    # Test deleting non-existing status comment
    if delete_status_comment "$TEST_REPO" "$TEST_PR_NUMBER"; then
        assert_true true "Should succeed when no comment to delete"
    else
        assert_true false "Should succeed when no comment to delete"
    fi
    
    disable_mocking
}

# =============================================================================
# UTILITY FUNCTION TESTS
# =============================================================================

test_get_comment_stats() {
    enable_mocking
    
    # Set up mock for multiple comments
    add_mock_response "gh api repos/$TEST_REPO/issues/$TEST_PR_NUMBER/comments" "$(get_mock_multiple_comments)" 0
    
    # Test getting comment statistics
    local stats_output
    stats_output=$(create_temp_file "comment_stats")
    get_comment_stats "$TEST_REPO" "$TEST_PR_NUMBER" 2>"$stats_output"
    
    # Verify statistics
    assert_contains "$(cat "$stats_output")" "Comment Statistics" "Should contain statistics header"
    assert_contains "$(cat "$stats_output")" "Total Comments: 3" "Should show correct total comments"
    assert_contains "$(cat "$stats_output")" "Status Comments: 1" "Should show correct status comments"
    
    disable_mocking
}

# =============================================================================
# TEST RUNNER
# =============================================================================

main() {
    init_test_framework "comment-manager-tests"
    set_verbose
    
    echo "🧪 Running Comment Manager Module Unit Tests"
    echo "============================================"
    
    # Set up test environment
    setup_test_environment
    
    # Run comment fetching tests
    echo "🔍 Testing Comment Fetching..."
    run_test "test_find_existing_status_comment_found" "Find existing status comment"
    run_test "test_find_existing_status_comment_not_found" "Find non-existing status comment"
    run_test "test_find_existing_status_comment_multiple" "Find status comment among multiple"
    run_test "test_get_all_pr_comments" "Get all PR comments"
    
    # Run comment creation tests
    echo "➕ Testing Comment Creation..."
    run_test "test_create_status_comment" "Create status comment"
    run_test "test_create_status_comment_api_error" "Create status comment with API error"
    
    # Run comment update tests
    echo "✏️ Testing Comment Updates..."
    run_test "test_update_status_comment" "Update status comment"
    run_test "test_update_status_comment_no_id" "Update status comment without ID"
    run_test "test_update_status_comment_api_error" "Update status comment with API error"
    
    # Run comment upsert tests
    echo "🔄 Testing Comment Upsert..."
    run_test "test_upsert_status_comment_create" "Upsert status comment (create)"
    run_test "test_upsert_status_comment_update" "Upsert status comment (update)"
    run_test "test_upsert_status_comment_invalid_input" "Upsert with invalid input"
    
    # Run comment skip logic tests
    echo "⏭️ Testing Comment Skip Logic..."
    run_test "test_should_skip_comment_update_same" "Skip update for same message"
    run_test "test_should_skip_comment_update_different" "Don't skip update for different message"
    run_test "test_should_skip_comment_update_no_previous" "Don't skip update with no previous comment"
    
    # Run specialized comment tests
    echo "🎯 Testing Specialized Comments..."
    run_test "test_post_approval_status_comment_sufficient" "Post sufficient approval status"
    run_test "test_post_approval_status_comment_insufficient" "Post insufficient approval status"
    run_test "test_post_merge_status_comment_success" "Post successful merge status"
    run_test "test_post_merge_status_comment_failed" "Post failed merge status"
    
    # Run comment cleanup tests
    echo "🧹 Testing Comment Cleanup..."
    run_test "test_delete_status_comment" "Delete status comment"
    run_test "test_delete_status_comment_not_found" "Delete non-existing status comment"
    
    # Run utility function tests
    echo "🔧 Testing Utility Functions..."
    run_test "test_get_comment_stats" "Get comment statistics"
    
    # Clean up test environment
    cleanup_test_environment
    
    # Print summary and exit
    print_test_summary
}

# Run tests if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
