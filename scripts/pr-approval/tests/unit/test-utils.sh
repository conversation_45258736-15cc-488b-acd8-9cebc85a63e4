#!/usr/bin/env bash
# =============================================================================
# Unit Tests for Utils Module
# =============================================================================
# This file contains comprehensive unit tests for the lib/utils.sh module,
# including logging, validation, caching, and error handling functions.

set -euo pipefail

# Source test framework and configuration
# shellcheck source=../test-utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/../test-utils.sh"
# shellcheck source=../test-config.sh
source "$(dirname "${BASH_SOURCE[0]}")/../test-config.sh"

# Source the module under test
# shellcheck source=../../lib/utils.sh
source "$(dirname "${BASH_SOURCE[0]}")/../../lib/utils.sh"

# =============================================================================
# LOGGING TESTS
# =============================================================================

test_log_debug() {
    local test_output
    test_output=$(create_temp_file "log_debug")
    
    # Test debug logging when LOG_LEVEL is DEBUG
    LOG_LEVEL="DEBUG" log_debug "Test debug message" 2>"$test_output"
    assert_contains "$(cat "$test_output")" "Test debug message" "Debug message should be logged when LOG_LEVEL is DEBUG"
    
    # Test debug logging when LOG_LEVEL is INFO (should not log)
    LOG_LEVEL="INFO" log_debug "Test debug message" 2>"$test_output"
    assert_equals "" "$(cat "$test_output")" "Debug message should not be logged when LOG_LEVEL is INFO"
}

test_log_info() {
    local test_output
    test_output=$(create_temp_file "log_info")
    
    # Test info logging when LOG_LEVEL is INFO
    LOG_LEVEL="INFO" log_info "Test info message" 2>"$test_output"
    assert_contains "$(cat "$test_output")" "Test info message" "Info message should be logged when LOG_LEVEL is INFO"
    
    # Test info logging when LOG_LEVEL is WARN (should not log)
    LOG_LEVEL="WARN" log_info "Test info message" 2>"$test_output"
    assert_equals "" "$(cat "$test_output")" "Info message should not be logged when LOG_LEVEL is WARN"
}

test_log_warn() {
    local test_output
    test_output=$(create_temp_file "log_warn")
    
    # Test warn logging when LOG_LEVEL is WARN
    LOG_LEVEL="WARN" log_warn "Test warn message" 2>"$test_output"
    assert_contains "$(cat "$test_output")" "Test warn message" "Warn message should be logged when LOG_LEVEL is WARN"
    
    # Test warn logging when LOG_LEVEL is ERROR (should not log)
    LOG_LEVEL="ERROR" log_warn "Test warn message" 2>"$test_output"
    assert_equals "" "$(cat "$test_output")" "Warn message should not be logged when LOG_LEVEL is ERROR"
}

test_log_error() {
    local test_output
    test_output=$(create_temp_file "log_error")
    
    # Test error logging (should always log)
    LOG_LEVEL="ERROR" log_error "Test error message" 2>"$test_output"
    assert_contains "$(cat "$test_output")" "Test error message" "Error message should always be logged"
}

test_log_timestamps() {
    local test_output
    test_output=$(create_temp_file "log_timestamps")
    
    # Test with timestamps enabled
    LOG_TIMESTAMPS="true" LOG_LEVEL="INFO" log_info "Test message" 2>"$test_output"
    assert_contains "$(cat "$test_output")" "$(date '+%Y-%m-%d')" "Log should contain timestamp when LOG_TIMESTAMPS is true"
    
    # Test with timestamps disabled
    LOG_TIMESTAMPS="false" LOG_LEVEL="INFO" log_info "Test message" 2>"$test_output"
    assert_not_equals "" "$(cat "$test_output")" "Log should not be empty"
}

# =============================================================================
# VALIDATION TESTS
# =============================================================================

test_validate_required_env() {
    # Test with all required variables set
    TEST_VAR1="value1" TEST_VAR2="value2" validate_required_env "TEST_VAR1" "TEST_VAR2"
    local result=$?
    assert_equals 0 $result "validate_required_env should succeed when all variables are set"
    
    # Test with missing variable (should fail)
    unset TEST_VAR1 2>/dev/null || true
    if TEST_VAR2="value2" validate_required_env "TEST_VAR1" "TEST_VAR2" 2>/dev/null; then
        assert_true false "validate_required_env should fail when variables are missing"
    else
        assert_true true "validate_required_env correctly failed for missing variable"
    fi
}

test_validate_repo_format() {
    # Test valid repository formats
    validate_repo_format "owner/repo"
    assert_equals 0 $? "Valid repo format should pass validation"
    
    validate_repo_format "my-org/my-repo"
    assert_equals 0 $? "Valid repo format with hyphens should pass validation"
    
    validate_repo_format "org123/repo_name"
    assert_equals 0 $? "Valid repo format with numbers and underscores should pass validation"
    
    # Test invalid repository formats
    if validate_repo_format "invalid-repo" 2>/dev/null; then
        assert_true false "Invalid repo format should fail validation"
    else
        assert_true true "Invalid repo format correctly failed validation"
    fi
    
    if validate_repo_format "owner/repo/extra" 2>/dev/null; then
        assert_true false "Repo format with extra parts should fail validation"
    else
        assert_true true "Repo format with extra parts correctly failed validation"
    fi
}

test_validate_pr_number() {
    # Test valid PR numbers
    validate_pr_number "123"
    assert_equals 0 $? "Valid PR number should pass validation"
    
    validate_pr_number "1"
    assert_equals 0 $? "Single digit PR number should pass validation"
    
    # Test invalid PR numbers
    if validate_pr_number "abc" 2>/dev/null; then
        assert_true false "Non-numeric PR number should fail validation"
    else
        assert_true true "Non-numeric PR number correctly failed validation"
    fi
    
    if validate_pr_number "-123" 2>/dev/null; then
        assert_true false "Negative PR number should fail validation"
    else
        assert_true true "Negative PR number correctly failed validation"
    fi
    
    if validate_pr_number "0" 2>/dev/null; then
        assert_true false "Zero PR number should fail validation"
    else
        assert_true true "Zero PR number correctly failed validation"
    fi
}

test_validate_merge_method() {
    # Test valid merge methods
    validate_merge_method "squash"
    assert_equals 0 $? "squash merge method should pass validation"
    
    validate_merge_method "merge"
    assert_equals 0 $? "merge method should pass validation"
    
    validate_merge_method "rebase"
    assert_equals 0 $? "rebase merge method should pass validation"
    
    # Test invalid merge methods
    if validate_merge_method "invalid" 2>/dev/null; then
        assert_true false "Invalid merge method should fail validation"
    else
        assert_true true "Invalid merge method correctly failed validation"
    fi
}

# =============================================================================
# UTILITY FUNCTION TESTS
# =============================================================================

test_command_exists() {
    # Test with existing command
    assert_true "$(command_exists "bash" && echo "true" || echo "false")" "bash command should exist"
    
    # Test with non-existing command
    assert_false "$(command_exists "nonexistent_command_12345" && echo "true" || echo "false")" "Non-existent command should not exist"
}

test_trim() {
    # Test trimming whitespace
    local result
    result=$(trim "  hello world  ")
    assert_equals "hello world" "$result" "Should trim leading and trailing whitespace"
    
    result=$(trim "no-whitespace")
    assert_equals "no-whitespace" "$result" "Should handle strings without whitespace"
    
    result=$(trim "")
    assert_equals "" "$result" "Should handle empty strings"
    
    result=$(trim "   ")
    assert_equals "" "$result" "Should handle strings with only whitespace"
}

test_array_contains() {
    local test_array=("apple" "banana" "cherry")
    
    # Test element that exists
    if array_contains "banana" "${test_array[@]}"; then
        assert_true true "Should find existing element in array"
    else
        assert_true false "Should find existing element in array"
    fi
    
    # Test element that doesn't exist
    if array_contains "grape" "${test_array[@]}"; then
        assert_true false "Should not find non-existing element in array"
    else
        assert_true true "Should not find non-existing element in array"
    fi
}

test_split_string() {
    local result_array=()
    
    # Test splitting comma-separated string
    split_string "apple,banana,cherry" "," result_array
    assert_equals 3 "${#result_array[@]}" "Should split string into 3 elements"
    assert_equals "apple" "${result_array[0]}" "First element should be 'apple'"
    assert_equals "banana" "${result_array[1]}" "Second element should be 'banana'"
    assert_equals "cherry" "${result_array[2]}" "Third element should be 'cherry'"
    
    # Test splitting with different delimiter
    split_string "one|two|three" "|" result_array
    assert_equals 3 "${#result_array[@]}" "Should split string with pipe delimiter"
    assert_equals "one" "${result_array[0]}" "First element should be 'one'"
}

# =============================================================================
# CACHE FUNCTION TESTS
# =============================================================================

test_generate_cache_key() {
    local key1 key2
    key1=$(generate_cache_key "test" "key" "parts")
    key2=$(generate_cache_key "test" "key" "parts")
    
    assert_equals "$key1" "$key2" "Same input should generate same cache key"
    
    local key3
    key3=$(generate_cache_key "different" "key" "parts")
    assert_not_equals "$key1" "$key3" "Different input should generate different cache key"
    
    # Check that key is a valid hash (64 characters)
    assert_equals 64 "${#key1}" "Cache key should be 64 characters long (SHA256)"
}

test_cache_operations() {
    local cache_key test_data
    cache_key=$(generate_cache_key "test" "cache" "operations")
    test_data="This is test data for caching"
    
    # Enable caching for this test
    ENABLE_CACHE=true
    
    # Test cache miss
    if cache_get "$cache_key" >/dev/null 2>&1; then
        assert_true false "Cache should miss on first attempt"
    else
        assert_true true "Cache correctly missed on first attempt"
    fi
    
    # Test cache set
    cache_set "$cache_key" "$test_data"
    
    # Test cache hit
    local cached_data
    cached_data=$(cache_get "$cache_key")
    assert_equals "$test_data" "$cached_data" "Cached data should match original data"
    
    # Clean up
    rm -f "${CACHE_DIR}/${cache_key}" 2>/dev/null || true
}

test_cache_ttl() {
    local cache_key cache_file
    cache_key=$(generate_cache_key "test" "ttl")
    cache_file="${CACHE_DIR}/${cache_key}"
    
    # Create a cache file
    echo "test data" > "$cache_file"
    
    # Test that fresh cache is valid
    if is_cache_valid "$cache_file" 300; then
        assert_true true "Fresh cache should be valid"
    else
        assert_true false "Fresh cache should be valid"
    fi
    
    # Test that expired cache is invalid (using 0 TTL)
    if is_cache_valid "$cache_file" 0; then
        assert_true false "Expired cache should be invalid"
    else
        assert_true true "Expired cache correctly identified as invalid"
    fi
    
    # Clean up
    rm -f "$cache_file" 2>/dev/null || true
}

# =============================================================================
# TEST RUNNER
# =============================================================================

main() {
    init_test_framework "utils-tests"
    set_verbose
    
    echo "🧪 Running Utils Module Unit Tests"
    echo "=================================="
    
    # Set up test environment
    setup_test_environment
    
    # Run logging tests
    echo "📝 Testing Logging Functions..."
    run_test "test_log_debug" "Debug logging with different log levels"
    run_test "test_log_info" "Info logging with different log levels"
    run_test "test_log_warn" "Warning logging with different log levels"
    run_test "test_log_error" "Error logging"
    run_test "test_log_timestamps" "Timestamp formatting in logs"
    
    # Run validation tests
    echo "✅ Testing Validation Functions..."
    run_test "test_validate_required_env" "Environment variable validation"
    run_test "test_validate_repo_format" "Repository format validation"
    run_test "test_validate_pr_number" "PR number validation"
    run_test "test_validate_merge_method" "Merge method validation"
    
    # Run utility function tests
    echo "🔧 Testing Utility Functions..."
    run_test "test_command_exists" "Command existence checking"
    run_test "test_trim" "String trimming"
    run_test "test_array_contains" "Array element checking"
    run_test "test_split_string" "String splitting"
    
    # Run cache function tests
    echo "💾 Testing Cache Functions..."
    run_test "test_generate_cache_key" "Cache key generation"
    run_test "test_cache_operations" "Cache set/get operations"
    run_test "test_cache_ttl" "Cache TTL validation"
    
    # Clean up test environment
    cleanup_test_environment
    
    # Print summary and exit
    print_test_summary
}

# Run tests if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
